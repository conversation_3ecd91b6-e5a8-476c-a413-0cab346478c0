#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復版本 DDE 監控器

解決 127 個連接限制 - 使用單一連接處理所有商品
"""

import asyncio
import signal
import time
import json
import sys
import csv
from pathlib import Path
from datetime import datetime
from collections import defaultdict

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from dydde.dydde import DDEClient


class FixedDDEMonitor:
    """修復版本 DDE 監控器 - 正確的連接管理"""
    
    def __init__(self, config_file="config/system_config.json"):
        self.config_file = config_file
        self.config = None
        self.dde_connections = {}  # key: "service.topic", value: DDEClient
        self.product_items = []    # 所有產品的項目列表
        self.running = False
        self.data_count = 0
        self.start_time = None
        self.csv_file = None
        self.json_data = []
        
    def load_config(self):
        """加載配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            return True
        except Exception as e:
            print(f"❌ 配置加載失敗: {str(e)}")
            return False
    
    def setup_output_files(self):
        """設置輸出文件"""
        output_dir = Path("outputs")
        output_dir.mkdir(exist_ok=True)
        
        # 創建 CSV 文件
        csv_path = output_dir / "fixed_dde_data.csv"
        self.csv_file = open(csv_path, 'w', newline='', encoding='utf-8')
        self.csv_writer = csv.writer(self.csv_file)
        self.csv_writer.writerow(['timestamp', 'item', 'original_value', 'processed_value'])
        
        print(f"📁 數據將保存到: {csv_path}")
    
    async def setup_dde_connections(self):
        """設置 DDE 連接 - 正確的方式"""
        print("🔗 設置 DDE 連接（修復版本）...")
        
        products = self.config.get('products', [])
        enabled_products = [p for p in products if p.get('enabled', False)]
        
        # 按 service.topic 分組產品
        service_groups = defaultdict(list)
        
        for product in enabled_products:
            symbol = product.get('symbol')
            service = product.get('service')
            topic = product.get('topic')
            items = product.get('items', [])
            
            service_key = f"{service}.{topic}"
            service_groups[service_key].append({
                'symbol': symbol,
                'items': items
            })
        
        print(f"📊 分析結果:")
        print(f"   總產品數: {len(enabled_products)}")
        print(f"   不同服務數: {len(service_groups)}")
        
        # 為每個不同的 service.topic 創建一個連接
        total_items = 0
        
        for service_key, products_in_group in service_groups.items():
            service, topic = service_key.split('.', 1)
            
            print(f"\n🔗 建立連接: {service_key}")
            print(f"   包含 {len(products_in_group)} 個產品")
            
            try:
                # 創建單一連接
                client = DDEClient(service, topic)
                client.connect()
                
                print(f"   ✅ 連接成功: {service_key}")
                
                # 測試所有項目
                valid_items = []
                
                for product_info in products_in_group:
                    symbol = product_info['symbol']
                    items = product_info['items']
                    
                    print(f"     測試產品: {symbol}")
                    
                    product_valid_items = []
                    for item in items:
                        try:
                            test_result = client.request(item)
                            if test_result and test_result.strip():
                                valid_items.append(item)
                                product_valid_items.append(item)
                                # 不顯示每個項目的結果，避免輸出過多
                            else:
                                print(f"       ⚪ {item}: 無數據")
                        except Exception as e:
                            print(f"       ❌ {item}: {str(e)}")
                    
                    if product_valid_items:
                        print(f"       ✅ {symbol}: {len(product_valid_items)}/{len(items)} 項目可用")
                    else:
                        print(f"       ❌ {symbol}: 沒有可用項目")
                
                if valid_items:
                    self.dde_connections[service_key] = client
                    self.product_items.extend(valid_items)
                    total_items += len(valid_items)
                    print(f"   ✅ {service_key}: {len(valid_items)} 個有效項目")
                else:
                    client.disconnect()
                    print(f"   ❌ {service_key}: 沒有有效項目")
                
            except Exception as e:
                print(f"   ❌ {service_key} 連接失敗: {str(e)}")
        
        print(f"\n✅ DDE 連接設置完成:")
        print(f"   實際連接數: {len(self.dde_connections)}")
        print(f"   總有效項目: {total_items}")
        
        # 關鍵信息
        if len(self.dde_connections) == 1:
            print(f"🎉 完美！只使用了 1 個 DDE 連接處理所有商品")
        else:
            print(f"⚠️  使用了 {len(self.dde_connections)} 個連接")
    
    def write_data(self, item, value):
        """寫入數據"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # 寫入 CSV
        self.csv_writer.writerow([timestamp, item, value, value])
        
        # 添加到 JSON 數據
        self.json_data.append({
            'timestamp': timestamp,
            'item': item,
            'original_value': value,
            'processed_value': value
        })
        
        self.data_count += 1
        
        # 每1000筆數據刷新一次
        if self.data_count % 1000 == 0:
            self.csv_file.flush()
    
    async def start_monitoring(self):
        """開始監控"""
        print("\n🚀 開始 DDE 數據監控（修復版本）")
        print("=" * 60)
        
        self.running = True
        self.start_time = time.time()
        
        # 設置信號處理
        def signal_handler(signum, frame):
            print(f"\n📡 接收到停止信號")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        print(f"📊 監控統計:")
        print(f"   DDE 連接數: {len(self.dde_connections)}")
        print(f"   監控項目數: {len(self.product_items)}")
        print(f"⏰ 開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🛑 按 Ctrl+C 停止監控")
        print("=" * 60)
        
        # 主監控循環
        await self.monitoring_loop()
    
    async def monitoring_loop(self):
        """監控循環 - 使用單一連接輪詢所有項目"""
        last_status_time = time.time()
        
        try:
            while self.running:
                # 輪詢所有項目（使用復用的連接）
                for service_key, client in self.dde_connections.items():
                    # 獲取該連接對應的所有項目
                    for item in self.product_items:
                        try:
                            value = client.request(item)
                            if value and value.strip():
                                self.write_data(item, value)
                        except Exception as e:
                            # 靜默處理錯誤，避免日誌過多
                            pass
                
                # 每30秒顯示一次狀態
                current_time = time.time()
                if current_time - last_status_time >= 30:
                    await self.show_status()
                    last_status_time = current_time
                
                # 輪詢間隔
                await asyncio.sleep(2.0)  # 2秒間隔，適合大量項目
                
        except Exception as e:
            print(f"❌ 監控循環錯誤: {str(e)}")
        finally:
            await self.stop_monitoring()
    
    async def show_status(self):
        """顯示狀態"""
        try:
            elapsed = time.time() - self.start_time
            rate = self.data_count / elapsed if elapsed > 0 else 0
            
            print(f"\n📊 監控狀態 ({datetime.now().strftime('%H:%M:%S')})")
            print(f"   運行時間: {elapsed/60:.1f} 分鐘")
            print(f"   數據筆數: {self.data_count}")
            print(f"   數據速率: {rate:.1f} 筆/秒")
            print(f"   DDE 連接數: {len(self.dde_connections)}")
            print(f"   監控項目數: {len(self.product_items)}")
            
            # 檢查文件大小
            if self.csv_file:
                self.csv_file.flush()
                csv_path = Path("outputs/fixed_dde_data.csv")
                if csv_path.exists():
                    size = csv_path.stat().st_size
                    print(f"   文件大小: {size/1024/1024:.2f} MB")
            
        except Exception as e:
            print(f"⚠️  狀態顯示錯誤: {str(e)}")
    
    async def stop_monitoring(self):
        """停止監控"""
        print(f"\n🛑 停止監控...")
        self.running = False
        
        # 關閉 DDE 連接
        for service_key, client in self.dde_connections.items():
            try:
                client.disconnect()
                print(f"   ✅ {service_key} 連接已關閉")
            except Exception as e:
                print(f"   ⚠️  {service_key} 關閉錯誤: {str(e)}")
        
        # 保存 JSON 文件
        if self.json_data:
            json_path = Path("outputs/fixed_dde_data.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(self.json_data, f, ensure_ascii=False, indent=2)
            print(f"   ✅ JSON 文件已保存: {len(self.json_data)} 筆記錄")
        
        # 關閉 CSV 文件
        if self.csv_file:
            self.csv_file.close()
            print(f"   ✅ CSV 文件已關閉")
        
        # 最終統計
        if self.start_time:
            elapsed = time.time() - self.start_time
            rate = self.data_count / elapsed if elapsed > 0 else 0
            
            print(f"\n📊 最終統計:")
            print(f"   總運行時間: {elapsed/60:.1f} 分鐘")
            print(f"   總數據筆數: {self.data_count}")
            print(f"   平均速率: {rate:.1f} 筆/秒")
            print(f"   使用連接數: {len(self.dde_connections)}")
        
        print("✅ 監控停止完成")
    
    async def run(self):
        """運行監控器"""
        try:
            # 加載配置
            if not self.load_config():
                return False
            
            # 設置輸出文件
            self.setup_output_files()
            
            # 設置 DDE 連接
            await self.setup_dde_connections()
            
            if not self.dde_connections:
                print("❌ 沒有可用的 DDE 連接")
                return False
            
            # 開始監控
            await self.start_monitoring()
            
            return True
            
        except KeyboardInterrupt:
            print(f"\n⏹️  用戶中斷")
        except Exception as e:
            print(f"❌ 運行失敗: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            await self.stop_monitoring()
        
        return False


async def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='修復版本 DDE 監控器')
    parser.add_argument('--config', default='config/multi_product_config.json', 
                       help='配置文件路徑')
    
    args = parser.parse_args()
    
    print("🔧 修復版本 DDE 監控器")
    print("解決 127 個連接限制 - 使用正確的連接管理")
    print("=" * 60)
    
    monitor = FixedDDEMonitor(args.config)
    await monitor.run()


if __name__ == "__main__":
    # 設置事件循環策略
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 運行監控器
    asyncio.run(main())
