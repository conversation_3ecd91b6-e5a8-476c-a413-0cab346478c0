# Schedule 模式結束時間行為設定

## 功能說明

在 `auto_connect_mode = schedule` 模式下，程式會根據設定的時間表自動進行連線和斷線操作。現在新增了一個設定選項，可以控制在結束時間時的行為。

## 設定選項

在 `config.ini` 的 `[AutoConnect]` 區塊中新增了以下設定：

```ini
# schedule 模式結束時間的行為: disconnect (完整斷線+取消訂閱) 或 unadvise_only (僅取消訂閱，預設值)
# 無效設定值會自動更正為 unadvise_only
schedule_end_action = unadvise_only
```

### 可選值

1. **`unadvise_only`** (預設值)
   - 在結束時間僅取消訂閱
   - 包含：僅取消所有訂閱 (unadvise)
   - 保持 DDE 連線不斷開

2. **`disconnect`**
   - 在結束時間執行完整的斷線操作
   - 包含：取消所有訂閱 (unadvise) + 斷開 DDE 連線 (disconnect)

### 自動修正機制

- 如果設定值無效或缺失，系統會自動設定為 `unadvise_only`
- 無效值會觸發警告日誌並自動更新配置文件
- 支援大小寫不敏感（如 `DISCONNECT`、`Unadvise_Only` 等）

## 使用場景

### 使用 `unadvise_only` 的情況（推薦）
- 希望保持與 DDE 服務的連線，但停止接收資料
- 方便在下一個時間段快速重新訂閱
- 避免重複建立連線的開銷
- 適合多時間段交易的場景

### 使用 `disconnect` 的情況
- 希望在結束時間完全斷開與 DDE 服務的連線
- 節省系統資源
- 避免意外的資料接收

## 設定範例

### 僅取消訂閱模式（預設，推薦）
```ini
[AutoConnect]
enable_auto_connect = True
auto_connect_mode = schedule
schedule_connect_times = 08:25:00-13:45:15;14:50:00-05:05:05
schedule_end_action = unadvise_only
```

### 完整斷線模式
```ini
[AutoConnect]
enable_auto_connect = True
auto_connect_mode = schedule
schedule_connect_times = 08:25:00-13:45:15;14:50:00-05:05:05
schedule_end_action = disconnect
```

## 日誌輸出

程式會在日誌中記錄相應的操作：

- `disconnect` 模式：`時間表自動斷線 (disconnect): HH:MM:SS`
- `unadvise_only` 模式：`時間表自動取消訂閱 (僅unadvise): HH:MM:SS`

## 注意事項

1. 此設定僅在 `auto_connect_mode = schedule` 時生效
2. 如果設定值無效或缺失，將自動預設並更正為 `unadvise_only`
3. 無效設定會自動更新配置文件並記錄警告日誌
4. 設定變更後需要重新啟動程式或重新載入設定才會生效
5. 設定值支援大小寫不敏感

## 測試結果範例

```
=== 无效值测试 ===
[WARNING] schedule_end_action 设定值无效: 'invalid_value'，已更正为 'unadvise_only'
[INFO] 已自动更正配置文件中的 schedule_end_action 设定
结果: schedule_end_action = 'unadvise_only'
```
