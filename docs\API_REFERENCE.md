# DDE 監控程式 API 參考文件

## 核心 API 參考

### DDEMonitor 類別

#### 初始化方法
```python
def __init__(self):
    """初始化 DDE 監控程式
    
    初始化順序:
    1. 載入設定檔
    2. 設置日誌系統
    3. 初始化 UI
    4. 初始化資料容器
    5. 初始化檔案處理器
    6. 啟動資料處理器線程
    """
```

#### 設定管理
```python
def load_config(self) -> None:
    """載入設定檔
    
    從 config.ini 讀取所有必要設定:
    - DDE 連接參數
    - 監控項目列表
    - 資料處理規則
    - 檔案輸出設定
    
    Raises:
        Exception: 設定檔讀取失敗時拋出異常
    """

def setup_logging(self) -> None:
    """設置日誌系統
    
    配置:
    - 日誌級別: INFO
    - 輸出格式: [時間] [級別] 訊息
    - 輸出目標: 檔案 + 控制台
    - 檔案路徑: ./logs/{date}/dde_monitor.log
    """
```

#### DDE 連接管理
```python
def connect_service(self) -> None:
    """連接 DDE 服務
    
    執行步驟:
    1. 驗證服務名稱和主題
    2. 建立 DDEClient 實例
    3. 執行連接操作
    4. 更新 UI 狀態
    
    Raises:
        DDEClientError: DDE 連接失敗時拋出
    """

def disconnect_service(self) -> None:
    """斷開 DDE 服務連接
    
    執行步驟:
    1. 取消所有訂閱
    2. 斷開 DDE 連接
    3. 清理資源
    4. 更新 UI 狀態
    """

def test_items(self) -> None:
    """測試所有監控項目
    
    對每個設定的項目執行 request 操作:
    - 成功: 更新項目值和狀態
    - 失敗: 標記為測試失敗
    
    完成後啟用訂閱按鈕
    """
```

#### 訂閱管理
```python
def toggle_subscription(self) -> None:
    """切換項目訂閱狀態
    
    根據當前狀態執行:
    - 未訂閱: 訂閱所有已測試的項目
    - 已訂閱: 取消所有項目訂閱
    
    使用 advise/unadvise 方法管理訂閱
    """
```

#### 資料處理
```python
def on_advise_data(self, item: str, value: str) -> None:
    """DDE 資料更新回調
    
    Args:
        item: 項目代碼
        value: 項目值
        
    處理流程:
    1. 將資料加入處理佇列
    2. 由 DataProcessor 批次處理
    3. 觸發 _process_advise_data
    """

def _process_advise_data(self, item: str, value: str) -> None:
    """實際處理資料的方法
    
    Args:
        item: 項目代碼
        value: 項目值
        
    處理步驟:
    1. 檢查項目重複換行
    2. 更新原始資料
    3. 更新項目資料
    4. 更新最後接收時間
    """
```

### DataFileHandler 類別

#### 初始化
```python
def __init__(self):
    """初始化檔案處理器
    
    屬性:
        data_file: 原始資料檔案路徑
        complete_data_file: 完整資料檔案路徑
        enable_data_file: 是否啟用原始資料檔案
        enable_complete_data_file: 是否啟用完整資料檔案
        enable_log_file: 是否啟用日誌檔案
    """

def init_files(self, config: configparser.ConfigParser, 
               items_data: Dict[str, ItemData]) -> None:
    """初始化檔案
    
    Args:
        config: 設定檔物件
        items_data: 項目資料字典
        
    執行步驟:
    1. 讀取檔案輸出控制設定
    2. 格式化檔案路徑 (支援 {date} 變數)
    3. 確保目錄存在
    4. 寫入 CSV 標題行
    """
```

#### 檔案操作
```python
def ensure_directory(self, file_path: str) -> None:
    """確保目錄存在
    
    Args:
        file_path: 檔案路徑
        
    如果目錄不存在則建立，支援多層目錄建立
    """

def write_headers(self) -> None:
    """寫入 CSV 標題行
    
    標題格式: [接收日期, 接收時間, 項目1名稱, 項目2名稱, ...]
    按照設定檔中的項目順序排列
    """

def save_row(self, row: RawDataRow, is_complete: bool = False) -> None:
    """儲存資料行
    
    Args:
        row: 要儲存的資料行
        is_complete: 是否為完整資料行
        
    根據 is_complete 參數決定寫入哪個檔案:
    - True: 寫入完整資料檔案
    - False: 寫入原始資料檔案
    """
```

### DataProcessor 類別

#### 初始化和控制
```python
def __init__(self):
    """初始化資料處理器
    
    屬性:
        data_queue: 資料佇列 (Queue)
        running: 運行狀態標誌
    """

def add_data(self, item: str, value: str) -> None:
    """將資料加入佇列
    
    Args:
        item: 項目代碼
        value: 項目值
        
    線程安全的佇列操作
    """

def process_data(self) -> None:
    """處理資料的主循環
    
    處理邏輯:
    1. 收集一批資料 (最多100筆)
    2. 批次處理資料
    3. 發送處理完成信號
    4. 短暫休眠避免過度消耗 CPU
    """

def stop(self) -> None:
    """停止資料處理器
    
    設置 running = False，結束處理循環
    """
```

### dydde.DDEClient 類別 (參考)

#### 連接管理
```python
def connect(self) -> None:
    """連接到 DDE 服務器
    
    執行 DDE 初始化和連接建立
    """

def disconnect(self, terminate_dde: bool = True) -> None:
    """斷開 DDE 連接
    
    Args:
        terminate_dde: 是否終止 DDE (影響其他程式)
        
    執行步驟:
    1. 取消所有訂閱
    2. 斷開連接 (可選)
    3. 清理資源
    4. 終止 DDE (可選)
    """

def is_connected(self) -> bool:
    """檢查連接狀態
    
    Returns:
        bool: 是否已連接
    """
```

#### 資料操作
```python
def request(self, item: str, timeout: Optional[int] = None) -> Optional[str]:
    """同步請求資料
    
    Args:
        item: 項目代碼
        timeout: 超時時間 (毫秒)
        
    Returns:
        str | None: 請求到的資料，失敗返回 None
        
    Raises:
        DDEClientError: 連接錯誤時拋出
    """

def advise(self, item: str, callback: Callable) -> bool:
    """訂閱資料更新
    
    Args:
        item: 項目代碼
        callback: 回調函數 (item, value) -> None
        
    Returns:
        bool: 訂閱是否成功
    """

def unadvise(self, item: str) -> bool:
    """取消訂閱
    
    Args:
        item: 項目代碼
        
    Returns:
        bool: 取消訂閱是否成功
    """
```

## 資料結構參考

### ItemData
```python
@dataclass
class ItemData:
    name: str                    # 項目顯示名稱
    code: str                    # DDE 項目代碼  
    value: Optional[str] = None  # 當前值
    update_time: Optional[datetime] = None  # 最後更新時間
    status: str = "未訂閱"        # 狀態: 未訂閱/已測試/已訂閱/測試失敗/訂閱失敗
```

### RawDataRow
```python
@dataclass
class RawDataRow:
    receive_date: str            # 接收日期 (YYYY-MM-DD)
    receive_time: str            # 接收時間 (HH:MM:SS.ffffff)
    values: Dict[str, str]       # 項目值字典 {項目代碼: 值}
    is_complete: bool = False    # 是否為完整資料行
```

## 設定檔 API

### config.ini 結構
```ini
[DDE]
service = XQTISC              # DDE 服務名稱
topic = Quote                 # DDE 主題
disconnect_on_exit = false    # 程式結束時是否斷開連接

[Items]
item{n}_name = 顯示名稱       # 項目顯示名稱
item{n}_code = DDE代碼        # DDE 項目代碼

[Table]
enable_time_newline = true    # 啟用時間間隔換行
time_newline_interval = 0.8   # 換行間隔 (秒)
enable_value_change_check = true  # 啟用值變化檢查
value_change_check_mode = all     # 檢查模式: single/multiple/all

[FileOutput]
enable_data_file = false      # 啟用原始資料檔案
enable_complete_data_file = true  # 啟用完整資料檔案
enable_log_file = true        # 啟用日誌檔案

[OutputPath]
log_file = ./logs/{date}/dde_monitor.log        # 日誌檔案路徑
data_file = ./data/dde_data_{date}.csv          # 原始資料檔案路徑
complete_data_file = ./data/complete_data_{date}.csv  # 完整資料檔案路徑
```

## 錯誤處理 API

### 異常類別
```python
class DDEError(Exception):
    """DDE 錯誤基類"""
    
class DDEClientError(DDEError):
    """DDE 客戶端錯誤"""
    
class DDEServerError(DDEError):
    """DDE 服務器錯誤"""
```

### 錯誤代碼
- `DMLERR_NO_CONV_ESTABLISHED`: 無法建立會話
- `DMLERR_SERVER_DIED`: 服務器已終止
- `DMLERR_ADVACKTIMEOUT`: 同步建議事務超時
- `DMLERR_DATAACKTIMEOUT`: 同步數據事務超時

---
*文件版本*: v1.0  
*建立日期*: 2025-06-16  
*最後更新*: 2025-06-16
