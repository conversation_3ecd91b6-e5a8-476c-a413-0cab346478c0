#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 檔案處理器
處理DDE資料的檔案輸出功能
"""

import os
import csv
import logging
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

from .data_structures import ItemData, RawDataRow


class DataFileHandler:
    """資料檔案處理器
    
    負責處理DDE資料的檔案輸出，包括:
    - CSV 資料檔案
    - 完整資料檔案
    - 日誌檔案
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """初始化檔案處理器
        
        Args:
            logger: 日誌記錄器
        """
        self.logger = logger or logging.getLogger(__name__)
        
        # 檔案路徑
        self.log_file: Optional[str] = None
        self.data_file: Optional[str] = None
        self.complete_data_file: Optional[str] = None
        
        # 檔案輸出選項
        self.enable_log_file = True
        self.enable_data_file = False
        self.enable_complete_data_file = True
        
        # 項目資料
        self.items_data: Dict[str, ItemData] = {}
        
        # 檔案標題行
        self._headers_initialized = False
    
    def init_file_paths(self, 
                       log_file: Optional[str] = None,
                       data_file: Optional[str] = None,
                       complete_data_file: Optional[str] = None):
        """初始化檔案路徑
        
        Args:
            log_file: 日誌檔案路徑
            data_file: 資料檔案路徑
            complete_data_file: 完整資料檔案路徑
        """
        self.log_file = log_file
        self.data_file = data_file
        self.complete_data_file = complete_data_file
        
        # 創建必要的目錄
        self._create_directories()
        
        self.logger.info("檔案路徑初始化完成")
    
    def set_items_data(self, items_data: Dict[str, ItemData]):
        """設置項目資料
        
        Args:
            items_data: 項目資料字典
        """
        self.items_data = items_data
        self._headers_initialized = False
        self.logger.info(f"設置項目資料完成，項目數: {len(items_data)}")
    
    def init_file_headers(self):
        """初始化檔案標題行"""
        try:
            if self._headers_initialized:
                return
                
            # 初始化完整資料檔案標題行
            if self.enable_complete_data_file and self.complete_data_file:
                self._init_complete_data_file_header()
                
            # 初始化資料檔案標題行
            if self.enable_data_file and self.data_file:
                self._init_data_file_header()
                
            self._headers_initialized = True
            self.logger.info("檔案標題行初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化檔案標題行失敗: {str(e)}")
    
    def save_row(self, row: RawDataRow, is_complete: bool = False):
        """保存資料行
        
        Args:
            row: 要保存的資料行
            is_complete: 是否為完整資料行
        """
        try:
            # 確保標題行已初始化
            if not self._headers_initialized:
                self.init_file_headers()
            
            # 保存到完整資料檔案
            if is_complete and self.enable_complete_data_file and self.complete_data_file:
                self._save_to_complete_data_file(row)
                
            # 保存到資料檔案
            if self.enable_data_file and self.data_file:
                self._save_to_data_file(row)
                
            # 記錄到日誌檔案
            if self.enable_log_file and self.log_file:
                self._log_to_file(row, is_complete)
                
        except Exception as e:
            self.logger.error(f"保存資料行失敗: {str(e)}")
    
    def _create_directories(self):
        """創建必要的目錄"""
        try:
            for file_path in [self.log_file, self.data_file, self.complete_data_file]:
                if file_path:
                    directory = os.path.dirname(file_path)
                    if directory:
                        Path(directory).mkdir(parents=True, exist_ok=True)
                        
        except Exception as e:
            self.logger.error(f"創建目錄失敗: {str(e)}")
    
    def _init_complete_data_file_header(self):
        """初始化完整資料檔案標題行"""
        try:
            if not os.path.exists(self.complete_data_file):
                with open(self.complete_data_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    
                    # 構建標題行
                    headers = ['接收日期', '接收時間']
                    
                    # 按照項目順序添加標題
                    for item_code, item_data in self.items_data.items():
                        headers.append(item_data.name)
                    
                    writer.writerow(headers)
                    
                self.logger.debug(f"完整資料檔案標題行已創建: {self.complete_data_file}")
                
        except Exception as e:
            self.logger.error(f"初始化完整資料檔案標題行失敗: {str(e)}")
    
    def _init_data_file_header(self):
        """初始化資料檔案標題行"""
        try:
            if not os.path.exists(self.data_file):
                with open(self.data_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    
                    # 構建標題行
                    headers = ['接收日期', '接收時間', '項目代碼', '項目名稱', '項目值']
                    writer.writerow(headers)
                    
                self.logger.debug(f"資料檔案標題行已創建: {self.data_file}")
                
        except Exception as e:
            self.logger.error(f"初始化資料檔案標題行失敗: {str(e)}")
    
    def _save_to_complete_data_file(self, row: RawDataRow):
        """保存到完整資料檔案
        
        Args:
            row: 資料行
        """
        try:
            with open(self.complete_data_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 構建資料行
                data_row = [row.receive_date, row.receive_time]
                
                # 按照項目順序添加值
                for item_code, item_data in self.items_data.items():
                    value = row.values.get(item_code, "")
                    data_row.append(value)
                
                writer.writerow(data_row)
                
        except Exception as e:
            self.logger.error(f"保存到完整資料檔案失敗: {str(e)}")
    
    def _save_to_data_file(self, row: RawDataRow):
        """保存到資料檔案
        
        Args:
            row: 資料行
        """
        try:
            with open(self.data_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 為每個項目寫入一行
                for item_code, value in row.values.items():
                    item_name = ""
                    if item_code in self.items_data:
                        item_name = self.items_data[item_code].name
                        
                    data_row = [row.receive_date, row.receive_time, item_code, item_name, value]
                    writer.writerow(data_row)
                
        except Exception as e:
            self.logger.error(f"保存到資料檔案失敗: {str(e)}")
    
    def _log_to_file(self, row: RawDataRow, is_complete: bool):
        """記錄到日誌檔案
        
        Args:
            row: 資料行
            is_complete: 是否為完整資料行
        """
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                timestamp = f"{row.receive_date} {row.receive_time}"
                status = "完整" if is_complete else "部分"
                item_count = len(row.values)
                
                log_line = f"{timestamp} - {status}資料行，項目數: {item_count}\n"
                f.write(log_line)
                
        except Exception as e:
            self.logger.error(f"記錄到日誌檔案失敗: {str(e)}")
    
    def clear_files(self):
        """清空所有檔案"""
        try:
            for file_path in [self.log_file, self.data_file, self.complete_data_file]:
                if file_path and os.path.exists(file_path):
                    open(file_path, 'w').close()
                    
            self._headers_initialized = False
            self.logger.info("所有檔案已清空")
            
        except Exception as e:
            self.logger.error(f"清空檔案失敗: {str(e)}")
    
    def get_file_info(self) -> Dict[str, Dict[str, any]]:
        """獲取檔案資訊
        
        Returns:
            檔案資訊字典
        """
        info = {}
        
        for name, path in [
            ('log_file', self.log_file),
            ('data_file', self.data_file),
            ('complete_data_file', self.complete_data_file)
        ]:
            if path:
                info[name] = {
                    'path': path,
                    'exists': os.path.exists(path),
                    'size': os.path.getsize(path) if os.path.exists(path) else 0,
                    'modified': datetime.fromtimestamp(os.path.getmtime(path)) if os.path.exists(path) else None
                }
            else:
                info[name] = {'path': None, 'exists': False, 'size': 0, 'modified': None}
                
        return info
