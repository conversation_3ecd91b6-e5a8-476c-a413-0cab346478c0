# 多商品版本比较测试指南

## 📋 概述

本文档说明如何使用引擎包装器版本与原本的多商品版本进行比较测试。

## 🏗️ 测试架构

```
原版多商品 (dde_monitor_multi.py)
    ↓
multi_config.ini (共用配置)
    ↓
引擎包装器版 (wrapper_multi_test.py)
    ↓
比较测试 (compare_multi_versions.py)
```

## 📁 文件结构

```
dde_engine_wrapper/
├── config/
│   └── multi_config.ini              # 多商品配置文件
├── core/
│   └── multi_config_parser.py        # 多商品配置解析器
├── examples/
│   ├── wrapper_multi_test.py         # 引擎包装器版测试程序
│   └── compare_multi_versions.py     # 版本比较测试
├── run_multi_test.py                 # 快速测试脚本
└── docs/
    └── MULTI_VERSION_COMPARISON.md   # 本文档
```

## 🔧 配置文件说明

### multi_config.ini 结构

```ini
# 系统设定
[System]
dde_service = XQTISC
dde_topic = Quote
output_base_path = ./data/
log_base_path = ./logs/

# 商品清单
[Symbols]
symbol_list = FITXN07.TF

# 资料类型模板
[Template_Tick_Items]
item1_name = 交易時間
item1_code = {symbol}-Time
# ... 更多项目

[DataType_tick]
items_template = Template_Tick_Items
table_enable_time_newline = true
table_time_newline_interval = 0.800
# ... 更多设定

# 商品配置
[FITXN07.TF]
enabled_types = tick,order,level2,daily
output_path = ./outputs/TEMP/data/mon/XQ/FITXN07/_m/
auto_connect_template = AutoConnect_FuturesFullDay
```

## 🚀 运行测试

### 1. 快速测试（仅引擎包装器版）

```bash
cd dde_engine_wrapper
python run_multi_test.py
```

### 2. 单独运行引擎包装器版

```bash
cd dde_engine_wrapper
python examples/wrapper_multi_test.py --config config/multi_config.ini
```

### 3. 定时测试（60秒）

```bash
cd dde_engine_wrapper
python examples/wrapper_multi_test.py --config config/multi_config.ini --duration 60
```

### 4. 版本比较测试

```bash
cd dde_engine_wrapper
python examples/compare_multi_versions.py --duration 60 --config multi_config.ini
```

## 📊 测试功能

### 引擎包装器版特性

1. **多商品配置解析**
   - 解析 multi_config.ini 格式
   - 转换为引擎包装器配置格式
   - 支持模板化配置

2. **并行处理**
   - 多线程任务处理
   - 优先级队列管理
   - 负载均衡

3. **实时监控**
   - 系统资源监控
   - 数据接收统计
   - 性能指标收集

4. **智能优化**
   - 内存使用优化
   - 线程池调优
   - 自动垃圾回收

### 比较测试功能

1. **进程监控**
   - CPU使用率监控
   - 内存使用监控
   - 运行时间统计

2. **性能比较**
   - 资源使用对比
   - 稳定性分析
   - 效率评估

3. **报告生成**
   - 详细比较报告
   - 性能图表
   - 建议输出

## 📈 测试指标

### 性能指标

- **CPU使用率**: 平均值、最大值
- **内存使用**: 平均值、峰值
- **运行时间**: 总运行时长
- **稳定性**: 是否完整运行

### 功能指标

- **数据接收**: 总接收数量
- **数据处理**: 处理成功率
- **错误率**: 异常发生频率
- **响应时间**: 数据处理延迟

## 🔍 测试结果分析

### 预期优势（引擎包装器版）

1. **更好的资源管理**
   - 智能内存优化
   - 动态线程调整
   - 自动垃圾回收

2. **更高的并发性能**
   - 多线程并行处理
   - 优先级任务调度
   - 负载均衡机制

3. **更强的监控能力**
   - 实时性能监控
   - 详细统计信息
   - 智能告警系统

4. **更好的可维护性**
   - 模块化架构
   - 完善的错误处理
   - 详细的日志记录

### 可能的劣势

1. **启动开销**
   - 更多的初始化步骤
   - 额外的监控线程
   - 复杂的架构层次

2. **内存占用**
   - 多层架构开销
   - 监控数据存储
   - 线程池维护

## 🛠️ 故障排除

### 常见问题

1. **配置文件不存在**
   ```
   解决方案: 确保 multi_config.ini 在正确位置
   ```

2. **DDE连接失败**
   ```
   解决方案: 检查DDE服务是否运行，确认商品代码正确
   ```

3. **进程启动失败**
   ```
   解决方案: 检查Python环境，确认依赖库已安装
   ```

4. **性能监控异常**
   ```
   解决方案: 确认psutil库已安装，检查进程权限
   ```

### 调试技巧

1. **启用详细日志**
   ```python
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **检查进程状态**
   ```bash
   ps aux | grep python
   ```

3. **监控资源使用**
   ```bash
   top -p <pid>
   ```

## 📝 测试报告示例

```
多版本比较测试报告
========================================
基本信息:
  测试时间: 2025-06-26 15:30:00
  测试持续: 60 秒
  配置文件: multi_config.ini

运行时间:
  原版多商品: 60.0 秒
  引擎包装器版: 60.0 秒

CPU使用率:
  原版多商品:
    平均: 15.2%
    最大: 28.5%
  引擎包装器版:
    平均: 12.8%
    最大: 22.1%

内存使用:
  原版多商品:
    平均: 45.6 MB
    最大: 52.3 MB
  引擎包装器版:
    平均: 38.2 MB
    最大: 44.7 MB

性能比较:
  CPU效率: 引擎包装器版比原版节省 15.8% CPU
  内存效率: 引擎包装器版比原版节省 16.2% 内存

稳定性分析:
  原版多商品: ✓ 稳定运行
  引擎包装器版: ✓ 稳定运行

总结:
  ✓ 两个版本都能稳定运行
  ✓ 引擎包装器版性能更优
```

## 🎯 测试建议

### 测试环境

1. **硬件要求**
   - CPU: 4核心以上
   - 内存: 8GB以上
   - 磁盘: SSD推荐

2. **软件要求**
   - Python 3.8+
   - PySide6 (可选)
   - psutil
   - 相关DDE库

### 测试策略

1. **短期测试** (1-5分钟)
   - 验证基本功能
   - 检查启动性能
   - 确认连接稳定

2. **中期测试** (10-30分钟)
   - 评估资源使用
   - 监控内存泄漏
   - 测试错误恢复

3. **长期测试** (1小时以上)
   - 验证长期稳定性
   - 评估性能衰减
   - 测试极限负载

## 🔮 未来改进

1. **增强监控**
   - 网络流量监控
   - 磁盘I/O监控
   - 更详细的性能分析

2. **自动化测试**
   - CI/CD集成
   - 自动化回归测试
   - 性能基准测试

3. **可视化报告**
   - 图表生成
   - 实时监控界面
   - 历史趋势分析
