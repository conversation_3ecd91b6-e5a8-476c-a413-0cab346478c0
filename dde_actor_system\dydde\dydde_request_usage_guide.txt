DDE 請求方法使用指南
==================

本文件說明了 DDEClient 中三種不同的請求方法及其適用場景。

1. 同步請求 (request)
-------------------
適用場景：
- 程序流程必須等待數據才能繼續執行
- 需要按特定順序處理多個請求
- 需要直接的錯誤處理
- 簡單的單次請求場景
- 命令行工具或批次處理程序

使用示例：
# 等待取得價格後才決定下單
price = client.request("QUOTE")
if price and float(price) < 100:
    place_order()

# 需要按順序處理多個數據
price = client.request("PRICE")
volume = client.request("VOLUME")
amount = float(price) * float(volume)

性能特點：
- 執行速度最快（約 0.002-0.004 秒）
- 直接返回結果
- 程序流程清晰易懂

優點：
- 使用簡單直觀
- 錯誤處理清晰
- 確保數據順序
- 執行速度最快
- 代碼結構簡單

缺點：
- 會阻塞主線程
- 不適合需要快速響應的場景
- 在 GUI 應用中可能造成界面卡頓


2. 異步回調請求 (request_async)
---------------------------
適用場景：
- 圖形界面應用程序
- 需要同時處理多個請求
- 不想阻塞主線程的場景
- 定時輪詢數據
- 需要保持界面響應的應用

使用示例：
def on_data(item, value):
    if value:
        print(f"收到數據：{value}")
    else:
        print("請求失敗")

client.request_async("QUOTE", on_data)
print("這行代碼會立即執行")

性能特點：
- 執行速度適中（約 0.028-0.034 秒）
- 通過回調函數異步處理結果
- 不阻塞主線程

優點：
- 不會阻塞主線程
- 適合處理多個並行請求
- 適合需要即時響應的場景
- 可以同時處理多個請求
- 界面保持響應

缺點：
- 回調函數可能使代碼結構複雜
- 錯誤處理相對複雜
- 需要注意回調函數的線程安全
- 執行時間略長於同步請求


3. 異步等待請求 (request_async_await)
--------------------------------
適用場景：
- 使用現代異步框架的應用
- 需要非阻塞但保持代碼清晰的場景
- 複雜的異步操作流程
- 需要與其他異步代碼集成的場景

使用示例：
async def get_data():
    try:
        data = await client.request_async_await("QUOTE")
        if data:
            print(f"收到數據：{data}")
    except DDEClientError as e:
        print(f"請求失敗：{e}")

asyncio.run(get_data())

性能特點：
- 執行速度與異步回調相近（約 0.030-0.035 秒）
- 使用現代異步語法
- 支持異步上下文管理

優點：
- 代碼結構清晰
- 非阻塞執行
- 錯誤處理方便
- 易於與其他異步代碼集成
- 支持 async/await 語法

缺點：
- 需要在異步環境中使用
- 可能需要額外的異步運行時支持
- 執行時間略長於同步請求
- 需要理解異步編程概念


選擇建議
-------
1. 如果你的應用是簡單的腳本或命令行工具：
   - 使用同步請求 (request)
   - 代碼簡單直觀
   - 執行速度最快

2. 如果你在開發圖形界面應用：
   - 使用異步回調請求 (request_async)
   - 避免界面卡頓
   - 可以處理多個並行請求

3. 如果你的應用使用了異步框架：
   - 使用異步等待請求 (request_async_await)
   - 或考慮使用異步回調請求
   - 根據實際需求選擇合適的方式


注意事項
-------
1. 同步請求：
   - 設置合適的超時時間
   - 避免在 GUI 線程中長時間阻塞
   - 注意異常處理

2. 異步回調請求：
   - 注意回調函數的線程安全
   - 使用信號機制更新 GUI
   - 合理組織回調邏輯

3. 異步等待請求：
   - 確保運行環境支持異步
   - 注意異步上下文管理
   - 可以考慮使用異步回調替代

4. 通用建議：
   - 根據應用場景選擇合適的請求方式
   - 實現適當的錯誤處理機制
   - 注意資源釋放和清理
   - 考慮使用日誌記錄追蹤問題
   - 定期檢查連接狀態

5. 性能考慮：
   - 同步請求速度最快但會阻塞
   - 異步方式略慢但更靈活
   - 選擇時要平衡性能和使用便利性 