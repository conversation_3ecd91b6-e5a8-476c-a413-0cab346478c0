import sys
import os
import logging
import configparser
import time
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Optional
from collections import deque
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QLineEdit, QPushButton, 
                             QTableWidget, QTableWidgetItem, QTextEdit, QSplitter,
                             QMessageBox, QComboBox)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QObject
from dydde.dydde import DDEClient
from queue import Queue

@dataclass
class ItemData:
    """項目資料結構
    用於儲存每個DDE項目的相關資訊
    
    屬性:
        name (str): 項目名稱
        code (str): DDE項目代碼
        value (Optional[str]): 當前值
        update_time (Optional[datetime]): 最後更新時間
        status (str): 訂閱狀態
    """
    name: str
    code: str
    value: Optional[str] = None
    update_time: Optional[datetime] = None
    status: str = "未訂閱"

@dataclass
class RawDataRow:
    """原始資料行結構
    用於儲存每一筆原始資料的完整資訊
    
    屬性:
        receive_date (str): 接收日期 (YYYY-MM-DD)
        receive_time (str): 接收時間 (HH:MM:SS.ffffff)
        values (Dict[str, str]): 項目值字典，key為項目名稱，value為項目值
        is_complete (bool): 是否為完整資料行
    """
    receive_date: str
    receive_time: str
    values: Dict[str, str]
    is_complete: bool = False

class DataFileHandler:
    """檔案處理類別
    負責處理所有檔案相關操作，包括初始化檔案、確保目錄存在、儲存資料等
    
    屬性:
        data_file (str): 原始資料檔案路徑
        complete_data_file (str): 完整資料檔案路徑
        logger (logging.Logger): 日誌記錄器
    """
    def __init__(self):
        self.data_file = None
        self.complete_data_file = None
        self.logger = logging.getLogger("DataFileHandler")
        self.items_data = None  # 新增 items_data 屬性
        self.enable_data_file = True
        self.enable_complete_data_file = True
        self.enable_log_file = True
        self.config = None  # 新增 config 屬性
        
    def init_files(self, config: configparser.ConfigParser, items_data: Dict[str, ItemData]):
        """初始化檔案
        根據設定檔初始化資料檔案和完整資料檔案
        
        參數:
            config (configparser.ConfigParser): 設定檔物件
            items_data (Dict[str, ItemData]): 項目資料字典
        """
        try:
            # 保存 config 和 items_data
            self.config = config
            self.items_data = items_data
            
            # 讀取檔案輸出控制設定
            self.enable_data_file = config.getboolean('FileOutput', 'enable_data_file', fallback=True)
            self.enable_complete_data_file = config.getboolean('FileOutput', 'enable_complete_data_file', fallback=True)
            self.enable_log_file = config.getboolean('FileOutput', 'enable_log_file', fallback=True)
            
            # 獲取當前日期
            now = datetime.now()
            date_str = now.strftime('%Y%m%d')
            
            # 格式化檔案路徑
            data_file_path = config.get('OutputPath', 'data_file', fallback='dde_data_{date}.csv')
            complete_data_file_path = config.get('OutputPath', 'complete_data_file', fallback='complete_data_{date}.csv')
            
            self.data_file = data_file_path.format(date=date_str)
            self.complete_data_file = complete_data_file_path.format(date=date_str)
            
            # 確保目錄存在
            if self.enable_data_file:
                self.ensure_directory(self.data_file)
            if self.enable_complete_data_file:
                self.ensure_directory(self.complete_data_file)
            
            # 寫入標題行
            self.write_headers()
            
            self.logger.info(f"資料檔案初始化完成: {self.data_file}")
            self.logger.info(f"完整資料檔案初始化完成: {self.complete_data_file}")
            
        except Exception as e:
            self.logger.error(f"初始化檔案失敗: {str(e)}")
            raise
            
    def write_headers(self):
        """寫入標題行到兩個檔案"""
        try:
            if not self.items_data:
                self.logger.error("items_data 未初始化，無法寫入標題行")
                return
                
            # 準備標題行
            headers = ["接收日期", "接收時間"]
            # 按照設定檔中的順序添加項目名稱
            for i in range(1, 100):  # 假設最多100個項目
                name_key = f'item{i}_name'
                code_key = f'item{i}_code'
                
                if name_key in self.config['Items'] and code_key in self.config['Items']:
                    name = self.config['Items'][name_key]
                    code = self.config['Items'][code_key]
                    if code in self.items_data:
                        headers.append(name)
            
            # 寫入標題行到兩個檔案
            header_line = ','.join(headers) + '\n'
            
            # 寫入原始資料檔案
            if self.enable_data_file:
                with open(self.data_file, 'w', encoding='utf-8') as f:
                    f.write(header_line)
                
            # 寫入完整資料檔案
            if self.enable_complete_data_file:
                with open(self.complete_data_file, 'w', encoding='utf-8') as f:
                    f.write(header_line)
                
            self.logger.debug("已寫入標題行到兩個檔案")
            
        except Exception as e:
            self.logger.error(f"寫入標題行失敗: {str(e)}")
            raise
            
    def ensure_directory(self, file_path: str):
        """確保目錄存在
        檢查並建立必要的目錄結構
        
        參數:
            file_path (str): 檔案路徑
        """
        try:
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
        except Exception as e:
            self.logger.error(f"建立目錄失敗: {directory}, 錯誤: {str(e)}")
            raise
            
    def save_row(self, row: RawDataRow, is_complete: bool = False):
        """儲存資料行
        將資料行寫入對應的檔案
        
        參數:
            row (RawDataRow): 要儲存的資料行
            is_complete (bool): 是否為完整資料行
        """
        try:
            if not self.items_data:
                self.logger.error("items_data 未初始化，無法儲存資料行")
                return
                
            # 準備資料行
            row_data = [row.receive_date, row.receive_time]
            # 按照設定檔中的順序添加項目值
            for i in range(1, 100):  # 假設最多100個項目
                name_key = f'item{i}_name'
                code_key = f'item{i}_code'
                
                if name_key in self.config['Items'] and code_key in self.config['Items']:
                    code = self.config['Items'][code_key]
                    if code in self.items_data:
                        row_data.append(row.values.get(code, ""))
            
            # 寫入檔案
            if is_complete and self.enable_complete_data_file:
                with open(self.complete_data_file, 'a', encoding='utf-8') as f:
                    f.write(','.join(row_data) + '\n')
            elif not is_complete and self.enable_data_file:
                with open(self.data_file, 'a', encoding='utf-8') as f:
                    f.write(','.join(row_data) + '\n')
                
            self.logger.debug(f"已儲存資料行到 {self.complete_data_file if is_complete else self.data_file}")
            
        except Exception as e:
            self.logger.error(f"儲存資料行失敗: {str(e)}")
            raise 

class DataProcessor(QObject):
    """資料處理類別，使用 QThread 處理資料"""
    data_processed = Signal(str, str)  # 定義信號，用於通知主線程資料已處理
    
    def __init__(self):
        super().__init__()
        self.data_queue = Queue()  # 資料佇列
        self.running = True
        
    def add_data(self, item: str, value: str):
        """將資料加入佇列"""
        self.data_queue.put((item, value))
        
    def process_data(self):
        """處理資料的主循環"""
        batch = []
        while self.running:
            try:
                # 收集一批資料
                while len(batch) < 100 and not self.data_queue.empty():
                    batch.append(self.data_queue.get())
                    
                if batch:
                    # 批次處理
                    for item, value in batch:
                        self.data_processed.emit(item, value)
                    batch = []
                    
                time.sleep(0.01)  # 避免過度消耗 CPU
            except Exception as e:
                logging.error(f"批次處理失敗: {str(e)}")
                
    def stop(self):
        """停止處理"""
        self.running = False

class DDEMonitor(QMainWindow):
    """DDE監控主類別
    負責管理整個DDE監控程式的運作，包括UI、DDE連接、資料處理等
    
    屬性:
        config (configparser.ConfigParser): 設定檔物件
        dde_client (DDEClient): DDE客戶端物件
        file_handler (DataFileHandler): 檔案處理物件
        items_data (Dict[str, ItemData]): 項目資料容器
        raw_data (deque): 原始資料佇列
        last_advise_time (float): 最後接收資料的時間戳
        time_newline_interval (float): 時間間隔換行設定
        enable_time_newline (bool): 是否啟用時間間隔換行
        enable_value_change_check (bool): 是否啟用值變化檢查
        value_change_check_items (str): 值變化檢查的項目名稱
        data_processor (DataProcessor): 資料處理器
        process_thread (QThread): 資料處理器線程
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DDE 監控程式 v6")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化設定
        self.config = None
        self.dde_client = None
        self.file_handler = DataFileHandler()
        
        # 資料容器
        self.items_data: Dict[str, ItemData] = {}  # 項目資料容器
        self.raw_data: deque = deque(maxlen=1000)  # 原始資料佇列
        
        # 計時器相關
        self.last_advise_time = None
        self.time_newline_interval = None
        self.enable_time_newline = None
        self.enable_value_change_check = None
        self.value_change_check_items = None
        
        # 初始化資料處理器
        self.data_processor = DataProcessor()
        self.process_thread = QThread()
        self.data_processor.moveToThread(self.process_thread)
        self.data_processor.data_processed.connect(self._process_advise_data)
        self.process_thread.started.connect(self.data_processor.process_data)
        
        # 初始化
        self.load_config()  # 先載入設定
        self.setup_logging()
        self.init_ui()  # 再初始化 UI
        self.init_data_containers()  # 再初始化資料容器
        self.file_handler.init_files(self.config, self.items_data)  # 最後初始化檔案
        
        # 設置定時器
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_time_interval)
        self.timer.start(100)  # 每100ms檢查一次
        
        # 啟動資料處理器線程
        self.process_thread.start()
        
    def load_config(self):
        """載入設定檔
        從config.ini讀取所有必要的設定
        """
        try:
            self.config = configparser.ConfigParser()
            self.config.read('config_v6.ini', encoding='utf-8')
            
            # 讀取時間換行設定
            self.enable_time_newline = self.config.getboolean('Table', 'enable_time_newline', fallback=True)
            self.time_newline_interval = self.config.getfloat('Table', 'time_newline_interval', fallback=0.800)
            
            # 讀取值變化檢查設定
            self.enable_value_change_check = self.config.getboolean('Table', 'enable_value_change_check', fallback=True)
            self.value_change_check_items = self.config.get('Table', 'value_change_check_items', fallback='')
            
            # 初始化檔案處理
            self.file_handler.init_files(self.config, self.items_data)
            
        except Exception as e:
            logging.error(f"載入設定檔失敗: {str(e)}")
            raise
            
    def setup_logging(self):
        """設置日誌系統"""
        try:
            # 獲取當前日期
            now = datetime.now()
            date_str = now.strftime('%Y%m%d')
            
            # 從設定檔讀取日誌路徑
            log_file_path = self.config.get('OutputPath', 'log_file', fallback='./logs/{date}/dde_monitor.log')
            log_file = log_file_path.format(date=date_str)
            
            # 確保日誌目錄存在
            self.file_handler.ensure_directory(log_file)
            
            # 設置日誌格式
            formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] %(message)s',
                                       datefmt='%Y-%m-%d %H:%M:%S')
            
            # 設置檔案處理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.INFO)  # 改為 INFO 級別
            
            # 設置控制台處理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            console_handler.setLevel(logging.INFO)  # 改為 INFO 級別
            
            # 設置根日誌記錄器
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.INFO)  # 改為 INFO 級別
            
            # 移除現有的處理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
                
            # 添加新的處理器
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            
            # 設置程式本身的日誌記錄器
            self.logger = logging.getLogger('Monitor')
            self.logger.setLevel(logging.INFO)  # 改為 INFO 級別
            
            self.logger.info(f"日誌檔案初始化完成: {log_file}")
            
        except Exception as e:
            print(f"設置日誌失敗: {str(e)}")
            raise
            
    def init_ui(self):
        """初始化使用者介面"""
        # 主視窗佈局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        
        # DDE連接區域
        dde_conn_layout = QHBoxLayout()
        
        # 服務和主題輸入框
        self.service_edit = QLineEdit(self.config.get('DDE', 'service', fallback=''))
        self.topic_edit = QLineEdit(self.config.get('DDE', 'topic', fallback=''))
        
        # 狀態標籤
        self.status_label = QLabel("尚未連線")
        
        # 一鍵連線/斷線按鈕（保留原有功能）
        self.connect_btn = QPushButton("連線")
        self.connect_btn.clicked.connect(self.toggle_connection)
        
        # 新增：服務主題連線/斷線按鈕
        self.service_connect_btn = QPushButton("服務連線")
        self.service_connect_btn.clicked.connect(self.toggle_service_connection)
        
        # 新增：測試項目按鈕
        self.test_items_btn = QPushButton("測試項目")
        self.test_items_btn.clicked.connect(self.test_items)
        self.test_items_btn.setEnabled(False)  # 初始狀態禁用
        
        # 新增：訂閱/取消訂閱按鈕
        self.subscribe_btn = QPushButton("訂閱項目")
        self.subscribe_btn.clicked.connect(self.toggle_subscription)
        self.subscribe_btn.setEnabled(False)  # 初始狀態禁用
        
        # 添加元件到佈局
        dde_conn_layout.addWidget(QLabel("DDE服務:"))
        dde_conn_layout.addWidget(self.service_edit)
        dde_conn_layout.addWidget(QLabel("DDE主題:"))
        dde_conn_layout.addWidget(self.topic_edit)
        dde_conn_layout.addWidget(self.status_label)
        dde_conn_layout.addWidget(self.service_connect_btn)
        dde_conn_layout.addWidget(self.test_items_btn)
        dde_conn_layout.addWidget(self.subscribe_btn)
        dde_conn_layout.addWidget(self.connect_btn)
        
        main_layout.addLayout(dde_conn_layout)
        
        # 項目表格
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["項目名稱", "DDE項目", "值", "更新時間", "狀態"])
        main_layout.addWidget(self.items_table)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        main_layout.addWidget(self.log_text)
        
        # 設置定時器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(100)  # 每100ms更新一次
        
    def init_data_containers(self):
        """初始化資料容器
        從設定檔讀取項目設定並初始化資料容器
        """
        try:
            # 讀取項目設定
            for i in range(1, 100):  # 假設最多100個項目
                name_key = f'item{i}_name'
                code_key = f'item{i}_code'
                
                if name_key in self.config['Items'] and code_key in self.config['Items']:
                    name = self.config['Items'][name_key]
                    code = self.config['Items'][code_key]
                    self.items_data[code] = ItemData(name=name, code=code)
            
            # 更新項目表格
            if hasattr(self, 'items_table'):  # 確保 items_table 已初始化
                self.update_items_table()
            
        except Exception as e:
            logging.error(f"初始化資料容器失敗: {str(e)}")
            raise

    def toggle_service_connection(self):
        """切換服務主題連接狀態"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                self.connect_service()
            else:
                self.disconnect_service()
        except Exception as e:
            self.logger.error(f"切換服務連接狀態失敗: {str(e)}")
            
    def connect_service(self):
        """連接 DDE 服務"""
        try:
            service = self.service_edit.text()
            topic = self.topic_edit.text()
            
            if not service or not topic:
                QMessageBox.warning(self, "警告", "請輸入服務名稱和主題")
                return
                
            # 從設定檔讀取 disconnect_on_exit 設定
            disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
            
            # 建立 DDE 客戶端，傳入 disconnect_on_exit 參數
            self.dde_client = DDEClient(service, topic, disconnect_on_exit=disconnect_on_exit)
            self.dde_client.connect()
            
            self.status_label.setText("已連線")
            self.service_connect_btn.setText("斷線")
            self.test_items_btn.setEnabled(True)
            self.logger.info(f"已連接到 DDE 服務: {service}.{topic}")
            
        except Exception as e:
            self.logger.error(f"連接 DDE 服務失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"連接 DDE 服務失敗: {str(e)}")
            
    def disconnect_service(self):
        """斷開DDE服務連接"""
        try:
            if self.dde_client:
                # 根據設定決定是否終止 DDE
                disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
                self.dde_client.disconnect(terminate_dde=disconnect_on_exit)
                self.dde_client = None
                
            self.status_label.setText("服務已斷線")
            self.service_connect_btn.setText("服務連線")
            self.test_items_btn.setEnabled(False)  # 禁用測試按鈕
            self.subscribe_btn.setEnabled(False)  # 禁用訂閱按鈕
            self.subscribe_btn.setText("訂閱項目")  # 重置訂閱按鈕文字
            self.logger.info("已斷開DDE服務連接")
            
        except Exception as e:
            self.logger.error(f"斷開DDE服務連接失敗: {str(e)}")
            
    def test_items(self):
        """測試所有項目"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                QMessageBox.warning(self, "警告", "請先連接DDE服務")
                return
                
            # 測試所有項目
            for code, item_data in self.items_data.items():
                value = self.dde_client.request(code)
                if value is not None:
                    item_data.value = value
                    item_data.update_time = datetime.now()
                    item_data.status = "已測試"
                else:
                    item_data.status = "測試失敗"
                    
            self.update_items_table()
            self.subscribe_btn.setEnabled(True)  # 啟用訂閱按鈕
            self.logger.info("項目測試完成")
            
        except Exception as e:
            self.logger.error(f"測試項目失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"測試項目失敗: {str(e)}")
            
    def toggle_subscription(self):
        """切換項目訂閱狀態"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                QMessageBox.warning(self, "警告", "請先連接DDE服務")
                return
                
            # 檢查當前訂閱狀態
            is_subscribed = any(item_data.status == "已訂閱" for item_data in self.items_data.values())
            
            if is_subscribed:
                # 取消訂閱所有項目
                for code, item_data in self.items_data.items():
                    if item_data.status == "已訂閱":
                        self.dde_client.unadvise(code)
                        item_data.status = "已測試"
                        
                self.subscribe_btn.setText("訂閱項目")
                self.logger.info("已取消所有項目訂閱")
            else:
                # 訂閱所有項目
                for code, item_data in self.items_data.items():
                    if item_data.status == "已測試":
                        if self.dde_client.advise(code, self.on_advise_data):
                            item_data.status = "已訂閱"
                        else:
                            item_data.status = "訂閱失敗"
                            
                self.subscribe_btn.setText("取消訂閱")
                self.logger.info("已訂閱所有項目")
                
            self.update_items_table()
            
        except Exception as e:
            self.logger.error(f"切換訂閱狀態失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"切換訂閱狀態失敗: {str(e)}")
            
    def toggle_connection(self):
        """一鍵連線/斷線（保留原有功能）"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                self.connect_dde()
            else:
                self.disconnect_dde()
        except Exception as e:
            self.logger.error(f"切換連接狀態失敗: {str(e)}")
            
    def connect_dde(self):
        """一鍵連線（保留原有功能）"""
        try:
            service = self.service_edit.text()
            topic = self.topic_edit.text()
            
            if not service or not topic:
                QMessageBox.warning(self, "警告", "請輸入服務和主題")
                return
                
            self.dde_client = DDEClient(service, topic)
            self.dde_client.connect()
            
            # 測試並訂閱所有項目
            self.test_and_subscribe_items()
            
            self.status_label.setText("已連線")
            self.connect_btn.setText("斷線")
            self.logger.info(f"成功連接到DDE服務: {service}, 主題: {topic}")
            
        except Exception as e:
            self.logger.error(f"連接DDE失敗: {str(e)}")
            self.status_label.setText("連線失敗")
            QMessageBox.critical(self, "錯誤", f"連接DDE失敗: {str(e)}")
            
    def disconnect_dde(self):
        """一鍵斷線（保留原有功能）"""
        try:
            if self.dde_client:
                # 根據設定決定是否終止 DDE
                disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
                self.dde_client.disconnect(terminate_dde=disconnect_on_exit)
                self.dde_client = None
                
            self.status_label.setText("尚未連線")
            self.connect_btn.setText("連線")
            self.logger.info("已斷開DDE連接")
            
        except Exception as e:
            self.logger.error(f"斷開DDE連接失敗: {str(e)}")
            
    def test_and_subscribe_items(self):
        """測試並訂閱所有項目
        先測試所有項目的可訪問性，再進行訂閱
        """
        try:
            # 1. 先測試所有項目
            for code, item_data in self.items_data.items():
                # 測試項目
                value = self.dde_client.request(code)
                if value is not None:
                    item_data.value = value
                    item_data.update_time = datetime.now()
                    item_data.status = "已測試"
                else:
                    item_data.status = "測試失敗"
                    
            # 更新表格顯示測試結果
            self.update_items_table()
            
            # 2. 再訂閱所有項目
            for code, item_data in self.items_data.items():
                if item_data.status == "已測試":
                    # 訂閱項目
                    if self.dde_client.advise(code, self.on_advise_data):
                        item_data.status = "已訂閱"
                    else:
                        item_data.status = "訂閱失敗"
                        
            # 更新表格顯示訂閱結果
            self.update_items_table()
            
        except Exception as e:
            logging.error(f"測試並訂閱項目失敗: {str(e)}")
            raise
            
    def on_advise_data(self, item: str, value: str):
        """處理DDE資料更新"""
        try:
            # 將資料加入處理佇列
            self.data_processor.add_data(item, value)
        except Exception as e:
            self.logger.error(f"加入資料到佇列失敗: {str(e)}")
            
    def _process_advise_data(self, item: str, value: str):
        """實際處理資料的方法"""
        try:
            # 1. 檢查是否需要項目重複換行
            if self.check_item_repeat_newline(item, value):
                return
                
            # 2. 更新原始資料
            self.update_raw_data(item, value)
            
            # 3. 更新項目資料
            self.update_items_data(item, value)
            
            # 4. 更新最後接收時間
            self.last_advise_time = time.time()
            
        except Exception as e:
            self.logger.error(f"處理advise數據失敗: {str(e)}")
            
    def check_time_interval(self):
        """檢查時間間隔
        定期檢查是否需要換行
        """
        if not self.enable_time_newline:
            return
            
        now = time.time()
        
        if (self.last_advise_time is not None and 
            now - self.last_advise_time >= self.time_newline_interval):
            
            self.logger.debug("check_time_interval() - 時間間隔已到，開始換行")
            self.check_time_interval_newline()
            self.last_advise_time = now
            
    def check_time_interval_newline(self):
        """檢查是否需要時間間隔換行
        根據時間間隔檢查是否需要換行
        """
        if not self.enable_time_newline:
            self.logger.debug("check_time_interval_newline() - 未啟用時間間隔換行")
            return
            
        if not self.raw_data:
            self.logger.debug("check_time_interval_newline() - 原始資料佇列為空")
            return
            
        current_row = self.raw_data[0]
        
        # 檢查行是否有資料
        if not current_row.values:
            self.logger.debug("check_time_interval_newline() - 當前行沒有資料")
            return
            
        # 檢查並補齊缺失資料
        if self.has_missing_data(current_row):
            self.logger.debug("check_time_interval_newline() - 當前行缺少資料，開始補齊")
            self.fill_missing_data(current_row)
            
        # 如果啟用了值變化檢查，則檢查值是否有變化
        if self.enable_value_change_check:
            self.logger.debug(f"check_time_interval_newline() - raw_data 長度: {len(self.raw_data)}")
            has_changed = self.check_value_change(current_row)
            self.logger.debug(f"check_time_interval_newline() - 值變化檢查結果: {has_changed}")
            
            if has_changed:
                self.logger.debug("check_time_interval_newline() - 值有變化，儲存完整資料行")
                # 儲存完整資料行
                self.file_handler.save_row(current_row, is_complete=True)
                # 建立新行
                self.create_new_row()
            else:
                self.logger.debug("check_time_interval_newline() - 值未變化，不儲存資料行")
                # 值未變化，不儲存資料行，但建立新行
                self.create_new_row()
        else:
            # 未啟用值變化檢查，直接儲存資料行
            self.logger.debug("check_time_interval_newline() - 未啟用值變化檢查，直接儲存資料行")
            self.file_handler.save_row(current_row, is_complete=True)
            self.create_new_row()
            
    def check_item_repeat_newline(self, item: str, value: str) -> bool:
        """檢查是否需要項目重複換行
        當收到重複項目的資料時，檢查是否需要換行
        
        參數:
            item (str): 項目代碼
            value (str): 項目值
            
        回傳:
            bool: 是否需要換行
        """
        if not self.raw_data:
            self.logger.debug("check_item_repeat_newline() - 原始資料佇列為空")
            return False
            
        current_row = self.raw_data[0]
        
        # 檢查項目是否已存在於當前行
        if item in current_row.values:
            self.logger.debug(f"check_item_repeat_newline() - 項目 {item} 已存在於當前行")
            
            # 補齊缺失資料
            if self.has_missing_data(current_row):
                self.logger.debug("check_item_repeat_newline() - 當前行缺少資料，開始補齊")
                self.fill_missing_data(current_row)
                
            # 如果啟用了值變化檢查，則檢查值是否有變化
            if self.enable_value_change_check:
                self.logger.debug(f"check_item_repeat_newline() - raw_data 長度: {len(self.raw_data)}")
                has_changed = self.check_value_change(current_row)
                self.logger.debug(f"check_item_repeat_newline() - 值變化檢查結果: {has_changed}")
                
                if has_changed:
                    self.logger.debug("check_item_repeat_newline() - 值有變化，儲存完整資料行")
                    # 儲存完整資料行
                    self.file_handler.save_row(current_row, is_complete=True)
                    # 建立新行
                    self.create_new_row()
                    # 將收到的項目值填入新行
                    new_row = self.raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                    return True
                else:
                    self.logger.debug("check_item_repeat_newline() - 值未變化，不儲存資料行")
                    # 值未變化，不儲存資料行，但建立新行
                    self.create_new_row()
                    # 將收到的項目值填入新行
                    new_row = self.raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                    return True
            else:
                # 未啟用值變化檢查，直接儲存資料行
                self.logger.debug("check_item_repeat_newline() - 未啟用值變化檢查，直接儲存資料行")
                self.file_handler.save_row(current_row, is_complete=True)
                self.create_new_row()
                new_row = self.raw_data[0]
                new_row.values[item] = value
                new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                return True
                
        self.logger.debug(f"check_item_repeat_newline() - 項目 {item} 不存在於當前行")
        return False
        
    def update_raw_data(self, item: str, value: str):
        """更新原始資料
        更新原始資料佇列中的資料
        
        參數:
            item (str): 項目代碼
            value (str): 項目值
        """
        try:
            self.logger.debug(f"update_raw_data() - 更新原始資料: {item} = {value}")
            now = datetime.now()
            current_row = None
            
            # 檢查是否需要建立新行
            if not self.raw_data:
                self.logger.debug("update_raw_data() - 建立新行")
                current_row = RawDataRow(
                    receive_date=now.strftime("%Y-%m-%d"),
                    receive_time=now.strftime("%H:%M:%S.%f"),
                    values={}
                )
                self.raw_data.appendleft(current_row)
            else:
                current_row = self.raw_data[0]
                
            # 更新值
            current_row.values[item] = value
            current_row.receive_date = now.strftime("%Y-%m-%d")
            current_row.receive_time = now.strftime("%H:%M:%S.%f")
            
            # 儲存原始資料行
            self.file_handler.save_row(current_row, is_complete=False)
            
            self.logger.debug(f"update_raw_data() - 更新後的值: {current_row.values}")
            
        except Exception as e:
            self.logger.error(f"更新原始資料失敗: {str(e)}")
            raise
            
    def update_items_data(self, item: str, value: str):
        """更新項目資料
        更新項目資料容器中的資料
        
        參數:
            item (str): 項目代碼
            value (str): 項目值
        """
        if item in self.items_data:
            self.logger.debug(f"update_items_data() - 更新項目資料: {item} = {value}")
            self.items_data[item].value = value
            self.items_data[item].update_time = datetime.now()
            self.items_data[item].status = "已訂閱"
            self.update_items_table()
            
    def has_missing_data(self, row: RawDataRow) -> bool:
        """檢查是否有缺失資料
        檢查資料行是否缺少任何項目的資料
        
        參數:
            row (RawDataRow): 要檢查的資料行
            
        回傳:
            bool: 是否有缺失資料
        """
        has_missing = len(row.values) < len(self.items_data)
        self.logger.debug(f"has_missing_data() - 檢查缺失資料: {has_missing}")
        return has_missing
        
    def fill_missing_data(self, row: RawDataRow):
        """補齊缺失資料
        使用項目資料容器中的最新值補齊缺失資料
        
        參數:
            row (RawDataRow): 要補齊的資料行
        """
        try:
            for code, item_data in self.items_data.items():
                if code not in row.values and item_data.value is not None:
                    row.values[code] = item_data.value
                    self.logger.debug(f"fill_missing_data() - 補齊缺失資料: {code} = {item_data.value}")
                    
        except Exception as e:
            logging.error(f"補齊缺失資料失敗: {str(e)}")
            raise
            
    def check_value_change(self, current_row: RawDataRow) -> bool:
        """檢查值變化
        根據設定檢查指定項目的值是否發生變化
        
        參數:
            current_row (RawDataRow): 當前的資料行
            
        回傳:
            bool: 值是否發生變化
        """
        # 如果未啟用值變化檢查，直接返回 True
        if not self.enable_value_change_check:
            self.logger.debug("check_value_change() - 未啟用值變化檢查，返回 True")
            return True
            
        # 獲取檢查模式
        check_mode = self.config.get('Table', 'value_change_check_mode', fallback='single')
        self.logger.debug(f"check_value_change() - 檢查模式: {check_mode}")
        
        # 如果沒有前一行，直接返回 True
        if len(self.raw_data) <= 1:
            self.logger.debug("check_value_change() - 沒有前一行資料，這是第一筆資料，返回 True")
            return True
            
        previous_row = self.raw_data[1]
        
        # 根據不同模式進行檢查
        if check_mode == 'single':
            # 單一項目檢查
            check_item_name = self.config.get('Table', 'value_change_check_items', fallback='')
            if not check_item_name:
                self.logger.debug("check_value_change() - 未指定檢查項目，返回 True")
                return True
                
            # 獲取檢查項目的代碼
            check_item_code = None
            for code, item_data in self.items_data.items():
                if item_data.name == check_item_name:
                    check_item_code = code
                    break
                    
            if not check_item_code:
                self.logger.debug(f"check_value_change() - 找不到檢查項目 {check_item_name} 的代碼")
                return True
                
            # 檢查值是否變化
            current_value = current_row.values.get(check_item_code, "")
            previous_value = previous_row.values.get(check_item_code, "")
            has_changed = current_value != previous_value
            
            self.logger.debug(f"check_value_change() - 單一項目檢查:")
            self.logger.debug(f"  項目名稱: {check_item_name}")
            self.logger.debug(f"  項目代碼: {check_item_code}")
            self.logger.debug(f"  當前值: {current_value}")
            self.logger.debug(f"  前一個值: {previous_value}")
            self.logger.debug(f"  是否有變化: {has_changed}")
            
            return has_changed
            
        elif check_mode == 'multiple':
            # 多個項目檢查
            check_items_str = self.config.get('Table', 'value_change_check_items', fallback='')
            if not check_items_str:
                self.logger.debug("check_value_change() - 未指定檢查項目，返回 True")
                return True
                
            check_item_names = [item.strip() for item in check_items_str.split(',')]
            self.logger.debug(f"check_value_change() - 多個項目檢查: {check_item_names}")
            
            # 檢查每個項目
            for check_item_name in check_item_names:
                # 獲取檢查項目的代碼
                check_item_code = None
                for code, item_data in self.items_data.items():
                    if item_data.name == check_item_name:
                        check_item_code = code
                        break
                        
                if not check_item_code:
                    self.logger.debug(f"check_value_change() - 找不到檢查項目 {check_item_name} 的代碼")
                    continue
                    
                # 檢查值是否變化
                current_value = current_row.values.get(check_item_code, "")
                previous_value = previous_row.values.get(check_item_code, "")
                
                self.logger.debug(f"check_value_change() - 檢查項目 {check_item_name}:")
                self.logger.debug(f"  項目代碼: {check_item_code}")
                self.logger.debug(f"  當前值: {current_value}")
                self.logger.debug(f"  前一個值: {previous_value}")
                
                # 只要有一個項目值變化就返回 True
                if current_value != previous_value:
                    self.logger.debug(f"check_value_change() - 項目 {check_item_name} 值有變化")
                    return True
                    
            self.logger.debug("check_value_change() - 所有檢查項目值都沒有變化")
            return False
            
        elif check_mode == 'all':
            # 檢查所有項目
            self.logger.debug("check_value_change() - 檢查所有項目")
            
            # 檢查每個項目
            for code, item_data in self.items_data.items():
                # 跳過 receive_date 和 receive_time
                if code in ['receive_date', 'receive_time']:
                    continue
                    
                current_value = current_row.values.get(code, "")
                previous_value = previous_row.values.get(code, "")
                
                self.logger.debug(f"check_value_change() - 檢查項目 {item_data.name}:")
                self.logger.debug(f"  項目代碼: {code}")
                self.logger.debug(f"  當前值: {current_value}")
                self.logger.debug(f"  前一個值: {previous_value}")
                
                # 只要有一個項目值變化就返回 True
                if current_value != previous_value:
                    self.logger.debug(f"check_value_change() - 項目 {item_data.name} 值有變化")
                    return True
                    
            self.logger.debug("check_value_change() - 所有項目值都沒有變化")
            return False
            
        else:
            self.logger.warning(f"check_value_change() - 未知的檢查模式: {check_mode}")
            return True
        
    def create_new_row(self):
        """建立新的資料行
        在原始資料佇列中建立新的資料行
        """
        now = datetime.now()
        new_row = RawDataRow(
            receive_date=now.strftime("%Y-%m-%d"),
            receive_time=now.strftime("%H:%M:%S.%f"),
            values={}
        )
        self.raw_data.appendleft(new_row)
        self.logger.debug("create_new_row() - 已建立新行")
        
    def update_items_table(self):
        """更新項目表格
        更新UI中的項目表格顯示
        """
        try:
            self.items_table.setRowCount(len(self.items_data))
            for row, (code, item_data) in enumerate(self.items_data.items()):
                self.items_table.setItem(row, 0, QTableWidgetItem(item_data.name))
                self.items_table.setItem(row, 1, QTableWidgetItem(code))
                self.items_table.setItem(row, 2, QTableWidgetItem(str(item_data.value or "")))
                self.items_table.setItem(row, 3, QTableWidgetItem(
                    item_data.update_time.strftime("%H:%M:%S.%f") if item_data.update_time else ""))
                self.items_table.setItem(row, 4, QTableWidgetItem(item_data.status))
                
        except Exception as e:
            logging.error(f"更新項目表格失敗: {str(e)}")
            raise
            
    def update_ui(self):
        """更新使用者介面
        定期更新UI元件
        """
        try:
            # 更新項目表格
            self.update_items_table()
            
            # 更新日誌顯示
            self.update_log_display()
            
        except Exception as e:
            logging.error(f"更新使用者介面失敗: {str(e)}")
            raise
            
    def update_log_display(self):
        """更新日誌顯示
        更新UI中的日誌顯示區域
        """
        try:
            # 獲取最新的日誌記錄
            log_records = []
            for handler in logging.getLogger().handlers:
                if isinstance(handler, logging.FileHandler):
                    with open(handler.baseFilename, 'r', encoding='utf-8') as f:
                        log_records = f.readlines()[-100:]  # 只顯示最後100行
                        
            # 更新日誌顯示
            self.log_text.clear()
            self.log_text.append(''.join(log_records))
            
            # 滾動到最底部
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            logging.error(f"更新日誌顯示失敗: {str(e)}")
            raise

    def closeEvent(self, event):
        """關閉視窗事件處理"""
        try:
            if self.dde_client and self.dde_client.is_connected():
                # 檢查是否需要斷線
                disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
                
                if disconnect_on_exit:
                    # 顯示確認對話框
                    reply = QMessageBox.question(
                        self,
                        "確認關閉",
                        "程式仍在連線中，確定要結束程式嗎？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    
                    if reply == QMessageBox.Yes:
                        # 停止資料處理器
                        self.data_processor.stop()
                        self.process_thread.quit()
                        self.process_thread.wait()
                        
                        # 先取消所有訂閱
                        for code, item_data in self.items_data.items():
                            if item_data.status == "已訂閱":
                                self.dde_client.unadvise(code)
                                
                        # 再斷開連接
                        self.dde_client.disconnect(terminate_dde=disconnect_on_exit)
                        
                        # 手動調用清理方法
                        if hasattr(self.dde_client, '_cleanup_resources'):
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] [DEBUG] [M] 手動調用 _cleanup_resources')
                            self.dde_client._cleanup_resources()
                            
                        self.dde_client = None
                        event.accept()
                    else:
                        event.ignore()
                else:
                    # 只取消訂閱，不斷開連接
                    for code, item_data in self.items_data.items():
                        if item_data.status == "已訂閱":
                            self.dde_client.unadvise(code)
                            
                    # 停止資料處理器
                    self.data_processor.stop()
                    self.process_thread.quit()
                    self.process_thread.wait()
                    
                    # 手動調用清理方法
                    if hasattr(self.dde_client, '_cleanup_resources'):
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] [DEBUG] [M] 手動調用 _cleanup_resources')
                        self.dde_client._cleanup_resources()
                        
                    # 不設定 self.dde_client = None，保持連接
                    event.accept()
            else:
                # 停止資料處理器
                self.data_processor.stop()
                self.process_thread.quit()
                self.process_thread.wait()
                event.accept()
                
        except Exception as e:
            self.logger.error(f"關閉視窗時發生錯誤: {str(e)}")
            event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DDEMonitor()
    window.show()
    sys.exit(app.exec()) 