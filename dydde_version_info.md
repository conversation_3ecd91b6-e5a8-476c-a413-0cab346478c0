# DyDDE 版本管理說明

## 當前狀況

### 版本分佈
- **多商品版** (`/dydde/`): 已修復 reason 參數
- **混合版** (`/dde_multi_engine_hybrid/dydde/`): 已修復 reason 參數
- **模組化版** (`/dde_multi/dydde/`): 已修復 reason 參數

### 修復內容
1. **第2780行方法簽名**：
   ```python
   # 修復前
   def _handle_disconnect(self):
   
   # 修復後
   def _handle_disconnect(self, reason: str = "未知原因"):
   ```

2. **第1500行調用**：
   ```python
   # 修復前
   self._handle_disconnect()
   
   # 修復後
   self._handle_disconnect("DDE服務器主動斷開連接")
   ```

## 統一管理建議

### 方案1：符號連結（推薦）
```bash
# 將多商品版的 dydde 作為主版本
# 其他版本使用符號連結指向主版本
mklink /D dde_multi_engine_hybrid\dydde dydde
mklink /D dde_multi\dydde dydde
```

### 方案2：版本控制
- 在 dydde 模組中添加版本號
- 定期同步更新所有版本

### 方案3：獨立模組
- 將 dydde 提取為獨立的 Python 包
- 所有版本通過 pip 安裝相同版本

## 版本差異原因

1. **開發歷程**：不同版本在不同時間點複製
2. **獨立修復**：各版本獨立修復問題
3. **缺乏統一管理**：沒有版本控制機制

## 建議的統一流程

1. **選定主版本**：以多商品版為主版本
2. **創建備份**：備份所有現有版本
3. **統一更新**：將修復應用到所有版本
4. **建立機制**：建立版本同步機制
