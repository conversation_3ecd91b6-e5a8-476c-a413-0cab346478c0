# DDE Actor System 架構設計

## 設計原則

### 1. Actor模型核心原則
- **隔離性**：每個Actor都是獨立的執行單元
- **消息驅動**：Actor之間只通過消息通信
- **無共享狀態**：避免共享可變狀態
- **容錯性**：單個Actor失敗不影響整體系統

### 2. 高性能設計原則
- **零拷貝**：最小化數據複製操作
- **批量處理**：累積數據後批量處理
- **異步非阻塞**：所有I/O操作都是異步的
- **背壓控制**：智能處理數據流量突發

## 系統架構

### 整體架構圖

```
┌─────────────────────────────────────────────────────────────────┐
│                        DDE Actor System                         │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │ DDE Receiver│───▶│Data Processor│───▶│GUI Updater  │         │
│  │   Actor     │    │   Actor     │    │   Actor     │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│         │                   │                   │               │
│         ▼                   ▼                   ▼               │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │Ring Buffer  │    │Memory Pool  │    │Virtual Table│         │
│  │Manager      │    │Manager      │    │Manager      │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
│                                                                 │
├─────────────────────────────────────────────────────────────────┤
│                    Core Actor Framework                         │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐         │
│  │Message      │    │Event Loop   │    │Performance  │         │
│  │System       │    │Manager      │    │Monitor      │         │
│  └─────────────┘    └─────────────┘    └─────────────┘         │
└─────────────────────────────────────────────────────────────────┘
```

### 數據流架構

```
DDE數據源 ──┐
TCP數據源 ──┤
WS數據源  ──┤──▶ 接收緩衝區 ──▶ 環形緩衝區 ──▶ 處理管道 ──▶ 輸出分發
MQ數據源  ──┤      (Ring)        (Lock-free)    (Batch)     (Async)
其他數據源 ──┘
```

## 核心組件設計

### 1. Actor基礎框架

#### ActorBase類
```python
class ActorBase:
    def __init__(self, name: str, config: dict):
        self.name = name
        self.config = config
        self.message_queue = asyncio.Queue()
        self.running = False
        
    async def start(self):
        """啟動Actor"""
        
    async def stop(self):
        """停止Actor"""
        
    async def handle_message(self, message):
        """處理消息的抽象方法"""
        raise NotImplementedError
```

#### 消息系統
```python
class Message:
    def __init__(self, type: str, data: Any, sender: str, timestamp: float):
        self.type = type
        self.data = data
        self.sender = sender
        self.timestamp = timestamp
        
class MessageRouter:
    def __init__(self):
        self.actors = {}
        self.routes = {}
        
    async def send_message(self, target: str, message: Message):
        """發送消息到目標Actor"""
        
    def register_route(self, message_type: str, handler: str):
        """註冊消息路由"""
```

### 2. DDE接收Actor

#### 設計目標
- 最小化DDE回調函數處理時間
- 高效的數據緩衝機制
- 支持多個DDE連接

#### 核心實現
```python
class DDEReceiverActor(ActorBase):
    def __init__(self, config):
        super().__init__("DDEReceiver", config)
        self.dde_clients = {}
        self.ring_buffer = RingBuffer(size=1024*1024)
        
    def on_dde_data(self, item: str, value: str):
        """DDE回調函數 - 最小化處理時間"""
        # 直接寫入環形緩衝區，無鎖操作
        self.ring_buffer.write(item, value, time.time())
        
    async def process_buffer(self):
        """批量處理緩衝區數據"""
        batch = self.ring_buffer.read_batch(100)
        for item, value, timestamp in batch:
            message = Message("dde_data", {
                "item": item,
                "value": value,
                "timestamp": timestamp
            }, self.name, time.time())
            await self.send_message("DataProcessor", message)
```

### 3. 數據處理Actor

#### 設計目標
- 批量處理提高效率
- 支持多種數據驗證規則
- 內存池管理避免GC壓力

#### 核心實現
```python
class DataProcessorActor(ActorBase):
    def __init__(self, config):
        super().__init__("DataProcessor", config)
        self.memory_pool = MemoryPool()
        self.validators = []
        self.batch_size = config.get("batch_size", 100)
        
    async def handle_message(self, message):
        if message.type == "dde_data":
            await self.process_dde_data(message.data)
            
    async def process_dde_data(self, data):
        """處理DDE數據"""
        # 數據驗證
        if not self.validate_data(data):
            return
            
        # 業務邏輯處理
        processed_data = self.apply_business_logic(data)
        
        # 發送到GUI更新Actor
        gui_message = Message("gui_update", processed_data, 
                             self.name, time.time())
        await self.send_message("GUIUpdater", gui_message)
```

### 4. GUI更新Actor

#### 設計目標
- 虛擬化顯示支持大量數據
- 可配置的更新頻率
- 非阻塞的GUI更新

#### 核心實現
```python
class GUIUpdaterActor(ActorBase):
    def __init__(self, config, gui_manager):
        super().__init__("GUIUpdater", config)
        self.gui_manager = gui_manager
        self.update_queue = asyncio.Queue()
        self.update_interval = config.get("update_interval", 0.1)
        
    async def handle_message(self, message):
        if message.type == "gui_update":
            await self.update_queue.put(message.data)
            
    async def batch_update_gui(self):
        """批量更新GUI"""
        updates = []
        try:
            while len(updates) < 100:  # 批量收集更新
                update = await asyncio.wait_for(
                    self.update_queue.get(), timeout=0.01)
                updates.append(update)
        except asyncio.TimeoutError:
            pass
            
        if updates:
            # 使用Qt的invokeMethod確保在主線程執行
            QMetaObject.invokeMethod(
                self.gui_manager, "batch_update",
                Qt.QueuedConnection,
                Q_ARG(list, updates)
            )
```

## 性能優化策略

### 1. 內存管理
- **內存池**：預分配內存避免頻繁分配
- **對象重用**：重用消息對象減少GC壓力
- **零拷貝**：使用內存映射和指針傳遞

### 2. 並發控制
- **無鎖數據結構**：環形緩衝區使用原子操作
- **批量處理**：累積數據後批量處理
- **背壓控制**：智能丟棄或緩存過量數據

### 3. I/O優化
- **異步I/O**：所有I/O操作都是異步的
- **批量寫入**：文件和數據庫批量寫入
- **壓縮存儲**：使用壓縮算法減少存儲空間

## 擴展性設計

### 1. 水平擴展
- **Actor實例化**：可以創建多個相同類型的Actor
- **負載均衡**：消息路由支持負載均衡
- **分佈式部署**：支持跨進程和跨機器部署

### 2. 插件化架構
- **數據源插件**：支持新的數據源類型
- **處理器插件**：支持自定義數據處理邏輯
- **輸出插件**：支持新的輸出格式和目標

### 3. 配置驅動
- **動態配置**：支持運行時配置更新
- **性能調優**：可配置的性能參數
- **功能開關**：可選的功能模組

## 容錯和監控

### 1. 容錯機制
- **Actor重啟**：失敗的Actor自動重啟
- **消息重試**：失敗的消息自動重試
- **降級策略**：系統過載時的降級處理

### 2. 性能監控
- **實時指標**：吞吐量、延遲、錯誤率
- **資源監控**：CPU、內存、網絡使用率
- **告警機制**：異常情況自動告警

### 3. 日誌和追蹤
- **結構化日誌**：使用JSON格式的結構化日誌
- **分佈式追蹤**：跨Actor的請求追蹤
- **性能分析**：內置性能分析工具
