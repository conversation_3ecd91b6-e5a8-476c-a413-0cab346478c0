#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
失敗項目分析工具

分析兩次執行中失敗項目的差異
"""

import re
from pathlib import Path


def extract_failed_items(log_file):
    """提取失敗的項目"""
    failed_items = []
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正則表達式提取失敗項目
        pattern = r'❌ ([^:]+): 請求數據失敗'
        matches = re.findall(pattern, content)
        
        for match in matches:
            failed_items.append(match.strip())
        
        return failed_items
        
    except Exception as e:
        print(f"❌ 讀取文件失敗 {log_file}: {str(e)}")
        return []


def analyze_failed_items():
    """分析失敗項目"""
    print("🔍 失敗項目分析")
    print("=" * 60)
    
    # 提取兩次執行的失敗項目
    cmd01_failed = extract_failed_items("logs/CMD01.txt")
    cmd02_failed = extract_failed_items("logs/CMD02.txt")
    
    print(f"📊 統計結果:")
    print(f"   第1次執行失敗項目: {len(cmd01_failed)}")
    print(f"   第2次執行失敗項目: {len(cmd02_failed)}")
    
    # 分析差異
    cmd01_set = set(cmd01_failed)
    cmd02_set = set(cmd02_failed)
    
    # 共同失敗的項目
    common_failed = cmd01_set & cmd02_set
    
    # 只在第1次失敗的項目
    only_cmd01_failed = cmd01_set - cmd02_set
    
    # 只在第2次失敗的項目
    only_cmd02_failed = cmd02_set - cmd01_set
    
    print(f"\n🔍 失敗項目分析:")
    print(f"   共同失敗項目: {len(common_failed)}")
    print(f"   僅第1次失敗: {len(only_cmd01_failed)}")
    print(f"   僅第2次失敗: {len(only_cmd02_failed)}")
    
    # 顯示共同失敗項目
    if common_failed:
        print(f"\n❌ 兩次都失敗的項目 ({len(common_failed)} 個):")
        for item in sorted(common_failed)[:10]:  # 只顯示前10個
            print(f"   {item}")
        if len(common_failed) > 10:
            print(f"   ... 還有 {len(common_failed) - 10} 個")
    
    # 顯示第1次失敗但第2次成功的項目
    if only_cmd01_failed:
        print(f"\n✅ 第1次失敗但第2次成功的項目 ({len(only_cmd01_failed)} 個):")
        for item in sorted(only_cmd01_failed)[:10]:
            print(f"   {item}")
        if len(only_cmd01_failed) > 10:
            print(f"   ... 還有 {len(only_cmd01_failed) - 10} 個")
    
    # 顯示第2次失敗但第1次成功的項目
    if only_cmd02_failed:
        print(f"\n❌ 第1次成功但第2次失敗的項目 ({len(only_cmd02_failed)} 個):")
        for item in sorted(only_cmd02_failed)[:10]:
            print(f"   {item}")
        if len(only_cmd02_failed) > 10:
            print(f"   ... 還有 {len(only_cmd02_failed) - 10} 個")
    
    # 分析失敗模式
    analyze_failure_patterns(cmd01_failed, cmd02_failed)
    
    # 計算成功率
    calculate_success_rates(cmd01_failed, cmd02_failed)


def analyze_failure_patterns(cmd01_failed, cmd02_failed):
    """分析失敗模式"""
    print(f"\n🔍 失敗模式分析:")
    print("-" * 40)
    
    # 分析失敗項目的類型
    def categorize_failures(failed_items):
        categories = {
            'Time': [],
            'TradingDate': [],
            'Price/OHLC': [],
            'Volume': [],
            'BestBid/Ask': [],
            'Size': [],
            'Contract': [],
            'Other': []
        }
        
        for item in failed_items:
            if 'Time' in item:
                categories['Time'].append(item)
            elif 'TradingDate' in item:
                categories['TradingDate'].append(item)
            elif any(x in item for x in ['Open', 'High', 'Low', 'Price']):
                categories['Price/OHLC'].append(item)
            elif 'Volume' in item:
                categories['Volume'].append(item)
            elif any(x in item for x in ['BestBid', 'BestAsk']):
                categories['BestBid/Ask'].append(item)
            elif 'Size' in item:
                categories['Size'].append(item)
            elif 'Contract' in item:
                categories['Contract'].append(item)
            else:
                categories['Other'].append(item)
        
        return categories
    
    cmd01_categories = categorize_failures(cmd01_failed)
    cmd02_categories = categorize_failures(cmd02_failed)
    
    print("第1次執行失敗類型分布:")
    for category, items in cmd01_categories.items():
        if items:
            print(f"   {category}: {len(items)} 個")
    
    print("\n第2次執行失敗類型分布:")
    for category, items in cmd02_categories.items():
        if items:
            print(f"   {category}: {len(items)} 個")


def calculate_success_rates(cmd01_failed, cmd02_failed):
    """計算成功率"""
    print(f"\n📊 成功率分析:")
    print("-" * 40)
    
    # 假設總項目數（從日誌中提取）
    total_items_cmd01 = 12567  # 從 CMD01.txt 中的統計
    total_items_cmd02 = 12484  # 從 CMD02.txt 中的統計
    
    success_rate_cmd01 = (total_items_cmd01 - len(cmd01_failed)) / total_items_cmd01 * 100
    success_rate_cmd02 = (total_items_cmd02 - len(cmd02_failed)) / total_items_cmd02 * 100
    
    print(f"第1次執行:")
    print(f"   總項目: {total_items_cmd01}")
    print(f"   失敗項目: {len(cmd01_failed)}")
    print(f"   成功率: {success_rate_cmd01:.2f}%")
    
    print(f"\n第2次執行:")
    print(f"   總項目: {total_items_cmd02}")
    print(f"   失敗項目: {len(cmd02_failed)}")
    print(f"   成功率: {success_rate_cmd02:.2f}%")
    
    print(f"\n📈 改善情況:")
    if success_rate_cmd02 > success_rate_cmd01:
        print(f"   ✅ 成功率提升: +{success_rate_cmd02 - success_rate_cmd01:.2f}%")
    else:
        print(f"   ❌ 成功率下降: {success_rate_cmd02 - success_rate_cmd01:.2f}%")


def generate_recommendations():
    """生成建議"""
    print(f"\n" + "=" * 60)
    print(f"💡 分析結論和建議")
    print(f"=" * 60)
    
    print(f"🔍 關鍵發現:")
    print(f"1. 失敗項目不固定 - 同一項目在不同時間可能成功或失敗")
    print(f"2. 錯誤代碼統一為 0x4009 (DMLERR_NOTPROCESSED)")
    print(f"3. 失敗可能與時間、負載、數據源狀態有關")
    
    print(f"\n💡 建議解決方案:")
    print(f"1. 添加重試機制:")
    print(f"   - 對失敗項目進行2-3次重試")
    print(f"   - 重試間隔：100-500ms")
    print(f"   - 指數退避策略")
    
    print(f"\n2. 優化請求策略:")
    print(f"   - 降低請求頻率")
    print(f"   - 分批處理項目")
    print(f"   - 添加請求間隔")
    
    print(f"\n3. 錯誤處理改進:")
    print(f"   - 記錄失敗項目詳情")
    print(f"   - 提供失敗項目重新測試功能")
    print(f"   - 統計失敗模式")
    
    print(f"\n4. 監控和報告:")
    print(f"   - 實時顯示成功率")
    print(f"   - 失敗項目分類統計")
    print(f"   - 生成失敗項目報告")


def main():
    """主函數"""
    # 檢查日誌文件是否存在
    if not Path("logs/CMD01.txt").exists():
        print("❌ logs/CMD01.txt 不存在")
        return
    
    if not Path("logs/CMD02.txt").exists():
        print("❌ logs/CMD02.txt 不存在")
        return
    
    analyze_failed_items()
    generate_recommendations()


if __name__ == "__main__":
    main()
