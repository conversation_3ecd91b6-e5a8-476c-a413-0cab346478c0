#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成測試數據

直接生成 DDE 數據文件用於測試
"""

import time
import random
from pathlib import Path
from datetime import datetime


def generate_test_data():
    """生成測試數據"""
    print("📊 生成測試 DDE 數據")
    print("=" * 40)
    
    # 確保輸出目錄存在
    output_dir = Path("outputs")
    output_dir.mkdir(exist_ok=True)
    
    # 生成 CSV 數據
    csv_file = output_dir / "dde_data.csv"
    json_file = output_dir / "dde_data.json"
    
    print(f"1. 生成 CSV 數據到 {csv_file}")
    
    # CSV 標題
    csv_data = ["timestamp,item,original_value,processed_value"]
    
    # 生成 50 筆測試數據
    base_prices = {
        "FITXN07": 22290,
        "FITXN08": 22120,
        "FITXN09": 22200
    }
    
    for i in range(50):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # 隨機選擇產品
        symbol = random.choice(list(base_prices.keys()))
        base_price = base_prices[symbol]
        
        # 隨機選擇數據類型
        data_type = random.choice(["tick.成交價", "tick.成交量", "TF-Price", "TF-Volume"])
        
        if "價" in data_type or "Price" in data_type:
            # 價格數據：基準價格 ± 50
            value = base_price + random.randint(-50, 50)
        else:
            # 成交量數據：1-10
            value = random.randint(1, 10)
        
        item = f"{symbol}.{data_type}"
        csv_data.append(f"{timestamp},{item},{value},{value}")
        
        # 更新基準價格
        if "價" in data_type or "Price" in data_type:
            base_prices[symbol] = value
        
        time.sleep(0.1)  # 模擬實時數據間隔
    
    # 寫入 CSV 文件
    with open(csv_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(csv_data))
    
    print(f"✅ CSV 文件生成完成: {len(csv_data)-1} 筆數據")
    print(f"   文件大小: {csv_file.stat().st_size} bytes")
    
    # 生成 JSON 數據
    print(f"\n2. 生成 JSON 數據到 {json_file}")
    
    import json
    
    json_data = []
    for line in csv_data[1:]:  # 跳過標題
        parts = line.split(',')
        if len(parts) == 4:
            json_data.append({
                "timestamp": parts[0],
                "item": parts[1],
                "original_value": parts[2],
                "processed_value": parts[3]
            })
    
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ JSON 文件生成完成: {len(json_data)} 筆數據")
    print(f"   文件大小: {json_file.stat().st_size} bytes")
    
    # 顯示樣本數據
    print(f"\n3. 數據樣本:")
    print("CSV 格式:")
    for i, line in enumerate(csv_data[:6]):
        if i == 0:
            print(f"   標題: {line}")
        else:
            print(f"   {i:2d}: {line}")
    
    if len(csv_data) > 6:
        print(f"   ... 還有 {len(csv_data)-6} 筆數據")
    
    print(f"\n🎉 測試數據生成完成!")
    print(f"現在你可以:")
    print(f"1. 查看 outputs/dde_data.csv")
    print(f"2. 查看 outputs/dde_data.json") 
    print(f"3. 運行 python watch_data.py 監控文件")


if __name__ == "__main__":
    generate_test_data()
