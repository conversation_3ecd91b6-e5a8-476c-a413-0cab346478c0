#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引擎集成模块
将DDE数据引擎集成到多商品版本中
"""

import sys
import os
import logging
import threading
import time
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass

# 添加引擎路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'dde_data_engine'))

from dde_data_engine import DDEDataProcessor, DataFileHandler, DataProcessorConfig
from dde_data_engine.data_structures import ItemData, RawDataRow


@dataclass
class EngineInstance:
    """引擎实例"""
    product_symbol: str
    data_type: str
    processor: DDEDataProcessor
    file_handler: DataFileHandler
    items_data: Dict[str, ItemData]
    thread: Optional[threading.Thread] = None
    running: bool = False


class MultiProductEngineManager:
    """多商品引擎管理器
    
    负责管理多个商品的DDE数据处理引擎实例
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.engines: Dict[str, EngineInstance] = {}
        self.running = False
        
        # 回调函数
        self.on_data_received: Optional[Callable] = None
        self.on_engine_error: Optional[Callable] = None
        
    def create_engine(self, product_symbol: str, data_type: str, 
                     config_dict: dict, items_data: Dict[str, ItemData]) -> bool:
        """创建引擎实例"""
        try:
            engine_id = f"{product_symbol}_{data_type}"
            
            if engine_id in self.engines:
                self.logger.warning(f"引擎实例已存在: {engine_id}")
                return True
            
            # 创建配置
            config = DataProcessorConfig()
            config.load_from_dict(config_dict)
            
            # 创建数据处理器
            processor = DDEDataProcessor(config, self.logger)
            
            # 创建文件处理器
            file_handler = DataFileHandler(config, self.logger)
            
            # 设置回调
            processor.on_data_received = self._on_engine_data_received
            
            # 创建引擎实例
            engine = EngineInstance(
                product_symbol=product_symbol,
                data_type=data_type,
                processor=processor,
                file_handler=file_handler,
                items_data=items_data.copy()
            )
            
            self.engines[engine_id] = engine
            self.logger.info(f"创建引擎实例成功: {engine_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建引擎实例失败: {product_symbol}.{data_type}, {str(e)}")
            return False
    
    def start_engine(self, product_symbol: str, data_type: str) -> bool:
        """启动引擎实例"""
        try:
            engine_id = f"{product_symbol}_{data_type}"
            
            if engine_id not in self.engines:
                self.logger.error(f"引擎实例不存在: {engine_id}")
                return False
            
            engine = self.engines[engine_id]
            if engine.running:
                self.logger.warning(f"引擎实例已在运行: {engine_id}")
                return True
            
            # 启动引擎线程
            engine.thread = threading.Thread(
                target=self._engine_worker,
                args=(engine,),
                name=f"Engine-{engine_id}",
                daemon=True
            )
            engine.running = True
            engine.thread.start()
            
            self.logger.info(f"启动引擎实例成功: {engine_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动引擎实例失败: {product_symbol}.{data_type}, {str(e)}")
            return False
    
    def stop_engine(self, product_symbol: str, data_type: str) -> bool:
        """停止引擎实例"""
        try:
            engine_id = f"{product_symbol}_{data_type}"
            
            if engine_id not in self.engines:
                return True
            
            engine = self.engines[engine_id]
            engine.running = False
            
            if engine.thread and engine.thread.is_alive():
                engine.thread.join(timeout=5.0)
            
            self.logger.info(f"停止引擎实例成功: {engine_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"停止引擎实例失败: {product_symbol}.{data_type}, {str(e)}")
            return False
    
    def process_dde_data(self, product_symbol: str, data_type: str, 
                        item: str, value: str) -> bool:
        """处理DDE数据"""
        try:
            engine_id = f"{product_symbol}_{data_type}"
            
            if engine_id not in self.engines:
                self.logger.warning(f"引擎实例不存在: {engine_id}")
                return False
            
            engine = self.engines[engine_id]
            if not engine.running:
                return False
            
            # 处理数据
            return engine.processor.process_dde_data(item, value)
            
        except Exception as e:
            self.logger.error(f"处理DDE数据失败: {product_symbol}.{data_type}, {item}, {str(e)}")
            return False
    
    def get_engine_stats(self) -> Dict[str, dict]:
        """获取引擎统计信息"""
        stats = {}
        for engine_id, engine in self.engines.items():
            stats[engine_id] = {
                'product_symbol': engine.product_symbol,
                'data_type': engine.data_type,
                'running': engine.running,
                'stats': engine.processor.get_stats() if engine.processor else None
            }
        return stats
    
    def _engine_worker(self, engine: EngineInstance):
        """引擎工作线程"""
        try:
            self.logger.info(f"引擎线程启动: {engine.product_symbol}.{engine.data_type}")
            
            while engine.running:
                try:
                    # 这里可以添加引擎的周期性任务
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.logger.error(f"引擎线程异常: {engine.product_symbol}.{engine.data_type}, {str(e)}")
                    if self.on_engine_error:
                        self.on_engine_error(engine.product_symbol, engine.data_type, str(e))
                    
        except Exception as e:
            self.logger.error(f"引擎线程失败: {engine.product_symbol}.{engine.data_type}, {str(e)}")
        finally:
            self.logger.info(f"引擎线程结束: {engine.product_symbol}.{engine.data_type}")
    
    def _on_engine_data_received(self, item: str, value: str):
        """引擎数据接收回调"""
        try:
            if self.on_data_received:
                self.on_data_received(item, value)
        except Exception as e:
            self.logger.error(f"引擎数据回调失败: {item}, {str(e)}")
    
    def start_all(self) -> bool:
        """启动所有引擎"""
        try:
            self.running = True
            success_count = 0
            
            for engine_id, engine in self.engines.items():
                if self.start_engine(engine.product_symbol, engine.data_type):
                    success_count += 1
            
            self.logger.info(f"启动引擎: {success_count}/{len(self.engines)} 个成功")
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"启动所有引擎失败: {str(e)}")
            return False
    
    def stop_all(self) -> bool:
        """停止所有引擎"""
        try:
            self.running = False
            
            for engine_id, engine in self.engines.items():
                self.stop_engine(engine.product_symbol, engine.data_type)
            
            self.logger.info("所有引擎已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止所有引擎失败: {str(e)}")
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop_all()
            self.engines.clear()
            self.logger.info("引擎管理器清理完成")
        except Exception as e:
            self.logger.error(f"引擎管理器清理失败: {str(e)}")
