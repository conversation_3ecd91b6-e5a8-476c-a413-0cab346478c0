DDE 模組使用說明文件
====================

1. 模組概述
-----------
DDE (Dynamic Data Exchange) 模組是一個用於 Windows 系統的資料交換實現。
支援客戶端和伺服器端功能，可用於即時資料傳輸和監控。

2. 主要類別
-----------
2.1 DDEClient
- 功能：DDE 客戶端，用於連接 DDE 伺服器並接收資料
- 主要方法：
  * connect(service, topic) - 連接 DDE 伺服器
  * disconnect() - 斷開連接
  * request(item) - 請求特定項目的資料
  * advise(item, callback) - 訂閱資料更新
  * unadvise(item) - 取消訂閱特定項目
  * poke(item_name, value) - 向伺服器發送資料
  * execute(command) - 向伺服器發送命令
  * ensure_connected() - 確保連接狀態
  * reconnect() - 重新建立連接
  * get_connection_status() - 獲取連接狀態
  * get_performance_stats() - 獲取效能統計資訊
  * update_reconnect_settings() - 更新重連設定
  * update_health_check_settings() - 更新健康檢查設定

2.2 DDEServer
- 功能：DDE 伺服器，用於提供 DDE 服務
- 主要方法：
  * register_topic(topic_name) - 註冊主題
  * register_item(topic_name, item_name, value) - 註冊項目
  * post_advise(topic_name, item_name) - 推送更新
  * register_poke_callback(callback) - 註冊 POKE 回調函數
  * register_execute_callback(callback) - 註冊 EXECUTE 回調函數
  * get_connection_status() - 獲取連接狀態
  * get_performance_stats() - 獲取效能統計資訊

2.3 DDECache
- 功能：資料快取類別，用於暫存 DDE 資料
- 主要方法：
  * set(key, value) - 設置快取資料
  * get(key) - 獲取快取資料
  * _cleanup() - 清理過期資料

3. 使用範例
-----------
3.1 客戶端範例
--------------
from dydde import DDEClient

# 建立客戶端
client = DDEClient()

# 連接伺服器
client.connect("XQTISC.Quote", "TSE")

# 請求資料
data = client.request("2330.TW")

# 訂閱資料更新
def on_update(data):
    print(f"收到更新: {data}")
client.advise("2330.TW", on_update)

# 取消訂閱
client.unadvise("2330.TW")

# 向伺服器發送資料
client.poke("MyItem", "新值")

# 向伺服器發送命令
client.execute("MyCommand")

# 更新重連設定
client.update_reconnect_settings(
    auto_reconnect=True,
    max_attempts=5,
    interval=1.0,
    min_interval=0.5
)

# 更新健康檢查設定
client.update_health_check_settings(
    item="2330.TW",
    interval=30,
    threshold=3
)

# 斷開連接
client.disconnect()

3.2 伺服器範例
--------------
from dydde import DDEServer

# 建立伺服器
server = DDEServer()

# 註冊主題和項目
server.register_topic("MyTopic")
server.register_item("MyTopic", "MyItem", "初始值")

# 註冊 POKE 回調
def on_poke(topic_name, item_name, value):
    print(f"收到 POKE: {topic_name}.{item_name} = {value}")
server.register_poke_callback(on_poke)

# 註冊 EXECUTE 回調
def on_execute(topic_name, command):
    print(f"收到命令: {topic_name} - {command}")
    return True
server.register_execute_callback(on_execute)

# 更新資料
server.update_item("MyTopic", "MyItem", "新值")

# 推送更新
server.post_advise("MyTopic", "MyItem")

4. 錯誤處理
-----------
所有方法都會拋出 DDEError 異常，建議使用 try-except 進行錯誤處理：

try:
    client = DDEClient()
    client.connect("XQTISC.Quote", "TSE")
except DDEError as e:
    print(f"連接失敗: {str(e)}")

5. 效能監控
-----------
模組內建效能監控功能，可獲取各項操作的統計資訊：

# 獲取效能統計
stats = client.get_performance_stats()
print(f"平均請求時間: {stats['request_times']['avg']}秒")
print(f"請求次數: {stats['request_times']['count']}")

# 獲取連接狀態
status = client.get_connection_status()
print(f"連接狀態: {status['connected']}")
print(f"最後活動時間: {status['last_activity']}")

6. 日誌功能
-----------
模組提供完整的日誌記錄功能，支援不同級別的日誌輸出：

- DEBUG: 詳細的調試信息
- INFO: 一般操作信息
- WARNING: 警告信息
- ERROR: 錯誤信息

日誌輸出格式：
[時間戳] [日誌級別] [C/S] 訊息內容

7. 事件處理
-----------
模組提供事件處理機制，可註冊各種事件處理器：

# 註冊事件處理器
def on_connect(**kwargs):
    print("連接成功")
client.add_event_handler("connect", on_connect)

# 移除事件處理器
client.remove_event_handler("connect", on_connect)

8. 注意事項
-----------
- 使用前需確保 Windows DDE 服務已啟動
- 建議在 GUI 應用中使用時，在獨立的執行緒中運行
- 長時間運行時需注意資源清理
- 建議定期檢查連接狀態
- 使用 POKE 和 EXECUTE 時需確保伺服器端已註冊相應的回調函數
- 建議使用 DDECache 來暫存常用資料，提高效能
- 注意設定適當的重連和健康檢查參數

9. 常見問題
-----------
Q: 連接失敗怎麼辦？
A: 檢查服務名稱和主題名稱是否正確，確認 DDE 伺服器是否正在運行

Q: 資料更新不及時？
A: 檢查 post_advise 是否正確調用，確認回調函數是否正確註冊

Q: 記憶體使用過高？
A: 確保正確調用 disconnect() 方法，定期清理不需要的連接

Q: POKE 或 EXECUTE 失敗？
A: 檢查伺服器端是否已註冊相應的回調函數，確認參數格式是否正確

Q: 如何處理斷線重連？
A: 使用 update_reconnect_settings() 設定適當的重連參數，並監控連接狀態

Q: 如何監控系統健康狀態？
A: 使用 update_health_check_settings() 設定健康檢查參數，定期檢查系統狀態

10. 聯絡資訊
------------
如有問題，請聯繫開發團隊：
email: <EMAIL> 