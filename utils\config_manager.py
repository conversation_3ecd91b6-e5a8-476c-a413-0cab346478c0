#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
設定檔管理器
負責載入、保存和驗證設定檔
"""

import configparser
import os
import logging
from typing import Dict, Any, Optional, List, Tuple

class ConfigManager:
    """設定檔管理器"""
    
    def __init__(self, config_file: str = 'config.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        
    def load_config(self, config_file: str = None) -> configparser.ConfigParser:
        """載入設定檔"""
        try:
            # 如果指定了配置文件，則使用指定的文件
            if config_file:
                self.config_file = config_file

            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
                logging.info(f"設定檔載入成功: {self.config_file}")
            else:
                logging.warning(f"設定檔不存在: {self.config_file}")
                # 只有在使用默認配置文件時才創建默認配置
                if not config_file or config_file == 'config.ini':
                    self._create_default_config()
                else:
                    raise FileNotFoundError(f"指定的配置文件不存在: {self.config_file}")

            return self.config

        except Exception as e:
            logging.error(f"載入設定檔失敗: {str(e)}")
            # 只有在使用默認配置文件時才創建默認配置
            if not config_file or config_file == 'config.ini':
                self._create_default_config()
                return self.config
            else:
                raise
    
    def save_config(self) -> bool:
        """保存設定檔"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            logging.info(f"設定檔保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            logging.error(f"保存設定檔失敗: {str(e)}")
            return False
    
    def get_section(self, section_name: str) -> Optional[configparser.SectionProxy]:
        """獲取設定區塊"""
        if section_name in self.config:
            return self.config[section_name]
        return None
    
    def set_value(self, section: str, key: str, value: Any) -> bool:
        """設定值"""
        try:
            if section not in self.config:
                self.config.add_section(section)
            
            self.config.set(section, key, str(value))
            return True
            
        except Exception as e:
            logging.error(f"設定值失敗 [{section}]{key}={value}: {str(e)}")
            return False
    
    def get_value(self, section: str, key: str, fallback: Any = None) -> Any:
        """獲取值"""
        try:
            if section in self.config and key in self.config[section]:
                value = self.config[section][key]
                
                # 嘗試轉換為適當的類型
                if isinstance(fallback, bool):
                    return self.config.getboolean(section, key, fallback=fallback)
                elif isinstance(fallback, int):
                    return self.config.getint(section, key, fallback=fallback)
                elif isinstance(fallback, float):
                    return self.config.getfloat(section, key, fallback=fallback)
                else:
                    return value
            
            return fallback
            
        except Exception as e:
            logging.error(f"獲取值失敗 [{section}]{key}: {str(e)}")
            return fallback
    
    def _create_default_config(self):
        """創建預設設定檔"""
        try:
            # DDE 設定
            self.config.add_section('DDE')
            self.config.set('DDE', 'service', 'XQTISC')
            self.config.set('DDE', 'topic', 'Quote')
            self.config.set('DDE', 'disconnect_on_exit', 'false')
            
            # 項目設定
            self.config.add_section('Items')
            items = [
                ('交易時間', 'FITXN06.TF-Time'),
                ('交易日期', 'FITXN06.TF-TradingDate'),
                ('開盤價', 'FITXN06.TF-Open'),
                ('最高價', 'FITXN06.TF-High'),
                ('最低價', 'FITXN06.TF-Low'),
                ('成交價', 'FITXN06.TF-Price'),
                ('總量', 'FITXN06.TF-TotalVolume'),
                ('單量', 'FITXN06.TF-Volume')
            ]
            
            for i, (name, code) in enumerate(items, 1):
                self.config.set('Items', f'item{i}_name', name)
                self.config.set('Items', f'item{i}_code', code)
            
            # 表格設定
            self.config.add_section('Table')
            self.config.set('Table', 'enable_time_newline', 'true')
            self.config.set('Table', 'time_newline_interval', '0.800')
            self.config.set('Table', 'enable_value_change_check', 'true')
            self.config.set('Table', 'value_change_check_mode', 'single')
            self.config.set('Table', 'value_change_check_items', '總量')
            
            # 檔案輸出設定
            self.config.add_section('FileOutput')
            self.config.set('FileOutput', 'enable_data_file', 'false')
            self.config.set('FileOutput', 'enable_complete_data_file', 'true')
            self.config.set('FileOutput', 'enable_log_file', 'true')
            
            # 輸出路徑設定
            self.config.add_section('OutputPath')
            self.config.set('OutputPath', 'log_file', './logs/{date}/dde_monitor_01.log')
            self.config.set('OutputPath', 'data_file', 'R:/TEMP/data/mon/XQ/FITXN06/_a/dde_data_01.csv')
            self.config.set('OutputPath', 'complete_data_file', 'R:/TEMP/data/mon/XQ/FITXN06/_a/complete_data_01.csv')
            
            # 自動連線設定
            self.config.add_section('AutoConnect')
            self.config.set('AutoConnect', 'enable_auto_connect', 'false')
            self.config.set('AutoConnect', 'auto_connect_mode', 'delay')
            self.config.set('AutoConnect', 'auto_connect_delay', '30.0')
            self.config.set('AutoConnect', 'schedule_connect_times', '')
            self.config.set('AutoConnect', 'enable_cross_day_schedule', 'true')
            self.config.set('AutoConnect', 'skip_weekends', 'false')
            
            # 自動結束設定
            self.config.add_section('AutoShutdown')
            self.config.set('AutoShutdown', 'enable_auto_shutdown', 'false')
            self.config.set('AutoShutdown', 'shutdown_time', '17:30:00')
            self.config.set('AutoShutdown', 'shutdown_buffer_seconds', '300')
            self.config.set('AutoShutdown', 'shutdown_warning_seconds', '60')
            self.config.set('AutoShutdown', 'force_shutdown', 'true')
            self.config.set('AutoShutdown', 'save_data_before_shutdown', 'true')
            self.config.set('AutoShutdown', 'disconnect_before_shutdown', 'true')
            self.config.set('AutoShutdown', 'cleanup_temp_files', 'true')
            
            # 通知設定
            self.config.add_section('Notifications')
            self.config.set('Notifications', 'enable_system_notifications', 'true')
            self.config.set('Notifications', 'enable_sound_notifications', 'false')
            self.config.set('Notifications', 'notification_levels', 'INFO,WARNING,ERROR')
            self.config.set('Notifications', 'notify_auto_connect', 'true')
            self.config.set('Notifications', 'notify_auto_disconnect', 'true')
            self.config.set('Notifications', 'notify_auto_shutdown', 'true')
            
            # 保存預設設定檔
            self.save_config()
            logging.info("預設設定檔已創建")
            
        except Exception as e:
            logging.error(f"創建預設設定檔失敗: {str(e)}")
    
    def validate_config(self) -> Dict[str, list]:
        """驗證設定檔"""
        errors = {}
        
        # 檢查必要區塊
        required_sections = ['DDE', 'Items', 'AutoConnect', 'AutoShutdown', 'Notifications']
        for section in required_sections:
            if section not in self.config:
                if 'missing_sections' not in errors:
                    errors['missing_sections'] = []
                errors['missing_sections'].append(section)
        
        # 檢查 DDE 設定
        if 'DDE' in self.config:
            dde_section = self.config['DDE']
            if 'service' not in dde_section or not dde_section['service']:
                if 'dde_errors' not in errors:
                    errors['dde_errors'] = []
                errors['dde_errors'].append('service 不能為空')
            
            if 'topic' not in dde_section or not dde_section['topic']:
                if 'dde_errors' not in errors:
                    errors['dde_errors'] = []
                errors['dde_errors'].append('topic 不能為空')
        
        # 檢查項目設定
        if 'Items' in self.config:
            items_section = self.config['Items']
            item_count = 0
            for key in items_section:
                if key.endswith('_name'):
                    item_count += 1
            
            if item_count == 0:
                if 'items_errors' not in errors:
                    errors['items_errors'] = []
                errors['items_errors'].append('至少需要一個監控項目')
        
        return errors
    
    def backup_config(self, backup_suffix: str = None) -> bool:
        """備份設定檔"""
        try:
            if backup_suffix is None:
                from datetime import datetime
                backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            backup_file = f"{self.config_file}.backup_{backup_suffix}"
            
            if os.path.exists(self.config_file):
                import shutil
                shutil.copy2(self.config_file, backup_file)
                logging.info(f"設定檔備份成功: {backup_file}")
                return True
            else:
                logging.warning("原設定檔不存在，無法備份")
                return False
                
        except Exception as e:
            logging.error(f"備份設定檔失敗: {str(e)}")
            return False


class MultiProductConfigManager:
    """多商品配置管理器

    支持基于模板的多商品配置管理，使用{symbol}占位符
    实现配置模板的复用和商品特定配置的生成
    """

    def __init__(self, config_file: str = 'multi_config.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.symbols = []  # 启用的商品列表
        self.data_types = []  # 可用的数据类型
        self.templates = {}  # 数据类型模板
        self.symbol_configs = {}  # 每个商品的配置
        self.auto_connect_templates = {}  # 自动连接模板

    def load_config(self, config_file: str = None) -> bool:
        """载入多商品配置文件"""
        try:
            if config_file:
                self.config_file = config_file

            if not os.path.exists(self.config_file):
                logging.error(f"多商品配置文件不存在: {self.config_file}")
                return False

            self.config.read(self.config_file, encoding='utf-8')
            logging.info(f"多商品配置文件载入成功: {self.config_file}")

            # 解析配置
            self._parse_symbols()
            self._parse_templates()
            self._parse_data_types()
            self._parse_symbol_configs()
            self._parse_auto_connect_templates()

            return True

        except Exception as e:
            logging.error(f"载入多商品配置失败: {str(e)}")
            return False

    def _parse_symbols(self):
        """解析商品列表"""
        try:
            if 'Symbols' in self.config:
                symbol_list = self.config.get('Symbols', 'symbol_list', fallback='')
                self.symbols = [s.strip() for s in symbol_list.split(',') if s.strip()]
                logging.info(f"载入商品列表: {self.symbols}")
            else:
                logging.warning("配置文件中未找到 Symbols 区块")

        except Exception as e:
            logging.error(f"解析商品列表失败: {str(e)}")

    def _parse_templates(self):
        """解析数据类型模板"""
        try:
            self.templates = {}
            for section_name in self.config.sections():
                if section_name.startswith('Template_'):
                    # 保留完整的模板名称，包括 Template_ 前缀
                    template_name = section_name
                    template_items = {}

                    for key, value in self.config[section_name].items():
                        if key.endswith('_name') or key.endswith('_code'):
                            template_items[key] = value

                    self.templates[template_name] = template_items
                    logging.info(f"载入模板: {template_name}, 项目数: {len(template_items)//2}")

        except Exception as e:
            logging.error(f"解析数据类型模板失败: {str(e)}")

    def _parse_data_types(self):
        """解析数据类型配置"""
        try:
            self.data_types = []
            for section_name in self.config.sections():
                if section_name.startswith('DataType_'):
                    data_type = section_name.replace('DataType_', '')
                    self.data_types.append(data_type)

            logging.info(f"载入数据类型: {self.data_types}")

        except Exception as e:
            logging.error(f"解析数据类型失败: {str(e)}")

    def _parse_symbol_configs(self):
        """解析每个商品的配置"""
        try:
            self.symbol_configs = {}
            for symbol in self.symbols:
                if symbol in self.config:
                    symbol_config = dict(self.config[symbol])
                    self.symbol_configs[symbol] = symbol_config
                    logging.info(f"载入商品配置: {symbol}")
                else:
                    logging.warning(f"商品 {symbol} 的配置区块不存在")

        except Exception as e:
            logging.error(f"解析商品配置失败: {str(e)}")

    def _parse_auto_connect_templates(self):
        """解析自动连接模板"""
        try:
            self.auto_connect_templates = {}
            for section_name in self.config.sections():
                if section_name.startswith('AutoConnect_'):
                    # 保留完整的模板名称，包括 AutoConnect_ 前缀
                    template_name = section_name
                    template_config = dict(self.config[section_name])
                    self.auto_connect_templates[template_name] = template_config
                    logging.info(f"载入自动连接模板: {template_name}")

        except Exception as e:
            logging.error(f"解析自动连接模板失败: {str(e)}")

    def get_symbol_items(self, symbol: str, data_type: str) -> Dict[str, str]:
        """获取指定商品和数据类型的监控项目

        Args:
            symbol: 商品代码 (如 FITXN07.TF)
            data_type: 数据类型 (如 tick, order, level2, daily)

        Returns:
            Dict[str, str]: 项目代码到项目名称的映射
        """
        try:
            # 检查商品是否启用此数据类型
            if symbol not in self.symbol_configs:
                return {}

            enabled_types = self.symbol_configs[symbol].get('enabled_types', '')
            if data_type not in enabled_types.split(','):
                return {}

            # 获取数据类型配置
            datatype_section = f'DataType_{data_type}'
            if datatype_section not in self.config:
                return {}

            # 获取模板名称
            template_name = self.config.get(datatype_section, 'items_template', fallback='')
            if not template_name or template_name not in self.templates:
                return {}

            # 生成项目映射
            items = {}
            template = self.templates[template_name]

            for key, value in template.items():
                if key.endswith('_code'):
                    # 替换占位符
                    item_code = value.replace('{symbol}', symbol)
                    # 获取对应的名称
                    name_key = key.replace('_code', '_name')
                    if name_key in template:
                        item_name = template[name_key]
                        items[item_code] = item_name

            return items

        except Exception as e:
            logging.error(f"获取商品项目失败 {symbol}-{data_type}: {str(e)}")
            return {}

    def get_symbol_output_path(self, symbol: str) -> str:
        """获取商品的输出路径"""
        try:
            if symbol in self.symbol_configs:
                return self.symbol_configs[symbol].get('output_path', './data/')
            return './data/'

        except Exception as e:
            logging.error(f"获取商品输出路径失败 {symbol}: {str(e)}")
            return './data/'

    def get_symbol_auto_connect_config(self, symbol: str) -> Dict[str, Any]:
        """获取商品的自动连接配置"""
        try:
            if symbol not in self.symbol_configs:
                logging.warning(f"商品 {symbol} 不在配置中")
                return {}

            template_name = self.symbol_configs[symbol].get('auto_connect_template', '')
            if not template_name:
                logging.warning(f"商品 {symbol} 没有指定自动连接模板")
                return {}

            if template_name not in self.auto_connect_templates:
                logging.warning(f"自动连接模板 {template_name} 不存在，可用模板: {list(self.auto_connect_templates.keys())}")
                return {}

            logging.info(f"获取商品 {symbol} 的自动连接配置，模板: {template_name}")
            return self.auto_connect_templates[template_name].copy()

        except Exception as e:
            logging.error(f"获取商品自动连接配置失败 {symbol}: {str(e)}")
            return {}

    def get_datatype_config(self, data_type: str) -> Dict[str, Any]:
        """获取数据类型的配置"""
        try:
            section_name = f'DataType_{data_type}'
            if section_name in self.config:
                return dict(self.config[section_name])
            return {}

        except Exception as e:
            logging.error(f"获取数据类型配置失败 {data_type}: {str(e)}")
            return {}

    def get_common_config(self, config_type: str) -> Dict[str, Any]:
        """获取通用配置

        Args:
            config_type: 配置类型 (如 FileOutput, AutoShutdown, Notifications)
        """
        try:
            section_name = f'Common_{config_type}'
            if section_name in self.config:
                return dict(self.config[section_name])
            return {}

        except Exception as e:
            logging.error(f"获取通用配置失败 {config_type}: {str(e)}")
            return {}

    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        try:
            if 'System' in self.config:
                return dict(self.config['System'])
            return {}

        except Exception as e:
            logging.error(f"获取系统配置失败: {str(e)}")
            return {}

    def get_symbols(self) -> List[str]:
        """获取启用的商品列表"""
        return self.symbols.copy()

    def get_all_symbol_items(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """获取所有商品的所有监控项目

        Returns:
            Dict[symbol][data_type][item_code] = item_name
        """
        try:
            all_items = {}

            for symbol in self.symbols:
                all_items[symbol] = {}

                if symbol in self.symbol_configs:
                    enabled_types = self.symbol_configs[symbol].get('enabled_types', '')
                    for data_type in enabled_types.split(','):
                        data_type = data_type.strip()
                        if data_type:
                            items = self.get_symbol_items(symbol, data_type)
                            if items:
                                all_items[symbol][data_type] = items

            return all_items

        except Exception as e:
            logging.error(f"获取所有商品项目失败: {str(e)}")
            return {}

    def validate_multi_config(self) -> Dict[str, List[str]]:
        """验证多商品配置"""
        errors = {}

        try:
            # 检查必要区块
            required_sections = ['System', 'Symbols']
            for section in required_sections:
                if section not in self.config:
                    if 'missing_sections' not in errors:
                        errors['missing_sections'] = []
                    errors['missing_sections'].append(section)

            # 检查商品列表
            if not self.symbols:
                if 'symbol_errors' not in errors:
                    errors['symbol_errors'] = []
                errors['symbol_errors'].append('商品列表为空')

            # 检查每个商品的配置
            for symbol in self.symbols:
                if symbol not in self.config:
                    if 'symbol_errors' not in errors:
                        errors['symbol_errors'] = []
                    errors['symbol_errors'].append(f'商品 {symbol} 缺少配置区块')
                else:
                    # 检查启用的数据类型
                    enabled_types = self.symbol_configs.get(symbol, {}).get('enabled_types', '')
                    if not enabled_types:
                        if 'symbol_errors' not in errors:
                            errors['symbol_errors'] = []
                        errors['symbol_errors'].append(f'商品 {symbol} 未启用任何数据类型')

            # 检查模板
            if not self.templates:
                if 'template_errors' not in errors:
                    errors['template_errors'] = []
                errors['template_errors'].append('未找到任何数据类型模板')

            return errors

        except Exception as e:
            logging.error(f"验证多商品配置失败: {str(e)}")
            return {'validation_errors': [str(e)]}

    def generate_single_product_config(self, symbol: str, data_type: str,
                                     output_file: str = None) -> bool:
        """为指定商品和数据类型生成单商品配置文件

        Args:
            symbol: 商品代码
            data_type: 数据类型
            output_file: 输出文件路径，如果为None则自动生成
        """
        try:
            if symbol not in self.symbols:
                logging.error(f"商品 {symbol} 不在启用列表中")
                return False

            # 获取商品项目
            items = self.get_symbol_items(symbol, data_type)
            if not items:
                logging.error(f"商品 {symbol} 的数据类型 {data_type} 没有监控项目")
                return False

            # 创建单商品配置
            single_config = configparser.ConfigParser()

            # 系统配置
            system_config = self.get_system_config()
            single_config.add_section('DDE')
            single_config.set('DDE', 'service', system_config.get('dde_service', 'XQTISC'))
            single_config.set('DDE', 'topic', system_config.get('dde_topic', 'Quote'))
            single_config.set('DDE', 'disconnect_on_exit',
                            system_config.get('disconnect_on_exit', 'false'))

            # 项目配置
            single_config.add_section('Items')
            for i, (item_code, item_name) in enumerate(items.items(), 1):
                single_config.set('Items', f'item{i}_name', item_name)
                single_config.set('Items', f'item{i}_code', item_code)

            # 数据类型配置
            datatype_config = self.get_datatype_config(data_type)
            if datatype_config:
                single_config.add_section('Table')
                for key, value in datatype_config.items():
                    if key.startswith('table_'):
                        table_key = key.replace('table_', '')
                        single_config.set('Table', table_key, value)

            # 自动连接配置
            auto_config = self.get_symbol_auto_connect_config(symbol)
            if auto_config:
                single_config.add_section('AutoConnect')
                for key, value in auto_config.items():
                    single_config.set('AutoConnect', key, value)

            # 通用配置
            for config_type in ['FileOutput', 'AutoShutdown', 'Notifications']:
                common_config = self.get_common_config(config_type)
                if common_config:
                    single_config.add_section(config_type)
                    for key, value in common_config.items():
                        single_config.set(config_type, key, value)

            # 输出路径配置
            output_path = self.get_symbol_output_path(symbol)
            single_config.add_section('OutputPath')
            single_config.set('OutputPath', 'log_file',
                            f'{output_path}/logs/{symbol}_{data_type}.log')
            single_config.set('OutputPath', 'data_file',
                            f'{output_path}/data_{data_type}.csv')
            single_config.set('OutputPath', 'complete_data_file',
                            f'{output_path}/complete_data_{data_type}.csv')

            # 保存配置文件
            if output_file is None:
                output_file = f'config_{symbol}_{data_type}.ini'

            with open(output_file, 'w', encoding='utf-8') as f:
                single_config.write(f)

            logging.info(f"生成单商品配置文件: {output_file}")
            return True

        except Exception as e:
            logging.error(f"生成单商品配置失败: {str(e)}")
            return False
