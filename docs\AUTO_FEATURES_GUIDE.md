# DDE 監控程式自動化功能使用指南

## 功能概述

DDE 監控程式 v6 新增了完整的自動化功能，包括：

### 🔄 自動連線功能
- **立即連線**: 程式啟動後立即連線
- **延遲連線**: 程式啟動後等待指定時間再連線
- **時間表連線**: 按照預設時間表自動連線和斷線
- **自動重連**: 連線中斷時自動嘗試重連

### ⏰ 自動結束功能
- **定時結束**: 在指定時間自動結束程式
- **緩衝機制**: 超過指定時間後的緩衝期設定
- **結束前處理**: 自動保存資料、斷開連線、清理檔案

### 📊 智能管理
- **週末防護**: 防止週末起始連線操作（不中斷進行中的連線）
- **跨日支援**: 支援夜盤等跨日交易時間
- **狀態監控**: 即時顯示自動化狀態
- **完整日誌**: 所有自動操作都有詳細記錄

## 設定檔配置

### 自動連線設定 [AutoConnect]

```ini
[AutoConnect]
# 基本設定
enable_auto_connect = true          # 是否啟用自動連線
auto_connect_mode = schedule        # 連線模式: immediate/delay/schedule
auto_connect_delay = 5.0           # 延遲時間 (秒)

# 時間表設定
schedule_connect_times = 08:45:00-12:00:00;13:00:00-15:15:00
enable_cross_day_schedule = true   # 支援跨日時間表
prevent_weekend_startup = true    # 防止週末起始連線
schedule_end_action = unadvise_only # 結束時間行為: unadvise_only/disconnect

# 重連設定
auto_reconnect_on_disconnect = true # 自動重連
max_reconnect_attempts = 3         # 最大重連次數
reconnect_interval = 15.0          # 重連間隔 (秒)
```

### 自動結束設定 [AutoShutdown]

```ini
[AutoShutdown]
# 基本設定
enable_auto_shutdown = true        # 是否啟用自動結束
shutdown_time = 17:30:00          # 結束時間
shutdown_buffer_seconds = 600     # 緩衝時間 (秒)
shutdown_warning_seconds = 120    # 警告時間 (秒)
force_shutdown = false            # 是否強制結束

# 結束前動作
save_data_before_shutdown = true   # 保存資料
disconnect_before_shutdown = true  # 斷開連線
cleanup_temp_files = true         # 清理暫存檔案
```

### 通知設定 [Notifications]

```ini
[Notifications]
enable_system_notifications = true # 啟用通知
notify_auto_connect = true         # 連線通知
notify_auto_disconnect = true      # 斷線通知
notify_auto_shutdown = true        # 結束通知
```

## Schedule 模式結束時間行為

### schedule_end_action 設定說明

在 `auto_connect_mode = schedule` 模式下，可以控制在結束時間的行為：

#### 可選值
- **`unadvise_only`** (預設值，推薦)
  - 僅取消所有訂閱 (unadvise)
  - 保持 DDE 連線不斷開
  - 適合多時間段交易，下個時間段可快速重新訂閱

- **`disconnect`**
  - 取消所有訂閱 + 斷開 DDE 連線
  - 完全斷開與 DDE 服務的連線
  - 節省系統資源

#### 自動修正機制
- 無效設定值會自動修正為 `unadvise_only`
- 支援大小寫不敏感
- 自動更新配置文件並記錄警告日誌

## 使用場景範例

### 場景 1: 日盤交易 (9:00-15:00)

```ini
[AutoConnect]
enable_auto_connect = true
auto_connect_mode = schedule
schedule_connect_times = 08:45:00-15:15:00
prevent_weekend_startup = true

[AutoShutdown]
enable_auto_shutdown = true
shutdown_time = 16:00:00
shutdown_buffer_seconds = 600
```

**說明**: 
- 8:45 自動連線，15:15 自動斷線
- 16:00 自動結束程式
- 週末不執行任何操作

### 場景 2: 分段交易 (上午盤 + 下午盤) - 僅取消訂閱

```ini
[AutoConnect]
enable_auto_connect = true
auto_connect_mode = schedule
schedule_connect_times = 08:45:00-12:00:00;13:00:00-15:15:00
prevent_weekend_startup = true
schedule_end_action = unadvise_only

[AutoShutdown]
enable_auto_shutdown = true
shutdown_time = 16:00:00
```

**說明**:
- 8:45-12:00 第一段連線
- 12:00 僅取消訂閱，保持連線
- 13:00-15:15 第二段連線（快速重新訂閱）
- 16:00 自動結束

### 場景 2b: 分段交易 - 完整斷線

```ini
[AutoConnect]
enable_auto_connect = true
auto_connect_mode = schedule
schedule_connect_times = 08:45:00-12:00:00;13:00:00-15:15:00
prevent_weekend_startup = true
schedule_end_action = disconnect

[AutoShutdown]
enable_auto_shutdown = true
shutdown_time = 16:00:00
```

**說明**:
- 8:45-12:00 第一段連線
- 12:00 完整斷線（取消訂閱+斷開連線）
- 13:00-15:15 第二段連線（重新建立連線）
- 16:00 自動結束

### 場景 3: 夜盤交易 (跨日)

```ini
[AutoConnect]
enable_auto_connect = true
auto_connect_mode = schedule
schedule_connect_times = 21:00:00-05:00:00
enable_cross_day_schedule = true
prevent_weekend_startup = true

[AutoShutdown]
enable_auto_shutdown = true
shutdown_time = 06:00:00
```

**說明**:
- 21:00 連線，次日 5:00 斷線
- 支援跨日時間計算
- 6:00 自動結束程式

### 場景 4: 測試模式

```ini
[AutoConnect]
enable_auto_connect = true
auto_connect_mode = immediate

[AutoShutdown]
enable_auto_shutdown = false
```

**說明**:
- 程式啟動後立即連線
- 不自動結束，方便測試

### 場景 5: 延遲連線模式

```ini
[AutoConnect]
enable_auto_connect = true
auto_connect_mode = delay
auto_connect_delay = 30.0

[AutoShutdown]
enable_auto_shutdown = true
shutdown_time = 17:00:00
```

**說明**:
- 程式啟動後等待 30 秒再連線
- 17:00 自動結束

## 操作流程

### 自動連線流程
1. **程式啟動** → 載入設定檔
2. **啟動管理器** → 根據模式執行連線
3. **建立連線** → 自動測試項目
4. **自動訂閱** → 開始接收資料
5. **狀態監控** → 持續監控連線狀態

### 自動斷線流程
1. **時間檢查** → 檢查是否到達斷線時間
2. **取消訂閱** → 停止所有資料訂閱
3. **斷開連線** → 關閉 DDE 連線
4. **狀態更新** → 更新介面狀態

### 自動結束流程
1. **時間檢查** → 檢查是否到達結束時間
2. **顯示警告** → 提前警告即將結束
3. **保存資料** → 保存未完成的資料
4. **斷開連線** → 安全斷開所有連線
5. **清理檔案** → 清理暫存檔案
6. **結束程式** → 安全退出程式

## 日誌記錄

所有自動操作都會記錄到日誌檔案中，包括：

### 連線相關日誌
```
[2025-06-16 08:45:00] [INFO] 時間表自動連線: 08:45:00 - 12:00:00
[2025-06-16 08:45:02] [INFO] 自動連線成功，開始測試項目
[2025-06-16 08:45:05] [INFO] 開始自動訂閱 8 個項目
```

### 斷線相關日誌

**unadvise_only 模式:**
```
[2025-06-16 12:00:00] [INFO] 時間表自動取消訂閱 (僅unadvise): 12:00:00
[2025-06-16 12:00:01] [INFO] 已自動取消所有訂閱（保持連線）
```

**disconnect 模式:**
```
[2025-06-16 12:00:00] [INFO] 時間表自動斷線 (disconnect): 12:00:00
[2025-06-16 12:00:01] [INFO] 取消 8 個項目的訂閱
[2025-06-16 12:00:02] [INFO] 自動斷線完成
```

### 結束相關日誌
```
[2025-06-16 17:28:00] [WARNING] 程式將在 120 秒後自動結束
[2025-06-16 17:30:00] [INFO] 開始執行自動結束程式
[2025-06-16 17:30:01] [INFO] 結束前保存資料
[2025-06-16 17:30:02] [INFO] 結束前斷開連線
[2025-06-16 17:30:03] [INFO] 自動結束程式執行完成
```

## 注意事項

### ⚠️ 重要提醒
1. **時間格式**: 必須使用 HH:MM:SS 格式
2. **跨日設定**: 跨日時間表需啟用 `enable_cross_day_schedule`
3. **緩衝時間**: 設定適當的緩衝時間避免意外結束
4. **重連限制**: 重連次數不宜過高，避免對服務器造成壓力
5. **週末處理**: 建議啟用 `prevent_weekend_startup` 防止週末起始連線

### 🔧 故障排除
1. **連線失敗**: 檢查 DDE 服務是否正常運行
2. **時間不準**: 確認系統時間正確
3. **跨日問題**: 確認已啟用跨日支援
4. **重連失敗**: 檢查網路連線和服務狀態
5. **結束異常**: 檢查檔案權限和磁碟空間

### 📋 最佳實踐
1. **測試配置**: 先用短時間間隔測試配置
2. **備份設定**: 定期備份工作正常的設定檔
3. **監控日誌**: 定期檢查日誌檔案
4. **漸進部署**: 從簡單場景開始，逐步增加複雜度
5. **文件記錄**: 記錄每次配置變更的原因和效果

## 技術支援

如遇到問題，請：
1. 檢查日誌檔案中的錯誤訊息
2. 參考 [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
3. 確認設定檔格式正確
4. 測試基本的手動連線功能

---
*文件版本*: v1.0  
*建立日期*: 2025-06-16  
*適用程式版本*: DDE Monitor v6 (自動化版)
