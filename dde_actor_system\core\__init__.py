#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE Actor System - 核心框架模組

提供Actor模型的基礎設施，包括：
- Actor基礎類
- 消息系統
- 事件循環管理
- 性能監控
"""

from .actor_base import ActorBase, ActorState
from .message_system import Message, MessageType, MessageRouter
from .event_loop import EventLoopManager
from .performance import PerformanceMonitor, Profiler

__all__ = [
    'ActorBase',
    'ActorState', 
    'Message',
    'MessageType',
    'MessageRouter',
    'EventLoopManager',
    'PerformanceMonitor',
    'Profiler'
]

__version__ = '1.0.0'
