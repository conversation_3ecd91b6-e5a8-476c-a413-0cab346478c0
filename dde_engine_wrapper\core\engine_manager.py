#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE引擎管理器
负责管理多个DDE数据引擎实例的生命周期
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future
import queue

# 导入DDE数据引擎
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dde_data_engine'))

try:
    from dde_data_engine import DDEDataProcessor, DataProcessorConfig, DataFileHandler
    from dde_data_engine.data_structures import ItemData
except ImportError as e:
    print(f"导入DDE数据引擎失败: {e}")
    # 创建占位符类
    class DDEDataProcessor:
        def __init__(self, *args, **kwargs):
            pass
    class DataProcessorConfig:
        def __init__(self, *args, **kwargs):
            pass
    class DataFileHandler:
        def __init__(self, *args, **kwargs):
            pass
    class ItemData:
        pass

from config_manager import ProductConfig, EngineConfig

class EngineState(Enum):
    """引擎状态枚举"""
    IDLE = "idle"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"
    RESTARTING = "restarting"

@dataclass
class EngineInstance:
    """引擎实例数据类"""
    engine_id: str
    product_symbol: str
    data_type: str
    state: EngineState
    processor: Optional[DDEDataProcessor] = None
    file_handler: Optional[DataFileHandler] = None
    items_data: Optional[Dict[str, ItemData]] = None
    thread: Optional[threading.Thread] = None
    last_heartbeat: float = 0.0
    error_count: int = 0
    restart_count: int = 0
    created_time: float = 0.0
    
    def __post_init__(self):
        if self.created_time == 0.0:
            self.created_time = time.time()

@dataclass
class EngineStats:
    """引擎统计信息"""
    total_engines: int = 0
    running_engines: int = 0
    idle_engines: int = 0
    error_engines: int = 0
    total_restarts: int = 0
    total_errors: int = 0
    uptime: float = 0.0

class DDEEngineManager:
    """DDE引擎管理器
    
    负责管理多个DDE数据引擎实例，提供：
    - 引擎实例的创建和销毁
    - 引擎状态监控和健康检查
    - 引擎故障恢复和重启
    - 资源管理和负载均衡
    """
    
    def __init__(self, engine_config: EngineConfig):
        self.engine_config = engine_config
        self.logger = logging.getLogger(__name__)
        
        # 引擎实例管理
        self.engines: Dict[str, EngineInstance] = {}
        self.engine_lock = threading.RLock()
        
        # 线程池管理
        self.thread_pool = ThreadPoolExecutor(
            max_workers=engine_config.max_engines,
            thread_name_prefix="DDEEngine"
        )
        
        # 监控和统计
        self.stats = EngineStats()
        self.start_time = time.time()
        self.monitoring_active = False
        self.monitor_thread: Optional[threading.Thread] = None
        
        # 任务队列
        self.task_queue = queue.Queue()
        self.shutdown_event = threading.Event()
        
        self.logger.info("DDE引擎管理器初始化完成")
    
    def start_monitoring(self):
        """启动监控线程"""
        try:
            if self.monitoring_active:
                self.logger.warning("监控线程已经在运行")
                return
            
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                name="EngineMonitor",
                daemon=True
            )
            self.monitor_thread.start()
            self.logger.info("引擎监控线程已启动")
            
        except Exception as e:
            self.logger.error(f"启动监控线程失败: {str(e)}")
    
    def stop_monitoring(self):
        """停止监控线程"""
        try:
            self.monitoring_active = False
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5.0)
            self.logger.info("引擎监控线程已停止")
            
        except Exception as e:
            self.logger.error(f"停止监控线程失败: {str(e)}")
    
    def create_engine(self, product_config: ProductConfig, data_type: str) -> Optional[str]:
        """创建引擎实例"""
        try:
            engine_id = f"{product_config.symbol}_{data_type}_{int(time.time())}"
            
            with self.engine_lock:
                # 检查引擎数量限制
                if len(self.engines) >= self.engine_config.max_engines:
                    self.logger.error(f"引擎数量已达上限: {self.engine_config.max_engines}")
                    return None
                
                # 创建引擎实例
                engine_instance = EngineInstance(
                    engine_id=engine_id,
                    product_symbol=product_config.symbol,
                    data_type=data_type,
                    state=EngineState.IDLE
                )
                
                # 初始化引擎组件
                if not self._initialize_engine_components(engine_instance, product_config, data_type):
                    self.logger.error(f"初始化引擎组件失败: {engine_id}")
                    return None
                
                # 添加到引擎字典
                self.engines[engine_id] = engine_instance
                self.logger.info(f"引擎实例创建成功: {engine_id}")
                
                # 更新统计信息
                self._update_stats()
                
                return engine_id
                
        except Exception as e:
            self.logger.error(f"创建引擎实例失败: {str(e)}")
            return None
    
    def start_engine(self, engine_id: str) -> bool:
        """启动引擎实例"""
        try:
            with self.engine_lock:
                if engine_id not in self.engines:
                    self.logger.error(f"引擎实例不存在: {engine_id}")
                    return False
                
                engine = self.engines[engine_id]
                
                if engine.state != EngineState.IDLE:
                    self.logger.warning(f"引擎状态不正确，无法启动: {engine_id}, 当前状态: {engine.state}")
                    return False
                
                # 更新状态
                engine.state = EngineState.STARTING
                
                # 在线程池中启动引擎
                future = self.thread_pool.submit(self._run_engine, engine)
                
                self.logger.info(f"引擎实例启动中: {engine_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"启动引擎实例失败: {str(e)}")
            return False
    
    def stop_engine(self, engine_id: str, force: bool = False) -> bool:
        """停止引擎实例"""
        try:
            with self.engine_lock:
                if engine_id not in self.engines:
                    self.logger.error(f"引擎实例不存在: {engine_id}")
                    return False
                
                engine = self.engines[engine_id]
                
                if engine.state in [EngineState.STOPPED, EngineState.STOPPING]:
                    self.logger.warning(f"引擎已经停止或正在停止: {engine_id}")
                    return True
                
                # 更新状态
                engine.state = EngineState.STOPPING
                
                # 等待引擎线程结束
                if engine.thread and engine.thread.is_alive():
                    if force:
                        # 强制停止（不推荐）
                        self.logger.warning(f"强制停止引擎: {engine_id}")
                    else:
                        # 优雅停止
                        engine.thread.join(timeout=self.engine_config.engine_timeout)
                        if engine.thread.is_alive():
                            self.logger.warning(f"引擎停止超时: {engine_id}")
                
                # 清理资源
                self._cleanup_engine_resources(engine)
                
                # 更新状态
                engine.state = EngineState.STOPPED
                
                self.logger.info(f"引擎实例已停止: {engine_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"停止引擎实例失败: {str(e)}")
            return False
    
    def remove_engine(self, engine_id: str) -> bool:
        """移除引擎实例"""
        try:
            with self.engine_lock:
                if engine_id not in self.engines:
                    self.logger.error(f"引擎实例不存在: {engine_id}")
                    return False
                
                # 先停止引擎
                if not self.stop_engine(engine_id):
                    self.logger.error(f"停止引擎失败，无法移除: {engine_id}")
                    return False
                
                # 从字典中移除
                del self.engines[engine_id]
                
                # 更新统计信息
                self._update_stats()
                
                self.logger.info(f"引擎实例已移除: {engine_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"移除引擎实例失败: {str(e)}")
            return False
    
    def get_engine_state(self, engine_id: str) -> Optional[EngineState]:
        """获取引擎状态"""
        with self.engine_lock:
            if engine_id in self.engines:
                return self.engines[engine_id].state
            return None
    
    def get_all_engines(self) -> Dict[str, EngineInstance]:
        """获取所有引擎实例"""
        with self.engine_lock:
            return self.engines.copy()
    
    def get_stats(self) -> EngineStats:
        """获取统计信息"""
        with self.engine_lock:
            self.stats.uptime = time.time() - self.start_time
            return self.stats

    def shutdown(self):
        """关闭引擎管理器"""
        try:
            self.logger.info("开始关闭引擎管理器")

            # 设置关闭事件
            self.shutdown_event.set()

            # 停止所有引擎
            with self.engine_lock:
                engine_ids = list(self.engines.keys())

            for engine_id in engine_ids:
                self.stop_engine(engine_id)

            # 停止监控线程
            self.stop_monitoring()

            # 关闭线程池
            self.thread_pool.shutdown(wait=True)

            self.logger.info("引擎管理器已关闭")

        except Exception as e:
            self.logger.error(f"关闭引擎管理器失败: {str(e)}")

    def _initialize_engine_components(self, engine: EngineInstance,
                                    product_config: ProductConfig, data_type: str) -> bool:
        """初始化引擎组件"""
        try:
            # 创建配置对象
            config_dict = {
                'enable_time_newline': True,
                'time_newline_interval': 0.8,
                'enable_value_change_check': True,
                'value_change_check_mode': 'single',
                'enable_complete_data_file': True,
                'enable_log_file': True,
                'log_level': self.engine_config.log_level
            }

            processor_config = DataProcessorConfig(config_dict)

            # 创建输出目录
            output_dir = os.path.join(product_config.output_path, data_type)
            os.makedirs(output_dir, exist_ok=True)

            # 创建文件处理器
            engine.file_handler = DataFileHandler(self.logger)

            # 初始化文件路径
            log_file = os.path.join(output_dir, f"{product_config.symbol}_{data_type}.log")
            data_file = os.path.join(output_dir, f"{product_config.symbol}_{data_type}_data.csv")
            complete_data_file = os.path.join(output_dir, f"{product_config.symbol}_{data_type}_complete.csv")

            engine.file_handler.init_file_paths(
                log_file=log_file,
                data_file=data_file,
                complete_data_file=complete_data_file
            )

            # 创建项目数据容器
            engine.items_data = {}

            # 创建数据处理器
            engine.processor = DDEDataProcessor(
                config=processor_config,
                items_data=engine.items_data,
                file_handler=engine.file_handler
            )

            self.logger.info(f"引擎组件初始化完成: {engine.engine_id}")
            return True

        except Exception as e:
            self.logger.error(f"初始化引擎组件失败: {str(e)}")
            return False

    def _run_engine(self, engine: EngineInstance):
        """运行引擎实例"""
        try:
            self.logger.info(f"引擎开始运行: {engine.engine_id}")

            # 更新状态
            engine.state = EngineState.RUNNING
            engine.last_heartbeat = time.time()

            # 引擎主循环
            while not self.shutdown_event.is_set() and engine.state == EngineState.RUNNING:
                try:
                    # 更新心跳
                    engine.last_heartbeat = time.time()

                    # 检查时间间隔
                    if engine.processor:
                        engine.processor.check_time_interval()

                    # 短暂休眠
                    time.sleep(0.1)

                except Exception as e:
                    self.logger.error(f"引擎运行异常: {engine.engine_id}, {str(e)}")
                    engine.error_count += 1

                    if engine.error_count >= 5:
                        self.logger.error(f"引擎错误次数过多，停止运行: {engine.engine_id}")
                        engine.state = EngineState.ERROR
                        break

                    time.sleep(1.0)  # 错误后等待更长时间

            self.logger.info(f"引擎运行结束: {engine.engine_id}")

        except Exception as e:
            self.logger.error(f"引擎运行失败: {engine.engine_id}, {str(e)}")
            engine.state = EngineState.ERROR
            engine.error_count += 1

    def _cleanup_engine_resources(self, engine: EngineInstance):
        """清理引擎资源"""
        try:
            # 清理处理器
            if engine.processor:
                # 这里可以添加处理器的清理逻辑
                pass

            # 清理文件处理器
            if engine.file_handler:
                # 这里可以添加文件处理器的清理逻辑
                pass

            # 清理项目数据
            if engine.items_data:
                engine.items_data.clear()

            self.logger.debug(f"引擎资源清理完成: {engine.engine_id}")

        except Exception as e:
            self.logger.error(f"清理引擎资源失败: {str(e)}")

    def _monitoring_loop(self):
        """监控循环"""
        try:
            self.logger.info("引擎监控循环开始")

            while self.monitoring_active and not self.shutdown_event.is_set():
                try:
                    # 更新统计信息
                    self._update_stats()

                    # 检查引擎健康状态
                    self._check_engine_health()

                    # 处理故障恢复
                    if self.engine_config.restart_on_failure:
                        self._handle_failed_engines()

                    # 等待下一次检查
                    time.sleep(5.0)

                except Exception as e:
                    self.logger.error(f"监控循环异常: {str(e)}")
                    time.sleep(10.0)

            self.logger.info("引擎监控循环结束")

        except Exception as e:
            self.logger.error(f"监控循环失败: {str(e)}")

    def _update_stats(self):
        """更新统计信息"""
        try:
            with self.engine_lock:
                self.stats.total_engines = len(self.engines)
                self.stats.running_engines = sum(1 for e in self.engines.values()
                                               if e.state == EngineState.RUNNING)
                self.stats.idle_engines = sum(1 for e in self.engines.values()
                                            if e.state == EngineState.IDLE)
                self.stats.error_engines = sum(1 for e in self.engines.values()
                                             if e.state == EngineState.ERROR)
                self.stats.total_restarts = sum(e.restart_count for e in self.engines.values())
                self.stats.total_errors = sum(e.error_count for e in self.engines.values())

        except Exception as e:
            self.logger.error(f"更新统计信息失败: {str(e)}")

    def _check_engine_health(self):
        """检查引擎健康状态"""
        try:
            current_time = time.time()
            timeout = self.engine_config.engine_timeout

            with self.engine_lock:
                for engine in self.engines.values():
                    if engine.state == EngineState.RUNNING:
                        # 检查心跳超时
                        if current_time - engine.last_heartbeat > timeout:
                            self.logger.warning(f"引擎心跳超时: {engine.engine_id}")
                            engine.state = EngineState.ERROR
                            engine.error_count += 1

        except Exception as e:
            self.logger.error(f"检查引擎健康状态失败: {str(e)}")

    def _handle_failed_engines(self):
        """处理故障引擎"""
        try:
            with self.engine_lock:
                failed_engines = [e for e in self.engines.values()
                                if e.state == EngineState.ERROR and e.restart_count < 3]

            for engine in failed_engines:
                try:
                    self.logger.info(f"尝试重启故障引擎: {engine.engine_id}")

                    # 更新状态
                    engine.state = EngineState.RESTARTING
                    engine.restart_count += 1

                    # 清理资源
                    self._cleanup_engine_resources(engine)

                    # 重新初始化（这里需要产品配置信息，暂时跳过）
                    # 在实际实现中，需要保存产品配置信息以便重启

                    # 重新启动
                    engine.state = EngineState.IDLE
                    engine.error_count = 0

                    self.logger.info(f"引擎重启完成: {engine.engine_id}")

                except Exception as e:
                    self.logger.error(f"重启引擎失败: {engine.engine_id}, {str(e)}")
                    engine.state = EngineState.ERROR

        except Exception as e:
            self.logger.error(f"处理故障引擎失败: {str(e)}")
