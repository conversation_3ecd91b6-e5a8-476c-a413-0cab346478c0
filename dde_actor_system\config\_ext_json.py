import json

list_symbols = ["FITXN07.TF", "FIMTXN07.TF", "FITMN07.TF", "FITXN08.TF", "FIMTXN08.TF", "FITMN08.TF", "FITX07.TF", "FIMTX07.TF", "FITM07.TF", "FITX08.TF", "FIMTX08.TF", "FITM08.TF"
, "2330.TW", "2317.TW"
, "TX1N07C19500.TF", "TX1N07P19500.TF", "TX1N07C19600.TF", "TX1N07P19600.TF", "TX1N07C19700.TF", "TX1N07P19700.TF", "TX1N07C19800.TF", "TX1N07P19800.TF", "TX1N07C19900.TF", "TX1N07P19900.TF"
, "TX1N07C20000.TF", "TX1N07P20000.TF", "TX1N07C20100.TF", "TX1N07P20100.TF", "TX1N07C20200.TF", "TX1N07P20200.TF", "TX1N07C20300.TF", "TX1N07P20300.TF", "TX1N07C20400.TF", "TX1N07P20400.TF"
, "TX1N07C20500.TF", "TX1N07P20500.TF", "TX1N07C20600.TF", "TX1N07P20600.TF", "TX1N07C20700.TF", "TX1N07P20700.TF", "TX1N07C20800.TF", "TX1N07P20800.TF", "TX1N07C20900.TF", "TX1N07P20900.TF"
, "TX1N07C21000.TF", "TX1N07P21000.TF", "TX1N07C21100.TF", "TX1N07P21100.TF", "TX1N07C21150.TF", "TX1N07P21150.TF", "TX1N07C21200.TF", "TX1N07P21200.TF", "TX1N07C21250.TF", "TX1N07P21250.TF"
, "TX1N07C21300.TF", "TX1N07P21300.TF", "TX1N07C21350.TF", "TX1N07P21350.TF", "TX1N07C21400.TF", "TX1N07P21400.TF", "TX1N07C21450.TF", "TX1N07P21450.TF", "TX1N07C21500.TF", "TX1N07P21500.TF"
, "TX1N07C21550.TF", "TX1N07P21550.TF", "TX1N07C21600.TF", "TX1N07P21600.TF", "TX1N07C21650.TF", "TX1N07P21650.TF", "TX1N07C21700.TF", "TX1N07P21700.TF", "TX1N07C21750.TF", "TX1N07P21750.TF"
, "TX1N07C21800.TF", "TX1N07P21800.TF", "TX1N07C21850.TF", "TX1N07P21850.TF", "TX1N07C21900.TF", "TX1N07P21900.TF", "TX1N07C21950.TF", "TX1N07P21950.TF", "TX1N07C22000.TF", "TX1N07P22000.TF"
, "TX1N07C22050.TF", "TX1N07P22050.TF", "TX1N07C22100.TF", "TX1N07P22100.TF", "TX1N07C22150.TF", "TX1N07P22150.TF", "TX1N07C22200.TF", "TX1N07P22200.TF", "TX1N07C22250.TF", "TX1N07P22250.TF"
, "TX1N07C22300.TF", "TX1N07P22300.TF", "TX1N07C22350.TF", "TX1N07P22350.TF", "TX1N07C22400.TF", "TX1N07P22400.TF", "TX1N07C22450.TF", "TX1N07P22450.TF", "TX1N07C22500.TF", "TX1N07P22500.TF"
, "TX1N07C22550.TF", "TX1N07P22550.TF", "TX1N07C22600.TF", "TX1N07P22600.TF", "TX1N07C22650.TF", "TX1N07P22650.TF", "TX1N07C22700.TF", "TX1N07P22700.TF", "TX1N07C22750.TF", "TX1N07P22750.TF"
, "TX1N07C22800.TF", "TX1N07P22800.TF", "TX1N07C22850.TF", "TX1N07P22850.TF", "TX1N07C22900.TF", "TX1N07P22900.TF", "TX1N07C22950.TF", "TX1N07P22950.TF", "TX1N07C23000.TF", "TX1N07P23000.TF"
, "TX1N07C23050.TF", "TX1N07P23050.TF", "TX1N07C23100.TF", "TX1N07P23100.TF", "TX1N07C23150.TF", "TX1N07P23150.TF", "TX1N07C23200.TF", "TX1N07P23200.TF", "TX1N07C23250.TF", "TX1N07P23250.TF"
, "TX1N07C23300.TF", "TX1N07P23300.TF", "TX1N07C23400.TF", "TX1N07P23400.TF", "TX1N07C23500.TF", "TX1N07P23500.TF", "TX1N07C23600.TF", "TX1N07P23600.TF", "TX1N07C23700.TF", "TX1N07P23700.TF"
, "TX1N07C23800.TF", "TX1N07P23800.TF", "TX1N07C23900.TF", "TX1N07P23900.TF", "TX1N07C24000.TF", "TX1N07P24000.TF", "TX1N07C24100.TF", "TX1N07P24100.TF", "TX1N07C24200.TF", "TX1N07P24200.TF"
, "TX1N07C24300.TF", "TX1N07P24300.TF", "TX1N07C24400.TF", "TX1N07P24400.TF", "TX1N07C24500.TF", "TX1N07P24500.TF", "TX1N07C24600.TF", "TX1N07P24600.TF", "TX1N07C24700.TF", "TX1N07P24700.TF"
, "TX1N07C24800.TF", "TX1N07P24800.TF", "TX1N07C24900.TF", "TX1N07P24900.TF", "TX2N07C19900.TF", "TX2N07P19900.TF", "TX2N07C20000.TF", "TX2N07P20000.TF", "TX2N07C20100.TF", "TX2N07P20100.TF"
, "TX2N07C20200.TF", "TX2N07P20200.TF", "TX2N07C20300.TF", "TX2N07P20300.TF", "TX2N07C20400.TF", "TX2N07P20400.TF", "TX2N07C20500.TF", "TX2N07P20500.TF", "TX2N07C20600.TF", "TX2N07P20600.TF"
, "TX2N07C20700.TF", "TX2N07P20700.TF", "TX2N07C20800.TF", "TX2N07P20800.TF", "TX2N07C20900.TF", "TX2N07P20900.TF", "TX2N07C21000.TF", "TX2N07P21000.TF", "TX2N07C21100.TF", "TX2N07P21100.TF"
, "TX2N07C21200.TF", "TX2N07P21200.TF", "TX2N07C21300.TF", "TX2N07P21300.TF", "TX2N07C21400.TF", "TX2N07P21400.TF", "TX2N07C21500.TF", "TX2N07P21500.TF", "TX2N07C21550.TF", "TX2N07P21550.TF"
, "TX2N07C21600.TF", "TX2N07P21600.TF", "TX2N07C21650.TF", "TX2N07P21650.TF", "TX2N07C21700.TF", "TX2N07P21700.TF", "TX2N07C21750.TF", "TX2N07P21750.TF", "TX2N07C21800.TF", "TX2N07P21800.TF"
, "TX2N07C21850.TF", "TX2N07P21850.TF", "TX2N07C21900.TF", "TX2N07P21900.TF", "TX2N07C21950.TF", "TX2N07P21950.TF", "TX2N07C22000.TF", "TX2N07P22000.TF", "TX2N07C22050.TF", "TX2N07P22050.TF"
, "TX2N07C22100.TF", "TX2N07P22100.TF", "TX2N07C22150.TF", "TX2N07P22150.TF", "TX2N07C22200.TF", "TX2N07P22200.TF", "TX2N07C22250.TF", "TX2N07P22250.TF", "TX2N07C22300.TF", "TX2N07P22300.TF"
, "TX2N07C22350.TF", "TX2N07P22350.TF", "TX2N07C22400.TF", "TX2N07P22400.TF", "TX2N07C22450.TF", "TX2N07P22450.TF", "TX2N07C22500.TF", "TX2N07P22500.TF", "TX2N07C22550.TF", "TX2N07P22550.TF"
, "TX2N07C22600.TF", "TX2N07P22600.TF", "TX2N07C22650.TF", "TX2N07P22650.TF", "TX2N07C22700.TF", "TX2N07P22700.TF", "TX2N07C22750.TF", "TX2N07P22750.TF", "TX2N07C22800.TF", "TX2N07P22800.TF"
, "TX2N07C22850.TF", "TX2N07P22850.TF", "TX2N07C22900.TF", "TX2N07P22900.TF", "TX2N07C22950.TF", "TX2N07P22950.TF", "TX2N07C23000.TF", "TX2N07P23000.TF", "TX2N07C23050.TF", "TX2N07P23050.TF"
, "TX2N07C23100.TF", "TX2N07P23100.TF", "TX2N07C23150.TF", "TX2N07P23150.TF", "TX2N07C23200.TF", "TX2N07P23200.TF", "TX2N07C23250.TF", "TX2N07P23250.TF", "TX2N07C23300.TF", "TX2N07P23300.TF"
, "TX2N07C23400.TF", "TX2N07P23400.TF", "TX2N07C23500.TF", "TX2N07P23500.TF", "TX2N07C23600.TF", "TX2N07P23600.TF", "TX2N07C23700.TF", "TX2N07P23700.TF", "TX2N07C23800.TF", "TX2N07P23800.TF"
, "TX2N07C23900.TF", "TX2N07P23900.TF", "TX2N07C24000.TF", "TX2N07P24000.TF", "TX2N07C24100.TF", "TX2N07P24100.TF", "TX2N07C24200.TF", "TX2N07P24200.TF", "TX2N07C24300.TF", "TX2N07P24300.TF"
, "TX2N07C24400.TF", "TX2N07P24400.TF", "TX2N07C24500.TF", "TX2N07P24500.TF", "TX2N07C24600.TF", "TX2N07P24600.TF", "TX2N07C24700.TF", "TX2N07P24700.TF", "TX2N07C24800.TF", "TX2N07P24800.TF"
, "TX2N07C24900.TF", "TX2N07P24900.TF", "TXON03C17200.TF", "TXON03P17200.TF", "TXON03C17400.TF", "TXON03P17400.TF", "TXON03C17600.TF", "TXON03P17600.TF", "TXON03C17800.TF", "TXON03P17800.TF"
, "TXON03C18000.TF", "TXON03P18000.TF", "TXON03C18200.TF", "TXON03P18200.TF", "TXON03C18400.TF", "TXON03P18400.TF", "TXON03C18600.TF", "TXON03P18600.TF", "TXON03C18800.TF", "TXON03P18800.TF"
, "TXON03C19000.TF", "TXON03P19000.TF", "TXON03C19200.TF", "TXON03P19200.TF", "TXON03C19400.TF", "TXON03P19400.TF", "TXON03C19600.TF", "TXON03P19600.TF", "TXON03C19800.TF", "TXON03P19800.TF"
, "TXON03C20000.TF", "TXON03P20000.TF", "TXON03C20200.TF", "TXON03P20200.TF", "TXON03C20400.TF", "TXON03P20400.TF", "TXON03C20600.TF", "TXON03P20600.TF", "TXON03C20800.TF", "TXON03P20800.TF"
, "TXON03C21000.TF", "TXON03P21000.TF", "TXON03C21200.TF", "TXON03P21200.TF", "TXON03C21400.TF", "TXON03P21400.TF", "TXON03C21600.TF", "TXON03P21600.TF", "TXON03C21800.TF", "TXON03P21800.TF"
, "TXON03C22000.TF", "TXON03P22000.TF", "TXON03C22200.TF", "TXON03P22200.TF", "TXON03C22400.TF", "TXON03P22400.TF", "TXON03C22600.TF", "TXON03P22600.TF", "TXON03C22800.TF", "TXON03P22800.TF"
, "TXON03C23000.TF", "TXON03P23000.TF", "TXON03C23200.TF", "TXON03P23200.TF", "TXON03C23400.TF", "TXON03P23400.TF", "TXON03C23600.TF", "TXON03P23600.TF", "TXON03C23800.TF", "TXON03P23800.TF"
, "TXON03C24000.TF", "TXON03P24000.TF", "TXON03C24200.TF", "TXON03P24200.TF", "TXON03C24400.TF", "TXON03P24400.TF", "TXON03C24600.TF", "TXON03P24600.TF", "TXON03C24800.TF", "TXON03P24800.TF"
, "TXON03C25000.TF", "TXON03P25000.TF", "TXON03C25200.TF", "TXON03P25200.TF", "TXON03C25400.TF", "TXON03P25400.TF", "TXON03C25600.TF", "TXON03P25600.TF", "TXON03C25800.TF", "TXON03P25800.TF"
, "TXON03C26000.TF", "TXON03P26000.TF", "TXON03C26200.TF", "TXON03P26200.TF", "TXON03C26400.TF", "TXON03P26400.TF", "TXON03C26600.TF", "TXON03P26600.TF", "TXON03C26800.TF", "TXON03P26800.TF"
, "TXON03C27000.TF", "TXON03P27000.TF", "TXON03C27200.TF", "TXON03P27200.TF", "TXON07C15900.TF", "TXON07P15900.TF", "TXON07C16000.TF", "TXON07P16000.TF", "TXON07C16100.TF", "TXON07P16100.TF"
, "TXON07C16200.TF", "TXON07P16200.TF", "TXON07C16300.TF", "TXON07P16300.TF", "TXON07C16400.TF", "TXON07P16400.TF", "TXON07C16500.TF", "TXON07P16500.TF", "TXON07C16600.TF", "TXON07P16600.TF"
, "TXON07C16700.TF", "TXON07P16700.TF", "TXON07C16800.TF", "TXON07P16800.TF", "TXON07C16900.TF", "TXON07P16900.TF", "TXON07C17000.TF", "TXON07P17000.TF", "TXON07C17100.TF", "TXON07P17100.TF"
, "TXON07C17200.TF", "TXON07P17200.TF", "TXON07C17300.TF", "TXON07P17300.TF", "TXON07C17400.TF", "TXON07P17400.TF", "TXON07C17500.TF", "TXON07P17500.TF", "TXON07C17600.TF", "TXON07P17600.TF"
, "TXON07C17700.TF", "TXON07P17700.TF", "TXON07C17800.TF", "TXON07P17800.TF", "TXON07C17900.TF", "TXON07P17900.TF", "TXON07C18000.TF", "TXON07P18000.TF", "TXON07C18100.TF", "TXON07P18100.TF"
, "TXON07C18200.TF", "TXON07P18200.TF", "TXON07C18300.TF", "TXON07P18300.TF", "TXON07C18400.TF", "TXON07P18400.TF", "TXON07C18500.TF", "TXON07P18500.TF", "TXON07C18600.TF", "TXON07P18600.TF"
, "TXON07C18700.TF", "TXON07P18700.TF", "TXON07C18800.TF", "TXON07P18800.TF", "TXON07C18900.TF", "TXON07P18900.TF", "TXON07C19000.TF", "TXON07P19000.TF", "TXON07C19100.TF", "TXON07P19100.TF"
, "TXON07C19200.TF", "TXON07P19200.TF", "TXON07C19300.TF", "TXON07P19300.TF", "TXON07C19400.TF", "TXON07P19400.TF", "TXON07C19500.TF", "TXON07P19500.TF", "TXON07C19600.TF", "TXON07P19600.TF"
, "TXON07C19700.TF", "TXON07P19700.TF", "TXON07C19800.TF", "TXON07P19800.TF", "TXON07C19900.TF", "TXON07P19900.TF", "TXON07C20000.TF", "TXON07P20000.TF", "TXON07C20100.TF", "TXON07P20100.TF"
, "TXON07C20200.TF", "TXON07P20200.TF", "TXON07C20300.TF", "TXON07P20300.TF", "TXON07C20400.TF", "TXON07P20400.TF", "TXON07C20500.TF", "TXON07P20500.TF", "TXON07C20600.TF", "TXON07P20600.TF"
, "TXON07C20700.TF", "TXON07P20700.TF", "TXON07C20800.TF", "TXON07P20800.TF", "TXON07C20900.TF", "TXON07P20900.TF", "TXON07C21000.TF", "TXON07P21000.TF", "TXON07C21100.TF", "TXON07P21100.TF"
, "TXON07C21200.TF", "TXON07P21200.TF", "TXON07C21300.TF", "TXON07P21300.TF", "TXON07C21400.TF", "TXON07P21400.TF", "TXON07C21500.TF", "TXON07P21500.TF", "TXON07C21600.TF", "TXON07P21600.TF"
, "TXON07C21700.TF", "TXON07P21700.TF", "TXON07C21800.TF", "TXON07P21800.TF", "TXON07C21900.TF", "TXON07P21900.TF", "TXON07C21950.TF", "TXON07P21950.TF", "TXON07C22000.TF", "TXON07P22000.TF"
, "TXON07C22050.TF", "TXON07P22050.TF", "TXON07C22100.TF", "TXON07P22100.TF", "TXON07C22150.TF", "TXON07P22150.TF", "TXON07C22200.TF", "TXON07P22200.TF", "TXON07C22250.TF", "TXON07P22250.TF"
, "TXON07C22300.TF", "TXON07P22300.TF", "TXON07C22350.TF", "TXON07P22350.TF", "TXON07C22400.TF", "TXON07P22400.TF", "TXON07C22450.TF", "TXON07P22450.TF", "TXON07C22500.TF", "TXON07P22500.TF"
, "TXON07C22550.TF", "TXON07P22550.TF", "TXON07C22600.TF", "TXON07P22600.TF", "TXON07C22650.TF", "TXON07P22650.TF", "TXON07C22700.TF", "TXON07P22700.TF", "TXON07C22750.TF", "TXON07P22750.TF"
, "TXON07C22800.TF", "TXON07P22800.TF", "TXON07C22850.TF", "TXON07P22850.TF", "TXON07C22900.TF", "TXON07P22900.TF", "TXON07C22950.TF", "TXON07P22950.TF", "TXON07C23000.TF", "TXON07P23000.TF"
, "TXON07C23050.TF", "TXON07P23050.TF", "TXON07C23100.TF", "TXON07P23100.TF", "TXON07C23150.TF", "TXON07P23150.TF", "TXON07C23200.TF", "TXON07P23200.TF", "TXON07C23300.TF", "TXON07P23300.TF"
, "TXON07C23400.TF", "TXON07P23400.TF", "TXON07C23500.TF", "TXON07P23500.TF", "TXON07C23600.TF", "TXON07P23600.TF", "TXON07C23700.TF", "TXON07P23700.TF", "TXON07C23800.TF", "TXON07P23800.TF"
, "TXON07C23900.TF", "TXON07P23900.TF", "TXON07C24000.TF", "TXON07P24000.TF", "TXON07C24100.TF", "TXON07P24100.TF", "TXON07C24200.TF", "TXON07P24200.TF", "TXON07C24300.TF", "TXON07P24300.TF"
, "TXON07C24400.TF", "TXON07P24400.TF", "TXON07C24500.TF", "TXON07P24500.TF", "TXON07C24600.TF", "TXON07P24600.TF", "TXON07C24700.TF", "TXON07P24700.TF", "TXON07C24800.TF", "TXON07P24800.TF"
, "TXON07C24900.TF", "TXON07P24900.TF", "TXON07C25000.TF", "TXON07P25000.TF", "TXON07C25100.TF", "TXON07P25100.TF", "TXON07C25200.TF", "TXON07P25200.TF", "TXON07C25300.TF", "TXON07P25300.TF"
, "TXON07C25400.TF", "TXON07P25400.TF", "TXON07C25500.TF", "TXON07P25500.TF", "TXON07C25600.TF", "TXON07P25600.TF", "TXON07C25700.TF", "TXON07P25700.TF", "TXON07C25800.TF", "TXON07P25800.TF"
, "TXON07C25900.TF", "TXON07P25900.TF", "TXON07C26000.TF", "TXON07P26000.TF", "TXON08C17800.TF", "TXON08P17800.TF", "TXON08C17900.TF", "TXON08P17900.TF", "TXON08C18000.TF", "TXON08P18000.TF"
, "TXON08C18100.TF", "TXON08P18100.TF", "TXON08C18200.TF", "TXON08P18200.TF", "TXON08C18300.TF", "TXON08P18300.TF", "TXON08C18400.TF", "TXON08P18400.TF", "TXON08C18500.TF", "TXON08P18500.TF"
, "TXON08C18600.TF", "TXON08P18600.TF", "TXON08C18700.TF", "TXON08P18700.TF", "TXON08C18800.TF", "TXON08P18800.TF", "TXON08C18900.TF", "TXON08P18900.TF", "TXON08C19000.TF", "TXON08P19000.TF"
, "TXON08C19100.TF", "TXON08P19100.TF", "TXON08C19200.TF", "TXON08P19200.TF", "TXON08C19300.TF", "TXON08P19300.TF", "TXON08C19400.TF", "TXON08P19400.TF", "TXON08C19500.TF", "TXON08P19500.TF"
, "TXON08C19600.TF", "TXON08P19600.TF", "TXON08C19700.TF", "TXON08P19700.TF", "TXON08C19800.TF", "TXON08P19800.TF", "TXON08C19900.TF", "TXON08P19900.TF", "TXON08C20000.TF", "TXON08P20000.TF"
, "TXON08C20100.TF", "TXON08P20100.TF", "TXON08C20200.TF", "TXON08P20200.TF", "TXON08C20300.TF", "TXON08P20300.TF", "TXON08C20400.TF", "TXON08P20400.TF", "TXON08C20500.TF", "TXON08P20500.TF"
, "TXON08C20600.TF", "TXON08P20600.TF", "TXON08C20700.TF", "TXON08P20700.TF", "TXON08C20800.TF", "TXON08P20800.TF", "TXON08C20900.TF", "TXON08P20900.TF", "TXON08C21000.TF", "TXON08P21000.TF"
, "TXON08C21100.TF", "TXON08P21100.TF", "TXON08C21200.TF", "TXON08P21200.TF", "TXON08C21300.TF", "TXON08P21300.TF", "TXON08C21400.TF", "TXON08P21400.TF", "TXON08C21500.TF", "TXON08P21500.TF"
, "TXON08C21600.TF", "TXON08P21600.TF", "TXON08C21700.TF", "TXON08P21700.TF", "TXON08C21800.TF", "TXON08P21800.TF", "TXON08C21900.TF", "TXON08P21900.TF", "TXON08C22000.TF", "TXON08P22000.TF"
, "TXON08C22100.TF", "TXON08P22100.TF", "TXON08C22200.TF", "TXON08P22200.TF", "TXON08C22300.TF", "TXON08P22300.TF", "TXON08C22400.TF", "TXON08P22400.TF", "TXON08C22500.TF", "TXON08P22500.TF"
, "TXON08C22600.TF", "TXON08P22600.TF", "TXON08C22700.TF", "TXON08P22700.TF", "TXON08C22800.TF", "TXON08P22800.TF", "TXON08C22900.TF", "TXON08P22900.TF", "TXON08C23000.TF", "TXON08P23000.TF"
, "TXON08C23100.TF", "TXON08P23100.TF", "TXON08C23200.TF", "TXON08P23200.TF", "TXON08C23300.TF", "TXON08P23300.TF", "TXON08C23400.TF", "TXON08P23400.TF", "TXON08C23500.TF", "TXON08P23500.TF"
, "TXON08C23600.TF", "TXON08P23600.TF", "TXON08C23700.TF", "TXON08P23700.TF", "TXON08C23800.TF", "TXON08P23800.TF", "TXON08C23900.TF", "TXON08P23900.TF", "TXON08C24000.TF", "TXON08P24000.TF"
, "TXON08C24100.TF", "TXON08P24100.TF", "TXON08C24200.TF", "TXON08P24200.TF", "TXON08C24300.TF", "TXON08P24300.TF", "TXON08C24400.TF", "TXON08P24400.TF", "TXON08C24500.TF", "TXON08P24500.TF"
, "TXON08C24600.TF", "TXON08P24600.TF", "TXON08C24700.TF", "TXON08P24700.TF", "TXON08C24800.TF", "TXON08P24800.TF", "TXON08C24900.TF", "TXON08P24900.TF", "TXON08C25000.TF", "TXON08P25000.TF"
, "TXON08C25100.TF", "TXON08P25100.TF", "TXON08C25200.TF", "TXON08P25200.TF", "TXON08C25300.TF", "TXON08P25300.TF", "TXON08C25400.TF", "TXON08P25400.TF", "TXON08C25500.TF", "TXON08P25500.TF"
, "TXON08C25600.TF", "TXON08P25600.TF", "TXON08C25700.TF", "TXON08P25700.TF", "TXON08C25800.TF", "TXON08P25800.TF", "TXON08C25900.TF", "TXON08P25900.TF", "TXON08C26000.TF", "TXON08P26000.TF"
, "TXON09C13800.TF", "TXON09P13800.TF", "TXON09C14000.TF", "TXON09P14000.TF", "TXON09C14200.TF", "TXON09P14200.TF", "TXON09C14400.TF", "TXON09P14400.TF", "TXON09C14600.TF", "TXON09P14600.TF"
, "TXON09C14800.TF", "TXON09P14800.TF", "TXON09C15000.TF", "TXON09P15000.TF", "TXON09C15200.TF", "TXON09P15200.TF", "TXON09C15400.TF", "TXON09P15400.TF", "TXON09C15600.TF", "TXON09P15600.TF"
, "TXON09C15800.TF", "TXON09P15800.TF", "TXON09C16000.TF", "TXON09P16000.TF", "TXON09C16200.TF", "TXON09P16200.TF", "TXON09C16400.TF", "TXON09P16400.TF", "TXON09C16600.TF", "TXON09P16600.TF"
, "TXON09C16800.TF", "TXON09P16800.TF", "TXON09C17000.TF", "TXON09P17000.TF", "TXON09C17200.TF", "TXON09P17200.TF", "TXON09C17400.TF", "TXON09P17400.TF", "TXON09C17600.TF", "TXON09P17600.TF"
, "TXON09C17800.TF", "TXON09P17800.TF", "TXON09C18000.TF", "TXON09P18000.TF", "TXON09C18200.TF", "TXON09P18200.TF", "TXON09C18400.TF", "TXON09P18400.TF", "TXON09C18500.TF", "TXON09P18500.TF"
, "TXON09C18600.TF", "TXON09P18600.TF", "TXON09C18700.TF", "TXON09P18700.TF", "TXON09C18800.TF", "TXON09P18800.TF", "TXON09C18900.TF", "TXON09P18900.TF", "TXON09C19000.TF", "TXON09P19000.TF"
, "TXON09C19100.TF", "TXON09P19100.TF", "TXON09C19200.TF", "TXON09P19200.TF", "TXON09C19300.TF", "TXON09P19300.TF", "TXON09C19400.TF", "TXON09P19400.TF", "TXON09C19500.TF", "TXON09P19500.TF"
, "TXON09C19600.TF", "TXON09P19600.TF", "TXON09C19700.TF", "TXON09P19700.TF", "TXON09C19800.TF", "TXON09P19800.TF", "TXON09C19900.TF", "TXON09P19900.TF", "TXON09C20000.TF", "TXON09P20000.TF"
, "TXON09C20100.TF", "TXON09P20100.TF", "TXON09C20200.TF", "TXON09P20200.TF", "TXON09C20300.TF", "TXON09P20300.TF", "TXON09C20400.TF", "TXON09P20400.TF", "TXON09C20500.TF", "TXON09P20500.TF"
, "TXON09C20600.TF", "TXON09P20600.TF", "TXON09C20700.TF", "TXON09P20700.TF", "TXON09C20800.TF", "TXON09P20800.TF", "TXON09C20900.TF", "TXON09P20900.TF", "TXON09C21000.TF", "TXON09P21000.TF"
, "TXON09C21100.TF", "TXON09P21100.TF", "TXON09C21200.TF", "TXON09P21200.TF", "TXON09C21300.TF", "TXON09P21300.TF", "TXON09C21400.TF", "TXON09P21400.TF", "TXON09C21500.TF", "TXON09P21500.TF"
, "TXON09C21600.TF", "TXON09P21600.TF", "TXON09C21700.TF", "TXON09P21700.TF", "TXON09C21800.TF", "TXON09P21800.TF", "TXON09C21900.TF", "TXON09P21900.TF", "TXON09C22000.TF", "TXON09P22000.TF"
, "TXON09C22100.TF", "TXON09P22100.TF", "TXON09C22200.TF", "TXON09P22200.TF", "TXON09C22300.TF", "TXON09P22300.TF", "TXON09C22400.TF", "TXON09P22400.TF", "TXON09C22500.TF", "TXON09P22500.TF"
, "TXON09C22600.TF", "TXON09P22600.TF", "TXON09C22700.TF", "TXON09P22700.TF", "TXON09C22800.TF", "TXON09P22800.TF", "TXON09C22900.TF", "TXON09P22900.TF", "TXON09C23000.TF", "TXON09P23000.TF"
, "TXON09C23100.TF", "TXON09P23100.TF", "TXON09C23200.TF", "TXON09P23200.TF", "TXON09C23300.TF", "TXON09P23300.TF", "TXON09C23400.TF", "TXON09P23400.TF", "TXON09C23500.TF", "TXON09P23500.TF"
, "TXON09C23600.TF", "TXON09P23600.TF", "TXON09C23700.TF", "TXON09P23700.TF", "TXON09C23800.TF", "TXON09P23800.TF", "TXON09C23900.TF", "TXON09P23900.TF", "TXON09C24000.TF", "TXON09P24000.TF"
, "TXON09C24100.TF", "TXON09P24100.TF", "TXON09C24200.TF", "TXON09P24200.TF", "TXON09C24300.TF", "TXON09P24300.TF", "TXON09C24400.TF", "TXON09P24400.TF", "TXON09C24500.TF", "TXON09P24500.TF"
, "TXON09C24600.TF", "TXON09P24600.TF", "TXON09C24700.TF", "TXON09P24700.TF", "TXON09C24800.TF", "TXON09P24800.TF", "TXON09C24900.TF", "TXON09P24900.TF", "TXON09C25000.TF", "TXON09P25000.TF"
, "TXON09C25100.TF", "TXON09P25100.TF", "TXON09C25200.TF", "TXON09P25200.TF", "TXON09C25300.TF", "TXON09P25300.TF", "TXON09C25400.TF", "TXON09P25400.TF", "TXON09C25500.TF", "TXON09P25500.TF"
, "TXON09C25600.TF", "TXON09P25600.TF", "TXON09C25700.TF", "TXON09P25700.TF", "TXON09C25800.TF", "TXON09P25800.TF", "TXON09C25900.TF", "TXON09P25900.TF", "TXON09C26000.TF", "TXON09P26000.TF"
, "TXON09C26200.TF", "TXON09P26200.TF", "TXON09C26400.TF", "TXON09P26400.TF", "TXON09C26600.TF", "TXON09P26600.TF", "TXON09C26800.TF", "TXON09P26800.TF", "TXON09C27000.TF", "TXON09P27000.TF"
]

list_itemCode = [
    "Time", "TradingDate", "Open", "High", "Low", "Price", "TotalVolume", "Volume",
    "NWTotalBidContract", "NWTotalAskContract", "NWTotalBidSize", "NWTotalAskSize",
    "InSize", "OutSize", "TotalBidMatchTx", "TotalAskMatchTx",
    "BestBid1", "BestBid2", "BestBid3", "BestBid4", "BestBid5",
    "BestAsk1", "BestAsk2", "BestAsk3", "BestAsk4", "BestAsk5",
    "BestBidSize1", "BestBidSize2", "BestBidSize3", "BestBidSize4", "BestBidSize5",
    "BestAskSize1", "BestAskSize2", "BestAskSize3", "BestAskSize4", "BestAskSize5",
    "Name", "WContractDate", "SettlePrice", "UpLimit", "DownLimit", "OI",
    "TradingDate", "WRemainDate", "PreClose", "PreTotalVolume"
]

output = []

for full_symbol in list_symbols:
    parts = full_symbol.split('.')
    if len(parts) == 2:
        base_symbol, suffix = parts
        entry = {
            "symbol": base_symbol,
            "service": "XQTISC",
            "topic": "Quote",
            "items": [f"{base_symbol}.{suffix}-{code}" for code in list_itemCode],
            "enabled": True
        }
        output.append(entry)
    else:
        print(f"⚠️ 無法解析 symbol: {full_symbol}")

with open("output.json", "w", encoding="utf-8") as f:
    json.dump(output, f, indent=2, ensure_ascii=False)

print("✅ output.json 已完成，已根據每個 symbol 動態調整 item 格式。")
