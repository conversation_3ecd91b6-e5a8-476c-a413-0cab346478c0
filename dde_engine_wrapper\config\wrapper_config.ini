# DDE引擎包装器配置文件
# 多商品DDE监控系统配置

[Global]
app_name = DDE引擎包装器
version = 1.0.0
log_level = INFO
log_file = ./logs/wrapper.log
data_output_base = ./outputs
config_backup_enabled = true
performance_monitoring = true

[Engine]
# 引擎配置
max_engines = 20
engine_timeout = 30.0
restart_on_failure = true
performance_monitoring = true
log_level = INFO

[Products]
# 启用的商品列表
enabled_products = FITXN07,FITXN08,FITMN07,FITMN08

# 商品配置区块
[FITXN07]
data_types = tick,order,level2,daily
dde_service = SKCOM
dde_topic = SKCOM
output_path = ./outputs/FITXN07
auto_connect = true
auto_connect_times = 08:45:00,15:00:00

[FITXN08]
data_types = tick,order,level2,daily
dde_service = SKCOM
dde_topic = SKCOM
output_path = ./outputs/FITXN08
auto_connect = true
auto_connect_times = 08:45:00,15:00:00

[FITMN07]
data_types = tick,order,level2,daily
dde_service = SKCOM
dde_topic = SKCOM
output_path = ./outputs/FITMN07
auto_connect = true
auto_connect_times = 08:45:00,15:00:00

[FITMN08]
data_types = tick,order,level2,daily
dde_service = SKCOM
dde_topic = SKCOM
output_path = ./outputs/FITMN08
auto_connect = true
auto_connect_times = 08:45:00,15:00:00

# 配置模板
[Template_futures]
dde_service = SKCOM
dde_topic = SKCOM
output_path = ./outputs/{symbol}
data_types = tick,order,level2,daily
auto_connect = true
auto_connect_times = 08:45:00,15:00:00

[Template_stocks]
dde_service = SKCOM
dde_topic = SKCOM
output_path = ./outputs/{symbol}
data_types = tick,order
auto_connect = true
auto_connect_times = 09:00:00

# 数据类型配置
[DataType_tick]
description = 即时成交数据
items = Price,Volume,Time

[DataType_order]
description = 委托簿数据
items = BidPrice1,BidQty1,AskPrice1,AskQty1

[DataType_level2]
description = 五档报价数据
items = BidPrice1,BidPrice2,BidPrice3,BidPrice4,BidPrice5,AskPrice1,AskPrice2,AskPrice3,AskPrice4,AskPrice5

[DataType_daily]
description = 日线数据
items = Open,High,Low,Close,Volume
