[{"symbol": "FITXN07", "service": "XQTISC", "topic": "Quote", "items": ["FITXN07.TF-Time", "FITXN07.TF-TradingDate", "FITXN07.TF-Open", "FITXN07.TF-High", "FITXN07.TF-Low", "FITXN07.TF-Price", "FITXN07.TF-TotalVolume", "FITXN07.TF-Volume", "FITXN07.TF-NWTotalBidContract", "FITXN07.TF-NWTotalAskContract", "FITXN07.TF-NWTotalBidSize", "FITXN07.TF-NWTotalAskSize", "FITXN07.TF-InSize", "FITXN07.TF-OutSize", "FITXN07.TF-TotalBidMatchTx", "FITXN07.TF-TotalAskMatchTx", "FITXN07.TF-BestBid1", "FITXN07.TF-BestBid2", "FITXN07.TF-BestBid3", "FITXN07.TF-BestBid4", "FITXN07.TF-BestBid5", "FITXN07.TF-BestAsk1", "FITXN07.TF-BestAsk2", "FITXN07.TF-BestAsk3", "FITXN07.TF-BestAsk4", "FITXN07.TF-BestAsk5", "FITXN07.TF-BestBidSize1", "FITXN07.TF-BestBidSize2", "FITXN07.TF-BestBidSize3", "FITXN07.TF-BestBidSize4", "FITXN07.TF-BestBidSize5", "FITXN07.TF-BestAskSize1", "FITXN07.TF-BestAskSize2", "FITXN07.TF-BestAskSize3", "FITXN07.TF-BestAskSize4", "FITXN07.TF-BestAskSize5", "FITXN07.TF-Name", "FITXN07.TF-WContractDate", "FITXN07.TF-SettlePrice", "FITXN07.TF-UpLimit", "FITXN07.TF-DownLimit", "FITXN07.TF-OI", "FITXN07.TF-TradingDate", "FITXN07.TF-WRemainDate", "FITXN07.TF-PreClose", "FITXN07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FIMTXN07", "service": "XQTISC", "topic": "Quote", "items": ["FIMTXN07.TF-Time", "FIMTXN07.TF-TradingDate", "FIMTXN07.TF-Open", "FIMTXN07.TF-High", "FIMTXN07.TF-Low", "FIMTXN07.TF-Price", "FIMTXN07.TF-TotalVolume", "FIMTXN07.TF-Volume", "FIMTXN07.TF-NWTotalBidContract", "FIMTXN07.TF-NWTotalAskContract", "FIMTXN07.TF-NWTotalBidSize", "FIMTXN07.TF-NWTotalAskSize", "FIMTXN07.TF-InSize", "FIMTXN07.TF-OutSize", "FIMTXN07.TF-TotalBidMatchTx", "FIMTXN07.TF-TotalAskMatchTx", "FIMTXN07.TF-BestBid1", "FIMTXN07.TF-BestBid2", "FIMTXN07.TF-BestBid3", "FIMTXN07.TF-BestBid4", "FIMTXN07.TF-BestBid5", "FIMTXN07.TF-BestAsk1", "FIMTXN07.TF-BestAsk2", "FIMTXN07.TF-BestAsk3", "FIMTXN07.TF-BestAsk4", "FIMTXN07.TF-BestAsk5", "FIMTXN07.TF-BestBidSize1", "FIMTXN07.TF-BestBidSize2", "FIMTXN07.TF-BestBidSize3", "FIMTXN07.TF-BestBidSize4", "FIMTXN07.TF-BestBidSize5", "FIMTXN07.TF-BestAskSize1", "FIMTXN07.TF-BestAskSize2", "FIMTXN07.TF-BestAskSize3", "FIMTXN07.TF-BestAskSize4", "FIMTXN07.TF-BestAskSize5", "FIMTXN07.TF-Name", "FIMTXN07.TF-WContractDate", "FIMTXN07.TF-SettlePrice", "FIMTXN07.TF-UpLimit", "FIMTXN07.TF-DownLimit", "FIMTXN07.TF-OI", "FIMTXN07.TF-TradingDate", "FIMTXN07.TF-WRemainDate", "FIMTXN07.TF-PreClose", "FIMTXN07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITMN07", "service": "XQTISC", "topic": "Quote", "items": ["FITMN07.TF-Time", "FITMN07.TF-TradingDate", "FITMN07.TF-Open", "FITMN07.TF-High", "FITMN07.TF-Low", "FITMN07.TF-Price", "FITMN07.TF-TotalVolume", "FITMN07.TF-Volume", "FITMN07.TF-NWTotalBidContract", "FITMN07.TF-NWTotalAskContract", "FITMN07.TF-NWTotalBidSize", "FITMN07.TF-NWTotalAskSize", "FITMN07.TF-InSize", "FITMN07.TF-OutSize", "FITMN07.TF-TotalBidMatchTx", "FITMN07.TF-TotalAskMatchTx", "FITMN07.TF-BestBid1", "FITMN07.TF-BestBid2", "FITMN07.TF-BestBid3", "FITMN07.TF-BestBid4", "FITMN07.TF-BestBid5", "FITMN07.TF-BestAsk1", "FITMN07.TF-BestAsk2", "FITMN07.TF-BestAsk3", "FITMN07.TF-BestAsk4", "FITMN07.TF-BestAsk5", "FITMN07.TF-BestBidSize1", "FITMN07.TF-BestBidSize2", "FITMN07.TF-BestBidSize3", "FITMN07.TF-BestBidSize4", "FITMN07.TF-BestBidSize5", "FITMN07.TF-BestAskSize1", "FITMN07.TF-BestAskSize2", "FITMN07.TF-BestAskSize3", "FITMN07.TF-BestAskSize4", "FITMN07.TF-BestAskSize5", "FITMN07.TF-Name", "FITMN07.TF-WContractDate", "FITMN07.TF-SettlePrice", "FITMN07.TF-UpLimit", "FITMN07.TF-DownLimit", "FITMN07.TF-OI", "FITMN07.TF-TradingDate", "FITMN07.TF-WRemainDate", "FITMN07.TF-PreClose", "FITMN07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITXN08", "service": "XQTISC", "topic": "Quote", "items": ["FITXN08.TF-Time", "FITXN08.TF-TradingDate", "FITXN08.TF-Open", "FITXN08.TF-High", "FITXN08.TF-Low", "FITXN08.TF-Price", "FITXN08.TF-TotalVolume", "FITXN08.TF-Volume", "FITXN08.TF-NWTotalBidContract", "FITXN08.TF-NWTotalAskContract", "FITXN08.TF-NWTotalBidSize", "FITXN08.TF-NWTotalAskSize", "FITXN08.TF-InSize", "FITXN08.TF-OutSize", "FITXN08.TF-TotalBidMatchTx", "FITXN08.TF-TotalAskMatchTx", "FITXN08.TF-BestBid1", "FITXN08.TF-BestBid2", "FITXN08.TF-BestBid3", "FITXN08.TF-BestBid4", "FITXN08.TF-BestBid5", "FITXN08.TF-BestAsk1", "FITXN08.TF-BestAsk2", "FITXN08.TF-BestAsk3", "FITXN08.TF-BestAsk4", "FITXN08.TF-BestAsk5", "FITXN08.TF-BestBidSize1", "FITXN08.TF-BestBidSize2", "FITXN08.TF-BestBidSize3", "FITXN08.TF-BestBidSize4", "FITXN08.TF-BestBidSize5", "FITXN08.TF-BestAskSize1", "FITXN08.TF-BestAskSize2", "FITXN08.TF-BestAskSize3", "FITXN08.TF-BestAskSize4", "FITXN08.TF-BestAskSize5", "FITXN08.TF-Name", "FITXN08.TF-WContractDate", "FITXN08.TF-SettlePrice", "FITXN08.TF-UpLimit", "FITXN08.TF-DownLimit", "FITXN08.TF-OI", "FITXN08.TF-TradingDate", "FITXN08.TF-WRemainDate", "FITXN08.TF-PreClose", "FITXN08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FIMTXN08", "service": "XQTISC", "topic": "Quote", "items": ["FIMTXN08.TF-Time", "FIMTXN08.TF-TradingDate", "FIMTXN08.TF-Open", "FIMTXN08.TF-High", "FIMTXN08.TF-Low", "FIMTXN08.TF-Price", "FIMTXN08.TF-TotalVolume", "FIMTXN08.TF-Volume", "FIMTXN08.TF-NWTotalBidContract", "FIMTXN08.TF-NWTotalAskContract", "FIMTXN08.TF-NWTotalBidSize", "FIMTXN08.TF-NWTotalAskSize", "FIMTXN08.TF-InSize", "FIMTXN08.TF-OutSize", "FIMTXN08.TF-TotalBidMatchTx", "FIMTXN08.TF-TotalAskMatchTx", "FIMTXN08.TF-BestBid1", "FIMTXN08.TF-BestBid2", "FIMTXN08.TF-BestBid3", "FIMTXN08.TF-BestBid4", "FIMTXN08.TF-BestBid5", "FIMTXN08.TF-BestAsk1", "FIMTXN08.TF-BestAsk2", "FIMTXN08.TF-BestAsk3", "FIMTXN08.TF-BestAsk4", "FIMTXN08.TF-BestAsk5", "FIMTXN08.TF-BestBidSize1", "FIMTXN08.TF-BestBidSize2", "FIMTXN08.TF-BestBidSize3", "FIMTXN08.TF-BestBidSize4", "FIMTXN08.TF-BestBidSize5", "FIMTXN08.TF-BestAskSize1", "FIMTXN08.TF-BestAskSize2", "FIMTXN08.TF-BestAskSize3", "FIMTXN08.TF-BestAskSize4", "FIMTXN08.TF-BestAskSize5", "FIMTXN08.TF-Name", "FIMTXN08.TF-WContractDate", "FIMTXN08.TF-SettlePrice", "FIMTXN08.TF-UpLimit", "FIMTXN08.TF-DownLimit", "FIMTXN08.TF-OI", "FIMTXN08.TF-TradingDate", "FIMTXN08.TF-WRemainDate", "FIMTXN08.TF-PreClose", "FIMTXN08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITMN08", "service": "XQTISC", "topic": "Quote", "items": ["FITMN08.TF-Time", "FITMN08.TF-TradingDate", "FITMN08.TF-Open", "FITMN08.TF-High", "FITMN08.TF-Low", "FITMN08.TF-Price", "FITMN08.TF-TotalVolume", "FITMN08.TF-Volume", "FITMN08.TF-NWTotalBidContract", "FITMN08.TF-NWTotalAskContract", "FITMN08.TF-NWTotalBidSize", "FITMN08.TF-NWTotalAskSize", "FITMN08.TF-InSize", "FITMN08.TF-OutSize", "FITMN08.TF-TotalBidMatchTx", "FITMN08.TF-TotalAskMatchTx", "FITMN08.TF-BestBid1", "FITMN08.TF-BestBid2", "FITMN08.TF-BestBid3", "FITMN08.TF-BestBid4", "FITMN08.TF-BestBid5", "FITMN08.TF-BestAsk1", "FITMN08.TF-BestAsk2", "FITMN08.TF-BestAsk3", "FITMN08.TF-BestAsk4", "FITMN08.TF-BestAsk5", "FITMN08.TF-BestBidSize1", "FITMN08.TF-BestBidSize2", "FITMN08.TF-BestBidSize3", "FITMN08.TF-BestBidSize4", "FITMN08.TF-BestBidSize5", "FITMN08.TF-BestAskSize1", "FITMN08.TF-BestAskSize2", "FITMN08.TF-BestAskSize3", "FITMN08.TF-BestAskSize4", "FITMN08.TF-BestAskSize5", "FITMN08.TF-Name", "FITMN08.TF-WContractDate", "FITMN08.TF-SettlePrice", "FITMN08.TF-UpLimit", "FITMN08.TF-DownLimit", "FITMN08.TF-OI", "FITMN08.TF-TradingDate", "FITMN08.TF-WRemainDate", "FITMN08.TF-PreClose", "FITMN08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITX07", "service": "XQTISC", "topic": "Quote", "items": ["FITX07.TF-Time", "FITX07.TF-TradingDate", "FITX07.TF-Open", "FITX07.TF-High", "FITX07.TF-Low", "FITX07.TF-Price", "FITX07.TF-TotalVolume", "FITX07.TF-Volume", "FITX07.TF-NWTotalBidContract", "FITX07.TF-NWTotalAskContract", "FITX07.TF-NWTotalBidSize", "FITX07.TF-NWTotalAskSize", "FITX07.TF-InSize", "FITX07.TF-OutSize", "FITX07.TF-TotalBidMatchTx", "FITX07.TF-TotalAskMatchTx", "FITX07.TF-BestBid1", "FITX07.TF-BestBid2", "FITX07.TF-BestBid3", "FITX07.TF-BestBid4", "FITX07.TF-BestBid5", "FITX07.TF-BestAsk1", "FITX07.TF-BestAsk2", "FITX07.TF-BestAsk3", "FITX07.TF-BestAsk4", "FITX07.TF-BestAsk5", "FITX07.TF-BestBidSize1", "FITX07.TF-BestBidSize2", "FITX07.TF-BestBidSize3", "FITX07.TF-BestBidSize4", "FITX07.TF-BestBidSize5", "FITX07.TF-BestAskSize1", "FITX07.TF-BestAskSize2", "FITX07.TF-BestAskSize3", "FITX07.TF-BestAskSize4", "FITX07.TF-BestAskSize5", "FITX07.TF-Name", "FITX07.TF-WContractDate", "FITX07.TF-SettlePrice", "FITX07.TF-UpLimit", "FITX07.TF-DownLimit", "FITX07.TF-OI", "FITX07.TF-TradingDate", "FITX07.TF-WRemainDate", "FITX07.TF-PreClose", "FITX07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FIMTX07", "service": "XQTISC", "topic": "Quote", "items": ["FIMTX07.TF-Time", "FIMTX07.TF-TradingDate", "FIMTX07.TF-Open", "FIMTX07.TF-High", "FIMTX07.TF-Low", "FIMTX07.TF-Price", "FIMTX07.TF-TotalVolume", "FIMTX07.TF-Volume", "FIMTX07.TF-NWTotalBidContract", "FIMTX07.TF-NWTotalAskContract", "FIMTX07.TF-NWTotalBidSize", "FIMTX07.TF-NWTotalAskSize", "FIMTX07.TF-InSize", "FIMTX07.TF-OutSize", "FIMTX07.TF-TotalBidMatchTx", "FIMTX07.TF-TotalAskMatchTx", "FIMTX07.TF-BestBid1", "FIMTX07.TF-BestBid2", "FIMTX07.TF-BestBid3", "FIMTX07.TF-BestBid4", "FIMTX07.TF-BestBid5", "FIMTX07.TF-BestAsk1", "FIMTX07.TF-BestAsk2", "FIMTX07.TF-BestAsk3", "FIMTX07.TF-BestAsk4", "FIMTX07.TF-BestAsk5", "FIMTX07.TF-BestBidSize1", "FIMTX07.TF-BestBidSize2", "FIMTX07.TF-BestBidSize3", "FIMTX07.TF-BestBidSize4", "FIMTX07.TF-BestBidSize5", "FIMTX07.TF-BestAskSize1", "FIMTX07.TF-BestAskSize2", "FIMTX07.TF-BestAskSize3", "FIMTX07.TF-BestAskSize4", "FIMTX07.TF-BestAskSize5", "FIMTX07.TF-Name", "FIMTX07.TF-WContractDate", "FIMTX07.TF-SettlePrice", "FIMTX07.TF-UpLimit", "FIMTX07.TF-DownLimit", "FIMTX07.TF-OI", "FIMTX07.TF-TradingDate", "FIMTX07.TF-WRemainDate", "FIMTX07.TF-PreClose", "FIMTX07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITM07", "service": "XQTISC", "topic": "Quote", "items": ["FITM07.TF-Time", "FITM07.TF-TradingDate", "FITM07.TF-Open", "FITM07.TF-High", "FITM07.TF-Low", "FITM07.TF-Price", "FITM07.TF-TotalVolume", "FITM07.TF-Volume", "FITM07.TF-NWTotalBidContract", "FITM07.TF-NWTotalAskContract", "FITM07.TF-NWTotalBidSize", "FITM07.TF-NWTotalAskSize", "FITM07.TF-InSize", "FITM07.TF-OutSize", "FITM07.TF-TotalBidMatchTx", "FITM07.TF-TotalAskMatchTx", "FITM07.TF-BestBid1", "FITM07.TF-BestBid2", "FITM07.TF-BestBid3", "FITM07.TF-BestBid4", "FITM07.TF-BestBid5", "FITM07.TF-BestAsk1", "FITM07.TF-BestAsk2", "FITM07.TF-BestAsk3", "FITM07.TF-BestAsk4", "FITM07.TF-BestAsk5", "FITM07.TF-BestBidSize1", "FITM07.TF-BestBidSize2", "FITM07.TF-BestBidSize3", "FITM07.TF-BestBidSize4", "FITM07.TF-BestBidSize5", "FITM07.TF-BestAskSize1", "FITM07.TF-BestAskSize2", "FITM07.TF-BestAskSize3", "FITM07.TF-BestAskSize4", "FITM07.TF-BestAskSize5", "FITM07.TF-Name", "FITM07.TF-WContractDate", "FITM07.TF-SettlePrice", "FITM07.TF-UpLimit", "FITM07.TF-DownLimit", "FITM07.TF-OI", "FITM07.TF-TradingDate", "FITM07.TF-WRemainDate", "FITM07.TF-PreClose", "FITM07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITX08", "service": "XQTISC", "topic": "Quote", "items": ["FITX08.TF-Time", "FITX08.TF-TradingDate", "FITX08.TF-Open", "FITX08.TF-High", "FITX08.TF-Low", "FITX08.TF-Price", "FITX08.TF-TotalVolume", "FITX08.TF-Volume", "FITX08.TF-NWTotalBidContract", "FITX08.TF-NWTotalAskContract", "FITX08.TF-NWTotalBidSize", "FITX08.TF-NWTotalAskSize", "FITX08.TF-InSize", "FITX08.TF-OutSize", "FITX08.TF-TotalBidMatchTx", "FITX08.TF-TotalAskMatchTx", "FITX08.TF-BestBid1", "FITX08.TF-BestBid2", "FITX08.TF-BestBid3", "FITX08.TF-BestBid4", "FITX08.TF-BestBid5", "FITX08.TF-BestAsk1", "FITX08.TF-BestAsk2", "FITX08.TF-BestAsk3", "FITX08.TF-BestAsk4", "FITX08.TF-BestAsk5", "FITX08.TF-BestBidSize1", "FITX08.TF-BestBidSize2", "FITX08.TF-BestBidSize3", "FITX08.TF-BestBidSize4", "FITX08.TF-BestBidSize5", "FITX08.TF-BestAskSize1", "FITX08.TF-BestAskSize2", "FITX08.TF-BestAskSize3", "FITX08.TF-BestAskSize4", "FITX08.TF-BestAskSize5", "FITX08.TF-Name", "FITX08.TF-WContractDate", "FITX08.TF-SettlePrice", "FITX08.TF-UpLimit", "FITX08.TF-DownLimit", "FITX08.TF-OI", "FITX08.TF-TradingDate", "FITX08.TF-WRemainDate", "FITX08.TF-PreClose", "FITX08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FIMTX08", "service": "XQTISC", "topic": "Quote", "items": ["FIMTX08.TF-Time", "FIMTX08.TF-TradingDate", "FIMTX08.TF-Open", "FIMTX08.TF-High", "FIMTX08.TF-Low", "FIMTX08.TF-Price", "FIMTX08.TF-TotalVolume", "FIMTX08.TF-Volume", "FIMTX08.TF-NWTotalBidContract", "FIMTX08.TF-NWTotalAskContract", "FIMTX08.TF-NWTotalBidSize", "FIMTX08.TF-NWTotalAskSize", "FIMTX08.TF-InSize", "FIMTX08.TF-OutSize", "FIMTX08.TF-TotalBidMatchTx", "FIMTX08.TF-TotalAskMatchTx", "FIMTX08.TF-BestBid1", "FIMTX08.TF-BestBid2", "FIMTX08.TF-BestBid3", "FIMTX08.TF-BestBid4", "FIMTX08.TF-BestBid5", "FIMTX08.TF-BestAsk1", "FIMTX08.TF-BestAsk2", "FIMTX08.TF-BestAsk3", "FIMTX08.TF-BestAsk4", "FIMTX08.TF-BestAsk5", "FIMTX08.TF-BestBidSize1", "FIMTX08.TF-BestBidSize2", "FIMTX08.TF-BestBidSize3", "FIMTX08.TF-BestBidSize4", "FIMTX08.TF-BestBidSize5", "FIMTX08.TF-BestAskSize1", "FIMTX08.TF-BestAskSize2", "FIMTX08.TF-BestAskSize3", "FIMTX08.TF-BestAskSize4", "FIMTX08.TF-BestAskSize5", "FIMTX08.TF-Name", "FIMTX08.TF-WContractDate", "FIMTX08.TF-SettlePrice", "FIMTX08.TF-UpLimit", "FIMTX08.TF-DownLimit", "FIMTX08.TF-OI", "FIMTX08.TF-TradingDate", "FIMTX08.TF-WRemainDate", "FIMTX08.TF-PreClose", "FIMTX08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITM08", "service": "XQTISC", "topic": "Quote", "items": ["FITM08.TF-Time", "FITM08.TF-TradingDate", "FITM08.TF-Open", "FITM08.TF-High", "FITM08.TF-Low", "FITM08.TF-Price", "FITM08.TF-TotalVolume", "FITM08.TF-Volume", "FITM08.TF-NWTotalBidContract", "FITM08.TF-NWTotalAskContract", "FITM08.TF-NWTotalBidSize", "FITM08.TF-NWTotalAskSize", "FITM08.TF-InSize", "FITM08.TF-OutSize", "FITM08.TF-TotalBidMatchTx", "FITM08.TF-TotalAskMatchTx", "FITM08.TF-BestBid1", "FITM08.TF-BestBid2", "FITM08.TF-BestBid3", "FITM08.TF-BestBid4", "FITM08.TF-BestBid5", "FITM08.TF-BestAsk1", "FITM08.TF-BestAsk2", "FITM08.TF-BestAsk3", "FITM08.TF-BestAsk4", "FITM08.TF-BestAsk5", "FITM08.TF-BestBidSize1", "FITM08.TF-BestBidSize2", "FITM08.TF-BestBidSize3", "FITM08.TF-BestBidSize4", "FITM08.TF-BestBidSize5", "FITM08.TF-BestAskSize1", "FITM08.TF-BestAskSize2", "FITM08.TF-BestAskSize3", "FITM08.TF-BestAskSize4", "FITM08.TF-BestAskSize5", "FITM08.TF-Name", "FITM08.TF-WContractDate", "FITM08.TF-SettlePrice", "FITM08.TF-UpLimit", "FITM08.TF-DownLimit", "FITM08.TF-OI", "FITM08.TF-TradingDate", "FITM08.TF-WRemainDate", "FITM08.TF-PreClose", "FITM08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "2330", "service": "XQTISC", "topic": "Quote", "items": ["2330.TW-Time", "2330.TW-TradingDate", "2330.TW-Open", "2330.TW-High", "2330.TW-Low", "2330.TW-Price", "2330.TW-TotalVolume", "2330.TW-Volume", "2330.TW-NWTotalBidContract", "2330.TW-NWTotalAskContract", "2330.TW-NWTotalBidSize", "2330.TW-NWTotalAskSize", "2330.TW-InSize", "2330.TW-OutSize", "2330.TW-TotalBidMatchTx", "2330.TW-TotalAskMatchTx", "2330.TW-BestBid1", "2330.TW-BestBid2", "2330.TW-BestBid3", "2330.TW-BestBid4", "2330.TW-BestBid5", "2330.TW-BestAsk1", "2330.TW-BestAsk2", "2330.TW-BestAsk3", "2330.TW-BestAsk4", "2330.TW-BestAsk5", "2330.TW-BestBidSize1", "2330.TW-BestBidSize2", "2330.TW-BestBidSize3", "2330.TW-BestBidSize4", "2330.TW-BestBidSize5", "2330.TW-BestAskSize1", "2330.TW-BestAskSize2", "2330.TW-BestAskSize3", "2330.TW-BestAskSize4", "2330.TW-BestAskSize5", "2330.TW-Name", "2330.TW-WContractDate", "2330.TW-SettlePrice", "2330.TW-UpLimit", "2330.TW-DownLimit", "2330.TW-OI", "2330.TW-TradingDate", "2330.TW-WRemainDate", "2330.TW-PreClose", "2330.TW-PreTotalVolume"], "enabled": true}, {"symbol": "2317", "service": "XQTISC", "topic": "Quote", "items": ["2317.TW-Time", "2317.TW-TradingDate", "2317.TW-Open", "2317.TW-High", "2317.TW-Low", "2317.TW-Price", "2317.TW-TotalVolume", "2317.TW-Volume", "2317.TW-NWTotalBidContract", "2317.TW-NWTotalAskContract", "2317.TW-NWTotalBidSize", "2317.TW-NWTotalAskSize", "2317.TW-InSize", "2317.TW-OutSize", "2317.TW-TotalBidMatchTx", "2317.TW-TotalAskMatchTx", "2317.TW-BestBid1", "2317.TW-BestBid2", "2317.TW-BestBid3", "2317.TW-BestBid4", "2317.TW-BestBid5", "2317.TW-BestAsk1", "2317.TW-BestAsk2", "2317.TW-BestAsk3", "2317.TW-BestAsk4", "2317.TW-BestAsk5", "2317.TW-BestBidSize1", "2317.TW-BestBidSize2", "2317.TW-BestBidSize3", "2317.TW-BestBidSize4", "2317.TW-BestBidSize5", "2317.TW-BestAskSize1", "2317.TW-BestAskSize2", "2317.TW-BestAskSize3", "2317.TW-BestAskSize4", "2317.TW-BestAskSize5", "2317.TW-Name", "2317.TW-WContractDate", "2317.TW-SettlePrice", "2317.TW-UpLimit", "2317.TW-DownLimit", "2317.TW-OI", "2317.TW-TradingDate", "2317.TW-WRemainDate", "2317.TW-PreClose", "2317.TW-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22000", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22000.TF-Time", "TXoN07C22000.TF-TradingDate", "TXoN07C22000.TF-Open", "TXoN07C22000.TF-High", "TXoN07C22000.TF-Low", "TXoN07C22000.TF-Price", "TXoN07C22000.TF-TotalVolume", "TXoN07C22000.TF-Volume", "TXoN07C22000.TF-NWTotalBidContract", "TXoN07C22000.TF-NWTotalAskContract", "TXoN07C22000.TF-NWTotalBidSize", "TXoN07C22000.TF-NWTotalAskSize", "TXoN07C22000.TF-InSize", "TXoN07C22000.TF-OutSize", "TXoN07C22000.TF-TotalBidMatchTx", "TXoN07C22000.TF-TotalAskMatchTx", "TXoN07C22000.TF-BestBid1", "TXoN07C22000.TF-BestBid2", "TXoN07C22000.TF-BestBid3", "TXoN07C22000.TF-BestBid4", "TXoN07C22000.TF-BestBid5", "TXoN07C22000.TF-BestAsk1", "TXoN07C22000.TF-BestAsk2", "TXoN07C22000.TF-BestAsk3", "TXoN07C22000.TF-BestAsk4", "TXoN07C22000.TF-BestAsk5", "TXoN07C22000.TF-BestBidSize1", "TXoN07C22000.TF-BestBidSize2", "TXoN07C22000.TF-BestBidSize3", "TXoN07C22000.TF-BestBidSize4", "TXoN07C22000.TF-BestBidSize5", "TXoN07C22000.TF-BestAskSize1", "TXoN07C22000.TF-BestAskSize2", "TXoN07C22000.TF-BestAskSize3", "TXoN07C22000.TF-BestAskSize4", "TXoN07C22000.TF-BestAskSize5", "TXoN07C22000.TF-Name", "TXoN07C22000.TF-WContractDate", "TXoN07C22000.TF-SettlePrice", "TXoN07C22000.TF-UpLimit", "TXoN07C22000.TF-DownLimit", "TXoN07C22000.TF-OI", "TXoN07C22000.TF-TradingDate", "TXoN07C22000.TF-WRemainDate", "TXoN07C22000.TF-PreClose", "TXoN07C22000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22100", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22100.TF-Time", "TXoN07C22100.TF-TradingDate", "TXoN07C22100.TF-Open", "TXoN07C22100.TF-High", "TXoN07C22100.TF-Low", "TXoN07C22100.TF-Price", "TXoN07C22100.TF-TotalVolume", "TXoN07C22100.TF-Volume", "TXoN07C22100.TF-NWTotalBidContract", "TXoN07C22100.TF-NWTotalAskContract", "TXoN07C22100.TF-NWTotalBidSize", "TXoN07C22100.TF-NWTotalAskSize", "TXoN07C22100.TF-InSize", "TXoN07C22100.TF-OutSize", "TXoN07C22100.TF-TotalBidMatchTx", "TXoN07C22100.TF-TotalAskMatchTx", "TXoN07C22100.TF-BestBid1", "TXoN07C22100.TF-BestBid2", "TXoN07C22100.TF-BestBid3", "TXoN07C22100.TF-BestBid4", "TXoN07C22100.TF-BestBid5", "TXoN07C22100.TF-BestAsk1", "TXoN07C22100.TF-BestAsk2", "TXoN07C22100.TF-BestAsk3", "TXoN07C22100.TF-BestAsk4", "TXoN07C22100.TF-BestAsk5", "TXoN07C22100.TF-BestBidSize1", "TXoN07C22100.TF-BestBidSize2", "TXoN07C22100.TF-BestBidSize3", "TXoN07C22100.TF-BestBidSize4", "TXoN07C22100.TF-BestBidSize5", "TXoN07C22100.TF-BestAskSize1", "TXoN07C22100.TF-BestAskSize2", "TXoN07C22100.TF-BestAskSize3", "TXoN07C22100.TF-BestAskSize4", "TXoN07C22100.TF-BestAskSize5", "TXoN07C22100.TF-Name", "TXoN07C22100.TF-WContractDate", "TXoN07C22100.TF-SettlePrice", "TXoN07C22100.TF-UpLimit", "TXoN07C22100.TF-DownLimit", "TXoN07C22100.TF-OI", "TXoN07C22100.TF-TradingDate", "TXoN07C22100.TF-WRemainDate", "TXoN07C22100.TF-PreClose", "TXoN07C22100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22200", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22200.TF-Time", "TXoN07C22200.TF-TradingDate", "TXoN07C22200.TF-Open", "TXoN07C22200.TF-High", "TXoN07C22200.TF-Low", "TXoN07C22200.TF-Price", "TXoN07C22200.TF-TotalVolume", "TXoN07C22200.TF-Volume", "TXoN07C22200.TF-NWTotalBidContract", "TXoN07C22200.TF-NWTotalAskContract", "TXoN07C22200.TF-NWTotalBidSize", "TXoN07C22200.TF-NWTotalAskSize", "TXoN07C22200.TF-InSize", "TXoN07C22200.TF-OutSize", "TXoN07C22200.TF-TotalBidMatchTx", "TXoN07C22200.TF-TotalAskMatchTx", "TXoN07C22200.TF-BestBid1", "TXoN07C22200.TF-BestBid2", "TXoN07C22200.TF-BestBid3", "TXoN07C22200.TF-BestBid4", "TXoN07C22200.TF-BestBid5", "TXoN07C22200.TF-BestAsk1", "TXoN07C22200.TF-BestAsk2", "TXoN07C22200.TF-BestAsk3", "TXoN07C22200.TF-BestAsk4", "TXoN07C22200.TF-BestAsk5", "TXoN07C22200.TF-BestBidSize1", "TXoN07C22200.TF-BestBidSize2", "TXoN07C22200.TF-BestBidSize3", "TXoN07C22200.TF-BestBidSize4", "TXoN07C22200.TF-BestBidSize5", "TXoN07C22200.TF-BestAskSize1", "TXoN07C22200.TF-BestAskSize2", "TXoN07C22200.TF-BestAskSize3", "TXoN07C22200.TF-BestAskSize4", "TXoN07C22200.TF-BestAskSize5", "TXoN07C22200.TF-Name", "TXoN07C22200.TF-WContractDate", "TXoN07C22200.TF-SettlePrice", "TXoN07C22200.TF-UpLimit", "TXoN07C22200.TF-DownLimit", "TXoN07C22200.TF-OI", "TXoN07C22200.TF-TradingDate", "TXoN07C22200.TF-WRemainDate", "TXoN07C22200.TF-PreClose", "TXoN07C22200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22300", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22300.TF-Time", "TXoN07C22300.TF-TradingDate", "TXoN07C22300.TF-Open", "TXoN07C22300.TF-High", "TXoN07C22300.TF-Low", "TXoN07C22300.TF-Price", "TXoN07C22300.TF-TotalVolume", "TXoN07C22300.TF-Volume", "TXoN07C22300.TF-NWTotalBidContract", "TXoN07C22300.TF-NWTotalAskContract", "TXoN07C22300.TF-NWTotalBidSize", "TXoN07C22300.TF-NWTotalAskSize", "TXoN07C22300.TF-InSize", "TXoN07C22300.TF-OutSize", "TXoN07C22300.TF-TotalBidMatchTx", "TXoN07C22300.TF-TotalAskMatchTx", "TXoN07C22300.TF-BestBid1", "TXoN07C22300.TF-BestBid2", "TXoN07C22300.TF-BestBid3", "TXoN07C22300.TF-BestBid4", "TXoN07C22300.TF-BestBid5", "TXoN07C22300.TF-BestAsk1", "TXoN07C22300.TF-BestAsk2", "TXoN07C22300.TF-BestAsk3", "TXoN07C22300.TF-BestAsk4", "TXoN07C22300.TF-BestAsk5", "TXoN07C22300.TF-BestBidSize1", "TXoN07C22300.TF-BestBidSize2", "TXoN07C22300.TF-BestBidSize3", "TXoN07C22300.TF-BestBidSize4", "TXoN07C22300.TF-BestBidSize5", "TXoN07C22300.TF-BestAskSize1", "TXoN07C22300.TF-BestAskSize2", "TXoN07C22300.TF-BestAskSize3", "TXoN07C22300.TF-BestAskSize4", "TXoN07C22300.TF-BestAskSize5", "TXoN07C22300.TF-Name", "TXoN07C22300.TF-WContractDate", "TXoN07C22300.TF-SettlePrice", "TXoN07C22300.TF-UpLimit", "TXoN07C22300.TF-DownLimit", "TXoN07C22300.TF-OI", "TXoN07C22300.TF-TradingDate", "TXoN07C22300.TF-WRemainDate", "TXoN07C22300.TF-PreClose", "TXoN07C22300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22400", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22400.TF-Time", "TXoN07C22400.TF-TradingDate", "TXoN07C22400.TF-Open", "TXoN07C22400.TF-High", "TXoN07C22400.TF-Low", "TXoN07C22400.TF-Price", "TXoN07C22400.TF-TotalVolume", "TXoN07C22400.TF-Volume", "TXoN07C22400.TF-NWTotalBidContract", "TXoN07C22400.TF-NWTotalAskContract", "TXoN07C22400.TF-NWTotalBidSize", "TXoN07C22400.TF-NWTotalAskSize", "TXoN07C22400.TF-InSize", "TXoN07C22400.TF-OutSize", "TXoN07C22400.TF-TotalBidMatchTx", "TXoN07C22400.TF-TotalAskMatchTx", "TXoN07C22400.TF-BestBid1", "TXoN07C22400.TF-BestBid2", "TXoN07C22400.TF-BestBid3", "TXoN07C22400.TF-BestBid4", "TXoN07C22400.TF-BestBid5", "TXoN07C22400.TF-BestAsk1", "TXoN07C22400.TF-BestAsk2", "TXoN07C22400.TF-BestAsk3", "TXoN07C22400.TF-BestAsk4", "TXoN07C22400.TF-BestAsk5", "TXoN07C22400.TF-BestBidSize1", "TXoN07C22400.TF-BestBidSize2", "TXoN07C22400.TF-BestBidSize3", "TXoN07C22400.TF-BestBidSize4", "TXoN07C22400.TF-BestBidSize5", "TXoN07C22400.TF-BestAskSize1", "TXoN07C22400.TF-BestAskSize2", "TXoN07C22400.TF-BestAskSize3", "TXoN07C22400.TF-BestAskSize4", "TXoN07C22400.TF-BestAskSize5", "TXoN07C22400.TF-Name", "TXoN07C22400.TF-WContractDate", "TXoN07C22400.TF-SettlePrice", "TXoN07C22400.TF-UpLimit", "TXoN07C22400.TF-DownLimit", "TXoN07C22400.TF-OI", "TXoN07C22400.TF-TradingDate", "TXoN07C22400.TF-WRemainDate", "TXoN07C22400.TF-PreClose", "TXoN07C22400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22500", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22500.TF-Time", "TXoN07C22500.TF-TradingDate", "TXoN07C22500.TF-Open", "TXoN07C22500.TF-High", "TXoN07C22500.TF-Low", "TXoN07C22500.TF-Price", "TXoN07C22500.TF-TotalVolume", "TXoN07C22500.TF-Volume", "TXoN07C22500.TF-NWTotalBidContract", "TXoN07C22500.TF-NWTotalAskContract", "TXoN07C22500.TF-NWTotalBidSize", "TXoN07C22500.TF-NWTotalAskSize", "TXoN07C22500.TF-InSize", "TXoN07C22500.TF-OutSize", "TXoN07C22500.TF-TotalBidMatchTx", "TXoN07C22500.TF-TotalAskMatchTx", "TXoN07C22500.TF-BestBid1", "TXoN07C22500.TF-BestBid2", "TXoN07C22500.TF-BestBid3", "TXoN07C22500.TF-BestBid4", "TXoN07C22500.TF-BestBid5", "TXoN07C22500.TF-BestAsk1", "TXoN07C22500.TF-BestAsk2", "TXoN07C22500.TF-BestAsk3", "TXoN07C22500.TF-BestAsk4", "TXoN07C22500.TF-BestAsk5", "TXoN07C22500.TF-BestBidSize1", "TXoN07C22500.TF-BestBidSize2", "TXoN07C22500.TF-BestBidSize3", "TXoN07C22500.TF-BestBidSize4", "TXoN07C22500.TF-BestBidSize5", "TXoN07C22500.TF-BestAskSize1", "TXoN07C22500.TF-BestAskSize2", "TXoN07C22500.TF-BestAskSize3", "TXoN07C22500.TF-BestAskSize4", "TXoN07C22500.TF-BestAskSize5", "TXoN07C22500.TF-Name", "TXoN07C22500.TF-WContractDate", "TXoN07C22500.TF-SettlePrice", "TXoN07C22500.TF-UpLimit", "TXoN07C22500.TF-DownLimit", "TXoN07C22500.TF-OI", "TXoN07C22500.TF-TradingDate", "TXoN07C22500.TF-WRemainDate", "TXoN07C22500.TF-PreClose", "TXoN07C22500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22600", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22600.TF-Time", "TXoN07C22600.TF-TradingDate", "TXoN07C22600.TF-Open", "TXoN07C22600.TF-High", "TXoN07C22600.TF-Low", "TXoN07C22600.TF-Price", "TXoN07C22600.TF-TotalVolume", "TXoN07C22600.TF-Volume", "TXoN07C22600.TF-NWTotalBidContract", "TXoN07C22600.TF-NWTotalAskContract", "TXoN07C22600.TF-NWTotalBidSize", "TXoN07C22600.TF-NWTotalAskSize", "TXoN07C22600.TF-InSize", "TXoN07C22600.TF-OutSize", "TXoN07C22600.TF-TotalBidMatchTx", "TXoN07C22600.TF-TotalAskMatchTx", "TXoN07C22600.TF-BestBid1", "TXoN07C22600.TF-BestBid2", "TXoN07C22600.TF-BestBid3", "TXoN07C22600.TF-BestBid4", "TXoN07C22600.TF-BestBid5", "TXoN07C22600.TF-BestAsk1", "TXoN07C22600.TF-BestAsk2", "TXoN07C22600.TF-BestAsk3", "TXoN07C22600.TF-BestAsk4", "TXoN07C22600.TF-BestAsk5", "TXoN07C22600.TF-BestBidSize1", "TXoN07C22600.TF-BestBidSize2", "TXoN07C22600.TF-BestBidSize3", "TXoN07C22600.TF-BestBidSize4", "TXoN07C22600.TF-BestBidSize5", "TXoN07C22600.TF-BestAskSize1", "TXoN07C22600.TF-BestAskSize2", "TXoN07C22600.TF-BestAskSize3", "TXoN07C22600.TF-BestAskSize4", "TXoN07C22600.TF-BestAskSize5", "TXoN07C22600.TF-Name", "TXoN07C22600.TF-WContractDate", "TXoN07C22600.TF-SettlePrice", "TXoN07C22600.TF-UpLimit", "TXoN07C22600.TF-DownLimit", "TXoN07C22600.TF-OI", "TXoN07C22600.TF-TradingDate", "TXoN07C22600.TF-WRemainDate", "TXoN07C22600.TF-PreClose", "TXoN07C22600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22700", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22700.TF-Time", "TXoN07C22700.TF-TradingDate", "TXoN07C22700.TF-Open", "TXoN07C22700.TF-High", "TXoN07C22700.TF-Low", "TXoN07C22700.TF-Price", "TXoN07C22700.TF-TotalVolume", "TXoN07C22700.TF-Volume", "TXoN07C22700.TF-NWTotalBidContract", "TXoN07C22700.TF-NWTotalAskContract", "TXoN07C22700.TF-NWTotalBidSize", "TXoN07C22700.TF-NWTotalAskSize", "TXoN07C22700.TF-InSize", "TXoN07C22700.TF-OutSize", "TXoN07C22700.TF-TotalBidMatchTx", "TXoN07C22700.TF-TotalAskMatchTx", "TXoN07C22700.TF-BestBid1", "TXoN07C22700.TF-BestBid2", "TXoN07C22700.TF-BestBid3", "TXoN07C22700.TF-BestBid4", "TXoN07C22700.TF-BestBid5", "TXoN07C22700.TF-BestAsk1", "TXoN07C22700.TF-BestAsk2", "TXoN07C22700.TF-BestAsk3", "TXoN07C22700.TF-BestAsk4", "TXoN07C22700.TF-BestAsk5", "TXoN07C22700.TF-BestBidSize1", "TXoN07C22700.TF-BestBidSize2", "TXoN07C22700.TF-BestBidSize3", "TXoN07C22700.TF-BestBidSize4", "TXoN07C22700.TF-BestBidSize5", "TXoN07C22700.TF-BestAskSize1", "TXoN07C22700.TF-BestAskSize2", "TXoN07C22700.TF-BestAskSize3", "TXoN07C22700.TF-BestAskSize4", "TXoN07C22700.TF-BestAskSize5", "TXoN07C22700.TF-Name", "TXoN07C22700.TF-WContractDate", "TXoN07C22700.TF-SettlePrice", "TXoN07C22700.TF-UpLimit", "TXoN07C22700.TF-DownLimit", "TXoN07C22700.TF-OI", "TXoN07C22700.TF-TradingDate", "TXoN07C22700.TF-WRemainDate", "TXoN07C22700.TF-PreClose", "TXoN07C22700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22800", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22800.TF-Time", "TXoN07C22800.TF-TradingDate", "TXoN07C22800.TF-Open", "TXoN07C22800.TF-High", "TXoN07C22800.TF-Low", "TXoN07C22800.TF-Price", "TXoN07C22800.TF-TotalVolume", "TXoN07C22800.TF-Volume", "TXoN07C22800.TF-NWTotalBidContract", "TXoN07C22800.TF-NWTotalAskContract", "TXoN07C22800.TF-NWTotalBidSize", "TXoN07C22800.TF-NWTotalAskSize", "TXoN07C22800.TF-InSize", "TXoN07C22800.TF-OutSize", "TXoN07C22800.TF-TotalBidMatchTx", "TXoN07C22800.TF-TotalAskMatchTx", "TXoN07C22800.TF-BestBid1", "TXoN07C22800.TF-BestBid2", "TXoN07C22800.TF-BestBid3", "TXoN07C22800.TF-BestBid4", "TXoN07C22800.TF-BestBid5", "TXoN07C22800.TF-BestAsk1", "TXoN07C22800.TF-BestAsk2", "TXoN07C22800.TF-BestAsk3", "TXoN07C22800.TF-BestAsk4", "TXoN07C22800.TF-BestAsk5", "TXoN07C22800.TF-BestBidSize1", "TXoN07C22800.TF-BestBidSize2", "TXoN07C22800.TF-BestBidSize3", "TXoN07C22800.TF-BestBidSize4", "TXoN07C22800.TF-BestBidSize5", "TXoN07C22800.TF-BestAskSize1", "TXoN07C22800.TF-BestAskSize2", "TXoN07C22800.TF-BestAskSize3", "TXoN07C22800.TF-BestAskSize4", "TXoN07C22800.TF-BestAskSize5", "TXoN07C22800.TF-Name", "TXoN07C22800.TF-WContractDate", "TXoN07C22800.TF-SettlePrice", "TXoN07C22800.TF-UpLimit", "TXoN07C22800.TF-DownLimit", "TXoN07C22800.TF-OI", "TXoN07C22800.TF-TradingDate", "TXoN07C22800.TF-WRemainDate", "TXoN07C22800.TF-PreClose", "TXoN07C22800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C22900", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C22900.TF-Time", "TXoN07C22900.TF-TradingDate", "TXoN07C22900.TF-Open", "TXoN07C22900.TF-High", "TXoN07C22900.TF-Low", "TXoN07C22900.TF-Price", "TXoN07C22900.TF-TotalVolume", "TXoN07C22900.TF-Volume", "TXoN07C22900.TF-NWTotalBidContract", "TXoN07C22900.TF-NWTotalAskContract", "TXoN07C22900.TF-NWTotalBidSize", "TXoN07C22900.TF-NWTotalAskSize", "TXoN07C22900.TF-InSize", "TXoN07C22900.TF-OutSize", "TXoN07C22900.TF-TotalBidMatchTx", "TXoN07C22900.TF-TotalAskMatchTx", "TXoN07C22900.TF-BestBid1", "TXoN07C22900.TF-BestBid2", "TXoN07C22900.TF-BestBid3", "TXoN07C22900.TF-BestBid4", "TXoN07C22900.TF-BestBid5", "TXoN07C22900.TF-BestAsk1", "TXoN07C22900.TF-BestAsk2", "TXoN07C22900.TF-BestAsk3", "TXoN07C22900.TF-BestAsk4", "TXoN07C22900.TF-BestAsk5", "TXoN07C22900.TF-BestBidSize1", "TXoN07C22900.TF-BestBidSize2", "TXoN07C22900.TF-BestBidSize3", "TXoN07C22900.TF-BestBidSize4", "TXoN07C22900.TF-BestBidSize5", "TXoN07C22900.TF-BestAskSize1", "TXoN07C22900.TF-BestAskSize2", "TXoN07C22900.TF-BestAskSize3", "TXoN07C22900.TF-BestAskSize4", "TXoN07C22900.TF-BestAskSize5", "TXoN07C22900.TF-Name", "TXoN07C22900.TF-WContractDate", "TXoN07C22900.TF-SettlePrice", "TXoN07C22900.TF-UpLimit", "TXoN07C22900.TF-DownLimit", "TXoN07C22900.TF-OI", "TXoN07C22900.TF-TradingDate", "TXoN07C22900.TF-WRemainDate", "TXoN07C22900.TF-PreClose", "TXoN07C22900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23000", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23000.TF-Time", "TXoN07C23000.TF-TradingDate", "TXoN07C23000.TF-Open", "TXoN07C23000.TF-High", "TXoN07C23000.TF-Low", "TXoN07C23000.TF-Price", "TXoN07C23000.TF-TotalVolume", "TXoN07C23000.TF-Volume", "TXoN07C23000.TF-NWTotalBidContract", "TXoN07C23000.TF-NWTotalAskContract", "TXoN07C23000.TF-NWTotalBidSize", "TXoN07C23000.TF-NWTotalAskSize", "TXoN07C23000.TF-InSize", "TXoN07C23000.TF-OutSize", "TXoN07C23000.TF-TotalBidMatchTx", "TXoN07C23000.TF-TotalAskMatchTx", "TXoN07C23000.TF-BestBid1", "TXoN07C23000.TF-BestBid2", "TXoN07C23000.TF-BestBid3", "TXoN07C23000.TF-BestBid4", "TXoN07C23000.TF-BestBid5", "TXoN07C23000.TF-BestAsk1", "TXoN07C23000.TF-BestAsk2", "TXoN07C23000.TF-BestAsk3", "TXoN07C23000.TF-BestAsk4", "TXoN07C23000.TF-BestAsk5", "TXoN07C23000.TF-BestBidSize1", "TXoN07C23000.TF-BestBidSize2", "TXoN07C23000.TF-BestBidSize3", "TXoN07C23000.TF-BestBidSize4", "TXoN07C23000.TF-BestBidSize5", "TXoN07C23000.TF-BestAskSize1", "TXoN07C23000.TF-BestAskSize2", "TXoN07C23000.TF-BestAskSize3", "TXoN07C23000.TF-BestAskSize4", "TXoN07C23000.TF-BestAskSize5", "TXoN07C23000.TF-Name", "TXoN07C23000.TF-WContractDate", "TXoN07C23000.TF-SettlePrice", "TXoN07C23000.TF-UpLimit", "TXoN07C23000.TF-DownLimit", "TXoN07C23000.TF-OI", "TXoN07C23000.TF-TradingDate", "TXoN07C23000.TF-WRemainDate", "TXoN07C23000.TF-PreClose", "TXoN07C23000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23100", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23100.TF-Time", "TXoN07C23100.TF-TradingDate", "TXoN07C23100.TF-Open", "TXoN07C23100.TF-High", "TXoN07C23100.TF-Low", "TXoN07C23100.TF-Price", "TXoN07C23100.TF-TotalVolume", "TXoN07C23100.TF-Volume", "TXoN07C23100.TF-NWTotalBidContract", "TXoN07C23100.TF-NWTotalAskContract", "TXoN07C23100.TF-NWTotalBidSize", "TXoN07C23100.TF-NWTotalAskSize", "TXoN07C23100.TF-InSize", "TXoN07C23100.TF-OutSize", "TXoN07C23100.TF-TotalBidMatchTx", "TXoN07C23100.TF-TotalAskMatchTx", "TXoN07C23100.TF-BestBid1", "TXoN07C23100.TF-BestBid2", "TXoN07C23100.TF-BestBid3", "TXoN07C23100.TF-BestBid4", "TXoN07C23100.TF-BestBid5", "TXoN07C23100.TF-BestAsk1", "TXoN07C23100.TF-BestAsk2", "TXoN07C23100.TF-BestAsk3", "TXoN07C23100.TF-BestAsk4", "TXoN07C23100.TF-BestAsk5", "TXoN07C23100.TF-BestBidSize1", "TXoN07C23100.TF-BestBidSize2", "TXoN07C23100.TF-BestBidSize3", "TXoN07C23100.TF-BestBidSize4", "TXoN07C23100.TF-BestBidSize5", "TXoN07C23100.TF-BestAskSize1", "TXoN07C23100.TF-BestAskSize2", "TXoN07C23100.TF-BestAskSize3", "TXoN07C23100.TF-BestAskSize4", "TXoN07C23100.TF-BestAskSize5", "TXoN07C23100.TF-Name", "TXoN07C23100.TF-WContractDate", "TXoN07C23100.TF-SettlePrice", "TXoN07C23100.TF-UpLimit", "TXoN07C23100.TF-DownLimit", "TXoN07C23100.TF-OI", "TXoN07C23100.TF-TradingDate", "TXoN07C23100.TF-WRemainDate", "TXoN07C23100.TF-PreClose", "TXoN07C23100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23200", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23200.TF-Time", "TXoN07C23200.TF-TradingDate", "TXoN07C23200.TF-Open", "TXoN07C23200.TF-High", "TXoN07C23200.TF-Low", "TXoN07C23200.TF-Price", "TXoN07C23200.TF-TotalVolume", "TXoN07C23200.TF-Volume", "TXoN07C23200.TF-NWTotalBidContract", "TXoN07C23200.TF-NWTotalAskContract", "TXoN07C23200.TF-NWTotalBidSize", "TXoN07C23200.TF-NWTotalAskSize", "TXoN07C23200.TF-InSize", "TXoN07C23200.TF-OutSize", "TXoN07C23200.TF-TotalBidMatchTx", "TXoN07C23200.TF-TotalAskMatchTx", "TXoN07C23200.TF-BestBid1", "TXoN07C23200.TF-BestBid2", "TXoN07C23200.TF-BestBid3", "TXoN07C23200.TF-BestBid4", "TXoN07C23200.TF-BestBid5", "TXoN07C23200.TF-BestAsk1", "TXoN07C23200.TF-BestAsk2", "TXoN07C23200.TF-BestAsk3", "TXoN07C23200.TF-BestAsk4", "TXoN07C23200.TF-BestAsk5", "TXoN07C23200.TF-BestBidSize1", "TXoN07C23200.TF-BestBidSize2", "TXoN07C23200.TF-BestBidSize3", "TXoN07C23200.TF-BestBidSize4", "TXoN07C23200.TF-BestBidSize5", "TXoN07C23200.TF-BestAskSize1", "TXoN07C23200.TF-BestAskSize2", "TXoN07C23200.TF-BestAskSize3", "TXoN07C23200.TF-BestAskSize4", "TXoN07C23200.TF-BestAskSize5", "TXoN07C23200.TF-Name", "TXoN07C23200.TF-WContractDate", "TXoN07C23200.TF-SettlePrice", "TXoN07C23200.TF-UpLimit", "TXoN07C23200.TF-DownLimit", "TXoN07C23200.TF-OI", "TXoN07C23200.TF-TradingDate", "TXoN07C23200.TF-WRemainDate", "TXoN07C23200.TF-PreClose", "TXoN07C23200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23300", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23300.TF-Time", "TXoN07C23300.TF-TradingDate", "TXoN07C23300.TF-Open", "TXoN07C23300.TF-High", "TXoN07C23300.TF-Low", "TXoN07C23300.TF-Price", "TXoN07C23300.TF-TotalVolume", "TXoN07C23300.TF-Volume", "TXoN07C23300.TF-NWTotalBidContract", "TXoN07C23300.TF-NWTotalAskContract", "TXoN07C23300.TF-NWTotalBidSize", "TXoN07C23300.TF-NWTotalAskSize", "TXoN07C23300.TF-InSize", "TXoN07C23300.TF-OutSize", "TXoN07C23300.TF-TotalBidMatchTx", "TXoN07C23300.TF-TotalAskMatchTx", "TXoN07C23300.TF-BestBid1", "TXoN07C23300.TF-BestBid2", "TXoN07C23300.TF-BestBid3", "TXoN07C23300.TF-BestBid4", "TXoN07C23300.TF-BestBid5", "TXoN07C23300.TF-BestAsk1", "TXoN07C23300.TF-BestAsk2", "TXoN07C23300.TF-BestAsk3", "TXoN07C23300.TF-BestAsk4", "TXoN07C23300.TF-BestAsk5", "TXoN07C23300.TF-BestBidSize1", "TXoN07C23300.TF-BestBidSize2", "TXoN07C23300.TF-BestBidSize3", "TXoN07C23300.TF-BestBidSize4", "TXoN07C23300.TF-BestBidSize5", "TXoN07C23300.TF-BestAskSize1", "TXoN07C23300.TF-BestAskSize2", "TXoN07C23300.TF-BestAskSize3", "TXoN07C23300.TF-BestAskSize4", "TXoN07C23300.TF-BestAskSize5", "TXoN07C23300.TF-Name", "TXoN07C23300.TF-WContractDate", "TXoN07C23300.TF-SettlePrice", "TXoN07C23300.TF-UpLimit", "TXoN07C23300.TF-DownLimit", "TXoN07C23300.TF-OI", "TXoN07C23300.TF-TradingDate", "TXoN07C23300.TF-WRemainDate", "TXoN07C23300.TF-PreClose", "TXoN07C23300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23400", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23400.TF-Time", "TXoN07C23400.TF-TradingDate", "TXoN07C23400.TF-Open", "TXoN07C23400.TF-High", "TXoN07C23400.TF-Low", "TXoN07C23400.TF-Price", "TXoN07C23400.TF-TotalVolume", "TXoN07C23400.TF-Volume", "TXoN07C23400.TF-NWTotalBidContract", "TXoN07C23400.TF-NWTotalAskContract", "TXoN07C23400.TF-NWTotalBidSize", "TXoN07C23400.TF-NWTotalAskSize", "TXoN07C23400.TF-InSize", "TXoN07C23400.TF-OutSize", "TXoN07C23400.TF-TotalBidMatchTx", "TXoN07C23400.TF-TotalAskMatchTx", "TXoN07C23400.TF-BestBid1", "TXoN07C23400.TF-BestBid2", "TXoN07C23400.TF-BestBid3", "TXoN07C23400.TF-BestBid4", "TXoN07C23400.TF-BestBid5", "TXoN07C23400.TF-BestAsk1", "TXoN07C23400.TF-BestAsk2", "TXoN07C23400.TF-BestAsk3", "TXoN07C23400.TF-BestAsk4", "TXoN07C23400.TF-BestAsk5", "TXoN07C23400.TF-BestBidSize1", "TXoN07C23400.TF-BestBidSize2", "TXoN07C23400.TF-BestBidSize3", "TXoN07C23400.TF-BestBidSize4", "TXoN07C23400.TF-BestBidSize5", "TXoN07C23400.TF-BestAskSize1", "TXoN07C23400.TF-BestAskSize2", "TXoN07C23400.TF-BestAskSize3", "TXoN07C23400.TF-BestAskSize4", "TXoN07C23400.TF-BestAskSize5", "TXoN07C23400.TF-Name", "TXoN07C23400.TF-WContractDate", "TXoN07C23400.TF-SettlePrice", "TXoN07C23400.TF-UpLimit", "TXoN07C23400.TF-DownLimit", "TXoN07C23400.TF-OI", "TXoN07C23400.TF-TradingDate", "TXoN07C23400.TF-WRemainDate", "TXoN07C23400.TF-PreClose", "TXoN07C23400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23500", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23500.TF-Time", "TXoN07C23500.TF-TradingDate", "TXoN07C23500.TF-Open", "TXoN07C23500.TF-High", "TXoN07C23500.TF-Low", "TXoN07C23500.TF-Price", "TXoN07C23500.TF-TotalVolume", "TXoN07C23500.TF-Volume", "TXoN07C23500.TF-NWTotalBidContract", "TXoN07C23500.TF-NWTotalAskContract", "TXoN07C23500.TF-NWTotalBidSize", "TXoN07C23500.TF-NWTotalAskSize", "TXoN07C23500.TF-InSize", "TXoN07C23500.TF-OutSize", "TXoN07C23500.TF-TotalBidMatchTx", "TXoN07C23500.TF-TotalAskMatchTx", "TXoN07C23500.TF-BestBid1", "TXoN07C23500.TF-BestBid2", "TXoN07C23500.TF-BestBid3", "TXoN07C23500.TF-BestBid4", "TXoN07C23500.TF-BestBid5", "TXoN07C23500.TF-BestAsk1", "TXoN07C23500.TF-BestAsk2", "TXoN07C23500.TF-BestAsk3", "TXoN07C23500.TF-BestAsk4", "TXoN07C23500.TF-BestAsk5", "TXoN07C23500.TF-BestBidSize1", "TXoN07C23500.TF-BestBidSize2", "TXoN07C23500.TF-BestBidSize3", "TXoN07C23500.TF-BestBidSize4", "TXoN07C23500.TF-BestBidSize5", "TXoN07C23500.TF-BestAskSize1", "TXoN07C23500.TF-BestAskSize2", "TXoN07C23500.TF-BestAskSize3", "TXoN07C23500.TF-BestAskSize4", "TXoN07C23500.TF-BestAskSize5", "TXoN07C23500.TF-Name", "TXoN07C23500.TF-WContractDate", "TXoN07C23500.TF-SettlePrice", "TXoN07C23500.TF-UpLimit", "TXoN07C23500.TF-DownLimit", "TXoN07C23500.TF-OI", "TXoN07C23500.TF-TradingDate", "TXoN07C23500.TF-WRemainDate", "TXoN07C23500.TF-PreClose", "TXoN07C23500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23600", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23600.TF-Time", "TXoN07C23600.TF-TradingDate", "TXoN07C23600.TF-Open", "TXoN07C23600.TF-High", "TXoN07C23600.TF-Low", "TXoN07C23600.TF-Price", "TXoN07C23600.TF-TotalVolume", "TXoN07C23600.TF-Volume", "TXoN07C23600.TF-NWTotalBidContract", "TXoN07C23600.TF-NWTotalAskContract", "TXoN07C23600.TF-NWTotalBidSize", "TXoN07C23600.TF-NWTotalAskSize", "TXoN07C23600.TF-InSize", "TXoN07C23600.TF-OutSize", "TXoN07C23600.TF-TotalBidMatchTx", "TXoN07C23600.TF-TotalAskMatchTx", "TXoN07C23600.TF-BestBid1", "TXoN07C23600.TF-BestBid2", "TXoN07C23600.TF-BestBid3", "TXoN07C23600.TF-BestBid4", "TXoN07C23600.TF-BestBid5", "TXoN07C23600.TF-BestAsk1", "TXoN07C23600.TF-BestAsk2", "TXoN07C23600.TF-BestAsk3", "TXoN07C23600.TF-BestAsk4", "TXoN07C23600.TF-BestAsk5", "TXoN07C23600.TF-BestBidSize1", "TXoN07C23600.TF-BestBidSize2", "TXoN07C23600.TF-BestBidSize3", "TXoN07C23600.TF-BestBidSize4", "TXoN07C23600.TF-BestBidSize5", "TXoN07C23600.TF-BestAskSize1", "TXoN07C23600.TF-BestAskSize2", "TXoN07C23600.TF-BestAskSize3", "TXoN07C23600.TF-BestAskSize4", "TXoN07C23600.TF-BestAskSize5", "TXoN07C23600.TF-Name", "TXoN07C23600.TF-WContractDate", "TXoN07C23600.TF-SettlePrice", "TXoN07C23600.TF-UpLimit", "TXoN07C23600.TF-DownLimit", "TXoN07C23600.TF-OI", "TXoN07C23600.TF-TradingDate", "TXoN07C23600.TF-WRemainDate", "TXoN07C23600.TF-PreClose", "TXoN07C23600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23700", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23700.TF-Time", "TXoN07C23700.TF-TradingDate", "TXoN07C23700.TF-Open", "TXoN07C23700.TF-High", "TXoN07C23700.TF-Low", "TXoN07C23700.TF-Price", "TXoN07C23700.TF-TotalVolume", "TXoN07C23700.TF-Volume", "TXoN07C23700.TF-NWTotalBidContract", "TXoN07C23700.TF-NWTotalAskContract", "TXoN07C23700.TF-NWTotalBidSize", "TXoN07C23700.TF-NWTotalAskSize", "TXoN07C23700.TF-InSize", "TXoN07C23700.TF-OutSize", "TXoN07C23700.TF-TotalBidMatchTx", "TXoN07C23700.TF-TotalAskMatchTx", "TXoN07C23700.TF-BestBid1", "TXoN07C23700.TF-BestBid2", "TXoN07C23700.TF-BestBid3", "TXoN07C23700.TF-BestBid4", "TXoN07C23700.TF-BestBid5", "TXoN07C23700.TF-BestAsk1", "TXoN07C23700.TF-BestAsk2", "TXoN07C23700.TF-BestAsk3", "TXoN07C23700.TF-BestAsk4", "TXoN07C23700.TF-BestAsk5", "TXoN07C23700.TF-BestBidSize1", "TXoN07C23700.TF-BestBidSize2", "TXoN07C23700.TF-BestBidSize3", "TXoN07C23700.TF-BestBidSize4", "TXoN07C23700.TF-BestBidSize5", "TXoN07C23700.TF-BestAskSize1", "TXoN07C23700.TF-BestAskSize2", "TXoN07C23700.TF-BestAskSize3", "TXoN07C23700.TF-BestAskSize4", "TXoN07C23700.TF-BestAskSize5", "TXoN07C23700.TF-Name", "TXoN07C23700.TF-WContractDate", "TXoN07C23700.TF-SettlePrice", "TXoN07C23700.TF-UpLimit", "TXoN07C23700.TF-DownLimit", "TXoN07C23700.TF-OI", "TXoN07C23700.TF-TradingDate", "TXoN07C23700.TF-WRemainDate", "TXoN07C23700.TF-PreClose", "TXoN07C23700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23800", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23800.TF-Time", "TXoN07C23800.TF-TradingDate", "TXoN07C23800.TF-Open", "TXoN07C23800.TF-High", "TXoN07C23800.TF-Low", "TXoN07C23800.TF-Price", "TXoN07C23800.TF-TotalVolume", "TXoN07C23800.TF-Volume", "TXoN07C23800.TF-NWTotalBidContract", "TXoN07C23800.TF-NWTotalAskContract", "TXoN07C23800.TF-NWTotalBidSize", "TXoN07C23800.TF-NWTotalAskSize", "TXoN07C23800.TF-InSize", "TXoN07C23800.TF-OutSize", "TXoN07C23800.TF-TotalBidMatchTx", "TXoN07C23800.TF-TotalAskMatchTx", "TXoN07C23800.TF-BestBid1", "TXoN07C23800.TF-BestBid2", "TXoN07C23800.TF-BestBid3", "TXoN07C23800.TF-BestBid4", "TXoN07C23800.TF-BestBid5", "TXoN07C23800.TF-BestAsk1", "TXoN07C23800.TF-BestAsk2", "TXoN07C23800.TF-BestAsk3", "TXoN07C23800.TF-BestAsk4", "TXoN07C23800.TF-BestAsk5", "TXoN07C23800.TF-BestBidSize1", "TXoN07C23800.TF-BestBidSize2", "TXoN07C23800.TF-BestBidSize3", "TXoN07C23800.TF-BestBidSize4", "TXoN07C23800.TF-BestBidSize5", "TXoN07C23800.TF-BestAskSize1", "TXoN07C23800.TF-BestAskSize2", "TXoN07C23800.TF-BestAskSize3", "TXoN07C23800.TF-BestAskSize4", "TXoN07C23800.TF-BestAskSize5", "TXoN07C23800.TF-Name", "TXoN07C23800.TF-WContractDate", "TXoN07C23800.TF-SettlePrice", "TXoN07C23800.TF-UpLimit", "TXoN07C23800.TF-DownLimit", "TXoN07C23800.TF-OI", "TXoN07C23800.TF-TradingDate", "TXoN07C23800.TF-WRemainDate", "TXoN07C23800.TF-PreClose", "TXoN07C23800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C23900", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C23900.TF-Time", "TXoN07C23900.TF-TradingDate", "TXoN07C23900.TF-Open", "TXoN07C23900.TF-High", "TXoN07C23900.TF-Low", "TXoN07C23900.TF-Price", "TXoN07C23900.TF-TotalVolume", "TXoN07C23900.TF-Volume", "TXoN07C23900.TF-NWTotalBidContract", "TXoN07C23900.TF-NWTotalAskContract", "TXoN07C23900.TF-NWTotalBidSize", "TXoN07C23900.TF-NWTotalAskSize", "TXoN07C23900.TF-InSize", "TXoN07C23900.TF-OutSize", "TXoN07C23900.TF-TotalBidMatchTx", "TXoN07C23900.TF-TotalAskMatchTx", "TXoN07C23900.TF-BestBid1", "TXoN07C23900.TF-BestBid2", "TXoN07C23900.TF-BestBid3", "TXoN07C23900.TF-BestBid4", "TXoN07C23900.TF-BestBid5", "TXoN07C23900.TF-BestAsk1", "TXoN07C23900.TF-BestAsk2", "TXoN07C23900.TF-BestAsk3", "TXoN07C23900.TF-BestAsk4", "TXoN07C23900.TF-BestAsk5", "TXoN07C23900.TF-BestBidSize1", "TXoN07C23900.TF-BestBidSize2", "TXoN07C23900.TF-BestBidSize3", "TXoN07C23900.TF-BestBidSize4", "TXoN07C23900.TF-BestBidSize5", "TXoN07C23900.TF-BestAskSize1", "TXoN07C23900.TF-BestAskSize2", "TXoN07C23900.TF-BestAskSize3", "TXoN07C23900.TF-BestAskSize4", "TXoN07C23900.TF-BestAskSize5", "TXoN07C23900.TF-Name", "TXoN07C23900.TF-WContractDate", "TXoN07C23900.TF-SettlePrice", "TXoN07C23900.TF-UpLimit", "TXoN07C23900.TF-DownLimit", "TXoN07C23900.TF-OI", "TXoN07C23900.TF-TradingDate", "TXoN07C23900.TF-WRemainDate", "TXoN07C23900.TF-PreClose", "TXoN07C23900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24000", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24000.TF-Time", "TXoN07C24000.TF-TradingDate", "TXoN07C24000.TF-Open", "TXoN07C24000.TF-High", "TXoN07C24000.TF-Low", "TXoN07C24000.TF-Price", "TXoN07C24000.TF-TotalVolume", "TXoN07C24000.TF-Volume", "TXoN07C24000.TF-NWTotalBidContract", "TXoN07C24000.TF-NWTotalAskContract", "TXoN07C24000.TF-NWTotalBidSize", "TXoN07C24000.TF-NWTotalAskSize", "TXoN07C24000.TF-InSize", "TXoN07C24000.TF-OutSize", "TXoN07C24000.TF-TotalBidMatchTx", "TXoN07C24000.TF-TotalAskMatchTx", "TXoN07C24000.TF-BestBid1", "TXoN07C24000.TF-BestBid2", "TXoN07C24000.TF-BestBid3", "TXoN07C24000.TF-BestBid4", "TXoN07C24000.TF-BestBid5", "TXoN07C24000.TF-BestAsk1", "TXoN07C24000.TF-BestAsk2", "TXoN07C24000.TF-BestAsk3", "TXoN07C24000.TF-BestAsk4", "TXoN07C24000.TF-BestAsk5", "TXoN07C24000.TF-BestBidSize1", "TXoN07C24000.TF-BestBidSize2", "TXoN07C24000.TF-BestBidSize3", "TXoN07C24000.TF-BestBidSize4", "TXoN07C24000.TF-BestBidSize5", "TXoN07C24000.TF-BestAskSize1", "TXoN07C24000.TF-BestAskSize2", "TXoN07C24000.TF-BestAskSize3", "TXoN07C24000.TF-BestAskSize4", "TXoN07C24000.TF-BestAskSize5", "TXoN07C24000.TF-Name", "TXoN07C24000.TF-WContractDate", "TXoN07C24000.TF-SettlePrice", "TXoN07C24000.TF-UpLimit", "TXoN07C24000.TF-DownLimit", "TXoN07C24000.TF-OI", "TXoN07C24000.TF-TradingDate", "TXoN07C24000.TF-WRemainDate", "TXoN07C24000.TF-PreClose", "TXoN07C24000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24100", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24100.TF-Time", "TXoN07C24100.TF-TradingDate", "TXoN07C24100.TF-Open", "TXoN07C24100.TF-High", "TXoN07C24100.TF-Low", "TXoN07C24100.TF-Price", "TXoN07C24100.TF-TotalVolume", "TXoN07C24100.TF-Volume", "TXoN07C24100.TF-NWTotalBidContract", "TXoN07C24100.TF-NWTotalAskContract", "TXoN07C24100.TF-NWTotalBidSize", "TXoN07C24100.TF-NWTotalAskSize", "TXoN07C24100.TF-InSize", "TXoN07C24100.TF-OutSize", "TXoN07C24100.TF-TotalBidMatchTx", "TXoN07C24100.TF-TotalAskMatchTx", "TXoN07C24100.TF-BestBid1", "TXoN07C24100.TF-BestBid2", "TXoN07C24100.TF-BestBid3", "TXoN07C24100.TF-BestBid4", "TXoN07C24100.TF-BestBid5", "TXoN07C24100.TF-BestAsk1", "TXoN07C24100.TF-BestAsk2", "TXoN07C24100.TF-BestAsk3", "TXoN07C24100.TF-BestAsk4", "TXoN07C24100.TF-BestAsk5", "TXoN07C24100.TF-BestBidSize1", "TXoN07C24100.TF-BestBidSize2", "TXoN07C24100.TF-BestBidSize3", "TXoN07C24100.TF-BestBidSize4", "TXoN07C24100.TF-BestBidSize5", "TXoN07C24100.TF-BestAskSize1", "TXoN07C24100.TF-BestAskSize2", "TXoN07C24100.TF-BestAskSize3", "TXoN07C24100.TF-BestAskSize4", "TXoN07C24100.TF-BestAskSize5", "TXoN07C24100.TF-Name", "TXoN07C24100.TF-WContractDate", "TXoN07C24100.TF-SettlePrice", "TXoN07C24100.TF-UpLimit", "TXoN07C24100.TF-DownLimit", "TXoN07C24100.TF-OI", "TXoN07C24100.TF-TradingDate", "TXoN07C24100.TF-WRemainDate", "TXoN07C24100.TF-PreClose", "TXoN07C24100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24200", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24200.TF-Time", "TXoN07C24200.TF-TradingDate", "TXoN07C24200.TF-Open", "TXoN07C24200.TF-High", "TXoN07C24200.TF-Low", "TXoN07C24200.TF-Price", "TXoN07C24200.TF-TotalVolume", "TXoN07C24200.TF-Volume", "TXoN07C24200.TF-NWTotalBidContract", "TXoN07C24200.TF-NWTotalAskContract", "TXoN07C24200.TF-NWTotalBidSize", "TXoN07C24200.TF-NWTotalAskSize", "TXoN07C24200.TF-InSize", "TXoN07C24200.TF-OutSize", "TXoN07C24200.TF-TotalBidMatchTx", "TXoN07C24200.TF-TotalAskMatchTx", "TXoN07C24200.TF-BestBid1", "TXoN07C24200.TF-BestBid2", "TXoN07C24200.TF-BestBid3", "TXoN07C24200.TF-BestBid4", "TXoN07C24200.TF-BestBid5", "TXoN07C24200.TF-BestAsk1", "TXoN07C24200.TF-BestAsk2", "TXoN07C24200.TF-BestAsk3", "TXoN07C24200.TF-BestAsk4", "TXoN07C24200.TF-BestAsk5", "TXoN07C24200.TF-BestBidSize1", "TXoN07C24200.TF-BestBidSize2", "TXoN07C24200.TF-BestBidSize3", "TXoN07C24200.TF-BestBidSize4", "TXoN07C24200.TF-BestBidSize5", "TXoN07C24200.TF-BestAskSize1", "TXoN07C24200.TF-BestAskSize2", "TXoN07C24200.TF-BestAskSize3", "TXoN07C24200.TF-BestAskSize4", "TXoN07C24200.TF-BestAskSize5", "TXoN07C24200.TF-Name", "TXoN07C24200.TF-WContractDate", "TXoN07C24200.TF-SettlePrice", "TXoN07C24200.TF-UpLimit", "TXoN07C24200.TF-DownLimit", "TXoN07C24200.TF-OI", "TXoN07C24200.TF-TradingDate", "TXoN07C24200.TF-WRemainDate", "TXoN07C24200.TF-PreClose", "TXoN07C24200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24300", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24300.TF-Time", "TXoN07C24300.TF-TradingDate", "TXoN07C24300.TF-Open", "TXoN07C24300.TF-High", "TXoN07C24300.TF-Low", "TXoN07C24300.TF-Price", "TXoN07C24300.TF-TotalVolume", "TXoN07C24300.TF-Volume", "TXoN07C24300.TF-NWTotalBidContract", "TXoN07C24300.TF-NWTotalAskContract", "TXoN07C24300.TF-NWTotalBidSize", "TXoN07C24300.TF-NWTotalAskSize", "TXoN07C24300.TF-InSize", "TXoN07C24300.TF-OutSize", "TXoN07C24300.TF-TotalBidMatchTx", "TXoN07C24300.TF-TotalAskMatchTx", "TXoN07C24300.TF-BestBid1", "TXoN07C24300.TF-BestBid2", "TXoN07C24300.TF-BestBid3", "TXoN07C24300.TF-BestBid4", "TXoN07C24300.TF-BestBid5", "TXoN07C24300.TF-BestAsk1", "TXoN07C24300.TF-BestAsk2", "TXoN07C24300.TF-BestAsk3", "TXoN07C24300.TF-BestAsk4", "TXoN07C24300.TF-BestAsk5", "TXoN07C24300.TF-BestBidSize1", "TXoN07C24300.TF-BestBidSize2", "TXoN07C24300.TF-BestBidSize3", "TXoN07C24300.TF-BestBidSize4", "TXoN07C24300.TF-BestBidSize5", "TXoN07C24300.TF-BestAskSize1", "TXoN07C24300.TF-BestAskSize2", "TXoN07C24300.TF-BestAskSize3", "TXoN07C24300.TF-BestAskSize4", "TXoN07C24300.TF-BestAskSize5", "TXoN07C24300.TF-Name", "TXoN07C24300.TF-WContractDate", "TXoN07C24300.TF-SettlePrice", "TXoN07C24300.TF-UpLimit", "TXoN07C24300.TF-DownLimit", "TXoN07C24300.TF-OI", "TXoN07C24300.TF-TradingDate", "TXoN07C24300.TF-WRemainDate", "TXoN07C24300.TF-PreClose", "TXoN07C24300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24400", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24400.TF-Time", "TXoN07C24400.TF-TradingDate", "TXoN07C24400.TF-Open", "TXoN07C24400.TF-High", "TXoN07C24400.TF-Low", "TXoN07C24400.TF-Price", "TXoN07C24400.TF-TotalVolume", "TXoN07C24400.TF-Volume", "TXoN07C24400.TF-NWTotalBidContract", "TXoN07C24400.TF-NWTotalAskContract", "TXoN07C24400.TF-NWTotalBidSize", "TXoN07C24400.TF-NWTotalAskSize", "TXoN07C24400.TF-InSize", "TXoN07C24400.TF-OutSize", "TXoN07C24400.TF-TotalBidMatchTx", "TXoN07C24400.TF-TotalAskMatchTx", "TXoN07C24400.TF-BestBid1", "TXoN07C24400.TF-BestBid2", "TXoN07C24400.TF-BestBid3", "TXoN07C24400.TF-BestBid4", "TXoN07C24400.TF-BestBid5", "TXoN07C24400.TF-BestAsk1", "TXoN07C24400.TF-BestAsk2", "TXoN07C24400.TF-BestAsk3", "TXoN07C24400.TF-BestAsk4", "TXoN07C24400.TF-BestAsk5", "TXoN07C24400.TF-BestBidSize1", "TXoN07C24400.TF-BestBidSize2", "TXoN07C24400.TF-BestBidSize3", "TXoN07C24400.TF-BestBidSize4", "TXoN07C24400.TF-BestBidSize5", "TXoN07C24400.TF-BestAskSize1", "TXoN07C24400.TF-BestAskSize2", "TXoN07C24400.TF-BestAskSize3", "TXoN07C24400.TF-BestAskSize4", "TXoN07C24400.TF-BestAskSize5", "TXoN07C24400.TF-Name", "TXoN07C24400.TF-WContractDate", "TXoN07C24400.TF-SettlePrice", "TXoN07C24400.TF-UpLimit", "TXoN07C24400.TF-DownLimit", "TXoN07C24400.TF-OI", "TXoN07C24400.TF-TradingDate", "TXoN07C24400.TF-WRemainDate", "TXoN07C24400.TF-PreClose", "TXoN07C24400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24500", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24500.TF-Time", "TXoN07C24500.TF-TradingDate", "TXoN07C24500.TF-Open", "TXoN07C24500.TF-High", "TXoN07C24500.TF-Low", "TXoN07C24500.TF-Price", "TXoN07C24500.TF-TotalVolume", "TXoN07C24500.TF-Volume", "TXoN07C24500.TF-NWTotalBidContract", "TXoN07C24500.TF-NWTotalAskContract", "TXoN07C24500.TF-NWTotalBidSize", "TXoN07C24500.TF-NWTotalAskSize", "TXoN07C24500.TF-InSize", "TXoN07C24500.TF-OutSize", "TXoN07C24500.TF-TotalBidMatchTx", "TXoN07C24500.TF-TotalAskMatchTx", "TXoN07C24500.TF-BestBid1", "TXoN07C24500.TF-BestBid2", "TXoN07C24500.TF-BestBid3", "TXoN07C24500.TF-BestBid4", "TXoN07C24500.TF-BestBid5", "TXoN07C24500.TF-BestAsk1", "TXoN07C24500.TF-BestAsk2", "TXoN07C24500.TF-BestAsk3", "TXoN07C24500.TF-BestAsk4", "TXoN07C24500.TF-BestAsk5", "TXoN07C24500.TF-BestBidSize1", "TXoN07C24500.TF-BestBidSize2", "TXoN07C24500.TF-BestBidSize3", "TXoN07C24500.TF-BestBidSize4", "TXoN07C24500.TF-BestBidSize5", "TXoN07C24500.TF-BestAskSize1", "TXoN07C24500.TF-BestAskSize2", "TXoN07C24500.TF-BestAskSize3", "TXoN07C24500.TF-BestAskSize4", "TXoN07C24500.TF-BestAskSize5", "TXoN07C24500.TF-Name", "TXoN07C24500.TF-WContractDate", "TXoN07C24500.TF-SettlePrice", "TXoN07C24500.TF-UpLimit", "TXoN07C24500.TF-DownLimit", "TXoN07C24500.TF-OI", "TXoN07C24500.TF-TradingDate", "TXoN07C24500.TF-WRemainDate", "TXoN07C24500.TF-PreClose", "TXoN07C24500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24600", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24600.TF-Time", "TXoN07C24600.TF-TradingDate", "TXoN07C24600.TF-Open", "TXoN07C24600.TF-High", "TXoN07C24600.TF-Low", "TXoN07C24600.TF-Price", "TXoN07C24600.TF-TotalVolume", "TXoN07C24600.TF-Volume", "TXoN07C24600.TF-NWTotalBidContract", "TXoN07C24600.TF-NWTotalAskContract", "TXoN07C24600.TF-NWTotalBidSize", "TXoN07C24600.TF-NWTotalAskSize", "TXoN07C24600.TF-InSize", "TXoN07C24600.TF-OutSize", "TXoN07C24600.TF-TotalBidMatchTx", "TXoN07C24600.TF-TotalAskMatchTx", "TXoN07C24600.TF-BestBid1", "TXoN07C24600.TF-BestBid2", "TXoN07C24600.TF-BestBid3", "TXoN07C24600.TF-BestBid4", "TXoN07C24600.TF-BestBid5", "TXoN07C24600.TF-BestAsk1", "TXoN07C24600.TF-BestAsk2", "TXoN07C24600.TF-BestAsk3", "TXoN07C24600.TF-BestAsk4", "TXoN07C24600.TF-BestAsk5", "TXoN07C24600.TF-BestBidSize1", "TXoN07C24600.TF-BestBidSize2", "TXoN07C24600.TF-BestBidSize3", "TXoN07C24600.TF-BestBidSize4", "TXoN07C24600.TF-BestBidSize5", "TXoN07C24600.TF-BestAskSize1", "TXoN07C24600.TF-BestAskSize2", "TXoN07C24600.TF-BestAskSize3", "TXoN07C24600.TF-BestAskSize4", "TXoN07C24600.TF-BestAskSize5", "TXoN07C24600.TF-Name", "TXoN07C24600.TF-WContractDate", "TXoN07C24600.TF-SettlePrice", "TXoN07C24600.TF-UpLimit", "TXoN07C24600.TF-DownLimit", "TXoN07C24600.TF-OI", "TXoN07C24600.TF-TradingDate", "TXoN07C24600.TF-WRemainDate", "TXoN07C24600.TF-PreClose", "TXoN07C24600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24700", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24700.TF-Time", "TXoN07C24700.TF-TradingDate", "TXoN07C24700.TF-Open", "TXoN07C24700.TF-High", "TXoN07C24700.TF-Low", "TXoN07C24700.TF-Price", "TXoN07C24700.TF-TotalVolume", "TXoN07C24700.TF-Volume", "TXoN07C24700.TF-NWTotalBidContract", "TXoN07C24700.TF-NWTotalAskContract", "TXoN07C24700.TF-NWTotalBidSize", "TXoN07C24700.TF-NWTotalAskSize", "TXoN07C24700.TF-InSize", "TXoN07C24700.TF-OutSize", "TXoN07C24700.TF-TotalBidMatchTx", "TXoN07C24700.TF-TotalAskMatchTx", "TXoN07C24700.TF-BestBid1", "TXoN07C24700.TF-BestBid2", "TXoN07C24700.TF-BestBid3", "TXoN07C24700.TF-BestBid4", "TXoN07C24700.TF-BestBid5", "TXoN07C24700.TF-BestAsk1", "TXoN07C24700.TF-BestAsk2", "TXoN07C24700.TF-BestAsk3", "TXoN07C24700.TF-BestAsk4", "TXoN07C24700.TF-BestAsk5", "TXoN07C24700.TF-BestBidSize1", "TXoN07C24700.TF-BestBidSize2", "TXoN07C24700.TF-BestBidSize3", "TXoN07C24700.TF-BestBidSize4", "TXoN07C24700.TF-BestBidSize5", "TXoN07C24700.TF-BestAskSize1", "TXoN07C24700.TF-BestAskSize2", "TXoN07C24700.TF-BestAskSize3", "TXoN07C24700.TF-BestAskSize4", "TXoN07C24700.TF-BestAskSize5", "TXoN07C24700.TF-Name", "TXoN07C24700.TF-WContractDate", "TXoN07C24700.TF-SettlePrice", "TXoN07C24700.TF-UpLimit", "TXoN07C24700.TF-DownLimit", "TXoN07C24700.TF-OI", "TXoN07C24700.TF-TradingDate", "TXoN07C24700.TF-WRemainDate", "TXoN07C24700.TF-PreClose", "TXoN07C24700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24800", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24800.TF-Time", "TXoN07C24800.TF-TradingDate", "TXoN07C24800.TF-Open", "TXoN07C24800.TF-High", "TXoN07C24800.TF-Low", "TXoN07C24800.TF-Price", "TXoN07C24800.TF-TotalVolume", "TXoN07C24800.TF-Volume", "TXoN07C24800.TF-NWTotalBidContract", "TXoN07C24800.TF-NWTotalAskContract", "TXoN07C24800.TF-NWTotalBidSize", "TXoN07C24800.TF-NWTotalAskSize", "TXoN07C24800.TF-InSize", "TXoN07C24800.TF-OutSize", "TXoN07C24800.TF-TotalBidMatchTx", "TXoN07C24800.TF-TotalAskMatchTx", "TXoN07C24800.TF-BestBid1", "TXoN07C24800.TF-BestBid2", "TXoN07C24800.TF-BestBid3", "TXoN07C24800.TF-BestBid4", "TXoN07C24800.TF-BestBid5", "TXoN07C24800.TF-BestAsk1", "TXoN07C24800.TF-BestAsk2", "TXoN07C24800.TF-BestAsk3", "TXoN07C24800.TF-BestAsk4", "TXoN07C24800.TF-BestAsk5", "TXoN07C24800.TF-BestBidSize1", "TXoN07C24800.TF-BestBidSize2", "TXoN07C24800.TF-BestBidSize3", "TXoN07C24800.TF-BestBidSize4", "TXoN07C24800.TF-BestBidSize5", "TXoN07C24800.TF-BestAskSize1", "TXoN07C24800.TF-BestAskSize2", "TXoN07C24800.TF-BestAskSize3", "TXoN07C24800.TF-BestAskSize4", "TXoN07C24800.TF-BestAskSize5", "TXoN07C24800.TF-Name", "TXoN07C24800.TF-WContractDate", "TXoN07C24800.TF-SettlePrice", "TXoN07C24800.TF-UpLimit", "TXoN07C24800.TF-DownLimit", "TXoN07C24800.TF-OI", "TXoN07C24800.TF-TradingDate", "TXoN07C24800.TF-WRemainDate", "TXoN07C24800.TF-PreClose", "TXoN07C24800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C24900", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C24900.TF-Time", "TXoN07C24900.TF-TradingDate", "TXoN07C24900.TF-Open", "TXoN07C24900.TF-High", "TXoN07C24900.TF-Low", "TXoN07C24900.TF-Price", "TXoN07C24900.TF-TotalVolume", "TXoN07C24900.TF-Volume", "TXoN07C24900.TF-NWTotalBidContract", "TXoN07C24900.TF-NWTotalAskContract", "TXoN07C24900.TF-NWTotalBidSize", "TXoN07C24900.TF-NWTotalAskSize", "TXoN07C24900.TF-InSize", "TXoN07C24900.TF-OutSize", "TXoN07C24900.TF-TotalBidMatchTx", "TXoN07C24900.TF-TotalAskMatchTx", "TXoN07C24900.TF-BestBid1", "TXoN07C24900.TF-BestBid2", "TXoN07C24900.TF-BestBid3", "TXoN07C24900.TF-BestBid4", "TXoN07C24900.TF-BestBid5", "TXoN07C24900.TF-BestAsk1", "TXoN07C24900.TF-BestAsk2", "TXoN07C24900.TF-BestAsk3", "TXoN07C24900.TF-BestAsk4", "TXoN07C24900.TF-BestAsk5", "TXoN07C24900.TF-BestBidSize1", "TXoN07C24900.TF-BestBidSize2", "TXoN07C24900.TF-BestBidSize3", "TXoN07C24900.TF-BestBidSize4", "TXoN07C24900.TF-BestBidSize5", "TXoN07C24900.TF-BestAskSize1", "TXoN07C24900.TF-BestAskSize2", "TXoN07C24900.TF-BestAskSize3", "TXoN07C24900.TF-BestAskSize4", "TXoN07C24900.TF-BestAskSize5", "TXoN07C24900.TF-Name", "TXoN07C24900.TF-WContractDate", "TXoN07C24900.TF-SettlePrice", "TXoN07C24900.TF-UpLimit", "TXoN07C24900.TF-DownLimit", "TXoN07C24900.TF-OI", "TXoN07C24900.TF-TradingDate", "TXoN07C24900.TF-WRemainDate", "TXoN07C24900.TF-PreClose", "TXoN07C24900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25000", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25000.TF-Time", "TXoN07C25000.TF-TradingDate", "TXoN07C25000.TF-Open", "TXoN07C25000.TF-High", "TXoN07C25000.TF-Low", "TXoN07C25000.TF-Price", "TXoN07C25000.TF-TotalVolume", "TXoN07C25000.TF-Volume", "TXoN07C25000.TF-NWTotalBidContract", "TXoN07C25000.TF-NWTotalAskContract", "TXoN07C25000.TF-NWTotalBidSize", "TXoN07C25000.TF-NWTotalAskSize", "TXoN07C25000.TF-InSize", "TXoN07C25000.TF-OutSize", "TXoN07C25000.TF-TotalBidMatchTx", "TXoN07C25000.TF-TotalAskMatchTx", "TXoN07C25000.TF-BestBid1", "TXoN07C25000.TF-BestBid2", "TXoN07C25000.TF-BestBid3", "TXoN07C25000.TF-BestBid4", "TXoN07C25000.TF-BestBid5", "TXoN07C25000.TF-BestAsk1", "TXoN07C25000.TF-BestAsk2", "TXoN07C25000.TF-BestAsk3", "TXoN07C25000.TF-BestAsk4", "TXoN07C25000.TF-BestAsk5", "TXoN07C25000.TF-BestBidSize1", "TXoN07C25000.TF-BestBidSize2", "TXoN07C25000.TF-BestBidSize3", "TXoN07C25000.TF-BestBidSize4", "TXoN07C25000.TF-BestBidSize5", "TXoN07C25000.TF-BestAskSize1", "TXoN07C25000.TF-BestAskSize2", "TXoN07C25000.TF-BestAskSize3", "TXoN07C25000.TF-BestAskSize4", "TXoN07C25000.TF-BestAskSize5", "TXoN07C25000.TF-Name", "TXoN07C25000.TF-WContractDate", "TXoN07C25000.TF-SettlePrice", "TXoN07C25000.TF-UpLimit", "TXoN07C25000.TF-DownLimit", "TXoN07C25000.TF-OI", "TXoN07C25000.TF-TradingDate", "TXoN07C25000.TF-WRemainDate", "TXoN07C25000.TF-PreClose", "TXoN07C25000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25100", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25100.TF-Time", "TXoN07C25100.TF-TradingDate", "TXoN07C25100.TF-Open", "TXoN07C25100.TF-High", "TXoN07C25100.TF-Low", "TXoN07C25100.TF-Price", "TXoN07C25100.TF-TotalVolume", "TXoN07C25100.TF-Volume", "TXoN07C25100.TF-NWTotalBidContract", "TXoN07C25100.TF-NWTotalAskContract", "TXoN07C25100.TF-NWTotalBidSize", "TXoN07C25100.TF-NWTotalAskSize", "TXoN07C25100.TF-InSize", "TXoN07C25100.TF-OutSize", "TXoN07C25100.TF-TotalBidMatchTx", "TXoN07C25100.TF-TotalAskMatchTx", "TXoN07C25100.TF-BestBid1", "TXoN07C25100.TF-BestBid2", "TXoN07C25100.TF-BestBid3", "TXoN07C25100.TF-BestBid4", "TXoN07C25100.TF-BestBid5", "TXoN07C25100.TF-BestAsk1", "TXoN07C25100.TF-BestAsk2", "TXoN07C25100.TF-BestAsk3", "TXoN07C25100.TF-BestAsk4", "TXoN07C25100.TF-BestAsk5", "TXoN07C25100.TF-BestBidSize1", "TXoN07C25100.TF-BestBidSize2", "TXoN07C25100.TF-BestBidSize3", "TXoN07C25100.TF-BestBidSize4", "TXoN07C25100.TF-BestBidSize5", "TXoN07C25100.TF-BestAskSize1", "TXoN07C25100.TF-BestAskSize2", "TXoN07C25100.TF-BestAskSize3", "TXoN07C25100.TF-BestAskSize4", "TXoN07C25100.TF-BestAskSize5", "TXoN07C25100.TF-Name", "TXoN07C25100.TF-WContractDate", "TXoN07C25100.TF-SettlePrice", "TXoN07C25100.TF-UpLimit", "TXoN07C25100.TF-DownLimit", "TXoN07C25100.TF-OI", "TXoN07C25100.TF-TradingDate", "TXoN07C25100.TF-WRemainDate", "TXoN07C25100.TF-PreClose", "TXoN07C25100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25200", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25200.TF-Time", "TXoN07C25200.TF-TradingDate", "TXoN07C25200.TF-Open", "TXoN07C25200.TF-High", "TXoN07C25200.TF-Low", "TXoN07C25200.TF-Price", "TXoN07C25200.TF-TotalVolume", "TXoN07C25200.TF-Volume", "TXoN07C25200.TF-NWTotalBidContract", "TXoN07C25200.TF-NWTotalAskContract", "TXoN07C25200.TF-NWTotalBidSize", "TXoN07C25200.TF-NWTotalAskSize", "TXoN07C25200.TF-InSize", "TXoN07C25200.TF-OutSize", "TXoN07C25200.TF-TotalBidMatchTx", "TXoN07C25200.TF-TotalAskMatchTx", "TXoN07C25200.TF-BestBid1", "TXoN07C25200.TF-BestBid2", "TXoN07C25200.TF-BestBid3", "TXoN07C25200.TF-BestBid4", "TXoN07C25200.TF-BestBid5", "TXoN07C25200.TF-BestAsk1", "TXoN07C25200.TF-BestAsk2", "TXoN07C25200.TF-BestAsk3", "TXoN07C25200.TF-BestAsk4", "TXoN07C25200.TF-BestAsk5", "TXoN07C25200.TF-BestBidSize1", "TXoN07C25200.TF-BestBidSize2", "TXoN07C25200.TF-BestBidSize3", "TXoN07C25200.TF-BestBidSize4", "TXoN07C25200.TF-BestBidSize5", "TXoN07C25200.TF-BestAskSize1", "TXoN07C25200.TF-BestAskSize2", "TXoN07C25200.TF-BestAskSize3", "TXoN07C25200.TF-BestAskSize4", "TXoN07C25200.TF-BestAskSize5", "TXoN07C25200.TF-Name", "TXoN07C25200.TF-WContractDate", "TXoN07C25200.TF-SettlePrice", "TXoN07C25200.TF-UpLimit", "TXoN07C25200.TF-DownLimit", "TXoN07C25200.TF-OI", "TXoN07C25200.TF-TradingDate", "TXoN07C25200.TF-WRemainDate", "TXoN07C25200.TF-PreClose", "TXoN07C25200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25300", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25300.TF-Time", "TXoN07C25300.TF-TradingDate", "TXoN07C25300.TF-Open", "TXoN07C25300.TF-High", "TXoN07C25300.TF-Low", "TXoN07C25300.TF-Price", "TXoN07C25300.TF-TotalVolume", "TXoN07C25300.TF-Volume", "TXoN07C25300.TF-NWTotalBidContract", "TXoN07C25300.TF-NWTotalAskContract", "TXoN07C25300.TF-NWTotalBidSize", "TXoN07C25300.TF-NWTotalAskSize", "TXoN07C25300.TF-InSize", "TXoN07C25300.TF-OutSize", "TXoN07C25300.TF-TotalBidMatchTx", "TXoN07C25300.TF-TotalAskMatchTx", "TXoN07C25300.TF-BestBid1", "TXoN07C25300.TF-BestBid2", "TXoN07C25300.TF-BestBid3", "TXoN07C25300.TF-BestBid4", "TXoN07C25300.TF-BestBid5", "TXoN07C25300.TF-BestAsk1", "TXoN07C25300.TF-BestAsk2", "TXoN07C25300.TF-BestAsk3", "TXoN07C25300.TF-BestAsk4", "TXoN07C25300.TF-BestAsk5", "TXoN07C25300.TF-BestBidSize1", "TXoN07C25300.TF-BestBidSize2", "TXoN07C25300.TF-BestBidSize3", "TXoN07C25300.TF-BestBidSize4", "TXoN07C25300.TF-BestBidSize5", "TXoN07C25300.TF-BestAskSize1", "TXoN07C25300.TF-BestAskSize2", "TXoN07C25300.TF-BestAskSize3", "TXoN07C25300.TF-BestAskSize4", "TXoN07C25300.TF-BestAskSize5", "TXoN07C25300.TF-Name", "TXoN07C25300.TF-WContractDate", "TXoN07C25300.TF-SettlePrice", "TXoN07C25300.TF-UpLimit", "TXoN07C25300.TF-DownLimit", "TXoN07C25300.TF-OI", "TXoN07C25300.TF-TradingDate", "TXoN07C25300.TF-WRemainDate", "TXoN07C25300.TF-PreClose", "TXoN07C25300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25400", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25400.TF-Time", "TXoN07C25400.TF-TradingDate", "TXoN07C25400.TF-Open", "TXoN07C25400.TF-High", "TXoN07C25400.TF-Low", "TXoN07C25400.TF-Price", "TXoN07C25400.TF-TotalVolume", "TXoN07C25400.TF-Volume", "TXoN07C25400.TF-NWTotalBidContract", "TXoN07C25400.TF-NWTotalAskContract", "TXoN07C25400.TF-NWTotalBidSize", "TXoN07C25400.TF-NWTotalAskSize", "TXoN07C25400.TF-InSize", "TXoN07C25400.TF-OutSize", "TXoN07C25400.TF-TotalBidMatchTx", "TXoN07C25400.TF-TotalAskMatchTx", "TXoN07C25400.TF-BestBid1", "TXoN07C25400.TF-BestBid2", "TXoN07C25400.TF-BestBid3", "TXoN07C25400.TF-BestBid4", "TXoN07C25400.TF-BestBid5", "TXoN07C25400.TF-BestAsk1", "TXoN07C25400.TF-BestAsk2", "TXoN07C25400.TF-BestAsk3", "TXoN07C25400.TF-BestAsk4", "TXoN07C25400.TF-BestAsk5", "TXoN07C25400.TF-BestBidSize1", "TXoN07C25400.TF-BestBidSize2", "TXoN07C25400.TF-BestBidSize3", "TXoN07C25400.TF-BestBidSize4", "TXoN07C25400.TF-BestBidSize5", "TXoN07C25400.TF-BestAskSize1", "TXoN07C25400.TF-BestAskSize2", "TXoN07C25400.TF-BestAskSize3", "TXoN07C25400.TF-BestAskSize4", "TXoN07C25400.TF-BestAskSize5", "TXoN07C25400.TF-Name", "TXoN07C25400.TF-WContractDate", "TXoN07C25400.TF-SettlePrice", "TXoN07C25400.TF-UpLimit", "TXoN07C25400.TF-DownLimit", "TXoN07C25400.TF-OI", "TXoN07C25400.TF-TradingDate", "TXoN07C25400.TF-WRemainDate", "TXoN07C25400.TF-PreClose", "TXoN07C25400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25500", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25500.TF-Time", "TXoN07C25500.TF-TradingDate", "TXoN07C25500.TF-Open", "TXoN07C25500.TF-High", "TXoN07C25500.TF-Low", "TXoN07C25500.TF-Price", "TXoN07C25500.TF-TotalVolume", "TXoN07C25500.TF-Volume", "TXoN07C25500.TF-NWTotalBidContract", "TXoN07C25500.TF-NWTotalAskContract", "TXoN07C25500.TF-NWTotalBidSize", "TXoN07C25500.TF-NWTotalAskSize", "TXoN07C25500.TF-InSize", "TXoN07C25500.TF-OutSize", "TXoN07C25500.TF-TotalBidMatchTx", "TXoN07C25500.TF-TotalAskMatchTx", "TXoN07C25500.TF-BestBid1", "TXoN07C25500.TF-BestBid2", "TXoN07C25500.TF-BestBid3", "TXoN07C25500.TF-BestBid4", "TXoN07C25500.TF-BestBid5", "TXoN07C25500.TF-BestAsk1", "TXoN07C25500.TF-BestAsk2", "TXoN07C25500.TF-BestAsk3", "TXoN07C25500.TF-BestAsk4", "TXoN07C25500.TF-BestAsk5", "TXoN07C25500.TF-BestBidSize1", "TXoN07C25500.TF-BestBidSize2", "TXoN07C25500.TF-BestBidSize3", "TXoN07C25500.TF-BestBidSize4", "TXoN07C25500.TF-BestBidSize5", "TXoN07C25500.TF-BestAskSize1", "TXoN07C25500.TF-BestAskSize2", "TXoN07C25500.TF-BestAskSize3", "TXoN07C25500.TF-BestAskSize4", "TXoN07C25500.TF-BestAskSize5", "TXoN07C25500.TF-Name", "TXoN07C25500.TF-WContractDate", "TXoN07C25500.TF-SettlePrice", "TXoN07C25500.TF-UpLimit", "TXoN07C25500.TF-DownLimit", "TXoN07C25500.TF-OI", "TXoN07C25500.TF-TradingDate", "TXoN07C25500.TF-WRemainDate", "TXoN07C25500.TF-PreClose", "TXoN07C25500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25600", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25600.TF-Time", "TXoN07C25600.TF-TradingDate", "TXoN07C25600.TF-Open", "TXoN07C25600.TF-High", "TXoN07C25600.TF-Low", "TXoN07C25600.TF-Price", "TXoN07C25600.TF-TotalVolume", "TXoN07C25600.TF-Volume", "TXoN07C25600.TF-NWTotalBidContract", "TXoN07C25600.TF-NWTotalAskContract", "TXoN07C25600.TF-NWTotalBidSize", "TXoN07C25600.TF-NWTotalAskSize", "TXoN07C25600.TF-InSize", "TXoN07C25600.TF-OutSize", "TXoN07C25600.TF-TotalBidMatchTx", "TXoN07C25600.TF-TotalAskMatchTx", "TXoN07C25600.TF-BestBid1", "TXoN07C25600.TF-BestBid2", "TXoN07C25600.TF-BestBid3", "TXoN07C25600.TF-BestBid4", "TXoN07C25600.TF-BestBid5", "TXoN07C25600.TF-BestAsk1", "TXoN07C25600.TF-BestAsk2", "TXoN07C25600.TF-BestAsk3", "TXoN07C25600.TF-BestAsk4", "TXoN07C25600.TF-BestAsk5", "TXoN07C25600.TF-BestBidSize1", "TXoN07C25600.TF-BestBidSize2", "TXoN07C25600.TF-BestBidSize3", "TXoN07C25600.TF-BestBidSize4", "TXoN07C25600.TF-BestBidSize5", "TXoN07C25600.TF-BestAskSize1", "TXoN07C25600.TF-BestAskSize2", "TXoN07C25600.TF-BestAskSize3", "TXoN07C25600.TF-BestAskSize4", "TXoN07C25600.TF-BestAskSize5", "TXoN07C25600.TF-Name", "TXoN07C25600.TF-WContractDate", "TXoN07C25600.TF-SettlePrice", "TXoN07C25600.TF-UpLimit", "TXoN07C25600.TF-DownLimit", "TXoN07C25600.TF-OI", "TXoN07C25600.TF-TradingDate", "TXoN07C25600.TF-WRemainDate", "TXoN07C25600.TF-PreClose", "TXoN07C25600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25700", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25700.TF-Time", "TXoN07C25700.TF-TradingDate", "TXoN07C25700.TF-Open", "TXoN07C25700.TF-High", "TXoN07C25700.TF-Low", "TXoN07C25700.TF-Price", "TXoN07C25700.TF-TotalVolume", "TXoN07C25700.TF-Volume", "TXoN07C25700.TF-NWTotalBidContract", "TXoN07C25700.TF-NWTotalAskContract", "TXoN07C25700.TF-NWTotalBidSize", "TXoN07C25700.TF-NWTotalAskSize", "TXoN07C25700.TF-InSize", "TXoN07C25700.TF-OutSize", "TXoN07C25700.TF-TotalBidMatchTx", "TXoN07C25700.TF-TotalAskMatchTx", "TXoN07C25700.TF-BestBid1", "TXoN07C25700.TF-BestBid2", "TXoN07C25700.TF-BestBid3", "TXoN07C25700.TF-BestBid4", "TXoN07C25700.TF-BestBid5", "TXoN07C25700.TF-BestAsk1", "TXoN07C25700.TF-BestAsk2", "TXoN07C25700.TF-BestAsk3", "TXoN07C25700.TF-BestAsk4", "TXoN07C25700.TF-BestAsk5", "TXoN07C25700.TF-BestBidSize1", "TXoN07C25700.TF-BestBidSize2", "TXoN07C25700.TF-BestBidSize3", "TXoN07C25700.TF-BestBidSize4", "TXoN07C25700.TF-BestBidSize5", "TXoN07C25700.TF-BestAskSize1", "TXoN07C25700.TF-BestAskSize2", "TXoN07C25700.TF-BestAskSize3", "TXoN07C25700.TF-BestAskSize4", "TXoN07C25700.TF-BestAskSize5", "TXoN07C25700.TF-Name", "TXoN07C25700.TF-WContractDate", "TXoN07C25700.TF-SettlePrice", "TXoN07C25700.TF-UpLimit", "TXoN07C25700.TF-DownLimit", "TXoN07C25700.TF-OI", "TXoN07C25700.TF-TradingDate", "TXoN07C25700.TF-WRemainDate", "TXoN07C25700.TF-PreClose", "TXoN07C25700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25800", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25800.TF-Time", "TXoN07C25800.TF-TradingDate", "TXoN07C25800.TF-Open", "TXoN07C25800.TF-High", "TXoN07C25800.TF-Low", "TXoN07C25800.TF-Price", "TXoN07C25800.TF-TotalVolume", "TXoN07C25800.TF-Volume", "TXoN07C25800.TF-NWTotalBidContract", "TXoN07C25800.TF-NWTotalAskContract", "TXoN07C25800.TF-NWTotalBidSize", "TXoN07C25800.TF-NWTotalAskSize", "TXoN07C25800.TF-InSize", "TXoN07C25800.TF-OutSize", "TXoN07C25800.TF-TotalBidMatchTx", "TXoN07C25800.TF-TotalAskMatchTx", "TXoN07C25800.TF-BestBid1", "TXoN07C25800.TF-BestBid2", "TXoN07C25800.TF-BestBid3", "TXoN07C25800.TF-BestBid4", "TXoN07C25800.TF-BestBid5", "TXoN07C25800.TF-BestAsk1", "TXoN07C25800.TF-BestAsk2", "TXoN07C25800.TF-BestAsk3", "TXoN07C25800.TF-BestAsk4", "TXoN07C25800.TF-BestAsk5", "TXoN07C25800.TF-BestBidSize1", "TXoN07C25800.TF-BestBidSize2", "TXoN07C25800.TF-BestBidSize3", "TXoN07C25800.TF-BestBidSize4", "TXoN07C25800.TF-BestBidSize5", "TXoN07C25800.TF-BestAskSize1", "TXoN07C25800.TF-BestAskSize2", "TXoN07C25800.TF-BestAskSize3", "TXoN07C25800.TF-BestAskSize4", "TXoN07C25800.TF-BestAskSize5", "TXoN07C25800.TF-Name", "TXoN07C25800.TF-WContractDate", "TXoN07C25800.TF-SettlePrice", "TXoN07C25800.TF-UpLimit", "TXoN07C25800.TF-DownLimit", "TXoN07C25800.TF-OI", "TXoN07C25800.TF-TradingDate", "TXoN07C25800.TF-WRemainDate", "TXoN07C25800.TF-PreClose", "TXoN07C25800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07C25900", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07C25900.TF-Time", "TXoN07C25900.TF-TradingDate", "TXoN07C25900.TF-Open", "TXoN07C25900.TF-High", "TXoN07C25900.TF-Low", "TXoN07C25900.TF-Price", "TXoN07C25900.TF-TotalVolume", "TXoN07C25900.TF-Volume", "TXoN07C25900.TF-NWTotalBidContract", "TXoN07C25900.TF-NWTotalAskContract", "TXoN07C25900.TF-NWTotalBidSize", "TXoN07C25900.TF-NWTotalAskSize", "TXoN07C25900.TF-InSize", "TXoN07C25900.TF-OutSize", "TXoN07C25900.TF-TotalBidMatchTx", "TXoN07C25900.TF-TotalAskMatchTx", "TXoN07C25900.TF-BestBid1", "TXoN07C25900.TF-BestBid2", "TXoN07C25900.TF-BestBid3", "TXoN07C25900.TF-BestBid4", "TXoN07C25900.TF-BestBid5", "TXoN07C25900.TF-BestAsk1", "TXoN07C25900.TF-BestAsk2", "TXoN07C25900.TF-BestAsk3", "TXoN07C25900.TF-BestAsk4", "TXoN07C25900.TF-BestAsk5", "TXoN07C25900.TF-BestBidSize1", "TXoN07C25900.TF-BestBidSize2", "TXoN07C25900.TF-BestBidSize3", "TXoN07C25900.TF-BestBidSize4", "TXoN07C25900.TF-BestBidSize5", "TXoN07C25900.TF-BestAskSize1", "TXoN07C25900.TF-BestAskSize2", "TXoN07C25900.TF-BestAskSize3", "TXoN07C25900.TF-BestAskSize4", "TXoN07C25900.TF-BestAskSize5", "TXoN07C25900.TF-Name", "TXoN07C25900.TF-WContractDate", "TXoN07C25900.TF-SettlePrice", "TXoN07C25900.TF-UpLimit", "TXoN07C25900.TF-DownLimit", "TXoN07C25900.TF-OI", "TXoN07C25900.TF-TradingDate", "TXoN07C25900.TF-WRemainDate", "TXoN07C25900.TF-PreClose", "TXoN07C25900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22000", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22000.TF-Time", "TXoN07P22000.TF-TradingDate", "TXoN07P22000.TF-Open", "TXoN07P22000.TF-High", "TXoN07P22000.TF-Low", "TXoN07P22000.TF-Price", "TXoN07P22000.TF-TotalVolume", "TXoN07P22000.TF-Volume", "TXoN07P22000.TF-NWTotalBidContract", "TXoN07P22000.TF-NWTotalAskContract", "TXoN07P22000.TF-NWTotalBidSize", "TXoN07P22000.TF-NWTotalAskSize", "TXoN07P22000.TF-InSize", "TXoN07P22000.TF-OutSize", "TXoN07P22000.TF-TotalBidMatchTx", "TXoN07P22000.TF-TotalAskMatchTx", "TXoN07P22000.TF-BestBid1", "TXoN07P22000.TF-BestBid2", "TXoN07P22000.TF-BestBid3", "TXoN07P22000.TF-BestBid4", "TXoN07P22000.TF-BestBid5", "TXoN07P22000.TF-BestAsk1", "TXoN07P22000.TF-BestAsk2", "TXoN07P22000.TF-BestAsk3", "TXoN07P22000.TF-BestAsk4", "TXoN07P22000.TF-BestAsk5", "TXoN07P22000.TF-BestBidSize1", "TXoN07P22000.TF-BestBidSize2", "TXoN07P22000.TF-BestBidSize3", "TXoN07P22000.TF-BestBidSize4", "TXoN07P22000.TF-BestBidSize5", "TXoN07P22000.TF-BestAskSize1", "TXoN07P22000.TF-BestAskSize2", "TXoN07P22000.TF-BestAskSize3", "TXoN07P22000.TF-BestAskSize4", "TXoN07P22000.TF-BestAskSize5", "TXoN07P22000.TF-Name", "TXoN07P22000.TF-WContractDate", "TXoN07P22000.TF-SettlePrice", "TXoN07P22000.TF-UpLimit", "TXoN07P22000.TF-DownLimit", "TXoN07P22000.TF-OI", "TXoN07P22000.TF-TradingDate", "TXoN07P22000.TF-WRemainDate", "TXoN07P22000.TF-PreClose", "TXoN07P22000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22100", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22100.TF-Time", "TXoN07P22100.TF-TradingDate", "TXoN07P22100.TF-Open", "TXoN07P22100.TF-High", "TXoN07P22100.TF-Low", "TXoN07P22100.TF-Price", "TXoN07P22100.TF-TotalVolume", "TXoN07P22100.TF-Volume", "TXoN07P22100.TF-NWTotalBidContract", "TXoN07P22100.TF-NWTotalAskContract", "TXoN07P22100.TF-NWTotalBidSize", "TXoN07P22100.TF-NWTotalAskSize", "TXoN07P22100.TF-InSize", "TXoN07P22100.TF-OutSize", "TXoN07P22100.TF-TotalBidMatchTx", "TXoN07P22100.TF-TotalAskMatchTx", "TXoN07P22100.TF-BestBid1", "TXoN07P22100.TF-BestBid2", "TXoN07P22100.TF-BestBid3", "TXoN07P22100.TF-BestBid4", "TXoN07P22100.TF-BestBid5", "TXoN07P22100.TF-BestAsk1", "TXoN07P22100.TF-BestAsk2", "TXoN07P22100.TF-BestAsk3", "TXoN07P22100.TF-BestAsk4", "TXoN07P22100.TF-BestAsk5", "TXoN07P22100.TF-BestBidSize1", "TXoN07P22100.TF-BestBidSize2", "TXoN07P22100.TF-BestBidSize3", "TXoN07P22100.TF-BestBidSize4", "TXoN07P22100.TF-BestBidSize5", "TXoN07P22100.TF-BestAskSize1", "TXoN07P22100.TF-BestAskSize2", "TXoN07P22100.TF-BestAskSize3", "TXoN07P22100.TF-BestAskSize4", "TXoN07P22100.TF-BestAskSize5", "TXoN07P22100.TF-Name", "TXoN07P22100.TF-WContractDate", "TXoN07P22100.TF-SettlePrice", "TXoN07P22100.TF-UpLimit", "TXoN07P22100.TF-DownLimit", "TXoN07P22100.TF-OI", "TXoN07P22100.TF-TradingDate", "TXoN07P22100.TF-WRemainDate", "TXoN07P22100.TF-PreClose", "TXoN07P22100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22200", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22200.TF-Time", "TXoN07P22200.TF-TradingDate", "TXoN07P22200.TF-Open", "TXoN07P22200.TF-High", "TXoN07P22200.TF-Low", "TXoN07P22200.TF-Price", "TXoN07P22200.TF-TotalVolume", "TXoN07P22200.TF-Volume", "TXoN07P22200.TF-NWTotalBidContract", "TXoN07P22200.TF-NWTotalAskContract", "TXoN07P22200.TF-NWTotalBidSize", "TXoN07P22200.TF-NWTotalAskSize", "TXoN07P22200.TF-InSize", "TXoN07P22200.TF-OutSize", "TXoN07P22200.TF-TotalBidMatchTx", "TXoN07P22200.TF-TotalAskMatchTx", "TXoN07P22200.TF-BestBid1", "TXoN07P22200.TF-BestBid2", "TXoN07P22200.TF-BestBid3", "TXoN07P22200.TF-BestBid4", "TXoN07P22200.TF-BestBid5", "TXoN07P22200.TF-BestAsk1", "TXoN07P22200.TF-BestAsk2", "TXoN07P22200.TF-BestAsk3", "TXoN07P22200.TF-BestAsk4", "TXoN07P22200.TF-BestAsk5", "TXoN07P22200.TF-BestBidSize1", "TXoN07P22200.TF-BestBidSize2", "TXoN07P22200.TF-BestBidSize3", "TXoN07P22200.TF-BestBidSize4", "TXoN07P22200.TF-BestBidSize5", "TXoN07P22200.TF-BestAskSize1", "TXoN07P22200.TF-BestAskSize2", "TXoN07P22200.TF-BestAskSize3", "TXoN07P22200.TF-BestAskSize4", "TXoN07P22200.TF-BestAskSize5", "TXoN07P22200.TF-Name", "TXoN07P22200.TF-WContractDate", "TXoN07P22200.TF-SettlePrice", "TXoN07P22200.TF-UpLimit", "TXoN07P22200.TF-DownLimit", "TXoN07P22200.TF-OI", "TXoN07P22200.TF-TradingDate", "TXoN07P22200.TF-WRemainDate", "TXoN07P22200.TF-PreClose", "TXoN07P22200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22300", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22300.TF-Time", "TXoN07P22300.TF-TradingDate", "TXoN07P22300.TF-Open", "TXoN07P22300.TF-High", "TXoN07P22300.TF-Low", "TXoN07P22300.TF-Price", "TXoN07P22300.TF-TotalVolume", "TXoN07P22300.TF-Volume", "TXoN07P22300.TF-NWTotalBidContract", "TXoN07P22300.TF-NWTotalAskContract", "TXoN07P22300.TF-NWTotalBidSize", "TXoN07P22300.TF-NWTotalAskSize", "TXoN07P22300.TF-InSize", "TXoN07P22300.TF-OutSize", "TXoN07P22300.TF-TotalBidMatchTx", "TXoN07P22300.TF-TotalAskMatchTx", "TXoN07P22300.TF-BestBid1", "TXoN07P22300.TF-BestBid2", "TXoN07P22300.TF-BestBid3", "TXoN07P22300.TF-BestBid4", "TXoN07P22300.TF-BestBid5", "TXoN07P22300.TF-BestAsk1", "TXoN07P22300.TF-BestAsk2", "TXoN07P22300.TF-BestAsk3", "TXoN07P22300.TF-BestAsk4", "TXoN07P22300.TF-BestAsk5", "TXoN07P22300.TF-BestBidSize1", "TXoN07P22300.TF-BestBidSize2", "TXoN07P22300.TF-BestBidSize3", "TXoN07P22300.TF-BestBidSize4", "TXoN07P22300.TF-BestBidSize5", "TXoN07P22300.TF-BestAskSize1", "TXoN07P22300.TF-BestAskSize2", "TXoN07P22300.TF-BestAskSize3", "TXoN07P22300.TF-BestAskSize4", "TXoN07P22300.TF-BestAskSize5", "TXoN07P22300.TF-Name", "TXoN07P22300.TF-WContractDate", "TXoN07P22300.TF-SettlePrice", "TXoN07P22300.TF-UpLimit", "TXoN07P22300.TF-DownLimit", "TXoN07P22300.TF-OI", "TXoN07P22300.TF-TradingDate", "TXoN07P22300.TF-WRemainDate", "TXoN07P22300.TF-PreClose", "TXoN07P22300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22400", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22400.TF-Time", "TXoN07P22400.TF-TradingDate", "TXoN07P22400.TF-Open", "TXoN07P22400.TF-High", "TXoN07P22400.TF-Low", "TXoN07P22400.TF-Price", "TXoN07P22400.TF-TotalVolume", "TXoN07P22400.TF-Volume", "TXoN07P22400.TF-NWTotalBidContract", "TXoN07P22400.TF-NWTotalAskContract", "TXoN07P22400.TF-NWTotalBidSize", "TXoN07P22400.TF-NWTotalAskSize", "TXoN07P22400.TF-InSize", "TXoN07P22400.TF-OutSize", "TXoN07P22400.TF-TotalBidMatchTx", "TXoN07P22400.TF-TotalAskMatchTx", "TXoN07P22400.TF-BestBid1", "TXoN07P22400.TF-BestBid2", "TXoN07P22400.TF-BestBid3", "TXoN07P22400.TF-BestBid4", "TXoN07P22400.TF-BestBid5", "TXoN07P22400.TF-BestAsk1", "TXoN07P22400.TF-BestAsk2", "TXoN07P22400.TF-BestAsk3", "TXoN07P22400.TF-BestAsk4", "TXoN07P22400.TF-BestAsk5", "TXoN07P22400.TF-BestBidSize1", "TXoN07P22400.TF-BestBidSize2", "TXoN07P22400.TF-BestBidSize3", "TXoN07P22400.TF-BestBidSize4", "TXoN07P22400.TF-BestBidSize5", "TXoN07P22400.TF-BestAskSize1", "TXoN07P22400.TF-BestAskSize2", "TXoN07P22400.TF-BestAskSize3", "TXoN07P22400.TF-BestAskSize4", "TXoN07P22400.TF-BestAskSize5", "TXoN07P22400.TF-Name", "TXoN07P22400.TF-WContractDate", "TXoN07P22400.TF-SettlePrice", "TXoN07P22400.TF-UpLimit", "TXoN07P22400.TF-DownLimit", "TXoN07P22400.TF-OI", "TXoN07P22400.TF-TradingDate", "TXoN07P22400.TF-WRemainDate", "TXoN07P22400.TF-PreClose", "TXoN07P22400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22500", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22500.TF-Time", "TXoN07P22500.TF-TradingDate", "TXoN07P22500.TF-Open", "TXoN07P22500.TF-High", "TXoN07P22500.TF-Low", "TXoN07P22500.TF-Price", "TXoN07P22500.TF-TotalVolume", "TXoN07P22500.TF-Volume", "TXoN07P22500.TF-NWTotalBidContract", "TXoN07P22500.TF-NWTotalAskContract", "TXoN07P22500.TF-NWTotalBidSize", "TXoN07P22500.TF-NWTotalAskSize", "TXoN07P22500.TF-InSize", "TXoN07P22500.TF-OutSize", "TXoN07P22500.TF-TotalBidMatchTx", "TXoN07P22500.TF-TotalAskMatchTx", "TXoN07P22500.TF-BestBid1", "TXoN07P22500.TF-BestBid2", "TXoN07P22500.TF-BestBid3", "TXoN07P22500.TF-BestBid4", "TXoN07P22500.TF-BestBid5", "TXoN07P22500.TF-BestAsk1", "TXoN07P22500.TF-BestAsk2", "TXoN07P22500.TF-BestAsk3", "TXoN07P22500.TF-BestAsk4", "TXoN07P22500.TF-BestAsk5", "TXoN07P22500.TF-BestBidSize1", "TXoN07P22500.TF-BestBidSize2", "TXoN07P22500.TF-BestBidSize3", "TXoN07P22500.TF-BestBidSize4", "TXoN07P22500.TF-BestBidSize5", "TXoN07P22500.TF-BestAskSize1", "TXoN07P22500.TF-BestAskSize2", "TXoN07P22500.TF-BestAskSize3", "TXoN07P22500.TF-BestAskSize4", "TXoN07P22500.TF-BestAskSize5", "TXoN07P22500.TF-Name", "TXoN07P22500.TF-WContractDate", "TXoN07P22500.TF-SettlePrice", "TXoN07P22500.TF-UpLimit", "TXoN07P22500.TF-DownLimit", "TXoN07P22500.TF-OI", "TXoN07P22500.TF-TradingDate", "TXoN07P22500.TF-WRemainDate", "TXoN07P22500.TF-PreClose", "TXoN07P22500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22600", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22600.TF-Time", "TXoN07P22600.TF-TradingDate", "TXoN07P22600.TF-Open", "TXoN07P22600.TF-High", "TXoN07P22600.TF-Low", "TXoN07P22600.TF-Price", "TXoN07P22600.TF-TotalVolume", "TXoN07P22600.TF-Volume", "TXoN07P22600.TF-NWTotalBidContract", "TXoN07P22600.TF-NWTotalAskContract", "TXoN07P22600.TF-NWTotalBidSize", "TXoN07P22600.TF-NWTotalAskSize", "TXoN07P22600.TF-InSize", "TXoN07P22600.TF-OutSize", "TXoN07P22600.TF-TotalBidMatchTx", "TXoN07P22600.TF-TotalAskMatchTx", "TXoN07P22600.TF-BestBid1", "TXoN07P22600.TF-BestBid2", "TXoN07P22600.TF-BestBid3", "TXoN07P22600.TF-BestBid4", "TXoN07P22600.TF-BestBid5", "TXoN07P22600.TF-BestAsk1", "TXoN07P22600.TF-BestAsk2", "TXoN07P22600.TF-BestAsk3", "TXoN07P22600.TF-BestAsk4", "TXoN07P22600.TF-BestAsk5", "TXoN07P22600.TF-BestBidSize1", "TXoN07P22600.TF-BestBidSize2", "TXoN07P22600.TF-BestBidSize3", "TXoN07P22600.TF-BestBidSize4", "TXoN07P22600.TF-BestBidSize5", "TXoN07P22600.TF-BestAskSize1", "TXoN07P22600.TF-BestAskSize2", "TXoN07P22600.TF-BestAskSize3", "TXoN07P22600.TF-BestAskSize4", "TXoN07P22600.TF-BestAskSize5", "TXoN07P22600.TF-Name", "TXoN07P22600.TF-WContractDate", "TXoN07P22600.TF-SettlePrice", "TXoN07P22600.TF-UpLimit", "TXoN07P22600.TF-DownLimit", "TXoN07P22600.TF-OI", "TXoN07P22600.TF-TradingDate", "TXoN07P22600.TF-WRemainDate", "TXoN07P22600.TF-PreClose", "TXoN07P22600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22700", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22700.TF-Time", "TXoN07P22700.TF-TradingDate", "TXoN07P22700.TF-Open", "TXoN07P22700.TF-High", "TXoN07P22700.TF-Low", "TXoN07P22700.TF-Price", "TXoN07P22700.TF-TotalVolume", "TXoN07P22700.TF-Volume", "TXoN07P22700.TF-NWTotalBidContract", "TXoN07P22700.TF-NWTotalAskContract", "TXoN07P22700.TF-NWTotalBidSize", "TXoN07P22700.TF-NWTotalAskSize", "TXoN07P22700.TF-InSize", "TXoN07P22700.TF-OutSize", "TXoN07P22700.TF-TotalBidMatchTx", "TXoN07P22700.TF-TotalAskMatchTx", "TXoN07P22700.TF-BestBid1", "TXoN07P22700.TF-BestBid2", "TXoN07P22700.TF-BestBid3", "TXoN07P22700.TF-BestBid4", "TXoN07P22700.TF-BestBid5", "TXoN07P22700.TF-BestAsk1", "TXoN07P22700.TF-BestAsk2", "TXoN07P22700.TF-BestAsk3", "TXoN07P22700.TF-BestAsk4", "TXoN07P22700.TF-BestAsk5", "TXoN07P22700.TF-BestBidSize1", "TXoN07P22700.TF-BestBidSize2", "TXoN07P22700.TF-BestBidSize3", "TXoN07P22700.TF-BestBidSize4", "TXoN07P22700.TF-BestBidSize5", "TXoN07P22700.TF-BestAskSize1", "TXoN07P22700.TF-BestAskSize2", "TXoN07P22700.TF-BestAskSize3", "TXoN07P22700.TF-BestAskSize4", "TXoN07P22700.TF-BestAskSize5", "TXoN07P22700.TF-Name", "TXoN07P22700.TF-WContractDate", "TXoN07P22700.TF-SettlePrice", "TXoN07P22700.TF-UpLimit", "TXoN07P22700.TF-DownLimit", "TXoN07P22700.TF-OI", "TXoN07P22700.TF-TradingDate", "TXoN07P22700.TF-WRemainDate", "TXoN07P22700.TF-PreClose", "TXoN07P22700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22800", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22800.TF-Time", "TXoN07P22800.TF-TradingDate", "TXoN07P22800.TF-Open", "TXoN07P22800.TF-High", "TXoN07P22800.TF-Low", "TXoN07P22800.TF-Price", "TXoN07P22800.TF-TotalVolume", "TXoN07P22800.TF-Volume", "TXoN07P22800.TF-NWTotalBidContract", "TXoN07P22800.TF-NWTotalAskContract", "TXoN07P22800.TF-NWTotalBidSize", "TXoN07P22800.TF-NWTotalAskSize", "TXoN07P22800.TF-InSize", "TXoN07P22800.TF-OutSize", "TXoN07P22800.TF-TotalBidMatchTx", "TXoN07P22800.TF-TotalAskMatchTx", "TXoN07P22800.TF-BestBid1", "TXoN07P22800.TF-BestBid2", "TXoN07P22800.TF-BestBid3", "TXoN07P22800.TF-BestBid4", "TXoN07P22800.TF-BestBid5", "TXoN07P22800.TF-BestAsk1", "TXoN07P22800.TF-BestAsk2", "TXoN07P22800.TF-BestAsk3", "TXoN07P22800.TF-BestAsk4", "TXoN07P22800.TF-BestAsk5", "TXoN07P22800.TF-BestBidSize1", "TXoN07P22800.TF-BestBidSize2", "TXoN07P22800.TF-BestBidSize3", "TXoN07P22800.TF-BestBidSize4", "TXoN07P22800.TF-BestBidSize5", "TXoN07P22800.TF-BestAskSize1", "TXoN07P22800.TF-BestAskSize2", "TXoN07P22800.TF-BestAskSize3", "TXoN07P22800.TF-BestAskSize4", "TXoN07P22800.TF-BestAskSize5", "TXoN07P22800.TF-Name", "TXoN07P22800.TF-WContractDate", "TXoN07P22800.TF-SettlePrice", "TXoN07P22800.TF-UpLimit", "TXoN07P22800.TF-DownLimit", "TXoN07P22800.TF-OI", "TXoN07P22800.TF-TradingDate", "TXoN07P22800.TF-WRemainDate", "TXoN07P22800.TF-PreClose", "TXoN07P22800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P22900", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P22900.TF-Time", "TXoN07P22900.TF-TradingDate", "TXoN07P22900.TF-Open", "TXoN07P22900.TF-High", "TXoN07P22900.TF-Low", "TXoN07P22900.TF-Price", "TXoN07P22900.TF-TotalVolume", "TXoN07P22900.TF-Volume", "TXoN07P22900.TF-NWTotalBidContract", "TXoN07P22900.TF-NWTotalAskContract", "TXoN07P22900.TF-NWTotalBidSize", "TXoN07P22900.TF-NWTotalAskSize", "TXoN07P22900.TF-InSize", "TXoN07P22900.TF-OutSize", "TXoN07P22900.TF-TotalBidMatchTx", "TXoN07P22900.TF-TotalAskMatchTx", "TXoN07P22900.TF-BestBid1", "TXoN07P22900.TF-BestBid2", "TXoN07P22900.TF-BestBid3", "TXoN07P22900.TF-BestBid4", "TXoN07P22900.TF-BestBid5", "TXoN07P22900.TF-BestAsk1", "TXoN07P22900.TF-BestAsk2", "TXoN07P22900.TF-BestAsk3", "TXoN07P22900.TF-BestAsk4", "TXoN07P22900.TF-BestAsk5", "TXoN07P22900.TF-BestBidSize1", "TXoN07P22900.TF-BestBidSize2", "TXoN07P22900.TF-BestBidSize3", "TXoN07P22900.TF-BestBidSize4", "TXoN07P22900.TF-BestBidSize5", "TXoN07P22900.TF-BestAskSize1", "TXoN07P22900.TF-BestAskSize2", "TXoN07P22900.TF-BestAskSize3", "TXoN07P22900.TF-BestAskSize4", "TXoN07P22900.TF-BestAskSize5", "TXoN07P22900.TF-Name", "TXoN07P22900.TF-WContractDate", "TXoN07P22900.TF-SettlePrice", "TXoN07P22900.TF-UpLimit", "TXoN07P22900.TF-DownLimit", "TXoN07P22900.TF-OI", "TXoN07P22900.TF-TradingDate", "TXoN07P22900.TF-WRemainDate", "TXoN07P22900.TF-PreClose", "TXoN07P22900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23000", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23000.TF-Time", "TXoN07P23000.TF-TradingDate", "TXoN07P23000.TF-Open", "TXoN07P23000.TF-High", "TXoN07P23000.TF-Low", "TXoN07P23000.TF-Price", "TXoN07P23000.TF-TotalVolume", "TXoN07P23000.TF-Volume", "TXoN07P23000.TF-NWTotalBidContract", "TXoN07P23000.TF-NWTotalAskContract", "TXoN07P23000.TF-NWTotalBidSize", "TXoN07P23000.TF-NWTotalAskSize", "TXoN07P23000.TF-InSize", "TXoN07P23000.TF-OutSize", "TXoN07P23000.TF-TotalBidMatchTx", "TXoN07P23000.TF-TotalAskMatchTx", "TXoN07P23000.TF-BestBid1", "TXoN07P23000.TF-BestBid2", "TXoN07P23000.TF-BestBid3", "TXoN07P23000.TF-BestBid4", "TXoN07P23000.TF-BestBid5", "TXoN07P23000.TF-BestAsk1", "TXoN07P23000.TF-BestAsk2", "TXoN07P23000.TF-BestAsk3", "TXoN07P23000.TF-BestAsk4", "TXoN07P23000.TF-BestAsk5", "TXoN07P23000.TF-BestBidSize1", "TXoN07P23000.TF-BestBidSize2", "TXoN07P23000.TF-BestBidSize3", "TXoN07P23000.TF-BestBidSize4", "TXoN07P23000.TF-BestBidSize5", "TXoN07P23000.TF-BestAskSize1", "TXoN07P23000.TF-BestAskSize2", "TXoN07P23000.TF-BestAskSize3", "TXoN07P23000.TF-BestAskSize4", "TXoN07P23000.TF-BestAskSize5", "TXoN07P23000.TF-Name", "TXoN07P23000.TF-WContractDate", "TXoN07P23000.TF-SettlePrice", "TXoN07P23000.TF-UpLimit", "TXoN07P23000.TF-DownLimit", "TXoN07P23000.TF-OI", "TXoN07P23000.TF-TradingDate", "TXoN07P23000.TF-WRemainDate", "TXoN07P23000.TF-PreClose", "TXoN07P23000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23100", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23100.TF-Time", "TXoN07P23100.TF-TradingDate", "TXoN07P23100.TF-Open", "TXoN07P23100.TF-High", "TXoN07P23100.TF-Low", "TXoN07P23100.TF-Price", "TXoN07P23100.TF-TotalVolume", "TXoN07P23100.TF-Volume", "TXoN07P23100.TF-NWTotalBidContract", "TXoN07P23100.TF-NWTotalAskContract", "TXoN07P23100.TF-NWTotalBidSize", "TXoN07P23100.TF-NWTotalAskSize", "TXoN07P23100.TF-InSize", "TXoN07P23100.TF-OutSize", "TXoN07P23100.TF-TotalBidMatchTx", "TXoN07P23100.TF-TotalAskMatchTx", "TXoN07P23100.TF-BestBid1", "TXoN07P23100.TF-BestBid2", "TXoN07P23100.TF-BestBid3", "TXoN07P23100.TF-BestBid4", "TXoN07P23100.TF-BestBid5", "TXoN07P23100.TF-BestAsk1", "TXoN07P23100.TF-BestAsk2", "TXoN07P23100.TF-BestAsk3", "TXoN07P23100.TF-BestAsk4", "TXoN07P23100.TF-BestAsk5", "TXoN07P23100.TF-BestBidSize1", "TXoN07P23100.TF-BestBidSize2", "TXoN07P23100.TF-BestBidSize3", "TXoN07P23100.TF-BestBidSize4", "TXoN07P23100.TF-BestBidSize5", "TXoN07P23100.TF-BestAskSize1", "TXoN07P23100.TF-BestAskSize2", "TXoN07P23100.TF-BestAskSize3", "TXoN07P23100.TF-BestAskSize4", "TXoN07P23100.TF-BestAskSize5", "TXoN07P23100.TF-Name", "TXoN07P23100.TF-WContractDate", "TXoN07P23100.TF-SettlePrice", "TXoN07P23100.TF-UpLimit", "TXoN07P23100.TF-DownLimit", "TXoN07P23100.TF-OI", "TXoN07P23100.TF-TradingDate", "TXoN07P23100.TF-WRemainDate", "TXoN07P23100.TF-PreClose", "TXoN07P23100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23200", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23200.TF-Time", "TXoN07P23200.TF-TradingDate", "TXoN07P23200.TF-Open", "TXoN07P23200.TF-High", "TXoN07P23200.TF-Low", "TXoN07P23200.TF-Price", "TXoN07P23200.TF-TotalVolume", "TXoN07P23200.TF-Volume", "TXoN07P23200.TF-NWTotalBidContract", "TXoN07P23200.TF-NWTotalAskContract", "TXoN07P23200.TF-NWTotalBidSize", "TXoN07P23200.TF-NWTotalAskSize", "TXoN07P23200.TF-InSize", "TXoN07P23200.TF-OutSize", "TXoN07P23200.TF-TotalBidMatchTx", "TXoN07P23200.TF-TotalAskMatchTx", "TXoN07P23200.TF-BestBid1", "TXoN07P23200.TF-BestBid2", "TXoN07P23200.TF-BestBid3", "TXoN07P23200.TF-BestBid4", "TXoN07P23200.TF-BestBid5", "TXoN07P23200.TF-BestAsk1", "TXoN07P23200.TF-BestAsk2", "TXoN07P23200.TF-BestAsk3", "TXoN07P23200.TF-BestAsk4", "TXoN07P23200.TF-BestAsk5", "TXoN07P23200.TF-BestBidSize1", "TXoN07P23200.TF-BestBidSize2", "TXoN07P23200.TF-BestBidSize3", "TXoN07P23200.TF-BestBidSize4", "TXoN07P23200.TF-BestBidSize5", "TXoN07P23200.TF-BestAskSize1", "TXoN07P23200.TF-BestAskSize2", "TXoN07P23200.TF-BestAskSize3", "TXoN07P23200.TF-BestAskSize4", "TXoN07P23200.TF-BestAskSize5", "TXoN07P23200.TF-Name", "TXoN07P23200.TF-WContractDate", "TXoN07P23200.TF-SettlePrice", "TXoN07P23200.TF-UpLimit", "TXoN07P23200.TF-DownLimit", "TXoN07P23200.TF-OI", "TXoN07P23200.TF-TradingDate", "TXoN07P23200.TF-WRemainDate", "TXoN07P23200.TF-PreClose", "TXoN07P23200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23300", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23300.TF-Time", "TXoN07P23300.TF-TradingDate", "TXoN07P23300.TF-Open", "TXoN07P23300.TF-High", "TXoN07P23300.TF-Low", "TXoN07P23300.TF-Price", "TXoN07P23300.TF-TotalVolume", "TXoN07P23300.TF-Volume", "TXoN07P23300.TF-NWTotalBidContract", "TXoN07P23300.TF-NWTotalAskContract", "TXoN07P23300.TF-NWTotalBidSize", "TXoN07P23300.TF-NWTotalAskSize", "TXoN07P23300.TF-InSize", "TXoN07P23300.TF-OutSize", "TXoN07P23300.TF-TotalBidMatchTx", "TXoN07P23300.TF-TotalAskMatchTx", "TXoN07P23300.TF-BestBid1", "TXoN07P23300.TF-BestBid2", "TXoN07P23300.TF-BestBid3", "TXoN07P23300.TF-BestBid4", "TXoN07P23300.TF-BestBid5", "TXoN07P23300.TF-BestAsk1", "TXoN07P23300.TF-BestAsk2", "TXoN07P23300.TF-BestAsk3", "TXoN07P23300.TF-BestAsk4", "TXoN07P23300.TF-BestAsk5", "TXoN07P23300.TF-BestBidSize1", "TXoN07P23300.TF-BestBidSize2", "TXoN07P23300.TF-BestBidSize3", "TXoN07P23300.TF-BestBidSize4", "TXoN07P23300.TF-BestBidSize5", "TXoN07P23300.TF-BestAskSize1", "TXoN07P23300.TF-BestAskSize2", "TXoN07P23300.TF-BestAskSize3", "TXoN07P23300.TF-BestAskSize4", "TXoN07P23300.TF-BestAskSize5", "TXoN07P23300.TF-Name", "TXoN07P23300.TF-WContractDate", "TXoN07P23300.TF-SettlePrice", "TXoN07P23300.TF-UpLimit", "TXoN07P23300.TF-DownLimit", "TXoN07P23300.TF-OI", "TXoN07P23300.TF-TradingDate", "TXoN07P23300.TF-WRemainDate", "TXoN07P23300.TF-PreClose", "TXoN07P23300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23400", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23400.TF-Time", "TXoN07P23400.TF-TradingDate", "TXoN07P23400.TF-Open", "TXoN07P23400.TF-High", "TXoN07P23400.TF-Low", "TXoN07P23400.TF-Price", "TXoN07P23400.TF-TotalVolume", "TXoN07P23400.TF-Volume", "TXoN07P23400.TF-NWTotalBidContract", "TXoN07P23400.TF-NWTotalAskContract", "TXoN07P23400.TF-NWTotalBidSize", "TXoN07P23400.TF-NWTotalAskSize", "TXoN07P23400.TF-InSize", "TXoN07P23400.TF-OutSize", "TXoN07P23400.TF-TotalBidMatchTx", "TXoN07P23400.TF-TotalAskMatchTx", "TXoN07P23400.TF-BestBid1", "TXoN07P23400.TF-BestBid2", "TXoN07P23400.TF-BestBid3", "TXoN07P23400.TF-BestBid4", "TXoN07P23400.TF-BestBid5", "TXoN07P23400.TF-BestAsk1", "TXoN07P23400.TF-BestAsk2", "TXoN07P23400.TF-BestAsk3", "TXoN07P23400.TF-BestAsk4", "TXoN07P23400.TF-BestAsk5", "TXoN07P23400.TF-BestBidSize1", "TXoN07P23400.TF-BestBidSize2", "TXoN07P23400.TF-BestBidSize3", "TXoN07P23400.TF-BestBidSize4", "TXoN07P23400.TF-BestBidSize5", "TXoN07P23400.TF-BestAskSize1", "TXoN07P23400.TF-BestAskSize2", "TXoN07P23400.TF-BestAskSize3", "TXoN07P23400.TF-BestAskSize4", "TXoN07P23400.TF-BestAskSize5", "TXoN07P23400.TF-Name", "TXoN07P23400.TF-WContractDate", "TXoN07P23400.TF-SettlePrice", "TXoN07P23400.TF-UpLimit", "TXoN07P23400.TF-DownLimit", "TXoN07P23400.TF-OI", "TXoN07P23400.TF-TradingDate", "TXoN07P23400.TF-WRemainDate", "TXoN07P23400.TF-PreClose", "TXoN07P23400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23500", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23500.TF-Time", "TXoN07P23500.TF-TradingDate", "TXoN07P23500.TF-Open", "TXoN07P23500.TF-High", "TXoN07P23500.TF-Low", "TXoN07P23500.TF-Price", "TXoN07P23500.TF-TotalVolume", "TXoN07P23500.TF-Volume", "TXoN07P23500.TF-NWTotalBidContract", "TXoN07P23500.TF-NWTotalAskContract", "TXoN07P23500.TF-NWTotalBidSize", "TXoN07P23500.TF-NWTotalAskSize", "TXoN07P23500.TF-InSize", "TXoN07P23500.TF-OutSize", "TXoN07P23500.TF-TotalBidMatchTx", "TXoN07P23500.TF-TotalAskMatchTx", "TXoN07P23500.TF-BestBid1", "TXoN07P23500.TF-BestBid2", "TXoN07P23500.TF-BestBid3", "TXoN07P23500.TF-BestBid4", "TXoN07P23500.TF-BestBid5", "TXoN07P23500.TF-BestAsk1", "TXoN07P23500.TF-BestAsk2", "TXoN07P23500.TF-BestAsk3", "TXoN07P23500.TF-BestAsk4", "TXoN07P23500.TF-BestAsk5", "TXoN07P23500.TF-BestBidSize1", "TXoN07P23500.TF-BestBidSize2", "TXoN07P23500.TF-BestBidSize3", "TXoN07P23500.TF-BestBidSize4", "TXoN07P23500.TF-BestBidSize5", "TXoN07P23500.TF-BestAskSize1", "TXoN07P23500.TF-BestAskSize2", "TXoN07P23500.TF-BestAskSize3", "TXoN07P23500.TF-BestAskSize4", "TXoN07P23500.TF-BestAskSize5", "TXoN07P23500.TF-Name", "TXoN07P23500.TF-WContractDate", "TXoN07P23500.TF-SettlePrice", "TXoN07P23500.TF-UpLimit", "TXoN07P23500.TF-DownLimit", "TXoN07P23500.TF-OI", "TXoN07P23500.TF-TradingDate", "TXoN07P23500.TF-WRemainDate", "TXoN07P23500.TF-PreClose", "TXoN07P23500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23600", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23600.TF-Time", "TXoN07P23600.TF-TradingDate", "TXoN07P23600.TF-Open", "TXoN07P23600.TF-High", "TXoN07P23600.TF-Low", "TXoN07P23600.TF-Price", "TXoN07P23600.TF-TotalVolume", "TXoN07P23600.TF-Volume", "TXoN07P23600.TF-NWTotalBidContract", "TXoN07P23600.TF-NWTotalAskContract", "TXoN07P23600.TF-NWTotalBidSize", "TXoN07P23600.TF-NWTotalAskSize", "TXoN07P23600.TF-InSize", "TXoN07P23600.TF-OutSize", "TXoN07P23600.TF-TotalBidMatchTx", "TXoN07P23600.TF-TotalAskMatchTx", "TXoN07P23600.TF-BestBid1", "TXoN07P23600.TF-BestBid2", "TXoN07P23600.TF-BestBid3", "TXoN07P23600.TF-BestBid4", "TXoN07P23600.TF-BestBid5", "TXoN07P23600.TF-BestAsk1", "TXoN07P23600.TF-BestAsk2", "TXoN07P23600.TF-BestAsk3", "TXoN07P23600.TF-BestAsk4", "TXoN07P23600.TF-BestAsk5", "TXoN07P23600.TF-BestBidSize1", "TXoN07P23600.TF-BestBidSize2", "TXoN07P23600.TF-BestBidSize3", "TXoN07P23600.TF-BestBidSize4", "TXoN07P23600.TF-BestBidSize5", "TXoN07P23600.TF-BestAskSize1", "TXoN07P23600.TF-BestAskSize2", "TXoN07P23600.TF-BestAskSize3", "TXoN07P23600.TF-BestAskSize4", "TXoN07P23600.TF-BestAskSize5", "TXoN07P23600.TF-Name", "TXoN07P23600.TF-WContractDate", "TXoN07P23600.TF-SettlePrice", "TXoN07P23600.TF-UpLimit", "TXoN07P23600.TF-DownLimit", "TXoN07P23600.TF-OI", "TXoN07P23600.TF-TradingDate", "TXoN07P23600.TF-WRemainDate", "TXoN07P23600.TF-PreClose", "TXoN07P23600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23700", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23700.TF-Time", "TXoN07P23700.TF-TradingDate", "TXoN07P23700.TF-Open", "TXoN07P23700.TF-High", "TXoN07P23700.TF-Low", "TXoN07P23700.TF-Price", "TXoN07P23700.TF-TotalVolume", "TXoN07P23700.TF-Volume", "TXoN07P23700.TF-NWTotalBidContract", "TXoN07P23700.TF-NWTotalAskContract", "TXoN07P23700.TF-NWTotalBidSize", "TXoN07P23700.TF-NWTotalAskSize", "TXoN07P23700.TF-InSize", "TXoN07P23700.TF-OutSize", "TXoN07P23700.TF-TotalBidMatchTx", "TXoN07P23700.TF-TotalAskMatchTx", "TXoN07P23700.TF-BestBid1", "TXoN07P23700.TF-BestBid2", "TXoN07P23700.TF-BestBid3", "TXoN07P23700.TF-BestBid4", "TXoN07P23700.TF-BestBid5", "TXoN07P23700.TF-BestAsk1", "TXoN07P23700.TF-BestAsk2", "TXoN07P23700.TF-BestAsk3", "TXoN07P23700.TF-BestAsk4", "TXoN07P23700.TF-BestAsk5", "TXoN07P23700.TF-BestBidSize1", "TXoN07P23700.TF-BestBidSize2", "TXoN07P23700.TF-BestBidSize3", "TXoN07P23700.TF-BestBidSize4", "TXoN07P23700.TF-BestBidSize5", "TXoN07P23700.TF-BestAskSize1", "TXoN07P23700.TF-BestAskSize2", "TXoN07P23700.TF-BestAskSize3", "TXoN07P23700.TF-BestAskSize4", "TXoN07P23700.TF-BestAskSize5", "TXoN07P23700.TF-Name", "TXoN07P23700.TF-WContractDate", "TXoN07P23700.TF-SettlePrice", "TXoN07P23700.TF-UpLimit", "TXoN07P23700.TF-DownLimit", "TXoN07P23700.TF-OI", "TXoN07P23700.TF-TradingDate", "TXoN07P23700.TF-WRemainDate", "TXoN07P23700.TF-PreClose", "TXoN07P23700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23800", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23800.TF-Time", "TXoN07P23800.TF-TradingDate", "TXoN07P23800.TF-Open", "TXoN07P23800.TF-High", "TXoN07P23800.TF-Low", "TXoN07P23800.TF-Price", "TXoN07P23800.TF-TotalVolume", "TXoN07P23800.TF-Volume", "TXoN07P23800.TF-NWTotalBidContract", "TXoN07P23800.TF-NWTotalAskContract", "TXoN07P23800.TF-NWTotalBidSize", "TXoN07P23800.TF-NWTotalAskSize", "TXoN07P23800.TF-InSize", "TXoN07P23800.TF-OutSize", "TXoN07P23800.TF-TotalBidMatchTx", "TXoN07P23800.TF-TotalAskMatchTx", "TXoN07P23800.TF-BestBid1", "TXoN07P23800.TF-BestBid2", "TXoN07P23800.TF-BestBid3", "TXoN07P23800.TF-BestBid4", "TXoN07P23800.TF-BestBid5", "TXoN07P23800.TF-BestAsk1", "TXoN07P23800.TF-BestAsk2", "TXoN07P23800.TF-BestAsk3", "TXoN07P23800.TF-BestAsk4", "TXoN07P23800.TF-BestAsk5", "TXoN07P23800.TF-BestBidSize1", "TXoN07P23800.TF-BestBidSize2", "TXoN07P23800.TF-BestBidSize3", "TXoN07P23800.TF-BestBidSize4", "TXoN07P23800.TF-BestBidSize5", "TXoN07P23800.TF-BestAskSize1", "TXoN07P23800.TF-BestAskSize2", "TXoN07P23800.TF-BestAskSize3", "TXoN07P23800.TF-BestAskSize4", "TXoN07P23800.TF-BestAskSize5", "TXoN07P23800.TF-Name", "TXoN07P23800.TF-WContractDate", "TXoN07P23800.TF-SettlePrice", "TXoN07P23800.TF-UpLimit", "TXoN07P23800.TF-DownLimit", "TXoN07P23800.TF-OI", "TXoN07P23800.TF-TradingDate", "TXoN07P23800.TF-WRemainDate", "TXoN07P23800.TF-PreClose", "TXoN07P23800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P23900", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P23900.TF-Time", "TXoN07P23900.TF-TradingDate", "TXoN07P23900.TF-Open", "TXoN07P23900.TF-High", "TXoN07P23900.TF-Low", "TXoN07P23900.TF-Price", "TXoN07P23900.TF-TotalVolume", "TXoN07P23900.TF-Volume", "TXoN07P23900.TF-NWTotalBidContract", "TXoN07P23900.TF-NWTotalAskContract", "TXoN07P23900.TF-NWTotalBidSize", "TXoN07P23900.TF-NWTotalAskSize", "TXoN07P23900.TF-InSize", "TXoN07P23900.TF-OutSize", "TXoN07P23900.TF-TotalBidMatchTx", "TXoN07P23900.TF-TotalAskMatchTx", "TXoN07P23900.TF-BestBid1", "TXoN07P23900.TF-BestBid2", "TXoN07P23900.TF-BestBid3", "TXoN07P23900.TF-BestBid4", "TXoN07P23900.TF-BestBid5", "TXoN07P23900.TF-BestAsk1", "TXoN07P23900.TF-BestAsk2", "TXoN07P23900.TF-BestAsk3", "TXoN07P23900.TF-BestAsk4", "TXoN07P23900.TF-BestAsk5", "TXoN07P23900.TF-BestBidSize1", "TXoN07P23900.TF-BestBidSize2", "TXoN07P23900.TF-BestBidSize3", "TXoN07P23900.TF-BestBidSize4", "TXoN07P23900.TF-BestBidSize5", "TXoN07P23900.TF-BestAskSize1", "TXoN07P23900.TF-BestAskSize2", "TXoN07P23900.TF-BestAskSize3", "TXoN07P23900.TF-BestAskSize4", "TXoN07P23900.TF-BestAskSize5", "TXoN07P23900.TF-Name", "TXoN07P23900.TF-WContractDate", "TXoN07P23900.TF-SettlePrice", "TXoN07P23900.TF-UpLimit", "TXoN07P23900.TF-DownLimit", "TXoN07P23900.TF-OI", "TXoN07P23900.TF-TradingDate", "TXoN07P23900.TF-WRemainDate", "TXoN07P23900.TF-PreClose", "TXoN07P23900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24000", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24000.TF-Time", "TXoN07P24000.TF-TradingDate", "TXoN07P24000.TF-Open", "TXoN07P24000.TF-High", "TXoN07P24000.TF-Low", "TXoN07P24000.TF-Price", "TXoN07P24000.TF-TotalVolume", "TXoN07P24000.TF-Volume", "TXoN07P24000.TF-NWTotalBidContract", "TXoN07P24000.TF-NWTotalAskContract", "TXoN07P24000.TF-NWTotalBidSize", "TXoN07P24000.TF-NWTotalAskSize", "TXoN07P24000.TF-InSize", "TXoN07P24000.TF-OutSize", "TXoN07P24000.TF-TotalBidMatchTx", "TXoN07P24000.TF-TotalAskMatchTx", "TXoN07P24000.TF-BestBid1", "TXoN07P24000.TF-BestBid2", "TXoN07P24000.TF-BestBid3", "TXoN07P24000.TF-BestBid4", "TXoN07P24000.TF-BestBid5", "TXoN07P24000.TF-BestAsk1", "TXoN07P24000.TF-BestAsk2", "TXoN07P24000.TF-BestAsk3", "TXoN07P24000.TF-BestAsk4", "TXoN07P24000.TF-BestAsk5", "TXoN07P24000.TF-BestBidSize1", "TXoN07P24000.TF-BestBidSize2", "TXoN07P24000.TF-BestBidSize3", "TXoN07P24000.TF-BestBidSize4", "TXoN07P24000.TF-BestBidSize5", "TXoN07P24000.TF-BestAskSize1", "TXoN07P24000.TF-BestAskSize2", "TXoN07P24000.TF-BestAskSize3", "TXoN07P24000.TF-BestAskSize4", "TXoN07P24000.TF-BestAskSize5", "TXoN07P24000.TF-Name", "TXoN07P24000.TF-WContractDate", "TXoN07P24000.TF-SettlePrice", "TXoN07P24000.TF-UpLimit", "TXoN07P24000.TF-DownLimit", "TXoN07P24000.TF-OI", "TXoN07P24000.TF-TradingDate", "TXoN07P24000.TF-WRemainDate", "TXoN07P24000.TF-PreClose", "TXoN07P24000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24100", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24100.TF-Time", "TXoN07P24100.TF-TradingDate", "TXoN07P24100.TF-Open", "TXoN07P24100.TF-High", "TXoN07P24100.TF-Low", "TXoN07P24100.TF-Price", "TXoN07P24100.TF-TotalVolume", "TXoN07P24100.TF-Volume", "TXoN07P24100.TF-NWTotalBidContract", "TXoN07P24100.TF-NWTotalAskContract", "TXoN07P24100.TF-NWTotalBidSize", "TXoN07P24100.TF-NWTotalAskSize", "TXoN07P24100.TF-InSize", "TXoN07P24100.TF-OutSize", "TXoN07P24100.TF-TotalBidMatchTx", "TXoN07P24100.TF-TotalAskMatchTx", "TXoN07P24100.TF-BestBid1", "TXoN07P24100.TF-BestBid2", "TXoN07P24100.TF-BestBid3", "TXoN07P24100.TF-BestBid4", "TXoN07P24100.TF-BestBid5", "TXoN07P24100.TF-BestAsk1", "TXoN07P24100.TF-BestAsk2", "TXoN07P24100.TF-BestAsk3", "TXoN07P24100.TF-BestAsk4", "TXoN07P24100.TF-BestAsk5", "TXoN07P24100.TF-BestBidSize1", "TXoN07P24100.TF-BestBidSize2", "TXoN07P24100.TF-BestBidSize3", "TXoN07P24100.TF-BestBidSize4", "TXoN07P24100.TF-BestBidSize5", "TXoN07P24100.TF-BestAskSize1", "TXoN07P24100.TF-BestAskSize2", "TXoN07P24100.TF-BestAskSize3", "TXoN07P24100.TF-BestAskSize4", "TXoN07P24100.TF-BestAskSize5", "TXoN07P24100.TF-Name", "TXoN07P24100.TF-WContractDate", "TXoN07P24100.TF-SettlePrice", "TXoN07P24100.TF-UpLimit", "TXoN07P24100.TF-DownLimit", "TXoN07P24100.TF-OI", "TXoN07P24100.TF-TradingDate", "TXoN07P24100.TF-WRemainDate", "TXoN07P24100.TF-PreClose", "TXoN07P24100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24200", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24200.TF-Time", "TXoN07P24200.TF-TradingDate", "TXoN07P24200.TF-Open", "TXoN07P24200.TF-High", "TXoN07P24200.TF-Low", "TXoN07P24200.TF-Price", "TXoN07P24200.TF-TotalVolume", "TXoN07P24200.TF-Volume", "TXoN07P24200.TF-NWTotalBidContract", "TXoN07P24200.TF-NWTotalAskContract", "TXoN07P24200.TF-NWTotalBidSize", "TXoN07P24200.TF-NWTotalAskSize", "TXoN07P24200.TF-InSize", "TXoN07P24200.TF-OutSize", "TXoN07P24200.TF-TotalBidMatchTx", "TXoN07P24200.TF-TotalAskMatchTx", "TXoN07P24200.TF-BestBid1", "TXoN07P24200.TF-BestBid2", "TXoN07P24200.TF-BestBid3", "TXoN07P24200.TF-BestBid4", "TXoN07P24200.TF-BestBid5", "TXoN07P24200.TF-BestAsk1", "TXoN07P24200.TF-BestAsk2", "TXoN07P24200.TF-BestAsk3", "TXoN07P24200.TF-BestAsk4", "TXoN07P24200.TF-BestAsk5", "TXoN07P24200.TF-BestBidSize1", "TXoN07P24200.TF-BestBidSize2", "TXoN07P24200.TF-BestBidSize3", "TXoN07P24200.TF-BestBidSize4", "TXoN07P24200.TF-BestBidSize5", "TXoN07P24200.TF-BestAskSize1", "TXoN07P24200.TF-BestAskSize2", "TXoN07P24200.TF-BestAskSize3", "TXoN07P24200.TF-BestAskSize4", "TXoN07P24200.TF-BestAskSize5", "TXoN07P24200.TF-Name", "TXoN07P24200.TF-WContractDate", "TXoN07P24200.TF-SettlePrice", "TXoN07P24200.TF-UpLimit", "TXoN07P24200.TF-DownLimit", "TXoN07P24200.TF-OI", "TXoN07P24200.TF-TradingDate", "TXoN07P24200.TF-WRemainDate", "TXoN07P24200.TF-PreClose", "TXoN07P24200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24300", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24300.TF-Time", "TXoN07P24300.TF-TradingDate", "TXoN07P24300.TF-Open", "TXoN07P24300.TF-High", "TXoN07P24300.TF-Low", "TXoN07P24300.TF-Price", "TXoN07P24300.TF-TotalVolume", "TXoN07P24300.TF-Volume", "TXoN07P24300.TF-NWTotalBidContract", "TXoN07P24300.TF-NWTotalAskContract", "TXoN07P24300.TF-NWTotalBidSize", "TXoN07P24300.TF-NWTotalAskSize", "TXoN07P24300.TF-InSize", "TXoN07P24300.TF-OutSize", "TXoN07P24300.TF-TotalBidMatchTx", "TXoN07P24300.TF-TotalAskMatchTx", "TXoN07P24300.TF-BestBid1", "TXoN07P24300.TF-BestBid2", "TXoN07P24300.TF-BestBid3", "TXoN07P24300.TF-BestBid4", "TXoN07P24300.TF-BestBid5", "TXoN07P24300.TF-BestAsk1", "TXoN07P24300.TF-BestAsk2", "TXoN07P24300.TF-BestAsk3", "TXoN07P24300.TF-BestAsk4", "TXoN07P24300.TF-BestAsk5", "TXoN07P24300.TF-BestBidSize1", "TXoN07P24300.TF-BestBidSize2", "TXoN07P24300.TF-BestBidSize3", "TXoN07P24300.TF-BestBidSize4", "TXoN07P24300.TF-BestBidSize5", "TXoN07P24300.TF-BestAskSize1", "TXoN07P24300.TF-BestAskSize2", "TXoN07P24300.TF-BestAskSize3", "TXoN07P24300.TF-BestAskSize4", "TXoN07P24300.TF-BestAskSize5", "TXoN07P24300.TF-Name", "TXoN07P24300.TF-WContractDate", "TXoN07P24300.TF-SettlePrice", "TXoN07P24300.TF-UpLimit", "TXoN07P24300.TF-DownLimit", "TXoN07P24300.TF-OI", "TXoN07P24300.TF-TradingDate", "TXoN07P24300.TF-WRemainDate", "TXoN07P24300.TF-PreClose", "TXoN07P24300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24400", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24400.TF-Time", "TXoN07P24400.TF-TradingDate", "TXoN07P24400.TF-Open", "TXoN07P24400.TF-High", "TXoN07P24400.TF-Low", "TXoN07P24400.TF-Price", "TXoN07P24400.TF-TotalVolume", "TXoN07P24400.TF-Volume", "TXoN07P24400.TF-NWTotalBidContract", "TXoN07P24400.TF-NWTotalAskContract", "TXoN07P24400.TF-NWTotalBidSize", "TXoN07P24400.TF-NWTotalAskSize", "TXoN07P24400.TF-InSize", "TXoN07P24400.TF-OutSize", "TXoN07P24400.TF-TotalBidMatchTx", "TXoN07P24400.TF-TotalAskMatchTx", "TXoN07P24400.TF-BestBid1", "TXoN07P24400.TF-BestBid2", "TXoN07P24400.TF-BestBid3", "TXoN07P24400.TF-BestBid4", "TXoN07P24400.TF-BestBid5", "TXoN07P24400.TF-BestAsk1", "TXoN07P24400.TF-BestAsk2", "TXoN07P24400.TF-BestAsk3", "TXoN07P24400.TF-BestAsk4", "TXoN07P24400.TF-BestAsk5", "TXoN07P24400.TF-BestBidSize1", "TXoN07P24400.TF-BestBidSize2", "TXoN07P24400.TF-BestBidSize3", "TXoN07P24400.TF-BestBidSize4", "TXoN07P24400.TF-BestBidSize5", "TXoN07P24400.TF-BestAskSize1", "TXoN07P24400.TF-BestAskSize2", "TXoN07P24400.TF-BestAskSize3", "TXoN07P24400.TF-BestAskSize4", "TXoN07P24400.TF-BestAskSize5", "TXoN07P24400.TF-Name", "TXoN07P24400.TF-WContractDate", "TXoN07P24400.TF-SettlePrice", "TXoN07P24400.TF-UpLimit", "TXoN07P24400.TF-DownLimit", "TXoN07P24400.TF-OI", "TXoN07P24400.TF-TradingDate", "TXoN07P24400.TF-WRemainDate", "TXoN07P24400.TF-PreClose", "TXoN07P24400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24500", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24500.TF-Time", "TXoN07P24500.TF-TradingDate", "TXoN07P24500.TF-Open", "TXoN07P24500.TF-High", "TXoN07P24500.TF-Low", "TXoN07P24500.TF-Price", "TXoN07P24500.TF-TotalVolume", "TXoN07P24500.TF-Volume", "TXoN07P24500.TF-NWTotalBidContract", "TXoN07P24500.TF-NWTotalAskContract", "TXoN07P24500.TF-NWTotalBidSize", "TXoN07P24500.TF-NWTotalAskSize", "TXoN07P24500.TF-InSize", "TXoN07P24500.TF-OutSize", "TXoN07P24500.TF-TotalBidMatchTx", "TXoN07P24500.TF-TotalAskMatchTx", "TXoN07P24500.TF-BestBid1", "TXoN07P24500.TF-BestBid2", "TXoN07P24500.TF-BestBid3", "TXoN07P24500.TF-BestBid4", "TXoN07P24500.TF-BestBid5", "TXoN07P24500.TF-BestAsk1", "TXoN07P24500.TF-BestAsk2", "TXoN07P24500.TF-BestAsk3", "TXoN07P24500.TF-BestAsk4", "TXoN07P24500.TF-BestAsk5", "TXoN07P24500.TF-BestBidSize1", "TXoN07P24500.TF-BestBidSize2", "TXoN07P24500.TF-BestBidSize3", "TXoN07P24500.TF-BestBidSize4", "TXoN07P24500.TF-BestBidSize5", "TXoN07P24500.TF-BestAskSize1", "TXoN07P24500.TF-BestAskSize2", "TXoN07P24500.TF-BestAskSize3", "TXoN07P24500.TF-BestAskSize4", "TXoN07P24500.TF-BestAskSize5", "TXoN07P24500.TF-Name", "TXoN07P24500.TF-WContractDate", "TXoN07P24500.TF-SettlePrice", "TXoN07P24500.TF-UpLimit", "TXoN07P24500.TF-DownLimit", "TXoN07P24500.TF-OI", "TXoN07P24500.TF-TradingDate", "TXoN07P24500.TF-WRemainDate", "TXoN07P24500.TF-PreClose", "TXoN07P24500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24600", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24600.TF-Time", "TXoN07P24600.TF-TradingDate", "TXoN07P24600.TF-Open", "TXoN07P24600.TF-High", "TXoN07P24600.TF-Low", "TXoN07P24600.TF-Price", "TXoN07P24600.TF-TotalVolume", "TXoN07P24600.TF-Volume", "TXoN07P24600.TF-NWTotalBidContract", "TXoN07P24600.TF-NWTotalAskContract", "TXoN07P24600.TF-NWTotalBidSize", "TXoN07P24600.TF-NWTotalAskSize", "TXoN07P24600.TF-InSize", "TXoN07P24600.TF-OutSize", "TXoN07P24600.TF-TotalBidMatchTx", "TXoN07P24600.TF-TotalAskMatchTx", "TXoN07P24600.TF-BestBid1", "TXoN07P24600.TF-BestBid2", "TXoN07P24600.TF-BestBid3", "TXoN07P24600.TF-BestBid4", "TXoN07P24600.TF-BestBid5", "TXoN07P24600.TF-BestAsk1", "TXoN07P24600.TF-BestAsk2", "TXoN07P24600.TF-BestAsk3", "TXoN07P24600.TF-BestAsk4", "TXoN07P24600.TF-BestAsk5", "TXoN07P24600.TF-BestBidSize1", "TXoN07P24600.TF-BestBidSize2", "TXoN07P24600.TF-BestBidSize3", "TXoN07P24600.TF-BestBidSize4", "TXoN07P24600.TF-BestBidSize5", "TXoN07P24600.TF-BestAskSize1", "TXoN07P24600.TF-BestAskSize2", "TXoN07P24600.TF-BestAskSize3", "TXoN07P24600.TF-BestAskSize4", "TXoN07P24600.TF-BestAskSize5", "TXoN07P24600.TF-Name", "TXoN07P24600.TF-WContractDate", "TXoN07P24600.TF-SettlePrice", "TXoN07P24600.TF-UpLimit", "TXoN07P24600.TF-DownLimit", "TXoN07P24600.TF-OI", "TXoN07P24600.TF-TradingDate", "TXoN07P24600.TF-WRemainDate", "TXoN07P24600.TF-PreClose", "TXoN07P24600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24700", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24700.TF-Time", "TXoN07P24700.TF-TradingDate", "TXoN07P24700.TF-Open", "TXoN07P24700.TF-High", "TXoN07P24700.TF-Low", "TXoN07P24700.TF-Price", "TXoN07P24700.TF-TotalVolume", "TXoN07P24700.TF-Volume", "TXoN07P24700.TF-NWTotalBidContract", "TXoN07P24700.TF-NWTotalAskContract", "TXoN07P24700.TF-NWTotalBidSize", "TXoN07P24700.TF-NWTotalAskSize", "TXoN07P24700.TF-InSize", "TXoN07P24700.TF-OutSize", "TXoN07P24700.TF-TotalBidMatchTx", "TXoN07P24700.TF-TotalAskMatchTx", "TXoN07P24700.TF-BestBid1", "TXoN07P24700.TF-BestBid2", "TXoN07P24700.TF-BestBid3", "TXoN07P24700.TF-BestBid4", "TXoN07P24700.TF-BestBid5", "TXoN07P24700.TF-BestAsk1", "TXoN07P24700.TF-BestAsk2", "TXoN07P24700.TF-BestAsk3", "TXoN07P24700.TF-BestAsk4", "TXoN07P24700.TF-BestAsk5", "TXoN07P24700.TF-BestBidSize1", "TXoN07P24700.TF-BestBidSize2", "TXoN07P24700.TF-BestBidSize3", "TXoN07P24700.TF-BestBidSize4", "TXoN07P24700.TF-BestBidSize5", "TXoN07P24700.TF-BestAskSize1", "TXoN07P24700.TF-BestAskSize2", "TXoN07P24700.TF-BestAskSize3", "TXoN07P24700.TF-BestAskSize4", "TXoN07P24700.TF-BestAskSize5", "TXoN07P24700.TF-Name", "TXoN07P24700.TF-WContractDate", "TXoN07P24700.TF-SettlePrice", "TXoN07P24700.TF-UpLimit", "TXoN07P24700.TF-DownLimit", "TXoN07P24700.TF-OI", "TXoN07P24700.TF-TradingDate", "TXoN07P24700.TF-WRemainDate", "TXoN07P24700.TF-PreClose", "TXoN07P24700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24800", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24800.TF-Time", "TXoN07P24800.TF-TradingDate", "TXoN07P24800.TF-Open", "TXoN07P24800.TF-High", "TXoN07P24800.TF-Low", "TXoN07P24800.TF-Price", "TXoN07P24800.TF-TotalVolume", "TXoN07P24800.TF-Volume", "TXoN07P24800.TF-NWTotalBidContract", "TXoN07P24800.TF-NWTotalAskContract", "TXoN07P24800.TF-NWTotalBidSize", "TXoN07P24800.TF-NWTotalAskSize", "TXoN07P24800.TF-InSize", "TXoN07P24800.TF-OutSize", "TXoN07P24800.TF-TotalBidMatchTx", "TXoN07P24800.TF-TotalAskMatchTx", "TXoN07P24800.TF-BestBid1", "TXoN07P24800.TF-BestBid2", "TXoN07P24800.TF-BestBid3", "TXoN07P24800.TF-BestBid4", "TXoN07P24800.TF-BestBid5", "TXoN07P24800.TF-BestAsk1", "TXoN07P24800.TF-BestAsk2", "TXoN07P24800.TF-BestAsk3", "TXoN07P24800.TF-BestAsk4", "TXoN07P24800.TF-BestAsk5", "TXoN07P24800.TF-BestBidSize1", "TXoN07P24800.TF-BestBidSize2", "TXoN07P24800.TF-BestBidSize3", "TXoN07P24800.TF-BestBidSize4", "TXoN07P24800.TF-BestBidSize5", "TXoN07P24800.TF-BestAskSize1", "TXoN07P24800.TF-BestAskSize2", "TXoN07P24800.TF-BestAskSize3", "TXoN07P24800.TF-BestAskSize4", "TXoN07P24800.TF-BestAskSize5", "TXoN07P24800.TF-Name", "TXoN07P24800.TF-WContractDate", "TXoN07P24800.TF-SettlePrice", "TXoN07P24800.TF-UpLimit", "TXoN07P24800.TF-DownLimit", "TXoN07P24800.TF-OI", "TXoN07P24800.TF-TradingDate", "TXoN07P24800.TF-WRemainDate", "TXoN07P24800.TF-PreClose", "TXoN07P24800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P24900", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P24900.TF-Time", "TXoN07P24900.TF-TradingDate", "TXoN07P24900.TF-Open", "TXoN07P24900.TF-High", "TXoN07P24900.TF-Low", "TXoN07P24900.TF-Price", "TXoN07P24900.TF-TotalVolume", "TXoN07P24900.TF-Volume", "TXoN07P24900.TF-NWTotalBidContract", "TXoN07P24900.TF-NWTotalAskContract", "TXoN07P24900.TF-NWTotalBidSize", "TXoN07P24900.TF-NWTotalAskSize", "TXoN07P24900.TF-InSize", "TXoN07P24900.TF-OutSize", "TXoN07P24900.TF-TotalBidMatchTx", "TXoN07P24900.TF-TotalAskMatchTx", "TXoN07P24900.TF-BestBid1", "TXoN07P24900.TF-BestBid2", "TXoN07P24900.TF-BestBid3", "TXoN07P24900.TF-BestBid4", "TXoN07P24900.TF-BestBid5", "TXoN07P24900.TF-BestAsk1", "TXoN07P24900.TF-BestAsk2", "TXoN07P24900.TF-BestAsk3", "TXoN07P24900.TF-BestAsk4", "TXoN07P24900.TF-BestAsk5", "TXoN07P24900.TF-BestBidSize1", "TXoN07P24900.TF-BestBidSize2", "TXoN07P24900.TF-BestBidSize3", "TXoN07P24900.TF-BestBidSize4", "TXoN07P24900.TF-BestBidSize5", "TXoN07P24900.TF-BestAskSize1", "TXoN07P24900.TF-BestAskSize2", "TXoN07P24900.TF-BestAskSize3", "TXoN07P24900.TF-BestAskSize4", "TXoN07P24900.TF-BestAskSize5", "TXoN07P24900.TF-Name", "TXoN07P24900.TF-WContractDate", "TXoN07P24900.TF-SettlePrice", "TXoN07P24900.TF-UpLimit", "TXoN07P24900.TF-DownLimit", "TXoN07P24900.TF-OI", "TXoN07P24900.TF-TradingDate", "TXoN07P24900.TF-WRemainDate", "TXoN07P24900.TF-PreClose", "TXoN07P24900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25000", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25000.TF-Time", "TXoN07P25000.TF-TradingDate", "TXoN07P25000.TF-Open", "TXoN07P25000.TF-High", "TXoN07P25000.TF-Low", "TXoN07P25000.TF-Price", "TXoN07P25000.TF-TotalVolume", "TXoN07P25000.TF-Volume", "TXoN07P25000.TF-NWTotalBidContract", "TXoN07P25000.TF-NWTotalAskContract", "TXoN07P25000.TF-NWTotalBidSize", "TXoN07P25000.TF-NWTotalAskSize", "TXoN07P25000.TF-InSize", "TXoN07P25000.TF-OutSize", "TXoN07P25000.TF-TotalBidMatchTx", "TXoN07P25000.TF-TotalAskMatchTx", "TXoN07P25000.TF-BestBid1", "TXoN07P25000.TF-BestBid2", "TXoN07P25000.TF-BestBid3", "TXoN07P25000.TF-BestBid4", "TXoN07P25000.TF-BestBid5", "TXoN07P25000.TF-BestAsk1", "TXoN07P25000.TF-BestAsk2", "TXoN07P25000.TF-BestAsk3", "TXoN07P25000.TF-BestAsk4", "TXoN07P25000.TF-BestAsk5", "TXoN07P25000.TF-BestBidSize1", "TXoN07P25000.TF-BestBidSize2", "TXoN07P25000.TF-BestBidSize3", "TXoN07P25000.TF-BestBidSize4", "TXoN07P25000.TF-BestBidSize5", "TXoN07P25000.TF-BestAskSize1", "TXoN07P25000.TF-BestAskSize2", "TXoN07P25000.TF-BestAskSize3", "TXoN07P25000.TF-BestAskSize4", "TXoN07P25000.TF-BestAskSize5", "TXoN07P25000.TF-Name", "TXoN07P25000.TF-WContractDate", "TXoN07P25000.TF-SettlePrice", "TXoN07P25000.TF-UpLimit", "TXoN07P25000.TF-DownLimit", "TXoN07P25000.TF-OI", "TXoN07P25000.TF-TradingDate", "TXoN07P25000.TF-WRemainDate", "TXoN07P25000.TF-PreClose", "TXoN07P25000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25100", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25100.TF-Time", "TXoN07P25100.TF-TradingDate", "TXoN07P25100.TF-Open", "TXoN07P25100.TF-High", "TXoN07P25100.TF-Low", "TXoN07P25100.TF-Price", "TXoN07P25100.TF-TotalVolume", "TXoN07P25100.TF-Volume", "TXoN07P25100.TF-NWTotalBidContract", "TXoN07P25100.TF-NWTotalAskContract", "TXoN07P25100.TF-NWTotalBidSize", "TXoN07P25100.TF-NWTotalAskSize", "TXoN07P25100.TF-InSize", "TXoN07P25100.TF-OutSize", "TXoN07P25100.TF-TotalBidMatchTx", "TXoN07P25100.TF-TotalAskMatchTx", "TXoN07P25100.TF-BestBid1", "TXoN07P25100.TF-BestBid2", "TXoN07P25100.TF-BestBid3", "TXoN07P25100.TF-BestBid4", "TXoN07P25100.TF-BestBid5", "TXoN07P25100.TF-BestAsk1", "TXoN07P25100.TF-BestAsk2", "TXoN07P25100.TF-BestAsk3", "TXoN07P25100.TF-BestAsk4", "TXoN07P25100.TF-BestAsk5", "TXoN07P25100.TF-BestBidSize1", "TXoN07P25100.TF-BestBidSize2", "TXoN07P25100.TF-BestBidSize3", "TXoN07P25100.TF-BestBidSize4", "TXoN07P25100.TF-BestBidSize5", "TXoN07P25100.TF-BestAskSize1", "TXoN07P25100.TF-BestAskSize2", "TXoN07P25100.TF-BestAskSize3", "TXoN07P25100.TF-BestAskSize4", "TXoN07P25100.TF-BestAskSize5", "TXoN07P25100.TF-Name", "TXoN07P25100.TF-WContractDate", "TXoN07P25100.TF-SettlePrice", "TXoN07P25100.TF-UpLimit", "TXoN07P25100.TF-DownLimit", "TXoN07P25100.TF-OI", "TXoN07P25100.TF-TradingDate", "TXoN07P25100.TF-WRemainDate", "TXoN07P25100.TF-PreClose", "TXoN07P25100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25200", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25200.TF-Time", "TXoN07P25200.TF-TradingDate", "TXoN07P25200.TF-Open", "TXoN07P25200.TF-High", "TXoN07P25200.TF-Low", "TXoN07P25200.TF-Price", "TXoN07P25200.TF-TotalVolume", "TXoN07P25200.TF-Volume", "TXoN07P25200.TF-NWTotalBidContract", "TXoN07P25200.TF-NWTotalAskContract", "TXoN07P25200.TF-NWTotalBidSize", "TXoN07P25200.TF-NWTotalAskSize", "TXoN07P25200.TF-InSize", "TXoN07P25200.TF-OutSize", "TXoN07P25200.TF-TotalBidMatchTx", "TXoN07P25200.TF-TotalAskMatchTx", "TXoN07P25200.TF-BestBid1", "TXoN07P25200.TF-BestBid2", "TXoN07P25200.TF-BestBid3", "TXoN07P25200.TF-BestBid4", "TXoN07P25200.TF-BestBid5", "TXoN07P25200.TF-BestAsk1", "TXoN07P25200.TF-BestAsk2", "TXoN07P25200.TF-BestAsk3", "TXoN07P25200.TF-BestAsk4", "TXoN07P25200.TF-BestAsk5", "TXoN07P25200.TF-BestBidSize1", "TXoN07P25200.TF-BestBidSize2", "TXoN07P25200.TF-BestBidSize3", "TXoN07P25200.TF-BestBidSize4", "TXoN07P25200.TF-BestBidSize5", "TXoN07P25200.TF-BestAskSize1", "TXoN07P25200.TF-BestAskSize2", "TXoN07P25200.TF-BestAskSize3", "TXoN07P25200.TF-BestAskSize4", "TXoN07P25200.TF-BestAskSize5", "TXoN07P25200.TF-Name", "TXoN07P25200.TF-WContractDate", "TXoN07P25200.TF-SettlePrice", "TXoN07P25200.TF-UpLimit", "TXoN07P25200.TF-DownLimit", "TXoN07P25200.TF-OI", "TXoN07P25200.TF-TradingDate", "TXoN07P25200.TF-WRemainDate", "TXoN07P25200.TF-PreClose", "TXoN07P25200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25300", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25300.TF-Time", "TXoN07P25300.TF-TradingDate", "TXoN07P25300.TF-Open", "TXoN07P25300.TF-High", "TXoN07P25300.TF-Low", "TXoN07P25300.TF-Price", "TXoN07P25300.TF-TotalVolume", "TXoN07P25300.TF-Volume", "TXoN07P25300.TF-NWTotalBidContract", "TXoN07P25300.TF-NWTotalAskContract", "TXoN07P25300.TF-NWTotalBidSize", "TXoN07P25300.TF-NWTotalAskSize", "TXoN07P25300.TF-InSize", "TXoN07P25300.TF-OutSize", "TXoN07P25300.TF-TotalBidMatchTx", "TXoN07P25300.TF-TotalAskMatchTx", "TXoN07P25300.TF-BestBid1", "TXoN07P25300.TF-BestBid2", "TXoN07P25300.TF-BestBid3", "TXoN07P25300.TF-BestBid4", "TXoN07P25300.TF-BestBid5", "TXoN07P25300.TF-BestAsk1", "TXoN07P25300.TF-BestAsk2", "TXoN07P25300.TF-BestAsk3", "TXoN07P25300.TF-BestAsk4", "TXoN07P25300.TF-BestAsk5", "TXoN07P25300.TF-BestBidSize1", "TXoN07P25300.TF-BestBidSize2", "TXoN07P25300.TF-BestBidSize3", "TXoN07P25300.TF-BestBidSize4", "TXoN07P25300.TF-BestBidSize5", "TXoN07P25300.TF-BestAskSize1", "TXoN07P25300.TF-BestAskSize2", "TXoN07P25300.TF-BestAskSize3", "TXoN07P25300.TF-BestAskSize4", "TXoN07P25300.TF-BestAskSize5", "TXoN07P25300.TF-Name", "TXoN07P25300.TF-WContractDate", "TXoN07P25300.TF-SettlePrice", "TXoN07P25300.TF-UpLimit", "TXoN07P25300.TF-DownLimit", "TXoN07P25300.TF-OI", "TXoN07P25300.TF-TradingDate", "TXoN07P25300.TF-WRemainDate", "TXoN07P25300.TF-PreClose", "TXoN07P25300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25400", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25400.TF-Time", "TXoN07P25400.TF-TradingDate", "TXoN07P25400.TF-Open", "TXoN07P25400.TF-High", "TXoN07P25400.TF-Low", "TXoN07P25400.TF-Price", "TXoN07P25400.TF-TotalVolume", "TXoN07P25400.TF-Volume", "TXoN07P25400.TF-NWTotalBidContract", "TXoN07P25400.TF-NWTotalAskContract", "TXoN07P25400.TF-NWTotalBidSize", "TXoN07P25400.TF-NWTotalAskSize", "TXoN07P25400.TF-InSize", "TXoN07P25400.TF-OutSize", "TXoN07P25400.TF-TotalBidMatchTx", "TXoN07P25400.TF-TotalAskMatchTx", "TXoN07P25400.TF-BestBid1", "TXoN07P25400.TF-BestBid2", "TXoN07P25400.TF-BestBid3", "TXoN07P25400.TF-BestBid4", "TXoN07P25400.TF-BestBid5", "TXoN07P25400.TF-BestAsk1", "TXoN07P25400.TF-BestAsk2", "TXoN07P25400.TF-BestAsk3", "TXoN07P25400.TF-BestAsk4", "TXoN07P25400.TF-BestAsk5", "TXoN07P25400.TF-BestBidSize1", "TXoN07P25400.TF-BestBidSize2", "TXoN07P25400.TF-BestBidSize3", "TXoN07P25400.TF-BestBidSize4", "TXoN07P25400.TF-BestBidSize5", "TXoN07P25400.TF-BestAskSize1", "TXoN07P25400.TF-BestAskSize2", "TXoN07P25400.TF-BestAskSize3", "TXoN07P25400.TF-BestAskSize4", "TXoN07P25400.TF-BestAskSize5", "TXoN07P25400.TF-Name", "TXoN07P25400.TF-WContractDate", "TXoN07P25400.TF-SettlePrice", "TXoN07P25400.TF-UpLimit", "TXoN07P25400.TF-DownLimit", "TXoN07P25400.TF-OI", "TXoN07P25400.TF-TradingDate", "TXoN07P25400.TF-WRemainDate", "TXoN07P25400.TF-PreClose", "TXoN07P25400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25500", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25500.TF-Time", "TXoN07P25500.TF-TradingDate", "TXoN07P25500.TF-Open", "TXoN07P25500.TF-High", "TXoN07P25500.TF-Low", "TXoN07P25500.TF-Price", "TXoN07P25500.TF-TotalVolume", "TXoN07P25500.TF-Volume", "TXoN07P25500.TF-NWTotalBidContract", "TXoN07P25500.TF-NWTotalAskContract", "TXoN07P25500.TF-NWTotalBidSize", "TXoN07P25500.TF-NWTotalAskSize", "TXoN07P25500.TF-InSize", "TXoN07P25500.TF-OutSize", "TXoN07P25500.TF-TotalBidMatchTx", "TXoN07P25500.TF-TotalAskMatchTx", "TXoN07P25500.TF-BestBid1", "TXoN07P25500.TF-BestBid2", "TXoN07P25500.TF-BestBid3", "TXoN07P25500.TF-BestBid4", "TXoN07P25500.TF-BestBid5", "TXoN07P25500.TF-BestAsk1", "TXoN07P25500.TF-BestAsk2", "TXoN07P25500.TF-BestAsk3", "TXoN07P25500.TF-BestAsk4", "TXoN07P25500.TF-BestAsk5", "TXoN07P25500.TF-BestBidSize1", "TXoN07P25500.TF-BestBidSize2", "TXoN07P25500.TF-BestBidSize3", "TXoN07P25500.TF-BestBidSize4", "TXoN07P25500.TF-BestBidSize5", "TXoN07P25500.TF-BestAskSize1", "TXoN07P25500.TF-BestAskSize2", "TXoN07P25500.TF-BestAskSize3", "TXoN07P25500.TF-BestAskSize4", "TXoN07P25500.TF-BestAskSize5", "TXoN07P25500.TF-Name", "TXoN07P25500.TF-WContractDate", "TXoN07P25500.TF-SettlePrice", "TXoN07P25500.TF-UpLimit", "TXoN07P25500.TF-DownLimit", "TXoN07P25500.TF-OI", "TXoN07P25500.TF-TradingDate", "TXoN07P25500.TF-WRemainDate", "TXoN07P25500.TF-PreClose", "TXoN07P25500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25600", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25600.TF-Time", "TXoN07P25600.TF-TradingDate", "TXoN07P25600.TF-Open", "TXoN07P25600.TF-High", "TXoN07P25600.TF-Low", "TXoN07P25600.TF-Price", "TXoN07P25600.TF-TotalVolume", "TXoN07P25600.TF-Volume", "TXoN07P25600.TF-NWTotalBidContract", "TXoN07P25600.TF-NWTotalAskContract", "TXoN07P25600.TF-NWTotalBidSize", "TXoN07P25600.TF-NWTotalAskSize", "TXoN07P25600.TF-InSize", "TXoN07P25600.TF-OutSize", "TXoN07P25600.TF-TotalBidMatchTx", "TXoN07P25600.TF-TotalAskMatchTx", "TXoN07P25600.TF-BestBid1", "TXoN07P25600.TF-BestBid2", "TXoN07P25600.TF-BestBid3", "TXoN07P25600.TF-BestBid4", "TXoN07P25600.TF-BestBid5", "TXoN07P25600.TF-BestAsk1", "TXoN07P25600.TF-BestAsk2", "TXoN07P25600.TF-BestAsk3", "TXoN07P25600.TF-BestAsk4", "TXoN07P25600.TF-BestAsk5", "TXoN07P25600.TF-BestBidSize1", "TXoN07P25600.TF-BestBidSize2", "TXoN07P25600.TF-BestBidSize3", "TXoN07P25600.TF-BestBidSize4", "TXoN07P25600.TF-BestBidSize5", "TXoN07P25600.TF-BestAskSize1", "TXoN07P25600.TF-BestAskSize2", "TXoN07P25600.TF-BestAskSize3", "TXoN07P25600.TF-BestAskSize4", "TXoN07P25600.TF-BestAskSize5", "TXoN07P25600.TF-Name", "TXoN07P25600.TF-WContractDate", "TXoN07P25600.TF-SettlePrice", "TXoN07P25600.TF-UpLimit", "TXoN07P25600.TF-DownLimit", "TXoN07P25600.TF-OI", "TXoN07P25600.TF-TradingDate", "TXoN07P25600.TF-WRemainDate", "TXoN07P25600.TF-PreClose", "TXoN07P25600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25700", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25700.TF-Time", "TXoN07P25700.TF-TradingDate", "TXoN07P25700.TF-Open", "TXoN07P25700.TF-High", "TXoN07P25700.TF-Low", "TXoN07P25700.TF-Price", "TXoN07P25700.TF-TotalVolume", "TXoN07P25700.TF-Volume", "TXoN07P25700.TF-NWTotalBidContract", "TXoN07P25700.TF-NWTotalAskContract", "TXoN07P25700.TF-NWTotalBidSize", "TXoN07P25700.TF-NWTotalAskSize", "TXoN07P25700.TF-InSize", "TXoN07P25700.TF-OutSize", "TXoN07P25700.TF-TotalBidMatchTx", "TXoN07P25700.TF-TotalAskMatchTx", "TXoN07P25700.TF-BestBid1", "TXoN07P25700.TF-BestBid2", "TXoN07P25700.TF-BestBid3", "TXoN07P25700.TF-BestBid4", "TXoN07P25700.TF-BestBid5", "TXoN07P25700.TF-BestAsk1", "TXoN07P25700.TF-BestAsk2", "TXoN07P25700.TF-BestAsk3", "TXoN07P25700.TF-BestAsk4", "TXoN07P25700.TF-BestAsk5", "TXoN07P25700.TF-BestBidSize1", "TXoN07P25700.TF-BestBidSize2", "TXoN07P25700.TF-BestBidSize3", "TXoN07P25700.TF-BestBidSize4", "TXoN07P25700.TF-BestBidSize5", "TXoN07P25700.TF-BestAskSize1", "TXoN07P25700.TF-BestAskSize2", "TXoN07P25700.TF-BestAskSize3", "TXoN07P25700.TF-BestAskSize4", "TXoN07P25700.TF-BestAskSize5", "TXoN07P25700.TF-Name", "TXoN07P25700.TF-WContractDate", "TXoN07P25700.TF-SettlePrice", "TXoN07P25700.TF-UpLimit", "TXoN07P25700.TF-DownLimit", "TXoN07P25700.TF-OI", "TXoN07P25700.TF-TradingDate", "TXoN07P25700.TF-WRemainDate", "TXoN07P25700.TF-PreClose", "TXoN07P25700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25800", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25800.TF-Time", "TXoN07P25800.TF-TradingDate", "TXoN07P25800.TF-Open", "TXoN07P25800.TF-High", "TXoN07P25800.TF-Low", "TXoN07P25800.TF-Price", "TXoN07P25800.TF-TotalVolume", "TXoN07P25800.TF-Volume", "TXoN07P25800.TF-NWTotalBidContract", "TXoN07P25800.TF-NWTotalAskContract", "TXoN07P25800.TF-NWTotalBidSize", "TXoN07P25800.TF-NWTotalAskSize", "TXoN07P25800.TF-InSize", "TXoN07P25800.TF-OutSize", "TXoN07P25800.TF-TotalBidMatchTx", "TXoN07P25800.TF-TotalAskMatchTx", "TXoN07P25800.TF-BestBid1", "TXoN07P25800.TF-BestBid2", "TXoN07P25800.TF-BestBid3", "TXoN07P25800.TF-BestBid4", "TXoN07P25800.TF-BestBid5", "TXoN07P25800.TF-BestAsk1", "TXoN07P25800.TF-BestAsk2", "TXoN07P25800.TF-BestAsk3", "TXoN07P25800.TF-BestAsk4", "TXoN07P25800.TF-BestAsk5", "TXoN07P25800.TF-BestBidSize1", "TXoN07P25800.TF-BestBidSize2", "TXoN07P25800.TF-BestBidSize3", "TXoN07P25800.TF-BestBidSize4", "TXoN07P25800.TF-BestBidSize5", "TXoN07P25800.TF-BestAskSize1", "TXoN07P25800.TF-BestAskSize2", "TXoN07P25800.TF-BestAskSize3", "TXoN07P25800.TF-BestAskSize4", "TXoN07P25800.TF-BestAskSize5", "TXoN07P25800.TF-Name", "TXoN07P25800.TF-WContractDate", "TXoN07P25800.TF-SettlePrice", "TXoN07P25800.TF-UpLimit", "TXoN07P25800.TF-DownLimit", "TXoN07P25800.TF-OI", "TXoN07P25800.TF-TradingDate", "TXoN07P25800.TF-WRemainDate", "TXoN07P25800.TF-PreClose", "TXoN07P25800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TXoN07P25900", "service": "XQTISC", "topic": "Quote", "items": ["TXoN07P25900.TF-Time", "TXoN07P25900.TF-TradingDate", "TXoN07P25900.TF-Open", "TXoN07P25900.TF-High", "TXoN07P25900.TF-Low", "TXoN07P25900.TF-Price", "TXoN07P25900.TF-TotalVolume", "TXoN07P25900.TF-Volume", "TXoN07P25900.TF-NWTotalBidContract", "TXoN07P25900.TF-NWTotalAskContract", "TXoN07P25900.TF-NWTotalBidSize", "TXoN07P25900.TF-NWTotalAskSize", "TXoN07P25900.TF-InSize", "TXoN07P25900.TF-OutSize", "TXoN07P25900.TF-TotalBidMatchTx", "TXoN07P25900.TF-TotalAskMatchTx", "TXoN07P25900.TF-BestBid1", "TXoN07P25900.TF-BestBid2", "TXoN07P25900.TF-BestBid3", "TXoN07P25900.TF-BestBid4", "TXoN07P25900.TF-BestBid5", "TXoN07P25900.TF-BestAsk1", "TXoN07P25900.TF-BestAsk2", "TXoN07P25900.TF-BestAsk3", "TXoN07P25900.TF-BestAsk4", "TXoN07P25900.TF-BestAsk5", "TXoN07P25900.TF-BestBidSize1", "TXoN07P25900.TF-BestBidSize2", "TXoN07P25900.TF-BestBidSize3", "TXoN07P25900.TF-BestBidSize4", "TXoN07P25900.TF-BestBidSize5", "TXoN07P25900.TF-BestAskSize1", "TXoN07P25900.TF-BestAskSize2", "TXoN07P25900.TF-BestAskSize3", "TXoN07P25900.TF-BestAskSize4", "TXoN07P25900.TF-BestAskSize5", "TXoN07P25900.TF-Name", "TXoN07P25900.TF-WContractDate", "TXoN07P25900.TF-SettlePrice", "TXoN07P25900.TF-UpLimit", "TXoN07P25900.TF-DownLimit", "TXoN07P25900.TF-OI", "TXoN07P25900.TF-TradingDate", "TXoN07P25900.TF-WRemainDate", "TXoN07P25900.TF-PreClose", "TXoN07P25900.TF-PreTotalVolume"], "enabled": true}]