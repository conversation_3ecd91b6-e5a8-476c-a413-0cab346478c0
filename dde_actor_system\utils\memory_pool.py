#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
內存池管理

提供高效的內存池管理功能，包括：
- 對象池管理
- 內存預分配
- 垃圾回收優化
- 內存使用監控
"""

from __future__ import annotations

import gc
import threading
import time
import weakref
from collections import deque
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union
from dataclasses import dataclass

T = TypeVar('T')


@dataclass
class PoolStats:
    """池統計信息"""
    total_created: int = 0
    total_acquired: int = 0
    total_released: int = 0
    current_available: int = 0
    current_in_use: int = 0
    peak_usage: int = 0
    hit_rate: float = 0.0


class MemoryPool:
    """通用內存池
    
    預分配對象以減少GC壓力和提高性能
    """
    
    def __init__(self, object_factory: Callable[[], T], 
                 initial_size: int = 100, 
                 max_size: int = 1000,
                 auto_shrink: bool = True):
        """初始化內存池
        
        Args:
            object_factory: 對象工廠函數
            initial_size: 初始池大小
            max_size: 最大池大小
            auto_shrink: 是否自動收縮
        """
        self.object_factory = object_factory
        self.initial_size = initial_size
        self.max_size = max_size
        self.auto_shrink = auto_shrink
        
        # 池管理
        self.available = deque()
        self.in_use = weakref.WeakSet()
        self.lock = threading.RLock()
        
        # 統計信息
        self.stats = PoolStats()
        
        # 自動收縮
        self.last_shrink_time = time.time()
        self.shrink_interval = 60.0  # 60秒
        
        # 預分配對象
        self._populate_pool(initial_size)
    
    def acquire(self) -> T:
        """獲取對象
        
        Returns:
            T: 池中的對象
        """
        with self.lock:
            if self.available:
                obj = self.available.popleft()
                self.stats.total_acquired += 1
            else:
                # 池為空，創建新對象
                obj = self.object_factory()
                self.stats.total_created += 1
                self.stats.total_acquired += 1
            
            # 添加到使用中集合
            self.in_use.add(obj)
            
            # 更新統計
            current_usage = len(self.in_use)
            if current_usage > self.stats.peak_usage:
                self.stats.peak_usage = current_usage
            
            self._update_hit_rate()
            
            return obj
    
    def release(self, obj: T) -> bool:
        """釋放對象
        
        Args:
            obj: 要釋放的對象
            
        Returns:
            bool: 釋放是否成功
        """
        with self.lock:
            if obj not in self.in_use:
                return False
            
            # 從使用中移除
            self.in_use.discard(obj)
            
            # 重置對象狀態
            if hasattr(obj, 'reset'):
                try:
                    obj.reset()
                except Exception:
                    # 重置失敗，不回收對象
                    return False
            
            # 檢查池大小限制
            if len(self.available) < self.max_size:
                self.available.append(obj)
                self.stats.total_released += 1
                return True
            else:
                # 池已滿，丟棄對象
                return False
    
    def clear(self):
        """清空池"""
        with self.lock:
            self.available.clear()
            self.in_use.clear()
    
    def shrink(self, target_size: Optional[int] = None):
        """收縮池大小
        
        Args:
            target_size: 目標大小，None表示收縮到初始大小
        """
        if target_size is None:
            target_size = self.initial_size
        
        with self.lock:
            while len(self.available) > target_size:
                self.available.popleft()
            
            self.last_shrink_time = time.time()
    
    def auto_shrink_check(self):
        """自動收縮檢查"""
        if not self.auto_shrink:
            return
        
        current_time = time.time()
        if current_time - self.last_shrink_time >= self.shrink_interval:
            # 如果使用率低，進行收縮
            usage_rate = len(self.in_use) / max(len(self.available) + len(self.in_use), 1)
            if usage_rate < 0.3:  # 使用率低於30%
                self.shrink()
    
    def _populate_pool(self, size: int):
        """填充池
        
        Args:
            size: 填充大小
        """
        for _ in range(size):
            try:
                obj = self.object_factory()
                self.available.append(obj)
                self.stats.total_created += 1
            except Exception:
                break  # 創建失敗，停止填充
    
    def _update_hit_rate(self):
        """更新命中率"""
        total_requests = self.stats.total_acquired
        if total_requests > 0:
            hits = total_requests - self.stats.total_created + self.initial_size
            self.stats.hit_rate = max(0.0, hits / total_requests)
    
    def get_stats(self) -> PoolStats:
        """獲取池統計信息
        
        Returns:
            PoolStats: 統計信息
        """
        with self.lock:
            self.stats.current_available = len(self.available)
            self.stats.current_in_use = len(self.in_use)
            return self.stats
    
    def __len__(self) -> int:
        """獲取池總大小"""
        with self.lock:
            return len(self.available) + len(self.in_use)


class ObjectPool:
    """對象池管理器
    
    管理多個不同類型的對象池
    """
    
    def __init__(self):
        """初始化對象池管理器"""
        self.pools: Dict[str, MemoryPool] = {}
        self.lock = threading.RLock()
    
    def create_pool(self, name: str, object_factory: Callable[[], T],
                   initial_size: int = 100, max_size: int = 1000) -> 'MemoryPool[T]':
        """創建對象池
        
        Args:
            name: 池名稱
            object_factory: 對象工廠函數
            initial_size: 初始大小
            max_size: 最大大小
            
        Returns:
            MemoryPool: 創建的對象池
        """
        with self.lock:
            if name in self.pools:
                raise ValueError(f"池 '{name}' 已存在")
            
            pool = MemoryPool(object_factory, initial_size, max_size)
            self.pools[name] = pool
            return pool
    
    def get_pool(self, name: str) -> Optional['MemoryPool']:
        """獲取對象池
        
        Args:
            name: 池名稱
            
        Returns:
            MemoryPool: 對象池，如果不存在則返回None
        """
        with self.lock:
            return self.pools.get(name)
    
    def acquire(self, pool_name: str) -> Optional[Any]:
        """從指定池獲取對象
        
        Args:
            pool_name: 池名稱
            
        Returns:
            Any: 對象，如果池不存在則返回None
        """
        pool = self.get_pool(pool_name)
        if pool:
            return pool.acquire()
        return None
    
    def release(self, pool_name: str, obj: Any) -> bool:
        """釋放對象到指定池
        
        Args:
            pool_name: 池名稱
            obj: 要釋放的對象
            
        Returns:
            bool: 釋放是否成功
        """
        pool = self.get_pool(pool_name)
        if pool:
            return pool.release(obj)
        return False
    
    def clear_pool(self, name: str):
        """清空指定池
        
        Args:
            name: 池名稱
        """
        pool = self.get_pool(name)
        if pool:
            pool.clear()
    
    def clear_all(self):
        """清空所有池"""
        with self.lock:
            for pool in self.pools.values():
                pool.clear()
    
    def remove_pool(self, name: str) -> bool:
        """移除指定池
        
        Args:
            name: 池名稱
            
        Returns:
            bool: 移除是否成功
        """
        with self.lock:
            if name in self.pools:
                self.pools[name].clear()
                del self.pools[name]
                return True
            return False
    
    def get_all_stats(self) -> Dict[str, PoolStats]:
        """獲取所有池的統計信息
        
        Returns:
            Dict[str, PoolStats]: 池統計信息字典
        """
        with self.lock:
            return {name: pool.get_stats() for name, pool in self.pools.items()}
    
    def auto_maintenance(self):
        """自動維護所有池"""
        with self.lock:
            for pool in self.pools.values():
                pool.auto_shrink_check()


class PooledObject:
    """池化對象基類
    
    提供對象重置功能的基類
    """
    
    def reset(self):
        """重置對象狀態
        
        子類應該重寫此方法來重置對象狀態
        """
        pass


class StringPool(MemoryPool):
    """字符串池
    
    專門用於字符串對象的池
    """
    
    def __init__(self, initial_size: int = 100, max_size: int = 1000):
        """初始化字符串池"""
        super().__init__(
            object_factory=lambda: "",
            initial_size=initial_size,
            max_size=max_size
        )


class ListPool(MemoryPool):
    """列表池
    
    專門用於列表對象的池
    """
    
    def __init__(self, initial_size: int = 100, max_size: int = 1000):
        """初始化列表池"""
        super().__init__(
            object_factory=list,
            initial_size=initial_size,
            max_size=max_size
        )
    
    def release(self, obj: List) -> bool:
        """釋放列表對象"""
        if isinstance(obj, list):
            obj.clear()  # 清空列表
        return super().release(obj)


class DictPool(MemoryPool):
    """字典池
    
    專門用於字典對象的池
    """
    
    def __init__(self, initial_size: int = 100, max_size: int = 1000):
        """初始化字典池"""
        super().__init__(
            object_factory=dict,
            initial_size=initial_size,
            max_size=max_size
        )
    
    def release(self, obj: Dict) -> bool:
        """釋放字典對象"""
        if isinstance(obj, dict):
            obj.clear()  # 清空字典
        return super().release(obj)


# 全局對象池管理器
_global_pool_manager = ObjectPool()


def get_global_pool_manager() -> ObjectPool:
    """獲取全局對象池管理器
    
    Returns:
        ObjectPool: 全局對象池管理器
    """
    return _global_pool_manager


def create_global_pool(name: str, object_factory: Callable[[], T],
                      initial_size: int = 100, max_size: int = 1000) -> 'MemoryPool':
    """創建全局對象池
    
    Args:
        name: 池名稱
        object_factory: 對象工廠函數
        initial_size: 初始大小
        max_size: 最大大小
        
    Returns:
        MemoryPool: 創建的對象池
    """
    return _global_pool_manager.create_pool(name, object_factory, initial_size, max_size)


def acquire_from_global_pool(pool_name: str) -> Optional[Any]:
    """從全局池獲取對象
    
    Args:
        pool_name: 池名稱
        
    Returns:
        Any: 對象
    """
    return _global_pool_manager.acquire(pool_name)


def release_to_global_pool(pool_name: str, obj: Any) -> bool:
    """釋放對象到全局池
    
    Args:
        pool_name: 池名稱
        obj: 對象
        
    Returns:
        bool: 釋放是否成功
    """
    return _global_pool_manager.release(pool_name, obj)
