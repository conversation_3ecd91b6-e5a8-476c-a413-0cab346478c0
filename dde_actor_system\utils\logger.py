#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日誌系統

提供統一的日誌管理功能，包括：
- 結構化日誌輸出
- 文件輪轉管理
- 性能優化的日誌記錄
- 多級別日誌過濾
"""

import logging
import logging.handlers
import os
import sys
import time
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional


class StructuredFormatter(logging.Formatter):
    """結構化日誌格式化器"""
    
    def __init__(self, include_extra: bool = True):
        """初始化格式化器
        
        Args:
            include_extra: 是否包含額外字段
        """
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日誌記錄
        
        Args:
            record: 日誌記錄
            
        Returns:
            str: 格式化後的日誌字符串
        """
        # 基本日誌信息
        log_data = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加異常信息
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # 添加額外字段
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 
                              'pathname', 'filename', 'module', 'lineno', 
                              'funcName', 'created', 'msecs', 'relativeCreated',
                              'thread', 'threadName', 'processName', 'process',
                              'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                    extra_fields[key] = value
            
            if extra_fields:
                log_data['extra'] = extra_fields
        
        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))


class PerformanceFilter(logging.Filter):
    """性能優化的日誌過濾器"""
    
    def __init__(self, max_rate: int = 1000):
        """初始化過濾器
        
        Args:
            max_rate: 每秒最大日誌數量
        """
        super().__init__()
        self.max_rate = max_rate
        self.message_counts = {}
        self.last_reset = time.time()
    
    def filter(self, record: logging.LogRecord) -> bool:
        """過濾日誌記錄
        
        Args:
            record: 日誌記錄
            
        Returns:
            bool: 是否允許記錄
        """
        current_time = time.time()
        
        # 每秒重置計數器
        if current_time - self.last_reset >= 1.0:
            self.message_counts.clear()
            self.last_reset = current_time
        
        # 檢查消息頻率
        message_key = f"{record.name}:{record.levelname}"
        count = self.message_counts.get(message_key, 0)
        
        if count >= self.max_rate:
            return False
        
        self.message_counts[message_key] = count + 1
        return True


class AsyncFileHandler(logging.handlers.RotatingFileHandler):
    """異步文件處理器"""
    
    def __init__(self, filename: str, mode: str = 'a', maxBytes: int = 0,
                 backupCount: int = 0, encoding: Optional[str] = None,
                 delay: bool = False, errors: Optional[str] = None):
        """初始化異步文件處理器"""
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay, errors)
        self.buffer = []
        self.buffer_size = 1000
        self.last_flush = time.time()
        self.flush_interval = 1.0  # 1秒刷新一次
    
    def emit(self, record: logging.LogRecord):
        """發送日誌記錄"""
        try:
            msg = self.format(record)
            self.buffer.append(msg + self.terminator)
            
            # 檢查是否需要刷新
            if (len(self.buffer) >= self.buffer_size or 
                time.time() - self.last_flush >= self.flush_interval):
                self._flush_buffer()
                
        except Exception:
            self.handleError(record)
    
    def _flush_buffer(self):
        """刷新緩衝區"""
        if not self.buffer:
            return
        
        try:
            if self.shouldRollover(None):
                self.doRollover()
            
            if self.stream is None:
                self.stream = self._open()
            
            for msg in self.buffer:
                self.stream.write(msg)
            
            self.stream.flush()
            self.buffer.clear()
            self.last_flush = time.time()
            
        except Exception:
            pass  # 忽略刷新錯誤
    
    def close(self):
        """關閉處理器"""
        self._flush_buffer()
        super().close()


def setup_logger(level: str = "INFO", 
                log_file: Optional[str] = None,
                max_size_mb: int = 100,
                backup_count: int = 5,
                enable_console: bool = True,
                enable_structured: bool = False,
                enable_performance_filter: bool = True) -> logging.Logger:
    """設置日誌系統
    
    Args:
        level: 日誌級別
        log_file: 日誌文件路径
        max_size_mb: 最大文件大小（MB）
        backup_count: 備份文件數量
        enable_console: 是否啟用控制台輸出
        enable_structured: 是否使用結構化格式
        enable_performance_filter: 是否啟用性能過濾器
        
    Returns:
        logging.Logger: 配置好的日誌器
    """
    # 獲取根日誌器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, level.upper()))
    
    # 清除現有處理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 創建格式化器
    if enable_structured:
        formatter = StructuredFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # 添加控制台處理器
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        
        if enable_performance_filter:
            console_handler.addFilter(PerformanceFilter(max_rate=100))
        
        root_logger.addHandler(console_handler)
    
    # 添加文件處理器
    if log_file:
        # 確保日誌目錄存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = AsyncFileHandler(
            filename=log_file,
            maxBytes=max_size_mb * 1024 * 1024,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        
        if enable_performance_filter:
            file_handler.addFilter(PerformanceFilter(max_rate=1000))
        
        root_logger.addHandler(file_handler)
    
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """獲取指定名稱的日誌器
    
    Args:
        name: 日誌器名稱
        
    Returns:
        logging.Logger: 日誌器實例
    """
    return logging.getLogger(name)


class LoggerMixin:
    """日誌器混入類
    
    為類提供便捷的日誌記錄功能
    """
    
    @property
    def logger(self) -> logging.Logger:
        """獲取日誌器"""
        if not hasattr(self, '_logger'):
            self._logger = logging.getLogger(self.__class__.__name__)
        return self._logger
    
    def log_debug(self, message: str, **kwargs):
        """記錄調試日誌"""
        self.logger.debug(message, extra=kwargs)
    
    def log_info(self, message: str, **kwargs):
        """記錄信息日誌"""
        self.logger.info(message, extra=kwargs)
    
    def log_warning(self, message: str, **kwargs):
        """記錄警告日誌"""
        self.logger.warning(message, extra=kwargs)
    
    def log_error(self, message: str, **kwargs):
        """記錄錯誤日誌"""
        self.logger.error(message, extra=kwargs)
    
    def log_exception(self, message: str, **kwargs):
        """記錄異常日誌"""
        self.logger.exception(message, extra=kwargs)


class ContextLogger:
    """上下文日誌器
    
    提供帶上下文信息的日誌記錄
    """
    
    def __init__(self, logger: logging.Logger, context: Dict[str, Any]):
        """初始化上下文日誌器
        
        Args:
            logger: 基礎日誌器
            context: 上下文信息
        """
        self.logger = logger
        self.context = context
    
    def _log(self, level: int, message: str, **kwargs):
        """記錄日誌"""
        extra = dict(self.context)
        extra.update(kwargs)
        self.logger.log(level, message, extra=extra)
    
    def debug(self, message: str, **kwargs):
        """記錄調試日誌"""
        self._log(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """記錄信息日誌"""
        self._log(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """記錄警告日誌"""
        self._log(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """記錄錯誤日誌"""
        self._log(logging.ERROR, message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """記錄異常日誌"""
        extra = dict(self.context)
        extra.update(kwargs)
        self.logger.exception(message, extra=extra)


def create_context_logger(name: str, context: Dict[str, Any]) -> ContextLogger:
    """創建上下文日誌器
    
    Args:
        name: 日誌器名稱
        context: 上下文信息
        
    Returns:
        ContextLogger: 上下文日誌器
    """
    logger = logging.getLogger(name)
    return ContextLogger(logger, context)


# 性能監控日誌器
class PerformanceLogger:
    """性能監控日誌器"""
    
    def __init__(self, logger: logging.Logger):
        """初始化性能日誌器
        
        Args:
            logger: 基礎日誌器
        """
        self.logger = logger
        self.start_times = {}
    
    def start_timing(self, operation: str):
        """開始計時
        
        Args:
            operation: 操作名稱
        """
        self.start_times[operation] = time.perf_counter()
    
    def end_timing(self, operation: str, **kwargs):
        """結束計時並記錄
        
        Args:
            operation: 操作名稱
            **kwargs: 額外信息
        """
        if operation in self.start_times:
            elapsed = time.perf_counter() - self.start_times[operation]
            del self.start_times[operation]
            
            self.logger.info(
                f"性能統計: {operation}",
                extra={
                    'operation': operation,
                    'elapsed_ms': elapsed * 1000,
                    **kwargs
                }
            )
    
    def log_metric(self, metric_name: str, value: float, unit: str = "", **kwargs):
        """記錄性能指標
        
        Args:
            metric_name: 指標名稱
            value: 指標值
            unit: 單位
            **kwargs: 額外信息
        """
        self.logger.info(
            f"性能指標: {metric_name} = {value} {unit}",
            extra={
                'metric_name': metric_name,
                'metric_value': value,
                'metric_unit': unit,
                **kwargs
            }
        )
