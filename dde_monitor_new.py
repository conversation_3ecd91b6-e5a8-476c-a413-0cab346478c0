#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 監控程式 - 重構版主程式
使用模組化設計，將功能分拆到不同模組中
"""

import sys
import os
import argparse
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 導入自定義模組
from utils.config_manager import ConfigManager
from utils.logger import setup_logging
from core.auto_manager import AutoConnectManager
from gui.main_window import DDEMainWindow

class DDEMonitorApp:
    """DDE 監控程式主應用類別"""

    def __init__(self, config_file=None):
        self.app = None
        self.main_window = None
        self.config_manager = None
        self.config = None
        self.logger = None
        self.auto_manager = None
        self.config_file = config_file

    def initialize(self):
        """初始化應用程式"""
        try:
            # 創建 QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("DDE Monitor")
            self.app.setApplicationVersion("6.1")

            # 初始化設定管理器
            self.config_manager = ConfigManager()

            # 載入指定的配置文件或默認配置文件
            if self.config_file:
                if not os.path.exists(self.config_file):
                    raise FileNotFoundError(f"指定的配置文件不存在: {self.config_file}")
                self.config = self.config_manager.load_config(self.config_file)
                print(f"使用配置文件: {self.config_file}")
            else:
                self.config = self.config_manager.load_config()
                print("使用默認配置文件: config.ini")
            
            # 初始化日誌系統
            log_file_path = None
            if 'OutputPath' in self.config:
                log_file_path = self.config.get('OutputPath', 'log_file', fallback=None)

            # 讀取日誌級別設定
            log_level = self.config.get('Logging', 'log_level', fallback='WARNING')
            console_log_level = self.config.get('Logging', 'console_log_level', fallback='WARNING')
            file_log_level = self.config.get('Logging', 'file_log_level', fallback='INFO')

            self.logger = setup_logging(log_file_path, log_level, console_log_level, file_log_level)
            self.logger.info("DDE 監控程式啟動")
            
            # 初始化自動化管理器
            self.auto_manager = AutoConnectManager(self.config, self.logger)
            
            # 初始化主視窗
            self.main_window = DDEMainWindow(self.config, self.logger, self.auto_manager)
            
            # 連接信號
            self._connect_signals()
            
            # 啟動自動化管理器
            self.auto_manager.start()
            
            self.logger.info("應用程式初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"應用程式初始化失敗: {str(e)}")
            else:
                print(f"應用程式初始化失敗: {str(e)}")
            return False
    
    def _connect_signals(self):
        """連接信號"""
        try:
            # 連接自動化管理器信號到主視窗
            self.auto_manager.auto_connect_requested.connect(self.main_window.auto_connect)
            self.auto_manager.auto_disconnect_requested.connect(self.main_window.auto_disconnect)
            self.auto_manager.auto_unadvise_only_requested.connect(self.main_window.auto_unadvise_only)
            self.auto_manager.auto_shutdown_requested.connect(self.main_window.auto_shutdown)
            self.auto_manager.status_changed.connect(self.main_window.update_auto_status)

            # 連接主視窗的設定變更信號
            self.main_window.settings_changed.connect(self._on_settings_changed)
            
        except Exception as e:
            self.logger.error(f"連接信號失敗: {str(e)}")
    
    def _on_settings_changed(self, settings):
        """處理設定變更"""
        try:
            # 重新載入設定
            self.config = self.config_manager.load_config()
            
            # 更新自動化管理器設定
            self.auto_manager.load_settings(self.config)
            
            self.logger.info("設定已更新")
            
        except Exception as e:
            self.logger.error(f"處理設定變更失敗: {str(e)}")
    
    def run(self):
        """運行應用程式"""
        try:
            if not self.initialize():
                return 1
            
            # 顯示主視窗
            self.main_window.show()
            
            # 運行應用程式
            return self.app.exec()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"運行應用程式失敗: {str(e)}")
            else:
                print(f"運行應用程式失敗: {str(e)}")
            return 1
    
    def cleanup(self):
        """清理資源"""
        try:
            if self.auto_manager:
                self.auto_manager.stop()
            
            if self.main_window:
                self.main_window.cleanup()
            
            if self.logger:
                self.logger.info("應用程式正常結束")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"清理資源失敗: {str(e)}")

def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(
        description='DDE 監控程式 v6.1 (模組化版)',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用範例:
  python dde_monitor_new.py                    # 使用默認配置文件 config.ini
  python dde_monitor_new.py -c config_a.ini   # 使用指定配置文件 config_a.ini
  python dde_monitor_new.py --config /path/to/my_config.ini  # 使用完整路徑的配置文件

配置文件管理建議:
  1. 為不同的交易品種創建不同的配置文件
  2. 配置文件命名建議: config_品種名.ini (如: config_FITX.ini, config_TXF.ini)
  3. 所有配置文件放在同一個程式目錄下，方便管理
        """
    )

    parser.add_argument(
        '-c', '--config',
        type=str,
        help='指定配置文件路徑 (默認: config.ini)'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='DDE 監控程式 v6.1 (模組化版)'
    )

    return parser.parse_args()

def main():
    """主函數"""
    # 解析命令行參數
    args = parse_arguments()

    # 創建應用程式實例
    app = DDEMonitorApp(config_file=args.config)

    try:
        exit_code = app.run()
    except KeyboardInterrupt:
        print("\n程式被使用者中斷")
        exit_code = 0
    except Exception as e:
        print(f"程式執行失敗: {str(e)}")
        exit_code = 1
    finally:
        app.cleanup()

    return exit_code

if __name__ == "__main__":
    sys.exit(main())
