# DDE Monitor Project - Development Requirements
# ==============================================
# 
# 這個檔案包含開發環境所需的所有依賴
# 包括測試、程式碼品質檢查、文件生成等工具
# 
# 使用方法: 
# pip install -r requirements-minimal.txt
# pip install -r requirements-dev.txt

# 首先安裝基本依賴
-r requirements-minimal.txt

# Testing Framework
# =================
pytest>=7.0.0
# 現代化的 Python 測試框架

pytest-cov>=4.0.0
# pytest 的覆蓋率插件

pytest-mock>=3.10.0
# pytest 的 mock 插件

pytest-qt>=4.2.0
# pytest 的 Qt 測試插件 (用於測試 PySide6 應用)

pytest-xvfb>=3.0.0
# pytest 的虛擬顯示插件 (用於 CI 環境)

# Code Quality Tools
# ==================
flake8>=6.0.0
# Python 程式碼風格檢查工具

black>=23.0.0
# Python 程式碼格式化工具

isort>=5.12.0
# Python import 排序工具

autopep8>=2.0.0
# 自動修復 PEP 8 風格問題

bandit>=1.7.0
# 安全性檢查工具

# Type Checking
# =============
mypy>=1.0.0
# 靜態型別檢查工具

types-requests>=2.31.0
# requests 的型別提示

# Documentation Tools
# ===================
sphinx>=6.0.0
# 文件生成工具

sphinx-rtd-theme>=1.2.0
# Sphinx 的 Read the Docs 主題

sphinx-autodoc-typehints>=1.23.0
# 自動生成型別提示文件

# Performance and Debugging
# =========================
psutil>=5.9.0
# 系統和程序監控工具

memory-profiler>=0.61.0
# 記憶體使用分析工具

line-profiler>=4.0.0
# 行級效能分析工具

py-spy>=0.3.14
# Python 程式效能分析工具

# Development Utilities
# =====================
ipython>=8.12.0
# 增強的 Python 互動式環境

ipdb>=0.13.13
# IPython 除錯器

pdbpp>=0.10.3
# 增強的 Python 除錯器

# Pre-commit Hooks
# ================
pre-commit>=3.3.0
# Git pre-commit hooks 管理工具

# Build and Packaging
# ===================
build>=0.10.0
# 現代化的 Python 套件建構工具

twine>=4.0.0
# PyPI 上傳工具

wheel>=0.40.0
# Python wheel 格式支援

# Environment Management
# ======================
python-dotenv>=1.0.0
# .env 檔案支援

# Mock and Testing Utilities
# ==========================
responses>=0.23.0
# HTTP 請求 mock 工具

freezegun>=1.2.0
# 時間 mock 工具

factory-boy>=3.2.0
# 測試資料生成工具

# Linting Extensions
# ==================
flake8-docstrings>=1.7.0
# 文件字串檢查

flake8-import-order>=0.18.2
# import 順序檢查

flake8-bugbear>=23.0.0
# 常見錯誤檢查

flake8-comprehensions>=3.12.0
# 列表推導式檢查

# Coverage Tools
# ==============
coverage>=7.2.0
# 程式碼覆蓋率工具

coverage-badge>=1.1.0
# 覆蓋率徽章生成

# Jupyter Support (Optional)
# ==========================
jupyter>=1.0.0
# Jupyter Notebook 支援

notebook>=6.5.0
# Jupyter Notebook

jupyterlab>=4.0.0
# JupyterLab

# Git Tools
# =========
gitpython>=3.1.0
# Git 操作工具

# Configuration Management
# ========================
pyyaml>=6.0
# YAML 格式支援

toml>=0.10.2
# TOML 格式支援

# Development Server (if needed)
# ==============================
watchdog>=3.0.0
# 檔案變化監控工具

# Benchmarking
# ============
pytest-benchmark>=4.0.0
# 效能基準測試

# Security
# ========
safety>=2.3.0
# 依賴安全性檢查

# Documentation Dependencies
# ==========================
myst-parser>=2.0.0
# Markdown 解析器 for Sphinx

sphinx-copybutton>=0.5.0
# 文件中的複製按鈕

# Development Notes:
# ==================
# 
# 安裝順序建議:
# 1. pip install -r requirements-minimal.txt
# 2. pip install -r requirements-dev.txt
# 
# 常用開發命令:
# - 執行測試: pytest
# - 程式碼格式化: black .
# - 程式碼檢查: flake8
# - 型別檢查: mypy dde_monitor.py
# - 生成文件: sphinx-build -b html docs docs/_build
# 
# Pre-commit 設置:
# - pre-commit install
# - pre-commit run --all-files
