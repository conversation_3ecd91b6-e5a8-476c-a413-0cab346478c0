#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引擎包装器配置管理器
负责多商品DDE引擎包装器的配置管理
"""

import configparser
import os
import logging
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass

@dataclass
class ProductConfig:
    """商品配置数据类"""
    symbol: str
    data_types: List[str]
    dde_service: str
    dde_topic: str
    output_path: str
    auto_connect: bool = False
    auto_connect_times: List[str] = None
    
    def __post_init__(self):
        if self.auto_connect_times is None:
            self.auto_connect_times = []

@dataclass
class EngineConfig:
    """引擎配置数据类"""
    max_engines: int = 20
    engine_timeout: float = 30.0
    restart_on_failure: bool = True
    performance_monitoring: bool = True
    log_level: str = 'INFO'

class WrapperConfigManager:
    """引擎包装器配置管理器
    
    专为多商品DDE引擎包装器设计的配置管理器，支持：
    - 多商品配置管理
    - 模板化配置
    - 引擎参数配置
    - 配置验证和热重载
    """
    
    def __init__(self, config_file: str = 'wrapper_config.ini'):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.logger = logging.getLogger(__name__)
        
        # 配置数据
        self.products: Dict[str, ProductConfig] = {}
        self.engine_config: EngineConfig = EngineConfig()
        self.templates: Dict[str, Dict[str, Any]] = {}
        self.global_settings: Dict[str, Any] = {}
        
    def load_config(self, config_file: str = None) -> bool:
        """载入配置文件"""
        try:
            if config_file:
                self.config_file = config_file
                
            if not os.path.exists(self.config_file):
                self.logger.warning(f"配置文件不存在，创建默认配置: {self.config_file}")
                self._create_default_config()
                return True
                
            self.config.read(self.config_file, encoding='utf-8')
            self.logger.info(f"配置文件载入成功: {self.config_file}")
            
            # 解析各个配置区块
            self._parse_global_settings()
            self._parse_engine_config()
            self._parse_templates()
            self._parse_products()
            
            # 验证配置
            validation_errors = self.validate_config()
            if validation_errors:
                self.logger.error(f"配置验证失败: {validation_errors}")
                return False
                
            self.logger.info("配置载入和验证完成")
            return True
            
        except Exception as e:
            self.logger.error(f"载入配置失败: {str(e)}")
            return False
    
    def _parse_global_settings(self):
        """解析全局设置"""
        try:
            if 'Global' in self.config:
                self.global_settings = dict(self.config['Global'])
                self.logger.info("全局设置载入完成")
            else:
                self.global_settings = self._get_default_global_settings()
                self.logger.info("使用默认全局设置")
                
        except Exception as e:
            self.logger.error(f"解析全局设置失败: {str(e)}")
            self.global_settings = self._get_default_global_settings()
    
    def _parse_engine_config(self):
        """解析引擎配置"""
        try:
            if 'Engine' in self.config:
                engine_section = self.config['Engine']
                self.engine_config = EngineConfig(
                    max_engines=engine_section.getint('max_engines', 20),
                    engine_timeout=engine_section.getfloat('engine_timeout', 30.0),
                    restart_on_failure=engine_section.getboolean('restart_on_failure', True),
                    performance_monitoring=engine_section.getboolean('performance_monitoring', True),
                    log_level=engine_section.get('log_level', 'INFO')
                )
                self.logger.info("引擎配置载入完成")
            else:
                self.engine_config = EngineConfig()
                self.logger.info("使用默认引擎配置")
                
        except Exception as e:
            self.logger.error(f"解析引擎配置失败: {str(e)}")
            self.engine_config = EngineConfig()
    
    def _parse_templates(self):
        """解析配置模板"""
        try:
            self.templates = {}
            for section_name in self.config.sections():
                if section_name.startswith('Template_'):
                    template_name = section_name.replace('Template_', '')
                    template_data = dict(self.config[section_name])
                    self.templates[template_name] = template_data
                    self.logger.info(f"载入模板: {template_name}")
                    
        except Exception as e:
            self.logger.error(f"解析模板失败: {str(e)}")
    
    def _parse_products(self):
        """解析商品配置"""
        try:
            self.products = {}
            
            # 获取启用的商品列表
            if 'Products' not in self.config:
                self.logger.warning("未找到 Products 配置区块")
                return
                
            product_list = self.config.get('Products', 'enabled_products', fallback='')
            enabled_products = [p.strip() for p in product_list.split(',') if p.strip()]
            
            for product_symbol in enabled_products:
                if product_symbol in self.config:
                    product_section = self.config[product_symbol]
                    
                    # 解析数据类型
                    data_types_str = product_section.get('data_types', '')
                    data_types = [dt.strip() for dt in data_types_str.split(',') if dt.strip()]
                    
                    # 解析自动连接时间
                    auto_connect_times_str = product_section.get('auto_connect_times', '')
                    auto_connect_times = [t.strip() for t in auto_connect_times_str.split(',') if t.strip()]
                    
                    # 创建商品配置
                    product_config = ProductConfig(
                        symbol=product_symbol,
                        data_types=data_types,
                        dde_service=product_section.get('dde_service', ''),
                        dde_topic=product_section.get('dde_topic', ''),
                        output_path=product_section.get('output_path', f'./outputs/{product_symbol}'),
                        auto_connect=product_section.getboolean('auto_connect', False),
                        auto_connect_times=auto_connect_times
                    )
                    
                    self.products[product_symbol] = product_config
                    self.logger.info(f"载入商品配置: {product_symbol}, 数据类型: {len(data_types)}")
                else:
                    self.logger.warning(f"商品 {product_symbol} 的配置区块不存在")
                    
        except Exception as e:
            self.logger.error(f"解析商品配置失败: {str(e)}")
    
    def validate_config(self) -> List[str]:
        """验证配置"""
        errors = []
        
        try:
            # 验证引擎配置
            if self.engine_config.max_engines <= 0:
                errors.append("引擎最大数量必须大于0")
                
            if self.engine_config.engine_timeout <= 0:
                errors.append("引擎超时时间必须大于0")
            
            # 验证商品配置
            if not self.products:
                errors.append("至少需要配置一个商品")
            
            for symbol, product in self.products.items():
                if not product.dde_service:
                    errors.append(f"商品 {symbol} 缺少 DDE 服务名称")
                    
                if not product.dde_topic:
                    errors.append(f"商品 {symbol} 缺少 DDE 主题")
                    
                if not product.data_types:
                    errors.append(f"商品 {symbol} 缺少数据类型配置")
                    
                if not product.output_path:
                    errors.append(f"商品 {symbol} 缺少输出路径配置")
            
        except Exception as e:
            errors.append(f"配置验证过程出错: {str(e)}")
            
        return errors

    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            # 更新配置对象
            self._update_config_from_data()

            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)

            self.logger.info(f"配置文件保存成功: {self.config_file}")
            return True

        except Exception as e:
            self.logger.error(f"保存配置文件失败: {str(e)}")
            return False

    def get_product_config(self, symbol: str) -> Optional[ProductConfig]:
        """获取商品配置"""
        return self.products.get(symbol)

    def get_all_products(self) -> Dict[str, ProductConfig]:
        """获取所有商品配置"""
        return self.products.copy()

    def add_product(self, product_config: ProductConfig) -> bool:
        """添加商品配置"""
        try:
            self.products[product_config.symbol] = product_config
            self.logger.info(f"添加商品配置: {product_config.symbol}")
            return True
        except Exception as e:
            self.logger.error(f"添加商品配置失败: {str(e)}")
            return False

    def remove_product(self, symbol: str) -> bool:
        """移除商品配置"""
        try:
            if symbol in self.products:
                del self.products[symbol]
                self.logger.info(f"移除商品配置: {symbol}")
                return True
            else:
                self.logger.warning(f"商品配置不存在: {symbol}")
                return False
        except Exception as e:
            self.logger.error(f"移除商品配置失败: {str(e)}")
            return False

    def get_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """获取配置模板"""
        return self.templates.get(template_name)

    def apply_template_to_product(self, symbol: str, template_name: str,
                                 template_vars: Dict[str, str] = None) -> bool:
        """将模板应用到商品配置"""
        try:
            template = self.get_template(template_name)
            if not template:
                self.logger.error(f"模板不存在: {template_name}")
                return False

            if template_vars is None:
                template_vars = {'symbol': symbol}
            else:
                template_vars['symbol'] = symbol

            # 应用模板变量替换
            applied_config = {}
            for key, value in template.items():
                if isinstance(value, str):
                    for var_name, var_value in template_vars.items():
                        value = value.replace(f'{{{var_name}}}', var_value)
                applied_config[key] = value

            self.logger.info(f"模板 {template_name} 应用到商品 {symbol}")
            return True

        except Exception as e:
            self.logger.error(f"应用模板失败: {str(e)}")
            return False

    def _get_default_global_settings(self) -> Dict[str, Any]:
        """获取默认全局设置"""
        return {
            'app_name': 'DDE引擎包装器',
            'version': '1.0.0',
            'log_level': 'INFO',
            'log_file': './logs/wrapper.log',
            'data_output_base': './outputs',
            'config_backup_enabled': 'true',
            'performance_monitoring': 'true'
        }

    def _update_config_from_data(self):
        """从数据更新配置对象"""
        try:
            # 清空现有配置
            self.config.clear()

            # 添加全局设置
            if self.global_settings:
                self.config.add_section('Global')
                for key, value in self.global_settings.items():
                    self.config.set('Global', key, str(value))

            # 添加引擎配置
            self.config.add_section('Engine')
            self.config.set('Engine', 'max_engines', str(self.engine_config.max_engines))
            self.config.set('Engine', 'engine_timeout', str(self.engine_config.engine_timeout))
            self.config.set('Engine', 'restart_on_failure', str(self.engine_config.restart_on_failure))
            self.config.set('Engine', 'performance_monitoring', str(self.engine_config.performance_monitoring))
            self.config.set('Engine', 'log_level', self.engine_config.log_level)

            # 添加商品列表
            if self.products:
                self.config.add_section('Products')
                product_list = ','.join(self.products.keys())
                self.config.set('Products', 'enabled_products', product_list)

                # 添加每个商品的配置
                for symbol, product in self.products.items():
                    self.config.add_section(symbol)
                    self.config.set(symbol, 'data_types', ','.join(product.data_types))
                    self.config.set(symbol, 'dde_service', product.dde_service)
                    self.config.set(symbol, 'dde_topic', product.dde_topic)
                    self.config.set(symbol, 'output_path', product.output_path)
                    self.config.set(symbol, 'auto_connect', str(product.auto_connect))
                    self.config.set(symbol, 'auto_connect_times', ','.join(product.auto_connect_times))

            # 添加模板
            for template_name, template_data in self.templates.items():
                section_name = f'Template_{template_name}'
                self.config.add_section(section_name)
                for key, value in template_data.items():
                    self.config.set(section_name, key, str(value))

        except Exception as e:
            self.logger.error(f"更新配置对象失败: {str(e)}")

    def _create_default_config(self):
        """创建默认配置文件"""
        try:
            # 设置默认数据
            self.global_settings = self._get_default_global_settings()
            self.engine_config = EngineConfig()

            # 创建示例商品配置
            example_product = ProductConfig(
                symbol='FITXN07',
                data_types=['tick', 'order'],
                dde_service='SKCOM',
                dde_topic='SKCOM',
                output_path='./outputs/FITXN07',
                auto_connect=False,
                auto_connect_times=['08:45:00', '15:00:00']
            )
            self.products['FITXN07'] = example_product

            # 创建示例模板
            self.templates['futures_template'] = {
                'dde_service': 'SKCOM',
                'dde_topic': 'SKCOM',
                'output_path': './outputs/{symbol}',
                'data_types': 'tick,order,level2,daily'
            }

            # 保存配置文件
            self.save_config()
            self.logger.info("默认配置文件创建完成")

        except Exception as e:
            self.logger.error(f"创建默认配置失败: {str(e)}")

    def reload_config(self) -> bool:
        """重新载入配置文件"""
        return self.load_config()

    def backup_config(self, backup_suffix: str = None) -> bool:
        """备份配置文件"""
        try:
            if backup_suffix is None:
                from datetime import datetime
                backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")

            backup_file = f"{self.config_file}.backup_{backup_suffix}"

            if os.path.exists(self.config_file):
                import shutil
                shutil.copy2(self.config_file, backup_file)
                self.logger.info(f"配置文件备份成功: {backup_file}")
                return True
            else:
                self.logger.warning("原配置文件不存在，无法备份")
                return False

        except Exception as e:
            self.logger.error(f"备份配置文件失败: {str(e)}")
            return False
