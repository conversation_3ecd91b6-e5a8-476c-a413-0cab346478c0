#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE Actor System - 工具模組

提供系統所需的工具和輔助功能，包括：
- 配置管理
- 日誌系統
- 性能分析
- 內存池管理
"""

from .config_manager import Config<PERSON>anager, DynamicConfigManager
from .logger import setup_logger, get_logger

# 嘗試導入可選模組
try:
    from .memory_pool import MemoryPool, ObjectPool
except ImportError:
    MemoryPool = None
    ObjectPool = None

try:
    from .profiler import SystemProfiler, PerformanceProfiler
except ImportError:
    SystemProfiler = None
    PerformanceProfiler = None

# 動態構建 __all__ 列表
__all__ = [
    'ConfigManager',
    'DynamicConfigManager',
    'setup_logger',
    'get_logger'
]

# 添加可用的可選模組
if MemoryPool is not None:
    __all__.extend(['MemoryPool', 'ObjectPool'])

if SystemProfiler is not None:
    __all__.extend(['SystemProfiler', 'PerformanceProfiler'])

__version__ = '1.0.0'
