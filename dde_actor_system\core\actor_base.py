#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Actor基礎類實現

提供Actor模型的核心抽象，包括：
- Actor生命週期管理
- 消息處理機制
- 狀態管理
- 錯誤處理和恢復
"""

import asyncio
import logging
import time
import uuid
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, Optional, Callable, List
from dataclasses import dataclass

from .message_system import Message, MessageType


class ActorState(Enum):
    """Actor狀態枚舉"""
    CREATED = "created"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class ActorStats:
    """Actor統計信息"""
    messages_received: int = 0
    messages_sent: int = 0
    messages_processed: int = 0
    messages_failed: int = 0
    start_time: float = 0.0
    last_activity_time: float = 0.0
    processing_time_total: float = 0.0
    error_count: int = 0


class ActorBase(ABC):
    """Actor基礎類
    
    所有Actor都應該繼承此類並實現handle_message方法
    """
    
    def __init__(self, name: str, config: Optional[Dict] = None):
        """初始化Actor
        
        Args:
            name: Actor名稱，必須唯一
            config: Actor配置字典
        """
        self.name = name
        self.actor_id = str(uuid.uuid4())
        self.config = config or {}
        self.state = ActorState.CREATED
        self.stats = ActorStats()
        
        # 消息處理
        self.message_queue = asyncio.Queue(
            maxsize=self.config.get('max_queue_size', 10000)
        )
        self.message_handlers: Dict[MessageType, Callable] = {}
        
        # 生命週期管理
        self.running = False
        self.task: Optional[asyncio.Task] = None
        self.shutdown_event = asyncio.Event()
        
        # 錯誤處理
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 1.0)
        
        # 日誌
        self.logger = logging.getLogger(f"Actor.{self.name}")
        
        # 性能監控
        self.enable_profiling = self.config.get('enable_profiling', False)
        self.message_router: Optional['MessageRouter'] = None
        
    async def start(self) -> bool:
        """啟動Actor
        
        Returns:
            bool: 啟動是否成功
        """
        try:
            if self.state != ActorState.CREATED:
                self.logger.warning(f"Actor {self.name} 已經啟動或處於錯誤狀態")
                return False
                
            self.logger.info(f"正在啟動Actor: {self.name}")
            self.state = ActorState.STARTING
            
            # 初始化Actor
            await self.on_start()
            
            # 啟動消息處理循環
            self.running = True
            self.task = asyncio.create_task(self._message_loop())
            
            self.state = ActorState.RUNNING
            self.stats.start_time = time.time()
            self.stats.last_activity_time = time.time()
            
            self.logger.info(f"Actor {self.name} 啟動成功")
            return True
            
        except Exception as e:
            self.logger.error(f"啟動Actor {self.name} 失敗: {str(e)}")
            self.state = ActorState.ERROR
            return False
    
    async def stop(self) -> bool:
        """停止Actor
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if self.state not in [ActorState.RUNNING, ActorState.ERROR]:
                self.logger.warning(f"Actor {self.name} 未在運行狀態")
                return False
                
            self.logger.info(f"正在停止Actor: {self.name}")
            self.state = ActorState.STOPPING
            
            # 停止消息處理循環
            self.running = False
            self.shutdown_event.set()
            
            # 等待任務完成
            if self.task and not self.task.done():
                try:
                    await asyncio.wait_for(self.task, timeout=5.0)
                except asyncio.TimeoutError:
                    self.logger.warning(f"Actor {self.name} 停止超時，強制取消")
                    self.task.cancel()
                    
            # 清理資源
            await self.on_stop()
            
            self.state = ActorState.STOPPED
            self.logger.info(f"Actor {self.name} 停止成功")
            return True
            
        except Exception as e:
            self.logger.error(f"停止Actor {self.name} 失敗: {str(e)}")
            self.state = ActorState.ERROR
            return False
    
    async def send_message(self, target: str, message: Message) -> bool:
        """發送消息到目標Actor
        
        Args:
            target: 目標Actor名稱
            message: 要發送的消息
            
        Returns:
            bool: 發送是否成功
        """
        try:
            if not self.message_router:
                self.logger.error("消息路由器未設置")
                return False
                
            message.sender = self.name
            success = await self.message_router.send_message(target, message)
            
            if success:
                self.stats.messages_sent += 1
            else:
                self.stats.messages_failed += 1
                
            return success
            
        except Exception as e:
            self.logger.error(f"發送消息失敗: {str(e)}")
            self.stats.messages_failed += 1
            return False
    
    async def receive_message(self, message: Message) -> bool:
        """接收消息
        
        Args:
            message: 接收到的消息
            
        Returns:
            bool: 接收是否成功
        """
        try:
            if not self.running:
                return False
                
            await self.message_queue.put(message)
            self.stats.messages_received += 1
            return True
            
        except asyncio.QueueFull:
            self.logger.warning(f"Actor {self.name} 消息隊列已滿，丟棄消息")
            return False
        except Exception as e:
            self.logger.error(f"接收消息失敗: {str(e)}")
            return False
    
    def register_message_handler(self, message_type: MessageType, 
                                handler: Callable[[Message], Any]):
        """註冊消息處理器
        
        Args:
            message_type: 消息類型
            handler: 處理函數
        """
        self.message_handlers[message_type] = handler
        self.logger.debug(f"註冊消息處理器: {message_type}")
    
    def set_message_router(self, router: 'MessageRouter'):
        """設置消息路由器
        
        Args:
            router: 消息路由器實例
        """
        self.message_router = router
    
    async def _message_loop(self):
        """消息處理循環"""
        self.logger.debug(f"Actor {self.name} 消息處理循環開始")
        
        while self.running:
            try:
                # 等待消息或關閉事件
                message_task = asyncio.create_task(self.message_queue.get())
                shutdown_task = asyncio.create_task(self.shutdown_event.wait())
                
                done, pending = await asyncio.wait(
                    [message_task, shutdown_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # 取消未完成的任務
                for task in pending:
                    task.cancel()
                
                # 檢查是否收到關閉信號
                if shutdown_task in done:
                    break
                
                # 處理消息
                if message_task in done:
                    message = message_task.result()
                    await self._process_message(message)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"消息處理循環錯誤: {str(e)}")
                self.stats.error_count += 1
                
        self.logger.debug(f"Actor {self.name} 消息處理循環結束")
    
    async def _process_message(self, message: Message):
        """處理單個消息"""
        start_time = time.time()
        
        try:
            self.stats.last_activity_time = time.time()
            
            # 檢查是否有專用處理器
            if message.type in self.message_handlers:
                await self.message_handlers[message.type](message)
            else:
                # 使用通用處理器
                await self.handle_message(message)
                
            self.stats.messages_processed += 1
            
        except Exception as e:
            self.logger.error(f"處理消息失敗: {str(e)}, 消息: {message}")
            self.stats.messages_failed += 1
            self.stats.error_count += 1
            
            # 錯誤恢復
            await self.on_error(e, message)
            
        finally:
            # 更新處理時間統計
            processing_time = time.time() - start_time
            self.stats.processing_time_total += processing_time
    
    @abstractmethod
    async def handle_message(self, message: Message):
        """處理消息的抽象方法
        
        子類必須實現此方法來處理接收到的消息
        
        Args:
            message: 要處理的消息
        """
        pass
    
    async def on_start(self):
        """Actor啟動時的回調方法
        
        子類可以重寫此方法來執行初始化邏輯
        """
        pass
    
    async def on_stop(self):
        """Actor停止時的回調方法
        
        子類可以重寫此方法來執行清理邏輯
        """
        pass
    
    async def on_error(self, error: Exception, message: Optional[Message] = None):
        """錯誤處理回調方法
        
        子類可以重寫此方法來實現自定義錯誤處理
        
        Args:
            error: 發生的錯誤
            message: 導致錯誤的消息（如果有）
        """
        self.logger.error(f"Actor {self.name} 發生錯誤: {str(error)}")
    
    def get_stats(self) -> ActorStats:
        """獲取Actor統計信息
        
        Returns:
            ActorStats: 統計信息對象
        """
        return self.stats
    
    def get_info(self) -> Dict[str, Any]:
        """獲取Actor信息
        
        Returns:
            Dict: Actor信息字典
        """
        return {
            'name': self.name,
            'actor_id': self.actor_id,
            'state': self.state.value,
            'queue_size': self.message_queue.qsize(),
            'stats': {
                'messages_received': self.stats.messages_received,
                'messages_sent': self.stats.messages_sent,
                'messages_processed': self.stats.messages_processed,
                'messages_failed': self.stats.messages_failed,
                'error_count': self.stats.error_count,
                'uptime': time.time() - self.stats.start_time if self.stats.start_time > 0 else 0,
                'avg_processing_time': (
                    self.stats.processing_time_total / self.stats.messages_processed
                    if self.stats.messages_processed > 0 else 0
                )
            }
        }
    
    def __repr__(self) -> str:
        return f"Actor(name={self.name}, state={self.state.value})"
