#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE Actor System - Actor實現模組

提供各種專用Actor實現，包括：
- DDE數據接收Actor
- 數據處理Actor
- GUI更新Actor
- 文件寫入Actor
"""

from .dde_receiver import DDEReceiverActor
from .data_processor import DataProcessor<PERSON>ctor
from .gui_updater import GUIUpdaterActor
from .file_writer import FileWriterActor

__all__ = [
    'DDEReceiverActor',
    'DataProcessorActor', 
    'GUIUpdaterActor',
    'FileWriterActor'
]

__version__ = '1.0.0'
