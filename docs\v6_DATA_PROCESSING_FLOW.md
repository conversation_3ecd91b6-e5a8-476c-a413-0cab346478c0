# DDE v6版資料處理流程文件

## 概述

本文件詳細說明 DDE v6版 (`dde_monitor_v6.py`) 從接收資料到完整輸出的完整流程。v6版是基於PySide6的GUI應用程式，採用單一檔案架構，包含完整的DDE監控功能。

## 核心架構

### 主要類別
- **DDEMainWindow**: 主視窗類別，包含所有功能
- **DataFileHandler**: 檔案處理類別
- **ItemData**: DDE項目資料結構
- **RawDataRow**: 原始資料行結構

### 資料結構
```python
@dataclass
class ItemData:
    name: str                           # 項目名稱
    code: str                           # DDE項目代碼
    value: Optional[str] = None         # 當前值
    update_time: Optional[datetime] = None  # 最後更新時間
    status: str = "未訂閱"              # 訂閱狀態

@dataclass
class RawDataRow:
    receive_date: str                   # 接收日期 (YYYY-MM-DD)
    receive_time: str                   # 接收時間 (HH:MM:SS.ffffff)
    values: Dict[str, str]              # 項目值字典，key為項目名稱，value為項目值
    is_complete: bool = False           # 是否為完整資料行
```

## 完整資料處理流程

### 1. 資料接收入口 (`on_advise_data` 方法)

```python
def on_advise_data(self, item: str, value: str):
    """處理DDE資料更新"""
    try:
        # 將資料加入處理佇列
        self.data_processor.add_data(item, value)
    except Exception as e:
        self.logger.error(f"加入資料到佇列失敗: {str(e)}")
```

**特點**：
- 使用佇列機制處理資料
- 非同步處理，避免阻塞DDE回調

### 2. 實際資料處理 (`_process_advise_data` 方法)

```python
def _process_advise_data(self, item: str, value: str):
    """實際處理資料的方法"""
    try:
        # 1. 檢查是否需要項目重複換行
        if self.check_item_repeat_newline(item, value):
            return
            
        # 2. 更新原始資料
        self.update_raw_data(item, value)
        
        # 3. 更新項目資料
        self.update_items_data(item, value)
        
        # 4. 更新最後接收時間
        self.last_advise_time = time.time()
        
    except Exception as e:
        self.logger.error(f"處理advise數據失敗: {str(e)}")
```

**處理順序**：
1. 項目重複換行檢查
2. 原始資料更新
3. 項目資料更新
4. 時間記錄更新

### 3. 項目重複換行檢查 (`check_item_repeat_newline` 方法)

```python
def check_item_repeat_newline(self, item: str, value: str) -> bool:
    """檢查是否需要項目重複換行"""
    if not self.raw_data:
        return False
        
    current_row = self.raw_data[0]
    
    # 檢查項目是否已存在於當前行
    if item in current_row.values:
        # 補齊缺失資料
        if self.has_missing_data(current_row):
            self.fill_missing_data(current_row)
            
        # 如果啟用了值變化檢查，則檢查值是否有變化
        if self.enable_value_change_check:
            has_changed = self.check_value_change(current_row)
            
            if has_changed:
                # 儲存完整資料行
                self.file_handler.save_row(current_row, is_complete=True)
                # 建立新行
                self.create_new_row()
                # 將收到的項目值填入新行
                new_row = self.raw_data[0]
                new_row.values[item] = value
                new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                return True
            else:
                # 值未變化，不儲存資料行，但建立新行
                self.create_new_row()
                # 將收到的項目值填入新行
                new_row = self.raw_data[0]
                new_row.values[item] = value
                new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                return True
        else:
            # 未啟用值變化檢查，直接儲存資料行
            self.file_handler.save_row(current_row, is_complete=True)
            self.create_new_row()
            new_row = self.raw_data[0]
            new_row.values[item] = value
            new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
            new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
            return True
            
    return False
```

**重複檢查邏輯**：
1. 檢查項目是否已存在於當前行
2. 補齊缺失資料
3. 根據值變化檢查設定決定是否保存
4. 建立新行並填入新項目值

### 4. 時間間隔檢查 (`check_time_interval` 和 `check_time_interval_newline` 方法)

```python
def check_time_interval(self):
    """檢查時間間隔"""
    if not self.enable_time_newline:
        return
        
    now = time.time()
    
    if (self.last_advise_time is not None and 
        now - self.last_advise_time >= self.time_newline_interval):
        
        self.check_time_interval_newline()
        self.last_advise_time = now

def check_time_interval_newline(self):
    """檢查是否需要時間間隔換行"""
    if not self.enable_time_newline:
        return
        
    if not self.raw_data:
        return
        
    current_row = self.raw_data[0]
    
    # 檢查行是否有資料
    if not current_row.values:
        return
        
    # 檢查並補齊缺失資料
    if self.has_missing_data(current_row):
        self.fill_missing_data(current_row)
        
    # 如果啟用了值變化檢查，則檢查值是否有變化
    if self.enable_value_change_check:
        has_changed = self.check_value_change(current_row)
        
        if has_changed:
            # 儲存完整資料行
            self.file_handler.save_row(current_row, is_complete=True)
            # 建立新行
            self.create_new_row()
        else:
            # 值未變化，不儲存資料行，但建立新行
            self.create_new_row()
    else:
        # 未啟用值變化檢查，直接儲存資料行
        self.file_handler.save_row(current_row, is_complete=True)
        self.create_new_row()
```

**時間間隔檢查特點**：
- 定期檢查是否超過設定的時間間隔
- 與項目重複檢查使用相同的邏輯
- 支援值變化檢查

### 5. 值變化檢查 (`check_value_change` 方法)

v6版支援三種值變化檢查模式：

#### 5.1 單項檢查模式 (single)
```python
if check_mode == 'single':
    # 單一項目檢查
    check_item_name = self.config.get('Table', 'value_change_check_items', fallback='')
    if not check_item_name:
        return True
        
    # 獲取檢查項目的代碼
    check_item_code = None
    for code, item_data in self.items_data.items():
        if item_data.name == check_item_name:
            check_item_code = code
            break
            
    if not check_item_code:
        return True
        
    # 檢查值是否變化
    current_value = current_row.values.get(check_item_code, "")
    previous_value = previous_row.values.get(check_item_code, "")
    has_changed = current_value != previous_value
    
    return has_changed
```

#### 5.2 多項檢查模式 (multiple)
```python
elif check_mode == 'multiple':
    # 多個項目檢查
    check_items_str = self.config.get('Table', 'value_change_check_items', fallback='')
    if not check_items_str:
        return True
        
    check_item_names = [item.strip() for item in check_items_str.split(',')]
    
    # 檢查每個項目
    for check_item_name in check_item_names:
        # 獲取檢查項目的代碼
        check_item_code = None
        for code, item_data in self.items_data.items():
            if item_data.name == check_item_name:
                check_item_code = code
                break
                
        if not check_item_code:
            continue
            
        # 檢查值是否變化
        current_value = current_row.values.get(check_item_code, "")
        previous_value = previous_row.values.get(check_item_code, "")
        
        if current_value != previous_value:
            return True
    
    return False
```

#### 5.3 全部檢查模式 (all)
```python
elif check_mode == 'all':
    # 全部項目檢查 - 檢查所有DDE項目（排除時間戳記）
    for item_code in current_row.values.keys():
        current_value = current_row.values.get(item_code, "")
        previous_value = previous_row.values.get(item_code, "")
        if current_value != previous_value:
            return True
    
    return False
```

### 6. 資料補齊 (`fill_missing_data` 方法)

```python
def fill_missing_data(self, row: RawDataRow):
    """補齊缺失的資料項目"""
    try:
        for code, item_data in self.items_data.items():
            if code not in row.values and item_data.value is not None:
                row.values[code] = item_data.value
                
    except Exception as e:
        logging.error(f"補齊缺失資料失敗: {str(e)}")
        raise
```

### 7. 原始資料更新 (`update_raw_data` 方法)

```python
def update_raw_data(self, item: str, value: str):
    """更新原始資料"""
    try:
        now = datetime.now()
        current_row = None
        
        # 檢查是否需要建立新行
        if not self.raw_data:
            current_row = RawDataRow(
                receive_date=now.strftime("%Y-%m-%d"),
                receive_time=now.strftime("%H:%M:%S.%f"),
                values={}
            )
            self.raw_data.appendleft(current_row)
        else:
            current_row = self.raw_data[0]
            
        # 更新值
        current_row.values[item] = value
        current_row.receive_date = now.strftime("%Y-%m-%d")
        current_row.receive_time = now.strftime("%H:%M:%S.%f")
        
    except Exception as e:
        self.logger.error(f"更新原始資料失敗: {str(e)}")
```

## 檔案處理機制

### DataFileHandler 檔案處理器

```python
class DataFileHandler:
    """檔案處理類別"""
    
    def __init__(self):
        self.data_file = None
        self.complete_data_file = None
        self.logger = logging.getLogger("DataFileHandler")
        self.items_data = None
        self.enable_data_file = True
        self.enable_complete_data_file = True
        self.enable_log_file = True
        self.config = None
```

### 檔案初始化
```python
def init_files(self, config: configparser.ConfigParser, items_data: Dict[str, ItemData]):
    """初始化檔案"""
    # 讀取檔案輸出控制設定
    self.enable_data_file = config.getboolean('FileOutput', 'enable_data_file', fallback=True)
    self.enable_complete_data_file = config.getboolean('FileOutput', 'enable_complete_data_file', fallback=True)
    self.enable_log_file = config.getboolean('FileOutput', 'enable_log_file', fallback=True)
    
    # 獲取當前日期
    now = datetime.now()
    date_str = now.strftime('%Y%m%d')
    
    # 格式化檔案路徑
    data_file_path = config.get('OutputPath', 'data_file', fallback='dde_data_{date}.csv')
    complete_data_file_path = config.get('OutputPath', 'complete_data_file', fallback='complete_data_{date}.csv')
    
    self.data_file = data_file_path.format(date=date_str)
    self.complete_data_file = complete_data_file_path.format(date=date_str)
```

## 配置管理

### 主要配置項目
```ini
[Table]
enable_time_newline = true
time_newline_interval = 0.800
enable_value_change_check = true
value_change_check_mode = single
value_change_check_items = 總量

[FileOutput]
enable_data_file = true
enable_complete_data_file = true
enable_log_file = true

[OutputPath]
data_file = dde_data_{date}.csv
complete_data_file = complete_data_{date}.csv
log_file = dde_log_{date}.txt
```

## 關鍵特性

### 1. 換行觸發條件
- **項目重複**: 當收到已存在於當前行中的項目資料時
- **時間間隔**: 當距離上次更新超過設定的時間間隔時

### 2. 資料完整性保證
- 每次換行前檢查並補齊缺失資料
- 使用項目資料容器中的最新值補齊缺失資料
- 確保每行資料都包含所有項目的值

### 3. 值變化檢測
- **單項模式**: 檢查指定單一項目的值變化
- **多項模式**: 檢查指定多個項目的值變化（任一變化即觸發）
- **全部模式**: 檢查所有項目的值變化（任一變化即觸發）

### 4. GUI整合
- 完整的PySide6 GUI介面
- 即時資料顯示和狀態更新
- 自動連接和排程功能

### 5. 佇列處理機制
- 使用Queue進行非同步資料處理
- 避免DDE回調阻塞
- 支援高頻資料處理

## 資料流程圖

```
DDE資料接收 → on_advise_data → 加入佇列 → _process_advise_data
     ↓
項目重複檢查 → 補齊資料 → 值變化檢查 → 保存/跳過決策 → 建立新行
     ↓
原始資料更新 → 項目資料更新 → 時間記錄更新
     ↓
定時檢查 → check_time_interval → check_time_interval_newline → 時間間隔處理
```

## 錯誤處理

### 分層錯誤處理
- 每個關鍵方法都有完整的try-catch機制
- 詳細的日誌記錄
- 異常情況下的資料保護

### 資料一致性
- 使用deque確保資料順序
- 原子操作保證資料完整性
- 異常恢復機制

---

*本文件基於 DDE v6版 (dde_monitor_v6.py) 的實際程式碼內容編寫，反映了v6版的完整資料處理流程和架構設計。*
