#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控系统
负责系统性能监控、告警和健康检查
"""

import logging
import threading
import time
import psutil
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import deque
import json

class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    network_bytes_sent: int
    network_bytes_recv: int
    process_count: int
    thread_count: int

@dataclass
class EngineMetrics:
    """引擎指标"""
    engine_id: str
    symbol: str
    data_type: str
    state: str
    data_received: int
    data_processed: int
    error_count: int
    last_activity: float
    processing_time_avg: float
    memory_usage_mb: float

@dataclass
class Alert:
    """告警信息"""
    alert_id: str
    level: AlertLevel
    title: str
    message: str
    timestamp: float
    source: str
    resolved: bool = False
    resolved_time: Optional[float] = None

@dataclass
class MonitoringConfig:
    """监控配置"""
    # 系统监控配置
    cpu_threshold_warning: float = 80.0
    cpu_threshold_critical: float = 95.0
    memory_threshold_warning: float = 80.0
    memory_threshold_critical: float = 95.0
    disk_threshold_warning: float = 85.0
    disk_threshold_critical: float = 95.0
    
    # 引擎监控配置
    engine_timeout_warning: float = 30.0
    engine_timeout_critical: float = 60.0
    error_rate_threshold: float = 0.1  # 10%
    
    # 监控间隔
    system_monitor_interval: float = 5.0
    engine_monitor_interval: float = 2.0
    alert_check_interval: float = 1.0
    
    # 历史数据保留
    metrics_history_size: int = 1000
    alerts_history_size: int = 500

class MonitoringSystem:
    """监控系统
    
    负责系统和引擎的监控，提供：
    - 系统资源监控
    - 引擎性能监控
    - 告警管理
    - 健康检查
    """
    
    def __init__(self, config: MonitoringConfig = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or MonitoringConfig()
        
        # 监控状态
        self.running = False
        self.shutdown_event = threading.Event()
        
        # 监控线程
        self.system_monitor_thread: Optional[threading.Thread] = None
        self.engine_monitor_thread: Optional[threading.Thread] = None
        self.alert_manager_thread: Optional[threading.Thread] = None
        
        # 数据存储
        self.system_metrics_history: deque = deque(maxlen=self.config.metrics_history_size)
        self.engine_metrics: Dict[str, EngineMetrics] = {}
        self.alerts: deque = deque(maxlen=self.config.alerts_history_size)
        self.active_alerts: Dict[str, Alert] = {}
        
        # 线程锁
        self.metrics_lock = threading.RLock()
        self.alerts_lock = threading.RLock()
        
        # 回调函数
        self.on_alert: Optional[Callable[[Alert], None]] = None
        self.on_metrics_update: Optional[Callable[[SystemMetrics], None]] = None
        
        # 引擎管理器引用（用于获取引擎信息）
        self.engine_manager = None
        
        self.logger.info("监控系统初始化完成")
    
    def set_engine_manager(self, engine_manager):
        """设置引擎管理器引用"""
        self.engine_manager = engine_manager
        self.logger.info("引擎管理器引用已设置")
    
    def start(self):
        """启动监控系统"""
        try:
            if self.running:
                self.logger.warning("监控系统已经在运行")
                return
            
            self.running = True
            self.shutdown_event.clear()
            
            # 启动系统监控线程
            self.system_monitor_thread = threading.Thread(
                target=self._system_monitor_loop,
                name="SystemMonitor",
                daemon=True
            )
            self.system_monitor_thread.start()
            
            # 启动引擎监控线程
            self.engine_monitor_thread = threading.Thread(
                target=self._engine_monitor_loop,
                name="EngineMonitor",
                daemon=True
            )
            self.engine_monitor_thread.start()
            
            # 启动告警管理线程
            self.alert_manager_thread = threading.Thread(
                target=self._alert_manager_loop,
                name="AlertManager",
                daemon=True
            )
            self.alert_manager_thread.start()
            
            self.logger.info("监控系统启动成功")
            
        except Exception as e:
            self.logger.error(f"启动监控系统失败: {str(e)}")
            self.running = False
    
    def stop(self):
        """停止监控系统"""
        try:
            if not self.running:
                self.logger.warning("监控系统未在运行")
                return
            
            self.logger.info("开始停止监控系统")
            
            # 设置停止标志
            self.running = False
            self.shutdown_event.set()
            
            # 等待监控线程结束
            threads = [
                self.system_monitor_thread,
                self.engine_monitor_thread,
                self.alert_manager_thread
            ]
            
            for thread in threads:
                if thread and thread.is_alive():
                    thread.join(timeout=5.0)
            
            self.logger.info("监控系统已停止")
            
        except Exception as e:
            self.logger.error(f"停止监控系统失败: {str(e)}")
    
    def get_current_system_metrics(self) -> Optional[SystemMetrics]:
        """获取当前系统指标"""
        try:
            with self.metrics_lock:
                if self.system_metrics_history:
                    return self.system_metrics_history[-1]
                return None
                
        except Exception as e:
            self.logger.error(f"获取系统指标失败: {str(e)}")
            return None
    
    def get_system_metrics_history(self, count: int = 100) -> List[SystemMetrics]:
        """获取系统指标历史"""
        try:
            with self.metrics_lock:
                return list(self.system_metrics_history)[-count:]
                
        except Exception as e:
            self.logger.error(f"获取系统指标历史失败: {str(e)}")
            return []
    
    def get_engine_metrics(self) -> Dict[str, EngineMetrics]:
        """获取引擎指标"""
        try:
            with self.metrics_lock:
                return self.engine_metrics.copy()
                
        except Exception as e:
            self.logger.error(f"获取引擎指标失败: {str(e)}")
            return {}
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        try:
            with self.alerts_lock:
                return list(self.active_alerts.values())
                
        except Exception as e:
            self.logger.error(f"获取活跃告警失败: {str(e)}")
            return []
    
    def get_alerts_history(self, count: int = 50) -> List[Alert]:
        """获取告警历史"""
        try:
            with self.alerts_lock:
                return list(self.alerts)[-count:]
                
        except Exception as e:
            self.logger.error(f"获取告警历史失败: {str(e)}")
            return []
    
    def create_alert(self, level: AlertLevel, title: str, message: str, source: str) -> str:
        """创建告警"""
        try:
            alert_id = f"{source}_{level.value}_{int(time.time()*1000)}"
            
            alert = Alert(
                alert_id=alert_id,
                level=level,
                title=title,
                message=message,
                timestamp=time.time(),
                source=source
            )
            
            with self.alerts_lock:
                # 添加到活跃告警
                self.active_alerts[alert_id] = alert
                
                # 添加到历史记录
                self.alerts.append(alert)
            
            # 触发回调
            if self.on_alert:
                self.on_alert(alert)
            
            self.logger.warning(f"创建告警: [{level.value.upper()}] {title} - {message}")
            return alert_id
            
        except Exception as e:
            self.logger.error(f"创建告警失败: {str(e)}")
            return ""
    
    def resolve_alert(self, alert_id: str):
        """解决告警"""
        try:
            with self.alerts_lock:
                if alert_id in self.active_alerts:
                    alert = self.active_alerts[alert_id]
                    alert.resolved = True
                    alert.resolved_time = time.time()
                    
                    # 从活跃告警中移除
                    del self.active_alerts[alert_id]
                    
                    self.logger.info(f"告警已解决: {alert_id}")
                else:
                    self.logger.warning(f"告警不存在: {alert_id}")
                    
        except Exception as e:
            self.logger.error(f"解决告警失败: {str(e)}")
    
    def _system_monitor_loop(self):
        """系统监控循环"""
        try:
            self.logger.info("系统监控线程启动")
            
            while self.running and not self.shutdown_event.is_set():
                try:
                    # 收集系统指标
                    metrics = self._collect_system_metrics()
                    
                    if metrics:
                        with self.metrics_lock:
                            self.system_metrics_history.append(metrics)
                        
                        # 触发回调
                        if self.on_metrics_update:
                            self.on_metrics_update(metrics)
                    
                    # 等待下次监控
                    time.sleep(self.config.system_monitor_interval)
                    
                except Exception as e:
                    self.logger.error(f"系统监控异常: {str(e)}")
                    time.sleep(self.config.system_monitor_interval * 2)
            
            self.logger.info("系统监控线程结束")
            
        except Exception as e:
            self.logger.error(f"系统监控失败: {str(e)}")
    
    def _engine_monitor_loop(self):
        """引擎监控循环"""
        try:
            self.logger.info("引擎监控线程启动")
            
            while self.running and not self.shutdown_event.is_set():
                try:
                    # 收集引擎指标
                    if self.engine_manager:
                        self._collect_engine_metrics()
                    
                    # 等待下次监控
                    time.sleep(self.config.engine_monitor_interval)
                    
                except Exception as e:
                    self.logger.error(f"引擎监控异常: {str(e)}")
                    time.sleep(self.config.engine_monitor_interval * 2)
            
            self.logger.info("引擎监控线程结束")
            
        except Exception as e:
            self.logger.error(f"引擎监控失败: {str(e)}")
    
    def _alert_manager_loop(self):
        """告警管理循环"""
        try:
            self.logger.info("告警管理线程启动")
            
            while self.running and not self.shutdown_event.is_set():
                try:
                    # 检查系统告警
                    self._check_system_alerts()
                    
                    # 检查引擎告警
                    self._check_engine_alerts()
                    
                    # 等待下次检查
                    time.sleep(self.config.alert_check_interval)
                    
                except Exception as e:
                    self.logger.error(f"告警管理异常: {str(e)}")
                    time.sleep(self.config.alert_check_interval * 2)
            
            self.logger.info("告警管理线程结束")
            
        except Exception as e:
            self.logger.error(f"告警管理失败: {str(e)}")
    
    def _collect_system_metrics(self) -> Optional[SystemMetrics]:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            
            # 网络信息
            network = psutil.net_io_counters()
            
            # 进程信息
            process_count = len(psutil.pids())
            
            # 当前进程的线程数
            current_process = psutil.Process()
            thread_count = current_process.num_threads()
            
            metrics = SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_usage_percent=disk.percent,
                network_bytes_sent=network.bytes_sent,
                network_bytes_recv=network.bytes_recv,
                process_count=process_count,
                thread_count=thread_count
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {str(e)}")
            return None
    
    def _collect_engine_metrics(self):
        """收集引擎指标"""
        try:
            if not self.engine_manager:
                return
            
            engines = self.engine_manager.get_all_engines()
            
            with self.metrics_lock:
                # 清空旧的引擎指标
                self.engine_metrics.clear()
                
                for engine_id, engine in engines.items():
                    try:
                        # 获取引擎统计信息
                        stats = engine.processor.get_stats() if engine.processor else None
                        
                        metrics = EngineMetrics(
                            engine_id=engine_id,
                            symbol=engine.product_symbol,
                            data_type=engine.data_type,
                            state=engine.state.value,
                            data_received=stats.total_received if stats else 0,
                            data_processed=stats.total_processed if stats else 0,
                            error_count=engine.error_count,
                            last_activity=engine.last_heartbeat,
                            processing_time_avg=0.0,  # 这里可以添加处理时间统计
                            memory_usage_mb=0.0  # 这里可以添加内存使用统计
                        )
                        
                        self.engine_metrics[engine_id] = metrics
                        
                    except Exception as e:
                        self.logger.error(f"收集引擎指标失败: {engine_id}, {str(e)}")
                        
        except Exception as e:
            self.logger.error(f"收集引擎指标失败: {str(e)}")
    
    def _check_system_alerts(self):
        """检查系统告警"""
        try:
            current_metrics = self.get_current_system_metrics()
            if not current_metrics:
                return
            
            # 检查CPU使用率
            if current_metrics.cpu_percent >= self.config.cpu_threshold_critical:
                self.create_alert(
                    AlertLevel.CRITICAL,
                    "CPU使用率过高",
                    f"CPU使用率: {current_metrics.cpu_percent:.1f}%",
                    "system"
                )
            elif current_metrics.cpu_percent >= self.config.cpu_threshold_warning:
                self.create_alert(
                    AlertLevel.WARNING,
                    "CPU使用率告警",
                    f"CPU使用率: {current_metrics.cpu_percent:.1f}%",
                    "system"
                )
            
            # 检查内存使用率
            if current_metrics.memory_percent >= self.config.memory_threshold_critical:
                self.create_alert(
                    AlertLevel.CRITICAL,
                    "内存使用率过高",
                    f"内存使用率: {current_metrics.memory_percent:.1f}%",
                    "system"
                )
            elif current_metrics.memory_percent >= self.config.memory_threshold_warning:
                self.create_alert(
                    AlertLevel.WARNING,
                    "内存使用率告警",
                    f"内存使用率: {current_metrics.memory_percent:.1f}%",
                    "system"
                )
            
            # 检查磁盘使用率
            if current_metrics.disk_usage_percent >= self.config.disk_threshold_critical:
                self.create_alert(
                    AlertLevel.CRITICAL,
                    "磁盘使用率过高",
                    f"磁盘使用率: {current_metrics.disk_usage_percent:.1f}%",
                    "system"
                )
            elif current_metrics.disk_usage_percent >= self.config.disk_threshold_warning:
                self.create_alert(
                    AlertLevel.WARNING,
                    "磁盘使用率告警",
                    f"磁盘使用率: {current_metrics.disk_usage_percent:.1f}%",
                    "system"
                )
                
        except Exception as e:
            self.logger.error(f"检查系统告警失败: {str(e)}")
    
    def _check_engine_alerts(self):
        """检查引擎告警"""
        try:
            current_time = time.time()
            engine_metrics = self.get_engine_metrics()
            
            for engine_id, metrics in engine_metrics.items():
                # 检查引擎超时
                if metrics.last_activity > 0:
                    inactive_time = current_time - metrics.last_activity
                    
                    if inactive_time >= self.config.engine_timeout_critical:
                        self.create_alert(
                            AlertLevel.CRITICAL,
                            "引擎超时",
                            f"引擎 {engine_id} 已 {inactive_time:.1f}s 无响应",
                            f"engine_{engine_id}"
                        )
                    elif inactive_time >= self.config.engine_timeout_warning:
                        self.create_alert(
                            AlertLevel.WARNING,
                            "引擎响应慢",
                            f"引擎 {engine_id} 已 {inactive_time:.1f}s 无响应",
                            f"engine_{engine_id}"
                        )
                
                # 检查错误率
                if metrics.data_received > 0:
                    error_rate = metrics.error_count / metrics.data_received
                    if error_rate >= self.config.error_rate_threshold:
                        self.create_alert(
                            AlertLevel.ERROR,
                            "引擎错误率过高",
                            f"引擎 {engine_id} 错误率: {error_rate:.1%}",
                            f"engine_{engine_id}"
                        )
                        
        except Exception as e:
            self.logger.error(f"检查引擎告警失败: {str(e)}")
