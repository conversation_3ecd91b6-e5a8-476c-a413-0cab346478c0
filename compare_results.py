#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 監控結果比較工具
比較模組化版本與引擎版本的輸出結果
"""

import os
import csv
import sys
from datetime import datetime
from typing import List, Dict, Tuple


class DDEResultComparator:
    """DDE結果比較器"""
    
    def __init__(self):
        self.modular_file = "./outputs/TEMP/data/mon/XQ/FITXN07/_a/complete_data_02.csv"
        self.engine_file = "./outputs/TEMP/data/mon/XQ/FITXN07/_a/complete_data_02_engine.csv"
    
    def load_csv_data(self, file_path: str) -> Tuple[List[str], List[List[str]]]:
        """載入CSV資料"""
        if not os.path.exists(file_path):
            print(f"警告: 檔案不存在 {file_path}")
            return [], []
        
        headers = []
        rows = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                headers = next(reader, [])
                rows = list(reader)
                
            print(f"載入檔案: {file_path}")
            print(f"  標題行: {len(headers)} 欄")
            print(f"  資料行: {len(rows)} 行")
            
            return headers, rows
            
        except Exception as e:
            print(f"載入檔案失敗 {file_path}: {str(e)}")
            return [], []
    
    def compare_headers(self, modular_headers: List[str], engine_headers: List[str]) -> bool:
        """比較標題行"""
        print("\n=== 標題行比較 ===")
        
        if modular_headers == engine_headers:
            print("✓ 標題行完全相同")
            return True
        else:
            print("✗ 標題行不同")
            print(f"模組化版本: {modular_headers}")
            print(f"引擎版本:   {engine_headers}")
            return False
    
    def compare_row_counts(self, modular_rows: List[List[str]], engine_rows: List[List[str]]) -> bool:
        """比較資料行數量"""
        print("\n=== 資料行數量比較 ===")
        
        modular_count = len(modular_rows)
        engine_count = len(engine_rows)
        
        print(f"模組化版本: {modular_count} 行")
        print(f"引擎版本:   {engine_count} 行")
        
        if modular_count == engine_count:
            print("✓ 資料行數量相同")
            return True
        else:
            print(f"✗ 資料行數量不同 (差異: {abs(modular_count - engine_count)} 行)")
            return False
    
    def compare_data_content(self, modular_headers: List[str], modular_rows: List[List[str]], 
                           engine_headers: List[str], engine_rows: List[List[str]]) -> bool:
        """比較資料內容"""
        print("\n=== 資料內容比較 ===")
        
        if not modular_headers or not engine_headers:
            print("無法比較: 缺少標題行")
            return False
        
        # 比較最少的行數
        min_rows = min(len(modular_rows), len(engine_rows))
        if min_rows == 0:
            print("無法比較: 沒有資料行")
            return False
        
        print(f"比較前 {min_rows} 行資料...")
        
        differences = []
        identical_rows = 0
        
        for i in range(min_rows):
            modular_row = modular_rows[i]
            engine_row = engine_rows[i]
            
            # 確保行長度相同
            if len(modular_row) != len(engine_row):
                differences.append(f"第{i+1}行: 欄位數量不同 (模組化:{len(modular_row)}, 引擎:{len(engine_row)})")
                continue
            
            # 比較每個欄位
            row_identical = True
            row_differences = []
            
            for j, (mod_val, eng_val) in enumerate(zip(modular_row, engine_row)):
                if mod_val != eng_val:
                    row_identical = False
                    header_name = modular_headers[j] if j < len(modular_headers) else f"欄位{j+1}"
                    row_differences.append(f"{header_name}: '{mod_val}' vs '{eng_val}'")
            
            if row_identical:
                identical_rows += 1
            else:
                differences.append(f"第{i+1}行差異: {', '.join(row_differences)}")
        
        print(f"相同行數: {identical_rows}/{min_rows}")
        print(f"不同行數: {len(differences)}")
        
        if differences:
            print("\n差異詳情:")
            for diff in differences[:10]:  # 只顯示前10個差異
                print(f"  {diff}")
            if len(differences) > 10:
                print(f"  ... 還有 {len(differences) - 10} 個差異")
        
        return len(differences) == 0
    
    def analyze_time_patterns(self, headers: List[str], rows: List[List[str]], version_name: str):
        """分析時間模式"""
        print(f"\n=== {version_name} 時間模式分析 ===")
        
        if not rows:
            print("沒有資料可分析")
            return
        
        # 找到時間欄位
        time_col = -1
        for i, header in enumerate(headers):
            if '時間' in header:
                time_col = i
                break
        
        if time_col == -1:
            print("找不到時間欄位")
            return
        
        # 分析時間間隔
        time_intervals = []
        prev_time = None
        
        for row in rows:
            if time_col < len(row):
                time_str = row[time_col]
                try:
                    # 解析時間 (假設格式為 HH:MM:SS.fff)
                    time_parts = time_str.split(':')
                    if len(time_parts) >= 3:
                        hour = int(time_parts[0])
                        minute = int(time_parts[1])
                        second_parts = time_parts[2].split('.')
                        second = int(second_parts[0])
                        millisecond = int(second_parts[1]) if len(second_parts) > 1 else 0
                        
                        current_time = hour * 3600 + minute * 60 + second + millisecond / 1000
                        
                        if prev_time is not None:
                            interval = current_time - prev_time
                            if interval > 0:  # 忽略負間隔（可能跨日）
                                time_intervals.append(interval)
                        
                        prev_time = current_time
                        
                except:
                    continue
        
        if time_intervals:
            avg_interval = sum(time_intervals) / len(time_intervals)
            min_interval = min(time_intervals)
            max_interval = max(time_intervals)
            
            print(f"時間間隔統計:")
            print(f"  平均間隔: {avg_interval:.3f} 秒")
            print(f"  最小間隔: {min_interval:.3f} 秒")
            print(f"  最大間隔: {max_interval:.3f} 秒")
            print(f"  間隔數量: {len(time_intervals)}")
        else:
            print("無法分析時間間隔")
    
    def compare_key_fields(self, modular_headers: List[str], modular_rows: List[List[str]], 
                          engine_headers: List[str], engine_rows: List[List[str]]):
        """比較關鍵欄位 (外盤量, 累賣成筆)"""
        print("\n=== 關鍵欄位比較 (外盤量, 累賣成筆) ===")
        
        # 找到關鍵欄位的索引
        key_fields = ['外盤量', '累賣成筆']
        modular_indices = {}
        engine_indices = {}
        
        for field in key_fields:
            for i, header in enumerate(modular_headers):
                if field in header:
                    modular_indices[field] = i
                    break
            
            for i, header in enumerate(engine_headers):
                if field in header:
                    engine_indices[field] = i
                    break
        
        print(f"找到的關鍵欄位:")
        for field in key_fields:
            mod_idx = modular_indices.get(field, -1)
            eng_idx = engine_indices.get(field, -1)
            print(f"  {field}: 模組化版本欄位{mod_idx+1}, 引擎版本欄位{eng_idx+1}")
        
        # 比較關鍵欄位的值
        min_rows = min(len(modular_rows), len(engine_rows))
        
        for field in key_fields:
            mod_idx = modular_indices.get(field, -1)
            eng_idx = engine_indices.get(field, -1)
            
            if mod_idx == -1 or eng_idx == -1:
                print(f"跳過 {field}: 欄位索引無效")
                continue
            
            differences = 0
            for i in range(min_rows):
                if (mod_idx < len(modular_rows[i]) and eng_idx < len(engine_rows[i])):
                    mod_val = modular_rows[i][mod_idx]
                    eng_val = engine_rows[i][eng_idx]
                    
                    if mod_val != eng_val:
                        differences += 1
                        if differences <= 5:  # 只顯示前5個差異
                            print(f"  第{i+1}行 {field}: '{mod_val}' vs '{eng_val}'")
            
            if differences == 0:
                print(f"✓ {field}: 所有值都相同")
            else:
                print(f"✗ {field}: {differences} 個差異")
    
    def run_comparison(self):
        """執行完整比較"""
        print("=" * 60)
        print("DDE 監控結果比較")
        print(f"比較時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # 載入資料
        modular_headers, modular_rows = self.load_csv_data(self.modular_file)
        engine_headers, engine_rows = self.load_csv_data(self.engine_file)
        
        if not modular_headers and not engine_headers:
            print("錯誤: 兩個檔案都無法載入")
            return False
        
        if not modular_headers:
            print("警告: 模組化版本檔案無法載入，只分析引擎版本")
            self.analyze_time_patterns(engine_headers, engine_rows, "引擎版本")
            return False
        
        if not engine_headers:
            print("警告: 引擎版本檔案無法載入，只分析模組化版本")
            self.analyze_time_patterns(modular_headers, modular_rows, "模組化版本")
            return False
        
        # 執行比較
        headers_match = self.compare_headers(modular_headers, engine_headers)
        counts_match = self.compare_row_counts(modular_rows, engine_rows)
        content_match = self.compare_data_content(modular_headers, modular_rows, engine_headers, engine_rows)
        
        # 分析時間模式
        self.analyze_time_patterns(modular_headers, modular_rows, "模組化版本")
        self.analyze_time_patterns(engine_headers, engine_rows, "引擎版本")
        
        # 比較關鍵欄位
        self.compare_key_fields(modular_headers, modular_rows, engine_headers, engine_rows)
        
        # 總結
        print("\n" + "=" * 60)
        print("比較結果總結")
        print("=" * 60)
        
        all_match = headers_match and counts_match and content_match
        
        if all_match:
            print("🎉 完全一致！DDE 資料處理引擎與模組化版本產生相同結果")
        else:
            print("⚠️  發現差異，需要進一步檢查:")
            if not headers_match:
                print("  - 標題行不同")
            if not counts_match:
                print("  - 資料行數量不同")
            if not content_match:
                print("  - 資料內容不同")
        
        print("=" * 60)
        return all_match


def main():
    """主函數"""
    comparator = DDEResultComparator()
    success = comparator.run_comparison()
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
