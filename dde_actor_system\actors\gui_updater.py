#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI更新Actor實現

提供高性能的GUI更新功能，包括：
- 虛擬化顯示
- 批量異步更新
- 可配置刷新頻率
- 非阻塞GUI操作
"""

import asyncio
import logging
import time
from collections import deque, defaultdict
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import threading

from core.actor_base import ActorBase
from core.message_system import Message, MessageType

try:
    from PySide6.QtCore import QObject, QTimer, Signal, QMetaObject, Qt, Q_ARG
    from PySide6.QtWidgets import QApplication
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    QObject = object
    Signal = lambda *args: None


@dataclass
class GUIUpdateItem:
    """GUI更新項目"""
    item: str
    value: str
    timestamp: float
    metadata: Dict[str, Any]
    update_type: str = "data"  # "data", "status", "config"


class VirtualTableManager:
    """虛擬化表格管理器
    
    只渲染可見的行，提高大量數據的顯示性能
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化虛擬化表格管理器
        
        Args:
            config: 配置字典
        """
        self.visible_rows = config.get('visible_rows', 100)
        self.row_height = config.get('row_height', 25)
        self.total_rows = 0
        self.scroll_position = 0
        
        # 數據存儲
        self.data_items: Dict[str, GUIUpdateItem] = {}
        self.sorted_keys: List[str] = []
        self.visible_data: Dict[str, GUIUpdateItem] = {}
        
        self.logger = logging.getLogger("VirtualTableManager")
    
    def update_item(self, item: GUIUpdateItem):
        """更新項目數據
        
        Args:
            item: 更新項目
        """
        self.data_items[item.item] = item
        
        # 更新排序鍵列表
        if item.item not in self.sorted_keys:
            self.sorted_keys.append(item.item)
            self.sorted_keys.sort()
        
        self.total_rows = len(self.sorted_keys)
        self._update_visible_data()
    
    def batch_update_items(self, items: List[GUIUpdateItem]):
        """批量更新項目
        
        Args:
            items: 更新項目列表
        """
        for item in items:
            self.data_items[item.item] = item
            if item.item not in self.sorted_keys:
                self.sorted_keys.append(item.item)
        
        self.sorted_keys.sort()
        self.total_rows = len(self.sorted_keys)
        self._update_visible_data()
    
    def set_scroll_position(self, position: int):
        """設置滾動位置
        
        Args:
            position: 滾動位置
        """
        self.scroll_position = max(0, min(position, self.total_rows - self.visible_rows))
        self._update_visible_data()
    
    def _update_visible_data(self):
        """更新可見數據"""
        self.visible_data.clear()
        
        start_index = self.scroll_position
        end_index = min(start_index + self.visible_rows, self.total_rows)
        
        for i in range(start_index, end_index):
            if i < len(self.sorted_keys):
                key = self.sorted_keys[i]
                if key in self.data_items:
                    self.visible_data[key] = self.data_items[key]
    
    def get_visible_data(self) -> Dict[str, GUIUpdateItem]:
        """獲取可見數據
        
        Returns:
            Dict: 可見數據字典
        """
        return self.visible_data.copy()
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取統計信息
        
        Returns:
            Dict: 統計信息
        """
        return {
            'total_rows': self.total_rows,
            'visible_rows': len(self.visible_data),
            'scroll_position': self.scroll_position,
            'data_items_count': len(self.data_items)
        }


class BatchGUIUpdater(QObject if GUI_AVAILABLE else object):
    """批量GUI更新器
    
    收集GUI更新請求並批量處理，減少GUI重繪次數
    """
    
    # Qt信號定義
    if GUI_AVAILABLE:
        batch_update_signal = Signal(list)
        status_update_signal = Signal(str, str)
        clear_signal = Signal()
    
    def __init__(self, config: Dict[str, Any]):
        """初始化批量GUI更新器
        
        Args:
            config: 配置字典
        """
        if GUI_AVAILABLE:
            super().__init__()
        
        self.update_interval_ms = config.get('update_interval_ms', 16)  # 60FPS
        self.max_batch_size = config.get('max_batch_size', 1000)
        self.enable_throttling = config.get('enable_throttling', True)
        
        # 更新隊列
        self.pending_updates: Dict[str, GUIUpdateItem] = {}
        self.update_lock = threading.Lock()
        
        # 定時器 - 延遲初始化以避免線程問題
        self.update_timer = None
        if GUI_AVAILABLE:
            # 在需要時才創建定時器
            pass
        
        # 批量更新統計信息
        self.batch_stats = {
            'updates_queued': 0,
            'updates_flushed': 0,
            'batches_sent': 0,
            'throttled_updates': 0
        }
        
        self.logger = logging.getLogger("BatchGUIUpdater")
    
    def queue_update(self, item: GUIUpdateItem):
        """隊列更新項目

        Args:
            item: 更新項目
        """
        with self.update_lock:
            # 如果啟用限流且隊列過大，丟棄舊更新
            if self.enable_throttling and len(self.pending_updates) > self.max_batch_size:
                # 保留最新的更新
                oldest_key = min(self.pending_updates.keys(),
                               key=lambda k: self.pending_updates[k].timestamp)
                del self.pending_updates[oldest_key]
                self.batch_stats['throttled_updates'] += 1

            self.pending_updates[item.item] = item
            self.batch_stats['updates_queued'] += 1

            # 如果沒有定時器，立即處理
            if not GUI_AVAILABLE or self.update_timer == "disabled":
                if len(self.pending_updates) >= self.max_batch_size:
                    self._flush_updates()
    
    def queue_batch_updates(self, items: List[GUIUpdateItem]):
        """隊列批量更新
        
        Args:
            items: 更新項目列表
        """
        with self.update_lock:
            for item in items:
                self.pending_updates[item.item] = item
            self.batch_stats['updates_queued'] += len(items)
    
    def _flush_updates(self):
        """刷新更新隊列"""
        if not GUI_AVAILABLE:
            return

        with self.update_lock:
            if not self.pending_updates:
                return

            # 獲取待更新項目
            updates = list(self.pending_updates.values())
            self.pending_updates.clear()

            # 發送批量更新信號
            if hasattr(self, 'batch_update_signal'):
                self.batch_update_signal.emit(updates)

            self.batch_stats['updates_flushed'] += len(updates)
            self.batch_stats['batches_sent'] += 1

    def _ensure_timer_created(self):
        """確保定時器已創建"""
        if GUI_AVAILABLE and self.update_timer is None:
            try:
                self.update_timer = QTimer()
                self.update_timer.timeout.connect(self._flush_updates)
                self.update_timer.start(self.update_interval_ms)
            except Exception as e:
                # 如果無法創建定時器，使用異步替代方案
                self.logger.warning(f"無法創建Qt定時器: {str(e)}")
                self.update_timer = "disabled"
    
    def force_flush(self):
        """強制刷新更新隊列"""
        self._flush_updates()
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取統計信息
        
        Returns:
            Dict: 統計信息
        """
        with self.update_lock:
            return {
                'stats': self.stats.copy(),
                'pending_count': len(self.pending_updates),
                'update_interval_ms': self.update_interval_ms
            }


class GUIUpdaterActor(ActorBase):
    """GUI更新Actor
    
    負責高性能的GUI更新和顯示管理
    """
    
    def __init__(self, name: str, config: Optional[Dict] = None):
        """初始化GUI更新Actor
        
        Args:
            name: Actor名稱
            config: 配置字典
        """
        super().__init__(name, config)
        
        if not GUI_AVAILABLE:
            self.logger.warning("PySide6不可用，GUI功能將被禁用")
        
        # GUI組件
        self.virtual_table = VirtualTableManager(self.config.get('virtual_table', {}))
        self.batch_updater = BatchGUIUpdater(self.config.get('batch_updater', {}))
        
        # 更新配置
        self.enable_virtual_table = self.config.get('enable_virtual_table', True)
        self.max_display_items = self.config.get('max_display_items', 10000)
        self.auto_scroll = self.config.get('auto_scroll', True)
        
        # GUI更新統計信息（不覆蓋父類的stats）
        self.gui_stats = {
            'gui_updates_received': 0,
            'gui_updates_processed': 0,
            'batch_updates_received': 0,
            'items_displayed': 0,
            'scroll_events': 0
        }
        
        # GUI回調函數
        self.gui_callbacks: Dict[str, Callable] = {}
    
    async def on_start(self):
        """Actor啟動時的初始化"""
        try:
            if GUI_AVAILABLE:
                # 連接GUI信號
                self._connect_gui_signals()
                # 嘗試創建定時器
                self.batch_updater._ensure_timer_created()

            self.logger.info(f"GUI更新Actor {self.name} 啟動成功")

        except Exception as e:
            self.logger.error(f"GUI更新Actor啟動失敗: {str(e)}")
            raise
    
    async def on_stop(self):
        """Actor停止時的清理"""
        try:
            if GUI_AVAILABLE and hasattr(self.batch_updater, 'update_timer') and self.batch_updater.update_timer:
                if hasattr(self.batch_updater.update_timer, 'stop'):
                    self.batch_updater.update_timer.stop()

            self.logger.info(f"GUI更新Actor {self.name} 停止成功")

        except Exception as e:
            self.logger.error(f"GUI更新Actor停止失敗: {str(e)}")
    
    async def handle_message(self, message: Message):
        """處理接收到的消息"""
        try:
            if message.type == MessageType.GUI_UPDATE:
                await self._handle_gui_update_message(message)
            elif message.type == MessageType.GUI_BATCH_UPDATE:
                await self._handle_gui_batch_update_message(message)
            elif message.type == MessageType.GUI_CLEAR:
                await self._handle_gui_clear_message(message)
            elif message.type == MessageType.GUI_REFRESH:
                await self._handle_gui_refresh_message(message)
            else:
                self.logger.warning(f"未知消息類型: {message.type}")
                
        except Exception as e:
            self.logger.error(f"處理GUI消息失敗: {str(e)}")
    
    async def _handle_gui_update_message(self, message: Message):
        """處理GUI更新消息"""
        try:
            data = message.data
            
            update_item = GUIUpdateItem(
                item=data.get('item', ''),
                value=data.get('value', ''),
                timestamp=data.get('timestamp', time.time()),
                metadata=data.get('metadata', {}),
                update_type=data.get('update_type', 'data')
            )
            
            await self._process_gui_update(update_item)
            self.gui_stats['gui_updates_received'] += 1
            
        except Exception as e:
            self.logger.error(f"處理GUI更新消息失敗: {str(e)}")
    
    async def _handle_gui_batch_update_message(self, message: Message):
        """處理GUI批量更新消息"""
        try:
            items_data = message.data.get('items', [])
            
            update_items = []
            for item_data in items_data:
                update_item = GUIUpdateItem(
                    item=item_data.get('item', ''),
                    value=item_data.get('value', ''),
                    timestamp=item_data.get('timestamp', time.time()),
                    metadata=item_data.get('metadata', {}),
                    update_type=item_data.get('update_type', 'data')
                )
                update_items.append(update_item)
            
            await self._process_gui_batch_update(update_items)
            self.gui_stats['batch_updates_received'] += 1
            
        except Exception as e:
            self.logger.error(f"處理GUI批量更新消息失敗: {str(e)}")
    
    async def _handle_gui_clear_message(self, message: Message):
        """處理GUI清空消息"""
        try:
            if GUI_AVAILABLE:
                self.batch_updater.clear_signal.emit()
            
            # 清空虛擬表格數據
            self.virtual_table.data_items.clear()
            self.virtual_table.sorted_keys.clear()
            self.virtual_table.visible_data.clear()
            
        except Exception as e:
            self.logger.error(f"處理GUI清空消息失敗: {str(e)}")
    
    async def _handle_gui_refresh_message(self, message: Message):
        """處理GUI刷新消息"""
        try:
            if GUI_AVAILABLE:
                self.batch_updater.force_flush()
            
        except Exception as e:
            self.logger.error(f"處理GUI刷新消息失敗: {str(e)}")
    
    async def _process_gui_update(self, update_item: GUIUpdateItem):
        """處理單個GUI更新
        
        Args:
            update_item: 更新項目
        """
        try:
            # 更新虛擬表格
            if self.enable_virtual_table:
                self.virtual_table.update_item(update_item)
            
            # 隊列GUI更新
            if GUI_AVAILABLE:
                self.batch_updater.queue_update(update_item)
            
            self.gui_stats['gui_updates_processed'] += 1
            
        except Exception as e:
            self.logger.error(f"處理GUI更新失敗: {str(e)}")
    
    async def _process_gui_batch_update(self, update_items: List[GUIUpdateItem]):
        """處理批量GUI更新
        
        Args:
            update_items: 更新項目列表
        """
        try:
            # 更新虛擬表格
            if self.enable_virtual_table:
                self.virtual_table.batch_update_items(update_items)
            
            # 隊列批量GUI更新
            if GUI_AVAILABLE:
                self.batch_updater.queue_batch_updates(update_items)
            
            self.gui_stats['gui_updates_processed'] += len(update_items)
            
        except Exception as e:
            self.logger.error(f"處理批量GUI更新失敗: {str(e)}")
    
    def _connect_gui_signals(self):
        """連接GUI信號"""
        if not GUI_AVAILABLE:
            return
        
        # 連接批量更新信號
        self.batch_updater.batch_update_signal.connect(self._on_batch_update)
        self.batch_updater.status_update_signal.connect(self._on_status_update)
        self.batch_updater.clear_signal.connect(self._on_clear)
    
    def _on_batch_update(self, updates: List[GUIUpdateItem]):
        """批量更新回調"""
        try:
            # 調用註冊的GUI回調函數
            if 'batch_update' in self.gui_callbacks:
                self.gui_callbacks['batch_update'](updates)
            
        except Exception as e:
            self.logger.error(f"批量更新回調失敗: {str(e)}")
    
    def _on_status_update(self, status_type: str, message: str):
        """狀態更新回調"""
        try:
            if 'status_update' in self.gui_callbacks:
                self.gui_callbacks['status_update'](status_type, message)
            
        except Exception as e:
            self.logger.error(f"狀態更新回調失敗: {str(e)}")
    
    def _on_clear(self):
        """清空回調"""
        try:
            if 'clear' in self.gui_callbacks:
                self.gui_callbacks['clear']()
            
        except Exception as e:
            self.logger.error(f"清空回調失敗: {str(e)}")
    
    def register_gui_callback(self, callback_type: str, callback: Callable):
        """註冊GUI回調函數
        
        Args:
            callback_type: 回調類型
            callback: 回調函數
        """
        self.gui_callbacks[callback_type] = callback
    
    def set_scroll_position(self, position: int):
        """設置滾動位置
        
        Args:
            position: 滾動位置
        """
        self.virtual_table.set_scroll_position(position)
        self.gui_stats['scroll_events'] += 1
    
    def get_visible_data(self) -> Dict[str, GUIUpdateItem]:
        """獲取可見數據
        
        Returns:
            Dict: 可見數據字典
        """
        return self.virtual_table.get_visible_data()
    
    def get_gui_stats(self) -> Dict[str, Any]:
        """獲取GUI統計信息
        
        Returns:
            Dict: GUI統計信息
        """
        virtual_table_stats = self.virtual_table.get_stats()
        batch_updater_stats = self.batch_updater.get_stats()
        
        return {
            'actor_stats': self.stats.copy(),
            'virtual_table_stats': virtual_table_stats,
            'batch_updater_stats': batch_updater_stats,
            'gui_available': GUI_AVAILABLE
        }
