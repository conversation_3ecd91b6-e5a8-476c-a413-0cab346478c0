@ECHO ON

if exist "%1" (
  set chkParam=ok
  set conf1=%1
  GOTO withConf
) else (
  Echo %1 Not found
  GOTO withoutConf
)
GOTO End

set run_dir=%CD%
call venv env310 1
cd %run_dir%
cls
@REM dir
@REM echo %run_dir%
@REM pause
@REM python dde_monitor.py

:withConf
set run_dir=%CD%
call venv env310 1
cd %run_dir%
cls
python dde_monitor_new.py -c %1
GOTO End

:withoutConf
set run_dir=%CD%
call venv env310 1
cd %run_dir%
cls
python dde_monitor_new.py
GOTO End

:End
@REM pause
