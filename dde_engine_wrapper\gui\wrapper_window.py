#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE引擎包装器主窗口
提供多商品DDE监控的图形界面
"""

import sys
import os
import logging
from typing import Dict, List, Optional
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from PySide6.QtWidgets import (
        QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
        QLabel, QPushButton, QTableWidget, QTableWidgetItem, QTextEdit,
        QTabWidget, QGroupBox, QProgressBar, QStatusBar, QMenuBar,
        QMenu, QAction, QMessageBox, QDialog, QFormLayout, QLineEdit,
        QSpinBox, QCheckBox, QComboBox, QSplitter, QFrame
    )
    from PySide6.QtCore import Qt, QTimer, Signal, QThread, QObject
    from PySide6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor
except ImportError:
    print("PySide6 未安装，GUI功能不可用")
    sys.exit(1)

# 导入核心模块
from core.engine_wrapper import MultiProductDDEWrapper, WrapperState
from core.monitoring_system import MonitoringSystem, Alert, AlertLevel
from core.parallel_processor import ParallelProcessor

class WrapperMainWindow(QMainWindow):
    """DDE引擎包装器主窗口"""

    # 信号定义
    status_updated = Signal(str)
    alert_received = Signal(object)
    metrics_updated = Signal(object)

    def __init__(self, config_file: str = None):
        super().__init__()

        self.logger = logging.getLogger(__name__)

        # 核心组件
        self.wrapper: Optional[MultiProductDDEWrapper] = None
        self.monitoring_system: Optional[MonitoringSystem] = None
        self.parallel_processor: Optional[ParallelProcessor] = None

        # 配置文件
        self.config_file = config_file or os.path.join(
            os.path.dirname(__file__), '..', 'config', 'wrapper_config.ini'
        )

        # GUI组件
        self.central_widget = None
        self.status_bar = None
        self.main_tabs = None

        # 监控相关
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_displays)

        # 初始化GUI
        self.init_ui()
        self.init_core_components()
        self.connect_signals()

        # 启动定时更新
        self.update_timer.start(1000)  # 每秒更新一次

        self.logger.info("DDE引擎包装器主窗口初始化完成")

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("DDE引擎包装器 v1.0")
        self.setGeometry(100, 100, 1400, 900)

        # 设置字体
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)

        # 创建菜单栏
        self.create_menu_bar()

        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(self.central_widget)

        # 创建工具栏
        toolbar_layout = self.create_toolbar()
        main_layout.addLayout(toolbar_layout)

        # 创建标签页
        self.main_tabs = QTabWidget()
        main_layout.addWidget(self.main_tabs)

        # 创建各个标签页
        self.create_overview_tab()
        self.create_engines_tab()
        self.create_monitoring_tab()
        self.create_alerts_tab()
        self.create_logs_tab()

        # 创建状态栏
        self.create_status_bar()

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 新建配置
        new_config_action = QAction('新建配置(&N)', self)
        new_config_action.triggered.connect(self.new_config)
        file_menu.addAction(new_config_action)

        # 打开配置
        open_config_action = QAction('打开配置(&O)', self)
        open_config_action.triggered.connect(self.open_config)
        file_menu.addAction(open_config_action)

        # 保存配置
        save_config_action = QAction('保存配置(&S)', self)
        save_config_action.triggered.connect(self.save_config)
        file_menu.addAction(save_config_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 控制菜单
        control_menu = menubar.addMenu('控制(&C)')

        # 启动包装器
        start_action = QAction('启动包装器(&S)', self)
        start_action.triggered.connect(self.start_wrapper)
        control_menu.addAction(start_action)

        # 停止包装器
        stop_action = QAction('停止包装器(&T)', self)
        stop_action.triggered.connect(self.stop_wrapper)
        control_menu.addAction(stop_action)

        control_menu.addSeparator()

        # 重启包装器
        restart_action = QAction('重启包装器(&R)', self)
        restart_action.triggered.connect(self.restart_wrapper)
        control_menu.addAction(restart_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self) -> QHBoxLayout:
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()

        # 启动按钮
        self.start_btn = QPushButton("启动包装器")
        self.start_btn.clicked.connect(self.start_wrapper)
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        toolbar_layout.addWidget(self.start_btn)

        # 停止按钮
        self.stop_btn = QPushButton("停止包装器")
        self.stop_btn.clicked.connect(self.stop_wrapper)
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        self.stop_btn.setEnabled(False)
        toolbar_layout.addWidget(self.stop_btn)

        # 重启按钮
        self.restart_btn = QPushButton("重启包装器")
        self.restart_btn.clicked.connect(self.restart_wrapper)
        self.restart_btn.setStyleSheet("QPushButton { background-color: #FF9800; color: white; }")
        self.restart_btn.setEnabled(False)
        toolbar_layout.addWidget(self.restart_btn)

        toolbar_layout.addStretch()

        # 状态指示器
        self.status_indicator = QLabel("状态: 未启动")
        self.status_indicator.setStyleSheet("QLabel { color: #666; font-weight: bold; }")
        toolbar_layout.addWidget(self.status_indicator)

        return toolbar_layout

    def create_overview_tab(self):
        """创建概览标签页"""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)

        # 左侧：系统信息
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # 系统状态组
        system_group = QGroupBox("系统状态")
        system_layout = QGridLayout(system_group)

        # 包装器状态
        system_layout.addWidget(QLabel("包装器状态:"), 0, 0)
        self.wrapper_status_label = QLabel("未启动")
        system_layout.addWidget(self.wrapper_status_label, 0, 1)

        # 运行时间
        system_layout.addWidget(QLabel("运行时间:"), 1, 0)
        self.uptime_label = QLabel("00:00:00")
        system_layout.addWidget(self.uptime_label, 1, 1)

        # 商品数量
        system_layout.addWidget(QLabel("监控商品:"), 2, 0)
        self.products_count_label = QLabel("0")
        system_layout.addWidget(self.products_count_label, 2, 1)

        # 引擎数量
        system_layout.addWidget(QLabel("引擎实例:"), 3, 0)
        self.engines_count_label = QLabel("0")
        system_layout.addWidget(self.engines_count_label, 3, 1)

        left_layout.addWidget(system_group)

        # 性能指标组
        performance_group = QGroupBox("性能指标")
        performance_layout = QGridLayout(performance_group)

        # CPU使用率
        performance_layout.addWidget(QLabel("CPU使用率:"), 0, 0)
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setRange(0, 100)
        performance_layout.addWidget(self.cpu_progress, 0, 1)
        self.cpu_label = QLabel("0%")
        performance_layout.addWidget(self.cpu_label, 0, 2)

        # 内存使用率
        performance_layout.addWidget(QLabel("内存使用率:"), 1, 0)
        self.memory_progress = QProgressBar()
        self.memory_progress.setRange(0, 100)
        performance_layout.addWidget(self.memory_progress, 1, 1)
        self.memory_label = QLabel("0%")
        performance_layout.addWidget(self.memory_label, 1, 2)

        # 数据处理量
        performance_layout.addWidget(QLabel("数据接收:"), 2, 0)
        self.data_received_label = QLabel("0")
        performance_layout.addWidget(self.data_received_label, 2, 1, 1, 2)

        performance_layout.addWidget(QLabel("数据处理:"), 3, 0)
        self.data_processed_label = QLabel("0")
        performance_layout.addWidget(self.data_processed_label, 3, 1, 1, 2)

        left_layout.addWidget(performance_group)
        left_layout.addStretch()

        splitter.addWidget(left_widget)

        # 右侧：商品状态表
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        right_layout.addWidget(QLabel("商品监控状态"))

        self.products_table = QTableWidget()
        self.products_table.setColumnCount(6)
        self.products_table.setHorizontalHeaderLabels([
            "商品代码", "数据类型", "连接状态", "数据接收", "最后活动", "状态"
        ])
        right_layout.addWidget(self.products_table)

        splitter.addWidget(right_widget)

        # 设置分割器比例
        splitter.setSizes([400, 800])

        self.main_tabs.addTab(overview_widget, "概览")

    def create_engines_tab(self):
        """创建引擎标签页"""
        engines_widget = QWidget()
        layout = QVBoxLayout(engines_widget)

        # 引擎控制按钮
        control_layout = QHBoxLayout()

        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_engines)
        control_layout.addWidget(refresh_btn)

        control_layout.addStretch()

        layout.addLayout(control_layout)

        # 引擎状态表
        self.engines_table = QTableWidget()
        self.engines_table.setColumnCount(8)
        self.engines_table.setHorizontalHeaderLabels([
            "引擎ID", "商品", "数据类型", "状态", "数据接收", "数据处理", "错误次数", "最后心跳"
        ])
        layout.addWidget(self.engines_table)

        self.main_tabs.addTab(engines_widget, "引擎管理")

    def create_monitoring_tab(self):
        """创建监控标签页"""
        monitoring_widget = QWidget()
        layout = QVBoxLayout(monitoring_widget)

        # 这里可以添加图表显示
        layout.addWidget(QLabel("监控图表 (待实现)"))

        self.main_tabs.addTab(monitoring_widget, "性能监控")

    def create_alerts_tab(self):
        """创建告警标签页"""
        alerts_widget = QWidget()
        layout = QVBoxLayout(alerts_widget)

        # 告警控制
        control_layout = QHBoxLayout()

        clear_alerts_btn = QPushButton("清除所有告警")
        clear_alerts_btn.clicked.connect(self.clear_alerts)
        control_layout.addWidget(clear_alerts_btn)

        control_layout.addStretch()

        layout.addLayout(control_layout)

        # 告警表
        self.alerts_table = QTableWidget()
        self.alerts_table.setColumnCount(5)
        self.alerts_table.setHorizontalHeaderLabels([
            "时间", "级别", "标题", "消息", "来源"
        ])
        layout.addWidget(self.alerts_table)

        self.main_tabs.addTab(alerts_widget, "告警管理")

    def create_logs_tab(self):
        """创建日志标签页"""
        logs_widget = QWidget()
        layout = QVBoxLayout(logs_widget)

        # 日志控制
        control_layout = QHBoxLayout()

        clear_logs_btn = QPushButton("清除日志")
        clear_logs_btn.clicked.connect(self.clear_logs)
        control_layout.addWidget(clear_logs_btn)

        control_layout.addStretch()

        layout.addLayout(control_layout)

        # 日志显示
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        self.logs_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.logs_text)

        self.main_tabs.addTab(logs_widget, "日志")

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 添加状态信息
        self.status_bar.showMessage("就绪")

        # 添加时间显示
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)

        # 更新时间显示
        self.update_time_display()

    def init_core_components(self):
        """初始化核心组件"""
        try:
            # 创建包装器
            self.wrapper = MultiProductDDEWrapper(self.config_file)

            # 创建监控系统
            self.monitoring_system = MonitoringSystem()

            # 创建并行处理器
            self.parallel_processor = ParallelProcessor()

            self.logger.info("核心组件初始化完成")

        except Exception as e:
            self.logger.error(f"初始化核心组件失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"初始化失败: {str(e)}")

    def connect_signals(self):
        """连接信号"""
        try:
            # 连接内部信号
            self.status_updated.connect(self.on_status_updated)
            self.alert_received.connect(self.on_alert_received)
            self.metrics_updated.connect(self.on_metrics_updated)

            # 设置监控系统回调
            if self.monitoring_system:
                self.monitoring_system.on_alert = self.on_monitoring_alert
                self.monitoring_system.on_metrics_update = self.on_monitoring_metrics

            self.logger.info("信号连接完成")

        except Exception as e:
            self.logger.error(f"连接信号失败: {str(e)}")

    def start_wrapper(self):
        """启动包装器"""
        try:
            if not self.wrapper:
                QMessageBox.warning(self, "警告", "包装器未初始化")
                return

            self.logger.info("开始启动包装器")

            # 初始化包装器
            if not self.wrapper.initialize():
                QMessageBox.critical(self, "错误", "包装器初始化失败")
                return

            # 启动包装器
            if not self.wrapper.start():
                QMessageBox.critical(self, "错误", "包装器启动失败")
                return

            # 启动监控系统
            if self.monitoring_system:
                self.monitoring_system.set_engine_manager(self.wrapper.engine_manager)
                self.monitoring_system.start()

            # 启动并行处理器
            if self.parallel_processor:
                self.parallel_processor.start()

            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.restart_btn.setEnabled(True)

            self.status_updated.emit("包装器已启动")
            self.logger.info("包装器启动成功")

        except Exception as e:
            self.logger.error(f"启动包装器失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动失败: {str(e)}")

    def stop_wrapper(self):
        """停止包装器"""
        try:
            self.logger.info("开始停止包装器")

            # 停止并行处理器
            if self.parallel_processor:
                self.parallel_processor.stop()

            # 停止监控系统
            if self.monitoring_system:
                self.monitoring_system.stop()

            # 停止包装器
            if self.wrapper:
                self.wrapper.stop()

            # 更新UI状态
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.restart_btn.setEnabled(False)

            self.status_updated.emit("包装器已停止")
            self.logger.info("包装器停止成功")

        except Exception as e:
            self.logger.error(f"停止包装器失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"停止失败: {str(e)}")

    def restart_wrapper(self):
        """重启包装器"""
        try:
            self.logger.info("重启包装器")
            self.stop_wrapper()
            # 等待一秒后重启
            QTimer.singleShot(1000, self.start_wrapper)

        except Exception as e:
            self.logger.error(f"重启包装器失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"重启失败: {str(e)}")

    def update_displays(self):
        """更新显示"""
        try:
            # 更新时间显示
            self.update_time_display()

            # 更新包装器状态
            self.update_wrapper_status()

            # 更新系统指标
            self.update_system_metrics()

            # 更新商品表
            self.update_products_table()

            # 更新引擎表
            self.update_engines_table()

            # 更新告警表
            self.update_alerts_table()

        except Exception as e:
            self.logger.error(f"更新显示失败: {str(e)}")

    def update_time_display(self):
        """更新时间显示"""
        try:
            current_time = datetime.now().strftime("%Y/%m/%d %H:%M:%S")
            self.time_label.setText(current_time)

        except Exception as e:
            self.logger.error(f"更新时间显示失败: {str(e)}")

    def update_wrapper_status(self):
        """更新包装器状态"""
        try:
            if self.wrapper:
                state = self.wrapper.get_state()
                self.wrapper_status_label.setText(state.value)
                self.status_indicator.setText(f"状态: {state.value}")

                # 根据状态设置颜色
                if state == WrapperState.RUNNING:
                    self.status_indicator.setStyleSheet("QLabel { color: #4CAF50; font-weight: bold; }")
                elif state == WrapperState.ERROR:
                    self.status_indicator.setStyleSheet("QLabel { color: #f44336; font-weight: bold; }")
                else:
                    self.status_indicator.setStyleSheet("QLabel { color: #FF9800; font-weight: bold; }")

                # 更新统计信息
                stats = self.wrapper.get_stats()
                if stats:
                    self.products_count_label.setText(str(stats.total_products))
                    self.data_received_label.setText(str(stats.total_data_received))
                    self.data_processed_label.setText(str(stats.total_data_processed))

                    # 更新运行时间
                    if stats.uptime > 0:
                        hours = int(stats.uptime // 3600)
                        minutes = int((stats.uptime % 3600) // 60)
                        seconds = int(stats.uptime % 60)
                        self.uptime_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")

                    # 更新引擎数量
                    if stats.engine_stats:
                        self.engines_count_label.setText(str(stats.engine_stats.total_engines))

        except Exception as e:
            self.logger.error(f"更新包装器状态失败: {str(e)}")

    def update_system_metrics(self):
        """更新系统指标"""
        try:
            if self.monitoring_system:
                metrics = self.monitoring_system.get_current_system_metrics()
                if metrics:
                    # 更新CPU使用率
                    cpu_percent = int(metrics.cpu_percent)
                    self.cpu_progress.setValue(cpu_percent)
                    self.cpu_label.setText(f"{cpu_percent}%")

                    # 更新内存使用率
                    memory_percent = int(metrics.memory_percent)
                    self.memory_progress.setValue(memory_percent)
                    self.memory_label.setText(f"{memory_percent}%")

                    # 设置进度条颜色
                    if cpu_percent > 80:
                        self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #f44336; }")
                    elif cpu_percent > 60:
                        self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #FF9800; }")
                    else:
                        self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #4CAF50; }")

                    if memory_percent > 80:
                        self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #f44336; }")
                    elif memory_percent > 60:
                        self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #FF9800; }")
                    else:
                        self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #4CAF50; }")

        except Exception as e:
            self.logger.error(f"更新系统指标失败: {str(e)}")

    def update_products_table(self):
        """更新商品表"""
        try:
            if not self.wrapper:
                return

            # 获取商品状态
            products = self.wrapper.config_manager.get_all_products()

            self.products_table.setRowCount(len(products) * 4)  # 假设每个商品有4种数据类型

            row = 0
            for symbol, product in products.items():
                for data_type in product.data_types:
                    self.products_table.setItem(row, 0, QTableWidgetItem(symbol))
                    self.products_table.setItem(row, 1, QTableWidgetItem(data_type))

                    # 获取连接状态
                    status = self.wrapper.get_product_status(symbol)
                    if status:
                        connected = "已连接" if status['connected'] else "未连接"
                        self.products_table.setItem(row, 2, QTableWidgetItem(connected))

                        last_activity = datetime.fromtimestamp(status['last_activity']).strftime("%H:%M:%S") if status['last_activity'] > 0 else "无"
                        self.products_table.setItem(row, 4, QTableWidgetItem(last_activity))
                    else:
                        self.products_table.setItem(row, 2, QTableWidgetItem("未知"))
                        self.products_table.setItem(row, 4, QTableWidgetItem("无"))

                    self.products_table.setItem(row, 3, QTableWidgetItem("0"))  # 数据接收
                    self.products_table.setItem(row, 5, QTableWidgetItem("正常"))  # 状态

                    row += 1

            # 调整列宽
            self.products_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新商品表失败: {str(e)}")

    def update_engines_table(self):
        """更新引擎表"""
        try:
            if not self.wrapper or not self.wrapper.engine_manager:
                return

            engines = self.wrapper.engine_manager.get_all_engines()
            self.engines_table.setRowCount(len(engines))

            row = 0
            for engine_id, engine in engines.items():
                self.engines_table.setItem(row, 0, QTableWidgetItem(engine_id))
                self.engines_table.setItem(row, 1, QTableWidgetItem(engine.product_symbol))
                self.engines_table.setItem(row, 2, QTableWidgetItem(engine.data_type))
                self.engines_table.setItem(row, 3, QTableWidgetItem(engine.state.value))
                self.engines_table.setItem(row, 4, QTableWidgetItem("0"))  # 数据接收
                self.engines_table.setItem(row, 5, QTableWidgetItem("0"))  # 数据处理
                self.engines_table.setItem(row, 6, QTableWidgetItem(str(engine.error_count)))

                last_heartbeat = datetime.fromtimestamp(engine.last_heartbeat).strftime("%H:%M:%S") if engine.last_heartbeat > 0 else "无"
                self.engines_table.setItem(row, 7, QTableWidgetItem(last_heartbeat))

                row += 1

            # 调整列宽
            self.engines_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新引擎表失败: {str(e)}")

    def update_alerts_table(self):
        """更新告警表"""
        try:
            if not self.monitoring_system:
                return

            alerts = self.monitoring_system.get_active_alerts()
            self.alerts_table.setRowCount(len(alerts))

            for row, alert in enumerate(alerts):
                time_str = datetime.fromtimestamp(alert.timestamp).strftime("%H:%M:%S")
                self.alerts_table.setItem(row, 0, QTableWidgetItem(time_str))
                self.alerts_table.setItem(row, 1, QTableWidgetItem(alert.level.value.upper()))
                self.alerts_table.setItem(row, 2, QTableWidgetItem(alert.title))
                self.alerts_table.setItem(row, 3, QTableWidgetItem(alert.message))
                self.alerts_table.setItem(row, 4, QTableWidgetItem(alert.source))

                # 根据告警级别设置颜色
                level_item = self.alerts_table.item(row, 1)
                if alert.level == AlertLevel.CRITICAL:
                    level_item.setBackground(QColor("#f44336"))
                elif alert.level == AlertLevel.ERROR:
                    level_item.setBackground(QColor("#FF5722"))
                elif alert.level == AlertLevel.WARNING:
                    level_item.setBackground(QColor("#FF9800"))
                else:
                    level_item.setBackground(QColor("#2196F3"))

            # 调整列宽
            self.alerts_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新告警表失败: {str(e)}")

    # 槽函数
    def on_status_updated(self, message: str):
        """状态更新槽函数"""
        self.status_bar.showMessage(message)

    def on_alert_received(self, alert: Alert):
        """告警接收槽函数"""
        # 在日志中显示告警
        time_str = datetime.fromtimestamp(alert.timestamp).strftime("%H:%M:%S")
        log_message = f"[{time_str}] [{alert.level.value.upper()}] {alert.title}: {alert.message}"
        self.logs_text.append(log_message)

    def on_metrics_updated(self, metrics):
        """指标更新槽函数"""
        # 这里可以添加实时图表更新
        pass

    def on_monitoring_alert(self, alert: Alert):
        """监控告警回调"""
        self.alert_received.emit(alert)

    def on_monitoring_metrics(self, metrics):
        """监控指标回调"""
        self.metrics_updated.emit(metrics)

    # 菜单动作
    def new_config(self):
        """新建配置"""
        QMessageBox.information(self, "信息", "新建配置功能待实现")

    def open_config(self):
        """打开配置"""
        QMessageBox.information(self, "信息", "打开配置功能待实现")

    def save_config(self):
        """保存配置"""
        try:
            if self.wrapper and self.wrapper.config_manager:
                if self.wrapper.config_manager.save_config():
                    QMessageBox.information(self, "成功", "配置保存成功")
                else:
                    QMessageBox.warning(self, "失败", "配置保存失败")
            else:
                QMessageBox.warning(self, "警告", "配置管理器未初始化")

        except Exception as e:
            self.logger.error(f"保存配置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        DDE引擎包装器 v1.0

        多商品DDE监控系统的引擎包装器实现

        特性:
        • 多商品同时监控
        • 高性能并行处理
        • 实时性能监控
        • 智能告警系统

        技术栈:
        • Python 3.8+
        • PySide6
        • 多线程架构

        © 2025 DDE Monitor Team
        """
        QMessageBox.about(self, "关于", about_text)

    # 控制按钮动作
    def refresh_engines(self):
        """刷新引擎"""
        self.update_engines_table()
        self.status_bar.showMessage("引擎信息已刷新")

    def clear_alerts(self):
        """清除告警"""
        try:
            if self.monitoring_system:
                # 清除活跃告警
                active_alerts = self.monitoring_system.get_active_alerts()
                for alert in active_alerts:
                    self.monitoring_system.resolve_alert(alert.alert_id)

                self.status_bar.showMessage("告警已清除")

        except Exception as e:
            self.logger.error(f"清除告警失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"清除告警失败: {str(e)}")

    def clear_logs(self):
        """清除日志"""
        self.logs_text.clear()
        self.status_bar.showMessage("日志已清除")

    # 窗口事件
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 询问是否确认退出
            reply = QMessageBox.question(
                self, "确认退出",
                "确定要退出DDE引擎包装器吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 停止所有组件
                self.stop_wrapper()
                event.accept()
                self.logger.info("应用程序退出")
            else:
                event.ignore()

        except Exception as e:
            self.logger.error(f"关闭窗口失败: {str(e)}")
            event.accept()


def main():
    """主函数"""
    import sys
    from PySide6.QtWidgets import QApplication

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )

    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("DDE引擎包装器")
    app.setApplicationVersion("1.0")

    # 创建主窗口
    window = WrapperMainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()