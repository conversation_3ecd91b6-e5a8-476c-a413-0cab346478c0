# DDE 監控程式開發指南

## 開發環境設置

### 系統需求
- **作業系統**: Windows 10/11 (DDE 為 Windows 特有協議)
- **Python 版本**: Python 3.10+
- **IDE**: 建議使用 VS Code 或 PyCharm

### 依賴套件
```bash
pip install PySide6          # GUI 框架
pip install pywin32          # Windows API 支援
pip install configparser     # 設定檔處理 (內建)
pip install logging          # 日誌系統 (內建)
```

### 專案結構
```
dde_monitor/
├── dde_monitor.py          # 主程式
├── config.ini              # 設定檔
├── Py_RunMe.bat            # 啟動腳本
├── dydde/                  # DDE 模組 (請勿修改)
│   ├── __init__.py
│   ├── dydde.py
│   ├── dydde_manual.txt
│   └── dydde_request_usage_guide.txt
├── docs/                   # 文件目錄
│   ├── PROJECT_ANALYSIS.md
│   ├── ARCHITECTURE_DESIGN.md
│   ├── API_REFERENCE.md
│   └── DEVELOPMENT_GUIDE.md
└── logs/                   # 日誌目錄 (自動建立)
```

## 開發規範

### 程式碼風格
- 遵循 PEP 8 規範
- 使用中文註釋和文件字串
- 類別名稱使用 PascalCase
- 方法名稱使用 snake_case
- 常數使用 UPPER_CASE

### 註釋規範
```python
def method_name(self, param1: str, param2: int) -> bool:
    """方法功能描述
    
    Args:
        param1: 參數1描述
        param2: 參數2描述
        
    Returns:
        bool: 返回值描述
        
    Raises:
        Exception: 異常情況描述
    """
```

### 日誌規範
```python
# 使用適當的日誌級別
self.logger.debug("調試資訊")      # 詳細的調試資訊
self.logger.info("一般資訊")       # 一般操作資訊
self.logger.warning("警告資訊")    # 警告但不影響運行
self.logger.error("錯誤資訊")      # 錯誤資訊
self.logger.critical("嚴重錯誤")   # 嚴重錯誤

# 日誌格式
self.logger.info(f"[{self.__class__.__name__}] 操作描述: {詳細資訊}")
```

## 開發流程

### 1. 功能開發流程
1. **需求分析**: 明確功能需求和技術要求
2. **設計階段**: 設計 API 和資料結構
3. **實作階段**: 編寫程式碼和單元測試
4. **測試階段**: 功能測試和整合測試
5. **文件更新**: 更新相關文件

### 2. 程式碼提交流程
1. **程式碼檢查**: 確保符合編碼規範
2. **測試驗證**: 執行相關測試
3. **文件更新**: 更新 API 文件和使用說明
4. **版本標記**: 更新版本號和變更日誌

### 3. 問題處理流程
1. **問題重現**: 確認問題可重現
2. **根因分析**: 分析問題根本原因
3. **解決方案**: 設計和實作解決方案
4. **測試驗證**: 確認問題已解決
5. **文件記錄**: 記錄問題和解決方案

## 常見開發任務

### 1. 新增監控項目
**步驟**:
1. 修改 `config.ini` 中的 `[Items]` 區段
2. 重新啟動程式載入新設定
3. 測試新項目的 DDE 連接

**範例**:
```ini
[Items]
item9_name = 新項目名稱
item9_code = DDE.項目.代碼
```

### 2. 修改資料處理邏輯
**位置**: `dde_monitor.py` 中的 `_process_advise_data` 方法
**注意事項**:
- 保持線程安全
- 處理異常情況
- 更新相關測試

**範例**:
```python
def _process_advise_data(self, item: str, value: str):
    """處理接收到的資料"""
    try:
        # 1. 資料驗證
        if not self._validate_data(item, value):
            return
            
        # 2. 資料轉換
        processed_value = self._transform_data(value)
        
        # 3. 更新資料容器
        self._update_data_container(item, processed_value)
        
        # 4. 觸發後續處理
        self._trigger_downstream_processing(item, processed_value)
        
    except Exception as e:
        self.logger.error(f"資料處理失敗: {str(e)}")
```

### 3. 新增檔案輸出格式
**位置**: `DataFileHandler` 類別
**步驟**:
1. 在 `config.ini` 新增輸出控制選項
2. 修改 `init_files` 方法
3. 新增對應的 `save_*` 方法
4. 更新 `save_row` 方法

**範例**:
```python
def save_json_format(self, row: RawDataRow):
    """儲存 JSON 格式資料"""
    if not self.enable_json_file:
        return
        
    json_data = {
        'timestamp': f"{row.receive_date} {row.receive_time}",
        'data': row.values
    }
    
    with open(self.json_file, 'a', encoding='utf-8') as f:
        f.write(json.dumps(json_data, ensure_ascii=False) + '\n')
```

### 4. 新增 UI 元件
**位置**: `init_ui` 方法
**步驟**:
1. 建立 UI 元件
2. 設定佈局
3. 連接信號和槽
4. 更新相關方法

**範例**:
```python
def init_ui(self):
    # 建立新按鈕
    self.export_btn = QPushButton("匯出資料")
    self.export_btn.clicked.connect(self.export_data)
    
    # 加入佈局
    button_layout.addWidget(self.export_btn)

def export_data(self):
    """匯出資料功能"""
    try:
        # 實作匯出邏輯
        pass
    except Exception as e:
        QMessageBox.critical(self, "錯誤", f"匯出失敗: {str(e)}")
```

## 測試指南

### 1. 單元測試
**測試框架**: unittest 或 pytest
**測試範圍**: 各個類別的獨立功能

**範例**:
```python
import unittest
from dde_monitor import DataFileHandler

class TestDataFileHandler(unittest.TestCase):
    def setUp(self):
        self.handler = DataFileHandler()
        
    def test_ensure_directory(self):
        """測試目錄建立功能"""
        test_path = "./test_dir/test_file.txt"
        self.handler.ensure_directory(test_path)
        self.assertTrue(os.path.exists("./test_dir"))
        
    def tearDown(self):
        # 清理測試資料
        pass
```

### 2. 整合測試
**測試範圍**: 模組間的互動功能
**重點測試**:
- DDE 連接和資料接收
- 檔案輸出功能
- UI 互動功能

### 3. 效能測試
**測試項目**:
- 大量資料處理效能
- 記憶體使用情況
- 長時間運行穩定性

**工具**:
```python
import cProfile
import memory_profiler

# 效能分析
cProfile.run('main_function()')

# 記憶體分析
@memory_profiler.profile
def memory_intensive_function():
    pass
```

## 除錯指南

### 1. 常見問題和解決方案

#### DDE 連接失敗
**症狀**: 無法連接到 DDE 服務
**可能原因**:
- DDE 服務未啟動
- 服務名稱或主題錯誤
- 防火牆阻擋

**解決方案**:
```python
# 檢查 DDE 服務狀態
def check_dde_service(self):
    try:
        test_client = DDEClient(self.service, self.topic)
        result = test_client.request("TEST_ITEM")
        return result is not None
    except Exception as e:
        self.logger.error(f"DDE 服務檢查失敗: {str(e)}")
        return False
```

#### 資料處理延遲
**症狀**: 資料更新不及時
**可能原因**:
- 處理佇列堆積
- UI 更新頻率過高
- 檔案 I/O 阻塞

**解決方案**:
```python
# 監控佇列狀態
def monitor_queue_status(self):
    queue_size = self.data_processor.data_queue.qsize()
    if queue_size > 100:
        self.logger.warning(f"資料佇列堆積: {queue_size}")
```

#### 記憶體洩漏
**症狀**: 程式運行時記憶體持續增長
**可能原因**:
- DDE 資源未正確釋放
- 資料容器無限增長
- 事件處理器未清理

**解決方案**:
```python
# 定期清理資源
def cleanup_resources(self):
    # 清理過期資料
    current_time = time.time()
    for key in list(self.data_cache.keys()):
        if current_time - self.data_cache[key]['timestamp'] > 3600:
            del self.data_cache[key]
```

### 2. 除錯工具

#### 日誌分析
```python
# 啟用詳細日誌
logging.getLogger().setLevel(logging.DEBUG)

# 分析日誌模式
def analyze_log_patterns(log_file):
    with open(log_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        error_count = len([line for line in lines if 'ERROR' in line])
        warning_count = len([line for line in lines if 'WARNING' in line])
    return {'errors': error_count, 'warnings': warning_count}
```

#### 效能監控
```python
import time
import psutil

class PerformanceMonitor:
    def __init__(self):
        self.start_time = time.time()
        self.process = psutil.Process()
        
    def get_stats(self):
        return {
            'runtime': time.time() - self.start_time,
            'memory_mb': self.process.memory_info().rss / 1024 / 1024,
            'cpu_percent': self.process.cpu_percent()
        }
```

## 部署指南

### 1. 開發環境部署
```bash
# 1. 建立虛擬環境
python -m venv venv

# 2. 啟動虛擬環境
venv\Scripts\activate

# 3. 安裝依賴
pip install -r requirements.txt

# 4. 執行程式
python dde_monitor.py
```

### 2. 生產環境部署
```bash
# 1. 建立執行檔 (使用 PyInstaller)
pip install pyinstaller
pyinstaller --onefile --windowed dde_monitor.py

# 2. 複製必要檔案
copy config.ini dist/
copy -r dydde dist/

# 3. 建立啟動腳本
echo "cd /d %~dp0 && dde_monitor.exe" > start.bat
```

### 3. 設定檔部署
- 根據實際環境修改 `config.ini`
- 確保輸出目錄有寫入權限
- 驗證 DDE 服務連接參數

---
*文件版本*: v1.0  
*建立日期*: 2025-06-16  
*最後更新*: 2025-06-16
