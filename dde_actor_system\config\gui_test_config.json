{"system": {"system_name": "DDE Actor System GUI Test", "version": "1.0.0", "debug_mode": true, "log_level": "INFO", "log_file": "logs/gui_test_system.log", "log_max_size_mb": 10, "log_backup_count": 3, "enable_monitoring": true, "monitoring_interval": 5.0, "health_check_interval": 30.0, "enable_remote_api": false, "api_host": "localhost", "api_port": 8080}, "performance": {"dde_buffer_size": 32768, "dde_batch_size": 50, "dde_batch_timeout": 0.1, "processing_batch_size": 50, "processing_queue_size": 500, "processing_workers": 2, "gui_update_interval_ms": 200, "gui_max_batch_size": 50, "gui_virtual_rows": 30, "file_batch_size": 50, "file_flush_interval": 3.0, "file_buffer_size": 2048, "backpressure_high_watermark": 400, "backpressure_low_watermark": 300, "backpressure_strategy": "drop_oldest", "memory_pool_initial_size": 50, "gc_threshold": 500}, "products": [{"symbol": "GUI_TEST", "data_types": ["tick"], "service": "MOCK_SERVICE", "topic": "MOCK_TOPIC", "items": ["GUI_TEST.tick.價格", "GUI_TEST.tick.數量"], "enabled": true, "priority": 1, "custom_settings": {"description": "GUI測試產品", "exchange": "MOCK", "contract_type": "test", "mock_data": true}}]}