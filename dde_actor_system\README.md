# DDE Actor System - 高性能DDE數據處理架構

## 專案概述

DDE Actor System 是一個基於Actor模型的高性能DDE數據處理系統，專為處理大量且高頻的金融數據而設計。

### 核心特性

- **Actor模型架構**：完全解耦的組件設計，支持水平擴展
- **零拷貝數據管道**：最小化內存分配和數據複製
- **背壓控制機制**：智能處理數據流量突發
- **虛擬化GUI**：高效的用戶界面更新
- **插件化設計**：支持多種數據源和輸出格式

### 性能目標

- **吞吐量**：支持每秒10萬+筆DDE數據更新
- **延遲**：端到端處理延遲 < 1ms
- **內存效率**：長時間運行無內存洩漏
- **CPU使用率**：多核心並行處理，CPU使用率 < 80%

## 系統架構

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   數據接收層     │    │    處理管道層     │    │    輸出分發層    │
├─────────────────┤    ├──────────────────┤    ├─────────────────┤
│ DDE Receiver    │───▶│ Ring Buffer      │───▶│ GUI Updater     │
│ TCP Receiver    │    │ Data Validator   │    │ File Writer     │
│ WebSocket Recv  │    │ Business Logic   │    │ DB Writer       │
│ Message Queue   │    │ Aggregator       │    │ Network Pusher  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Actor組件

1. **DDEReceiverActor**：高性能DDE數據接收
2. **DataProcessorActor**：數據驗證和業務邏輯處理
3. **GUIUpdaterActor**：異步GUI更新
4. **FileWriterActor**：高效文件寫入
5. **ConfigManagerActor**：動態配置管理

## 技術棧

- **語言**：Python 3.8+
- **GUI框架**：PySide6
- **異步處理**：asyncio + threading
- **數據結構**：collections.deque, mmap
- **配置管理**：configparser + JSON
- **測試框架**：pytest + asyncio-test

## 快速開始

### 安裝依賴

```bash
pip install -r requirements.txt
```

### 運行系統

```bash
python main.py --config config/multi_config.ini
```

### 性能測試

```bash
python tests/performance_test.py
```

## 目錄結構

```
dde_actor_system/
├── README.md                 # 專案說明
├── main.py                   # 主程式入口
├── requirements.txt          # 依賴套件
├── core/                     # 核心框架
│   ├── actor_base.py        # Actor基礎類
│   ├── message_system.py    # 消息系統
│   ├── event_loop.py        # 事件循環管理
│   └── performance.py       # 性能監控
├── actors/                   # Actor實現
│   ├── dde_receiver.py      # DDE接收Actor
│   ├── data_processor.py    # 數據處理Actor
│   ├── gui_updater.py       # GUI更新Actor
│   └── file_writer.py       # 文件寫入Actor
├── gui/                      # 用戶界面
│   ├── main_window.py       # 主視窗
│   ├── virtual_table.py     # 虛擬化表格
│   └── performance_panel.py # 性能監控面板
├── config/                   # 配置文件
│   ├── multi_config.ini     # 多產品配置
│   └── performance.json     # 性能參數
├── docs/                     # 文檔
│   ├── ARCHITECTURE.md      # 架構設計
│   ├── API_REFERENCE.md     # API參考
│   └── PERFORMANCE.md       # 性能調優指南
├── tests/                    # 測試
│   ├── unit_tests/          # 單元測試
│   ├── integration_tests/   # 整合測試
│   └── performance_tests/   # 性能測試
├── examples/                 # 範例
│   ├── simple_usage.py      # 簡單使用範例
│   └── custom_actor.py      # 自定義Actor範例
└── utils/                    # 工具模組
    ├── logger.py            # 日誌系統
    ├── profiler.py          # 性能分析
    └── memory_pool.py       # 內存池管理
```

## 開發指南

### 創建自定義Actor

```python
from core.actor_base import Actor

class CustomActor(Actor):
    async def handle_message(self, message):
        # 處理消息邏輯
        pass
```

### 性能調優

參考 `docs/PERFORMANCE.md` 獲取詳細的性能調優指南。

## 授權

本專案採用 MIT 授權條款。
