# DDE 監控程式 GUI 自動化功能使用指南

## 🎯 功能概述

DDE 監控程式現在提供了完整的 GUI 自動化設定介面，讓您可以直接在程式中調整所有自動化功能，無需手動編輯設定檔。

## 🎛️ 主視窗新增功能

### 1. 自動狀態顯示區域
**位置**: 主視窗上方，DDE 連線區域下方
**功能**: 即時顯示當前自動化功能的狀態摘要

**顯示內容**:
- 🟢 **綠色**: 功能正常啟用
- 🟠 **橙色**: 延遲或警告狀態  
- 🔵 **藍色**: 時間表模式
- ⚫ **灰色**: 功能已停用
- 🔄 **循環**: 自動重連功能

**範例顯示**:
```
🟠 延遲連線(30秒) | ⚫ 手動結束 | 🔄 自動重連(5次)
```

### 2. 快速開關控制
**位置**: 自動狀態顯示區域右側

#### 🔄 自動連線開關
- **功能**: 快速啟用/停用自動連線功能
- **即時生效**: 變更後立即重新載入自動管理器
- **自動儲存**: 變更會自動儲存到 config.ini

#### ⏰ 自動結束開關
- **功能**: 快速啟用/停用自動結束功能  
- **即時生效**: 變更後立即重新載入自動管理器
- **自動儲存**: 變更會自動儲存到 config.ini

### 3. 自動化設定按鈕
**外觀**: 綠色背景，白色粗體字
**功能**: 開啟詳細的自動化設定對話框
**特色**: 非阻塞操作，不影響主程式運行

## 🔧 自動化設定對話框

### 對話框特性
- **非模態視窗**: 不會阻塞主程式運行
- **即時預覽**: 可以套用設定而不儲存到檔案
- **分頁設計**: 三個主要分頁管理不同設定
- **智能驗證**: 自動驗證設定值的有效性

### 📡 自動連線分頁

#### 基本設定群組
- **啟用自動連線**: 總開關
- **連線模式**: 下拉選單
  - `immediate`: 程式啟動後立即連線
  - `delay`: 程式啟動後延遲指定時間連線
  - `schedule`: 按照時間表自動連線/斷線
- **延遲時間**: 0.1-300.0 秒 (僅在延遲模式下啟用)

#### 時間表設定群組
- **連線時間表**: 文字輸入框
  - 格式: `HH:MM:SS-HH:MM:SS;HH:MM:SS-HH:MM:SS`
  - 範例: `09:00:00-12:00:00;13:30:00-15:00:00`
- **支援跨日時間表**: 允許如 `23:00:00-05:00:00` 的設定
- **跳過週末**: 週六日不執行自動連線

#### 重連設定群組
- **自動重連**: 連線中斷時是否自動重連
- **最大重連次數**: 1-20 次
- **重連間隔**: 1.0-300.0 秒

### ⏰ 自動結束分頁

#### 基本設定群組
- **啟用自動結束**: 總開關
- **結束時間**: 時間選擇器 (HH:MM:SS 格式)
- **緩衝時間**: 0-3600 秒 (超過此時間不再執行自動結束)
- **警告時間**: 0-600 秒 (結束前多久顯示警告)

#### 結束行為群組
- **強制結束**: 不顯示確認對話框直接結束
- **結束前保存資料**: 自動保存未完成的資料
- **結束前斷開連線**: 安全斷開 DDE 連線
- **結束前清理暫存檔案**: 清理 *.tmp, *.temp 等檔案

### 🔔 通知設定分頁

#### 通知設定群組
- **啟用系統通知**: 啟用通知功能 (目前主要記錄到日誌)
- **啟用聲音通知**: 預留功能

#### 自動操作通知群組
- **自動連線通知**: 自動連線時是否通知
- **自動斷線通知**: 自動斷線時是否通知
- **自動結束通知**: 自動結束時是否通知

## 🎮 操作按鈕

### 套用按鈕
- **功能**: 立即套用當前設定，但不儲存到檔案
- **用途**: 測試設定是否正確
- **效果**: 重新載入自動管理器

### 儲存按鈕
- **功能**: 套用設定並儲存到 config.ini 檔案
- **用途**: 永久保存設定變更
- **效果**: 重新載入自動管理器 + 更新設定檔

### 重置按鈕
- **功能**: 將所有設定重置為預設值
- **確認**: 會顯示確認對話框
- **預設值**: 恢復到程式初始狀態

### 關閉按鈕
- **功能**: 關閉設定對話框
- **保存**: 不會自動儲存未套用的變更
- **提醒**: 如有未儲存的變更，建議先套用或儲存

## 🚀 使用方法

### 快速操作
1. **啟用自動連線**: 勾選主視窗的「自動連線」快速開關
2. **啟用自動結束**: 勾選主視窗的「自動結束」快速開關
3. **觀察狀態**: 查看自動狀態顯示區域的即時資訊

### 詳細設定
1. 點擊 **自動化設定** 按鈕 (綠色按鈕)
2. 在對應分頁中調整各項設定
3. 點擊 **套用** 測試設定是否正確
4. 點擊 **儲存** 永久保存設定

### 測試建議
1. 先設定較短的時間間隔進行測試
2. 使用 **套用** 按鈕測試設定，避免影響正式設定檔
3. 確認功能正常後再使用 **儲存** 按鈕

## ⚠️ 重要注意事項

### 非模態設計
- 設定對話框不會阻塞主程式運行
- 可以在設定的同時觀察程式運行狀態
- 關閉對話框不會影響已套用的設定

### 即時生效
- 大部分設定變更會立即生效
- 重新載入管理器可能會短暫中斷自動功能
- 建議在非關鍵時間進行設定調整

### 設定檔安全
- 設定變更會自動備份到 config.ini
- 建議定期備份工作正常的設定檔
- 避免同時手動編輯設定檔和使用 GUI 設定

### 相容性
- 新的 GUI 設定與手動編輯的設定檔完全相容
- 可以混合使用 GUI 設定和手動編輯
- 建議統一使用一種設定方式以避免混淆

## 🔍 故障排除

### 設定對話框無法開啟
1. 檢查是否已安裝 PySide6: `pip install PySide6`
2. 檢查 config.ini 檔案是否存在且格式正確
3. 查看日誌檔案中的錯誤訊息

### 快速開關無效
1. 確認設定檔有寫入權限
2. 檢查自動管理器是否正常啟動
3. 觀察自動狀態顯示是否有錯誤訊息

### 設定不生效
1. 確認已點擊「套用」或「儲存」按鈕
2. 檢查設定值是否在有效範圍內
3. 重新啟動程式以確保設定完全生效

## 📋 設定檔格式參考

```ini
[AutoConnect]
enable_auto_connect = true
auto_connect_mode = delay
auto_connect_delay = 30.0
schedule_connect_times = 09:00:00-12:00:00;13:30:00-15:00:00
enable_cross_day_schedule = true
skip_weekends = false
auto_reconnect_on_disconnect = true
max_reconnect_attempts = 5
reconnect_interval = 10.0

[AutoShutdown]
enable_auto_shutdown = false
shutdown_time = 17:30:00
shutdown_buffer_seconds = 300
shutdown_warning_seconds = 60
force_shutdown = true
save_data_before_shutdown = true
disconnect_before_shutdown = true
cleanup_temp_files = true

[Notifications]
enable_system_notifications = true
enable_sound_notifications = false
notify_auto_connect = true
notify_auto_disconnect = true
notify_auto_shutdown = true
```

---
*文件版本*: v1.0  
*建立日期*: 2025-06-17  
*適用程式版本*: DDE Monitor v6 (GUI 自動化版)
