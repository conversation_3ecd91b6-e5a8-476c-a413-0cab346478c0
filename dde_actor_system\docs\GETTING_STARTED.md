# DDE Actor System - 快速開始指南

## 概述

DDE Actor System 是一個基於Actor模型的高性能DDE數據處理系統，專為處理大量且高頻的金融數據而設計。

## 系統要求

### 硬體要求
- **CPU**: Intel i5-8400 或同等性能（建議 i7-8700K 或更高）
- **內存**: 8GB RAM（建議 16GB 或更高）
- **存儲**: 10GB 可用空間（SSD 建議）
- **網絡**: 千兆以太網

### 軟體要求
- **作業系統**: Windows 10/11 (64位)
- **Python**: 3.10+ (建議 3.11+)
- **DDE服務**: SKCOM 或其他 DDE 數據提供商

## 安裝步驟

### 1. 準備Python環境

```bash
# 檢查Python版本
python --version

# 創建虛擬環境
python -m venv venv

# 啟動虛擬環境 (Windows)
venv\Scripts\activate

# 啟動虛擬環境 (Linux/Mac)
source venv/bin/activate
```

### 2. 安裝依賴套件

```bash
# 安裝核心依賴
pip install PySide6>=6.5.0 aiofiles>=23.0.0 psutil>=5.9.0

# 或安裝完整依賴
pip install -r requirements.txt
```

### 3. 配置系統

複製並編輯配置文件：

```bash
# 複製範例配置
cp config/system_config.json config/my_config.json

# 編輯配置文件
notepad config/my_config.json
```

### 4. 運行系統

```bash
# 使用默認配置運行
python main.py

# 使用自定義配置運行
python main.py --config config/my_config.json

# 啟用調試模式
python main.py --debug --log-level DEBUG
```

## 配置說明

### 基本配置結構

```json
{
  "system": {
    "system_name": "DDE Actor System",
    "debug_mode": false,
    "log_level": "INFO"
  },
  "performance": {
    "dde_buffer_size": 1048576,
    "dde_batch_size": 1000,
    "processing_workers": 4
  },
  "products": [
    {
      "symbol": "FITXN07",
      "data_types": ["tick", "order"],
      "service": "SKCOM",
      "topic": "SKCOM",
      "items": ["FITXN07.tick.成交價", "FITXN07.tick.成交量"],
      "enabled": true
    }
  ]
}
```

### 關鍵配置參數

#### 系統配置 (system)
- `system_name`: 系統名稱
- `debug_mode`: 調試模式開關
- `log_level`: 日誌級別 (DEBUG, INFO, WARNING, ERROR)
- `log_file`: 日誌文件路径

#### 性能配置 (performance)
- `dde_buffer_size`: DDE緩衝區大小（字節）
- `dde_batch_size`: DDE批量處理大小
- `dde_batch_timeout`: DDE批量超時時間（秒）
- `processing_workers`: 處理工作線程數
- `gui_update_interval_ms`: GUI更新間隔（毫秒）

#### 產品配置 (products)
- `symbol`: 產品代碼
- `data_types`: 數據類型列表 ["tick", "order", "level2", "daily"]
- `service`: DDE服務名稱
- `topic`: DDE主題名稱
- `items`: DDE項目列表
- `enabled`: 是否啟用

## 使用範例

### 基本使用

```python
import asyncio
from main import DDEActorSystem

async def main():
    # 創建系統實例
    system = DDEActorSystem("config/system_config.json")
    
    try:
        # 初始化並啟動系統
        await system.initialize()
        await system.start()
        
        # 運行系統
        await system.run_forever()
        
    finally:
        await system.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

### 自定義Actor

```python
from core.actor_base import ActorBase
from core.message_system import Message, MessageType

class MyCustomActor(ActorBase):
    async def handle_message(self, message: Message):
        if message.type == MessageType.DDE_DATA:
            # 處理DDE數據
            item = message.data['item']
            value = message.data['value']
            
            # 自定義處理邏輯
            processed_value = self.process_data(item, value)
            
            # 發送處理結果
            result_message = Message(
                type=MessageType.CUSTOM,
                data={'processed_value': processed_value},
                sender=self.name
            )
            await self.send_message("TargetActor", result_message)
    
    def process_data(self, item: str, value: str):
        # 實現自定義處理邏輯
        return f"Processed: {value}"
```

## 性能調優

### 基本調優建議

1. **調整批量大小**
   ```json
   {
     "performance": {
       "dde_batch_size": 1000,        // 高頻數據建議 500-2000
       "processing_batch_size": 1000   // 根據CPU性能調整
     }
   }
   ```

2. **優化緩衝區大小**
   ```json
   {
     "performance": {
       "dde_buffer_size": 1048576,    // 1MB，可根據數據量調整
       "processing_queue_size": 10000  // 根據內存容量調整
     }
   }
   ```

3. **調整工作線程數**
   ```json
   {
     "performance": {
       "processing_workers": 4         // 建議為CPU核心數
     }
   }
   ```

### 高性能配置範例

```json
{
  "performance": {
    "dde_buffer_size": 2097152,
    "dde_batch_size": 2000,
    "dde_batch_timeout": 0.005,
    "processing_batch_size": 2000,
    "processing_workers": 8,
    "gui_update_interval_ms": 33,
    "backpressure_high_watermark": 15000,
    "backpressure_low_watermark": 10000
  }
}
```

## 監控和診斷

### 性能監控

系統內建性能監控功能：

```python
# 獲取系統狀態
status = system.get_system_status()
print(f"運行時間: {status['uptime']:.2f} 秒")
print(f"Actor狀態: {status['actors']}")

# 獲取性能指標
if 'performance' in status:
    metrics = status['performance']['current_metrics']
    print(f"吞吐量: {metrics.messages_per_second:.0f} 筆/秒")
    print(f"平均延遲: {metrics.avg_latency_ms:.2f} ms")
    print(f"內存使用: {metrics.memory_usage_mb:.1f} MB")
```

### 日誌分析

查看系統日誌：

```bash
# 查看實時日誌
tail -f logs/system.log

# 搜索錯誤
grep "ERROR" logs/system.log

# 搜索性能指標
grep "性能統計" logs/system.log
```

### 性能測試

運行內建性能測試：

```bash
# 運行完整性能測試
python tests/performance_test.py

# 查看測試報告
cat performance_test_report.txt
```

## 故障排除

### 常見問題

1. **系統啟動失敗**
   - 檢查配置文件格式
   - 確認DDE服務可用
   - 查看日誌錯誤信息

2. **性能不佳**
   - 調整批量大小
   - 增加工作線程數
   - 檢查系統資源使用

3. **內存洩漏**
   - 啟用內存監控
   - 檢查Actor實現
   - 調整GC參數

4. **數據丟失**
   - 檢查背壓控制設置
   - 增加隊列大小
   - 優化處理速度

### 調試技巧

1. **啟用調試模式**
   ```bash
   python main.py --debug --log-level DEBUG
   ```

2. **使用性能分析器**
   ```python
   from core.performance import PerformanceMonitor
   
   monitor = PerformanceMonitor()
   await monitor.start()
   # ... 運行代碼 ...
   report = monitor.get_performance_report()
   ```

3. **檢查Actor狀態**
   ```python
   for name, actor in system.actors.items():
       info = actor.get_info()
       print(f"{name}: {info['state']}, 隊列: {info['queue_size']}")
   ```

## 進階功能

### 動態配置更新

```python
# 更新性能配置
updates = {
    'performance': {
        'dde_batch_size': 1500,
        'processing_workers': 6
    }
}
await system.config_manager.update_config_async(updates)
```

### 自動性能調優

```python
# 啟用自動調優
system.config_manager.enable_auto_tuning(system.performance_monitor)
```

### 自定義數據處理規則

```python
from actors.data_processor import ProcessingRule

# 添加自定義處理規則
rule = ProcessingRule(
    name="price_filter",
    condition=lambda item, value: "價" in item,
    action=lambda item, value: float(value) if value.isdigit() else 0.0,
    priority=1
)
data_processor.add_processing_rule(rule)
```

## 部署建議

### 生產環境部署

1. **使用專用配置**
   ```json
   {
     "system": {
       "debug_mode": false,
       "log_level": "WARNING"
     }
   }
   ```

2. **設置系統服務**
   ```bash
   # 使用 NSSM 或其他服務管理工具
   nssm install DDEActorSystem python main.py
   ```

3. **監控和告警**
   - 設置性能監控
   - 配置日誌輪轉
   - 建立告警機制

### 高可用性部署

1. **多實例部署**
2. **負載均衡**
3. **故障轉移**
4. **數據備份**

## 支援和社群

- **文檔**: 查看 `docs/` 目錄下的詳細文檔
- **範例**: 參考 `examples/` 目錄下的使用範例
- **測試**: 運行 `tests/` 目錄下的測試案例

## 版本更新

查看 `CHANGELOG.md` 了解版本更新內容和升級指南。
