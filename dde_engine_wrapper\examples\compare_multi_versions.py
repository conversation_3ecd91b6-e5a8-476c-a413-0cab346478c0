#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多商品版本比较测试
同时运行原版多商品DDE监控和引擎包装器版，比较性能和功能
"""

import sys
import os
import time
import subprocess
import threading
import logging
import psutil
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class ProcessMonitor:
    """进程监控器"""
    
    def __init__(self, name: str):
        self.name = name
        self.process: Optional[subprocess.Popen] = None
        self.pid: Optional[int] = None
        self.start_time: Optional[float] = None
        self.end_time: Optional[float] = None
        self.cpu_usage: List[float] = []
        self.memory_usage: List[float] = []
        self.monitoring = False
        self.monitor_thread: Optional[threading.Thread] = None
        
    def start_process(self, command: List[str], cwd: str = None) -> bool:
        """启动进程"""
        try:
            self.process = subprocess.Popen(
                command,
                cwd=cwd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            self.pid = self.process.pid
            self.start_time = time.time()
            
            # 启动监控线程
            self.monitoring = True
            self.monitor_thread = threading.Thread(
                target=self._monitor_loop,
                name=f"Monitor-{self.name}",
                daemon=True
            )
            self.monitor_thread.start()
            
            print(f"✓ {self.name} 进程启动成功 (PID: {self.pid})")
            return True
            
        except Exception as e:
            print(f"✗ {self.name} 进程启动失败: {str(e)}")
            return False
    
    def stop_process(self) -> bool:
        """停止进程"""
        try:
            if self.process and self.process.poll() is None:
                self.process.terminate()
                
                # 等待进程结束
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    self.process.kill()
                    self.process.wait()
                
                self.end_time = time.time()
            
            # 停止监控
            self.monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)
            
            print(f"✓ {self.name} 进程已停止")
            return True
            
        except Exception as e:
            print(f"✗ {self.name} 进程停止失败: {str(e)}")
            return False
    
    def is_running(self) -> bool:
        """检查进程是否在运行"""
        return self.process and self.process.poll() is None
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        stats = {
            'name': self.name,
            'pid': self.pid,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': 0,
            'avg_cpu': 0,
            'max_cpu': 0,
            'avg_memory': 0,
            'max_memory': 0,
            'cpu_samples': len(self.cpu_usage),
            'memory_samples': len(self.memory_usage)
        }
        
        if self.start_time:
            end_time = self.end_time or time.time()
            stats['duration'] = end_time - self.start_time
        
        if self.cpu_usage:
            stats['avg_cpu'] = sum(self.cpu_usage) / len(self.cpu_usage)
            stats['max_cpu'] = max(self.cpu_usage)
        
        if self.memory_usage:
            stats['avg_memory'] = sum(self.memory_usage) / len(self.memory_usage)
            stats['max_memory'] = max(self.memory_usage)
        
        return stats
    
    def _monitor_loop(self):
        """监控循环"""
        try:
            while self.monitoring and self.is_running():
                try:
                    if self.pid:
                        process = psutil.Process(self.pid)
                        
                        # 获取CPU使用率
                        cpu_percent = process.cpu_percent()
                        self.cpu_usage.append(cpu_percent)
                        
                        # 获取内存使用率
                        memory_info = process.memory_info()
                        memory_mb = memory_info.rss / 1024 / 1024
                        self.memory_usage.append(memory_mb)
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    break
                except Exception as e:
                    print(f"监控异常 {self.name}: {str(e)}")
                
                time.sleep(1)  # 每秒监控一次
                
        except Exception as e:
            print(f"监控循环失败 {self.name}: {str(e)}")


class MultiVersionComparator:
    """多版本比较器"""
    
    def __init__(self):
        self.logger = self._setup_logging()
        
        # 进程监控器
        self.original_monitor = ProcessMonitor("原版多商品")
        self.wrapper_monitor = ProcessMonitor("引擎包装器版")
        
        # 测试配置
        self.test_duration = 60  # 默认测试60秒
        self.config_file = "multi_config.ini"
        
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        log_dir = os.path.join(os.path.dirname(__file__), '..', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, 'version_comparison.log')
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file, encoding='utf-8')
            ]
        )
        
        return logging.getLogger(__name__)
    
    def check_prerequisites(self) -> bool:
        """检查前置条件"""
        print("检查前置条件...")
        
        # 检查原版多商品程序
        original_path = os.path.join(os.path.dirname(__file__), '..', '..', 'dde_monitor_multi.py')
        if not os.path.exists(original_path):
            print(f"✗ 原版多商品程序不存在: {original_path}")
            return False
        print("✓ 原版多商品程序存在")
        
        # 检查配置文件
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', self.config_file)
        if not os.path.exists(config_path):
            print(f"✗ 配置文件不存在: {config_path}")
            return False
        print("✓ 配置文件存在")
        
        # 检查引擎包装器测试程序
        wrapper_path = os.path.join(os.path.dirname(__file__), 'wrapper_multi_test.py')
        if not os.path.exists(wrapper_path):
            print(f"✗ 引擎包装器测试程序不存在: {wrapper_path}")
            return False
        print("✓ 引擎包装器测试程序存在")
        
        return True
    
    def start_original_version(self) -> bool:
        """启动原版多商品程序"""
        try:
            original_dir = os.path.join(os.path.dirname(__file__), '..', '..')
            command = [
                sys.executable,
                'dde_monitor_multi.py',
                '--config', self.config_file
            ]
            
            return self.original_monitor.start_process(command, original_dir)
            
        except Exception as e:
            print(f"✗ 启动原版失败: {str(e)}")
            return False
    
    def start_wrapper_version(self) -> bool:
        """启动引擎包装器版"""
        try:
            wrapper_dir = os.path.join(os.path.dirname(__file__), '..')
            config_path = os.path.join('config', self.config_file)
            command = [
                sys.executable,
                'examples/wrapper_multi_test.py',
                '--config', config_path,
                '--duration', str(self.test_duration)
            ]
            
            return self.wrapper_monitor.start_process(command, wrapper_dir)
            
        except Exception as e:
            print(f"✗ 启动引擎包装器版失败: {str(e)}")
            return False
    
    def run_comparison_test(self, duration: int = 60) -> bool:
        """运行比较测试"""
        try:
            self.test_duration = duration
            
            print(f"\n{'='*60}")
            print(f"开始多版本比较测试 (持续 {duration} 秒)")
            print(f"{'='*60}")
            
            # 检查前置条件
            if not self.check_prerequisites():
                return False
            
            print(f"\n启动测试程序...")
            
            # 启动原版多商品程序
            if not self.start_original_version():
                return False
            
            # 等待一下再启动第二个程序
            time.sleep(2)
            
            # 启动引擎包装器版
            if not self.start_wrapper_version():
                self.original_monitor.stop_process()
                return False
            
            print(f"\n两个版本都已启动，开始 {duration} 秒测试...")
            
            # 等待测试完成
            start_time = time.time()
            while time.time() - start_time < duration:
                # 检查进程状态
                original_running = self.original_monitor.is_running()
                wrapper_running = self.wrapper_monitor.is_running()
                
                if not original_running and not wrapper_running:
                    print("两个进程都已结束")
                    break
                elif not original_running:
                    print("原版进程已结束")
                    break
                elif not wrapper_running:
                    print("引擎包装器版进程已结束")
                    break
                
                # 每10秒打印一次状态
                elapsed = time.time() - start_time
                if int(elapsed) % 10 == 0:
                    print(f"测试进行中... {elapsed:.0f}/{duration} 秒")
                
                time.sleep(1)
            
            print(f"\n测试完成，停止程序...")
            
            # 停止程序
            self.original_monitor.stop_process()
            self.wrapper_monitor.stop_process()
            
            # 生成比较报告
            self.generate_comparison_report()
            
            return True
            
        except Exception as e:
            print(f"✗ 比较测试失败: {str(e)}")
            return False
    
    def generate_comparison_report(self):
        """生成比较报告"""
        try:
            print(f"\n{'='*60}")
            print(f"多版本比较测试报告")
            print(f"{'='*60}")
            
            # 获取统计信息
            original_stats = self.original_monitor.get_stats()
            wrapper_stats = self.wrapper_monitor.get_stats()
            
            # 打印基本信息
            print(f"\n基本信息:")
            print(f"  测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"  测试持续: {self.test_duration} 秒")
            print(f"  配置文件: {self.config_file}")
            
            # 打印运行时间
            print(f"\n运行时间:")
            print(f"  原版多商品: {original_stats['duration']:.1f} 秒")
            print(f"  引擎包装器版: {wrapper_stats['duration']:.1f} 秒")
            
            # 打印CPU使用率
            print(f"\nCPU使用率:")
            print(f"  原版多商品:")
            print(f"    平均: {original_stats['avg_cpu']:.1f}%")
            print(f"    最大: {original_stats['max_cpu']:.1f}%")
            print(f"    样本数: {original_stats['cpu_samples']}")
            
            print(f"  引擎包装器版:")
            print(f"    平均: {wrapper_stats['avg_cpu']:.1f}%")
            print(f"    最大: {wrapper_stats['max_cpu']:.1f}%")
            print(f"    样本数: {wrapper_stats['cpu_samples']}")
            
            # 打印内存使用
            print(f"\n内存使用:")
            print(f"  原版多商品:")
            print(f"    平均: {original_stats['avg_memory']:.1f} MB")
            print(f"    最大: {original_stats['max_memory']:.1f} MB")
            print(f"    样本数: {original_stats['memory_samples']}")
            
            print(f"  引擎包装器版:")
            print(f"    平均: {wrapper_stats['avg_memory']:.1f} MB")
            print(f"    最大: {wrapper_stats['max_memory']:.1f} MB")
            print(f"    样本数: {wrapper_stats['memory_samples']}")
            
            # 性能比较
            print(f"\n性能比较:")
            if original_stats['avg_cpu'] > 0 and wrapper_stats['avg_cpu'] > 0:
                cpu_ratio = wrapper_stats['avg_cpu'] / original_stats['avg_cpu']
                if cpu_ratio < 1:
                    print(f"  CPU效率: 引擎包装器版比原版节省 {(1-cpu_ratio)*100:.1f}% CPU")
                else:
                    print(f"  CPU效率: 引擎包装器版比原版多用 {(cpu_ratio-1)*100:.1f}% CPU")
            
            if original_stats['avg_memory'] > 0 and wrapper_stats['avg_memory'] > 0:
                memory_ratio = wrapper_stats['avg_memory'] / original_stats['avg_memory']
                if memory_ratio < 1:
                    print(f"  内存效率: 引擎包装器版比原版节省 {(1-memory_ratio)*100:.1f}% 内存")
                else:
                    print(f"  内存效率: 引擎包装器版比原版多用 {(memory_ratio-1)*100:.1f}% 内存")
            
            # 稳定性分析
            print(f"\n稳定性分析:")
            original_completed = original_stats['duration'] >= self.test_duration * 0.9
            wrapper_completed = wrapper_stats['duration'] >= self.test_duration * 0.9
            
            print(f"  原版多商品: {'✓ 稳定运行' if original_completed else '✗ 提前结束'}")
            print(f"  引擎包装器版: {'✓ 稳定运行' if wrapper_completed else '✗ 提前结束'}")
            
            # 总结
            print(f"\n总结:")
            if wrapper_completed and original_completed:
                print("  ✓ 两个版本都能稳定运行")
            elif wrapper_completed:
                print("  ⚠ 引擎包装器版更稳定")
            elif original_completed:
                print("  ⚠ 原版更稳定")
            else:
                print("  ✗ 两个版本都不够稳定")
            
            print(f"{'='*60}")
            
            # 保存报告到文件
            self._save_report_to_file(original_stats, wrapper_stats)
            
        except Exception as e:
            print(f"✗ 生成比较报告失败: {str(e)}")
    
    def _save_report_to_file(self, original_stats: Dict, wrapper_stats: Dict):
        """保存报告到文件"""
        try:
            report_dir = os.path.join(os.path.dirname(__file__), '..', 'reports')
            os.makedirs(report_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = os.path.join(report_dir, f'comparison_report_{timestamp}.txt')
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"多版本比较测试报告\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"测试持续: {self.test_duration} 秒\n")
                f.write(f"配置文件: {self.config_file}\n\n")
                
                f.write(f"原版多商品统计:\n")
                for key, value in original_stats.items():
                    f.write(f"  {key}: {value}\n")
                
                f.write(f"\n引擎包装器版统计:\n")
                for key, value in wrapper_stats.items():
                    f.write(f"  {key}: {value}\n")
            
            print(f"报告已保存到: {report_file}")
            
        except Exception as e:
            print(f"保存报告失败: {str(e)}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='多商品版本比较测试')
    parser.add_argument('--duration', '-d', 
                       type=int, 
                       help='测试持续时间（秒）', 
                       default=60)
    parser.add_argument('--config', '-c', 
                       help='配置文件名', 
                       default='multi_config.ini')
    
    args = parser.parse_args()
    
    # 创建比较器
    comparator = MultiVersionComparator()
    comparator.config_file = args.config
    
    # 运行比较测试
    success = comparator.run_comparison_test(args.duration)
    
    if success:
        print("\n🎉 比较测试完成！")
    else:
        print("\n❌ 比较测试失败！")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
