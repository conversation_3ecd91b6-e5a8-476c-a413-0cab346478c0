# DDE 監控程式配置指南

## 📋 配置文件結構說明

### 🔗 相關配置項對照表

| 功能類別 | 配置段 | 配置項 | 說明 | 預設值 |
|---------|--------|--------|------|--------|
| **日誌文件** | `[FileOutput]` | `enable_log_file` | 是否啟用日誌文件 | `true` |
| **日誌級別** | `[Logging]` | `log_level` | 主日誌級別 | `WARNING` |
| **日誌級別** | `[Logging]` | `console_log_level` | 控制台日誌級別 | `WARNING` |
| **日誌級別** | `[Logging]` | `file_log_level` | 文件日誌級別 | `INFO` |
| **系統通知** | `[Notifications]` | `enable_system_notifications` | 是否啟用系統通知 | `true` |
| **通知級別** | `[Notifications]` | `notification_levels` | 觸發通知的日誌級別 | `WARNING,ERROR` |

---

## 🎯 配置項詳細說明

### 1. 日誌文件控制 `[FileOutput]`

```ini
[FileOutput]
enable_data_file = true           # 啟用原始數據文件
enable_complete_data_file = true  # 啟用完整數據文件  
enable_log_file = true            # 🔑 啟用日誌文件
```

**說明**：
- `enable_log_file = false` 時，程式不會創建日誌文件
- 即使設置了 `[Logging]` 中的 `file_log_level`，也不會生效

### 2. 日誌級別控制 `[Logging]`

```ini
[Logging]
log_level = WARNING          # 主日誌級別（最低記錄級別）
console_log_level = WARNING  # 控制台顯示級別
file_log_level = INFO        # 文件記錄級別
```

**級別說明**：
- `DEBUG` - 詳細調試信息（包括每筆數據處理）
- `INFO` - 一般操作信息（連接、訂閱、文件操作）
- `WARNING` - 警告信息（不影響運行但需注意）
- `ERROR` - 錯誤信息（影響功能但程式可繼續）
- `CRITICAL` - 嚴重錯誤（可能導致程式崩潰）

**工作原理**：
1. `log_level` 設定程式的最低記錄級別
2. `console_log_level` 控制 CMD 窗口顯示什麼
3. `file_log_level` 控制日誌文件記錄什麼

### 3. 系統通知控制 `[Notifications]`

```ini
[Notifications]
enable_system_notifications = True        # 啟用系統通知
notification_levels = WARNING,ERROR       # 觸發通知的級別
notify_auto_connect = True                # 自動連線時通知
notify_auto_disconnect = True             # 自動斷線時通知  
notify_auto_shutdown = True               # 自動結束時通知
```

**說明**：
- 只有在 `notification_levels` 中指定的日誌級別才會觸發系統通知
- 自動操作的通知可以單獨控制

---

## 🔧 常用配置組合

### 生產環境（推薦）
```ini
[FileOutput]
enable_log_file = true

[Logging]
log_level = WARNING
console_log_level = WARNING     # 控制台清爽
file_log_level = INFO          # 文件詳細記錄

[Notifications]
enable_system_notifications = True
notification_levels = WARNING,ERROR,CRITICAL
```

**效果**：
- 🖥️ CMD 窗口只顯示警告和錯誤
- 📁 日誌文件記錄詳細的操作信息
- 🔔 只在重要事件時彈出通知

### 調試環境
```ini
[FileOutput]
enable_log_file = true

[Logging]
log_level = DEBUG
console_log_level = DEBUG      # 顯示所有信息
file_log_level = DEBUG         # 記錄所有信息

[Notifications]
enable_system_notifications = False  # 避免調試時干擾
```

**效果**：
- 🖥️ CMD 窗口顯示所有調試信息
- 📁 日誌文件記錄所有詳細信息
- 🔕 關閉系統通知避免干擾

### 靜默運行
```ini
[FileOutput]
enable_log_file = true

[Logging]
log_level = ERROR
console_log_level = ERROR      # 只顯示錯誤
file_log_level = WARNING       # 文件記錄警告以上

[Notifications]
enable_system_notifications = True
notification_levels = ERROR,CRITICAL  # 只在錯誤時通知
```

**效果**：
- 🖥️ CMD 窗口非常安靜，只顯示錯誤
- 📁 日誌文件記錄警告和錯誤
- 🔔 只在出現錯誤時通知用戶

---

## ⚠️ 常見配置錯誤

### 1. 日誌文件未啟用但設置了文件級別
```ini
[FileOutput]
enable_log_file = false    # ❌ 關閉了日誌文件

[Logging]
file_log_level = INFO      # ❌ 這個設置會被忽略
```

### 2. 通知級別低於日誌級別
```ini
[Logging]
log_level = ERROR          # 只記錄 ERROR 以上

[Notifications]
notification_levels = INFO,WARNING  # ❌ 這些級別不會被記錄，所以不會通知
```

### 3. 控制台級別高於文件級別
```ini
[Logging]
console_log_level = ERROR  # 控制台只顯示錯誤
file_log_level = DEBUG     # 文件記錄所有信息
```
**說明**：這不是錯誤，但可能造成混淆。建議文件級別 ≤ 控制台級別。

---

## 🎨 配置建議

### 命名一致性建議
為了提高配置的一致性，建議未來版本考慮：

```ini
[Logging]
enable_file_logging = true        # 替代 [FileOutput] enable_log_file
enable_console_logging = true     # 新增控制台開關
enable_notifications = true       # 替代 [Notifications] enable_system_notifications

log_level = WARNING               # 主級別
console_log_level = WARNING       # 控制台級別
file_log_level = INFO            # 文件級別
notification_log_levels = WARNING,ERROR  # 通知級別
```

這樣所有日誌相關配置都集中在一個段落中，更容易理解和維護。

---

## 🛠️ 配置驗證工具

為了幫助檢查配置的一致性，提供了配置驗證工具：

```bash
python utils/config_validator.py
```

**驗證內容**：
- ✅ 日誌級別設置的有效性和邏輯關係
- ✅ 文件輸出開關與路徑配置的一致性
- ✅ 通知級別與日誌級別的匹配性
- ✅ 自動化功能的配置完整性

**示例輸出**：
```
📋 配置文件驗證結果：config.ini
==================================================
⚠️ 警告：
  1. file_log_level (INFO) 低於 log_level (WARNING)，可能不會記錄預期的日誌
  2. 通知級別 INFO 低於主日誌級別 WARNING，可能不會觸發通知

總計：0 個錯誤，2 個警告
```

---

## 📁 相關文件

- `config.ini` - 當前配置文件
- `config_template.ini` - 配置模板（包含詳細註釋）
- `docs/CONFIG_GUIDE.md` - 本配置指南
- `utils/config_validator.py` - 配置驗證工具

建議在修改配置後運行驗證工具，確保配置的正確性。
