#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE Actor System - 簡單使用範例

展示如何使用DDE Actor System的基本功能
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent.parent))

from main import DDEActorSystem
from core.message_system import Message, MessageType, create_dde_data_message


async def simple_example():
    """簡單使用範例"""
    print("DDE Actor System - 簡單使用範例")
    print("=" * 50)
    
    # 設置日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    
    # 創建系統實例
    config_file = "config/system_config.json"
    system = DDEActorSystem(config_file)
    
    try:
        print("1. 初始化系統...")
        if not await system.initialize():
            print("❌ 系統初始化失敗")
            return False
        print("✅ 系統初始化成功")
        
        print("\n2. 啟動系統...")
        if not await system.start():
            print("❌ 系統啟動失敗")
            return False
        print("✅ 系統啟動成功")
        
        print("\n3. 等待系統穩定...")
        await asyncio.sleep(2.0)
        
        print("\n4. 發送測試數據...")
        # 模擬發送一些DDE數據
        for i in range(10):
            message = create_dde_data_message(
                f"TEST_ITEM_{i}",
                f"VALUE_{i}",
                "SimpleExample"
            )
            
            # 這裡應該通過系統的消息路由發送
            print(f"   發送測試消息 {i+1}/10: {message.data['item']} = {message.data['value']}")
            await asyncio.sleep(0.1)
        
        print("\n5. 獲取系統狀態...")
        status = system.get_system_status()
        print(f"   系統運行狀態: {'✅ 正常' if status['running'] else '❌ 異常'}")
        print(f"   運行時間: {status['uptime']:.2f} 秒")
        print(f"   Actor數量: {len(status['actors'])}")
        
        print("\n6. 等待處理完成...")
        await asyncio.sleep(3.0)
        
        print("\n✅ 範例執行完成")
        return True
        
    except Exception as e:
        print(f"❌ 範例執行失敗: {str(e)}")
        return False
    finally:
        print("\n7. 停止系統...")
        await system.stop()
        print("✅ 系統已停止")


async def custom_actor_example():
    """自定義Actor範例"""
    from core.actor_base import ActorBase
    from core.message_system import Message, MessageType
    
    class CustomActor(ActorBase):
        """自定義Actor範例"""
        
        async def handle_message(self, message: Message):
            """處理消息"""
            if message.type == MessageType.CUSTOM:
                data = message.data
                self.logger.info(f"收到自定義消息: {data}")
                
                # 處理邏輯
                response_data = f"處理完成: {data}"
                
                # 發送回復
                response = Message(
                    type=MessageType.CUSTOM,
                    data=response_data,
                    sender=self.name
                )
                await self.send_message(message.sender, response)
    
    print("\nDDE Actor System - 自定義Actor範例")
    print("=" * 50)
    
    # 創建自定義Actor
    custom_actor = CustomActor("CustomActor", {})
    
    print("✅ 自定義Actor創建成功")
    print(f"   Actor名稱: {custom_actor.name}")
    print(f"   Actor狀態: {custom_actor.state}")


async def performance_monitoring_example():
    """性能監控範例"""
    from core.performance import PerformanceMonitor
    
    print("\nDDE Actor System - 性能監控範例")
    print("=" * 50)
    
    # 創建性能監控器
    monitor = PerformanceMonitor(collection_interval=1.0)
    
    try:
        print("1. 啟動性能監控...")
        await monitor.start()
        print("✅ 性能監控已啟動")
        
        print("\n2. 模擬工作負載...")
        for i in range(5):
            # 記錄延遲
            monitor.record_latency("test_operation", i * 10)
            
            # 增加吞吐量計數
            monitor.increment_throughput("test_messages")
            
            await asyncio.sleep(1.0)
            
            # 獲取當前指標
            metrics = monitor.get_current_metrics()
            print(f"   第 {i+1} 秒: 平均延遲 {metrics.avg_latency_ms:.2f}ms, "
                  f"CPU {metrics.cpu_usage_percent:.1f}%, "
                  f"內存 {metrics.memory_usage_mb:.1f}MB")
        
        print("\n3. 獲取性能報告...")
        report = monitor.get_performance_report()
        print("✅ 性能監控完成")
        
    finally:
        await monitor.stop()
        print("✅ 性能監控已停止")


async def configuration_example():
    """配置管理範例"""
    from utils.config_manager import ConfigManager, ProductConfig
    
    print("\nDDE Actor System - 配置管理範例")
    print("=" * 50)
    
    # 創建配置管理器
    config_manager = ConfigManager("config/system_config.json")
    
    print("1. 加載配置...")
    if config_manager.load_config():
        print("✅ 配置加載成功")
        
        # 顯示配置摘要
        summary = config_manager.get_config_summary()
        print(f"   系統名稱: {summary['system']['system_name']}")
        print(f"   產品數量: {summary['products_count']}")
        print(f"   啟用產品: {summary['enabled_products']}")
    else:
        print("❌ 配置加載失敗")
        return
    
    print("\n2. 添加新產品配置...")
    new_product = ProductConfig(
        symbol="TEST_PRODUCT",
        data_types=["tick"],
        service="TEST_SERVICE",
        topic="TEST_TOPIC",
        items=["TEST_ITEM_1", "TEST_ITEM_2"],
        enabled=True
    )
    
    if config_manager.add_product_config(new_product):
        print("✅ 新產品配置添加成功")
    else:
        print("❌ 新產品配置添加失敗")
    
    print("\n3. 驗證配置...")
    errors = config_manager.validate_config()
    if not errors:
        print("✅ 配置驗證通過")
    else:
        print(f"❌ 配置驗證失敗: {errors}")


async def main():
    """主函數"""
    print("DDE Actor System - 使用範例集合")
    print("=" * 60)
    
    try:
        # 1. 簡單使用範例
        await simple_example()
        
        # 2. 自定義Actor範例
        await custom_actor_example()
        
        # 3. 性能監控範例
        await performance_monitoring_example()
        
        # 4. 配置管理範例
        await configuration_example()
        
        print("\n" + "=" * 60)
        print("✅ 所有範例執行完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 範例執行失敗: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
