#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试时间判断逻辑
"""

import sys
import os
from datetime import datetime, time

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.config_manager import MultiProductConfigManager
from core.multi_auto_manager import SymbolAutoManager
from utils.logger import setup_logging

def test_time_logic():
    print("🕐 调试时间判断逻辑")
    
    # 设置日志
    logger = setup_logging(None, 'INFO', 'INFO', 'DEBUG')
    
    # 创建配置管理器
    config = MultiProductConfigManager('multi_config.ini')
    config.load_config()
    
    # 当前时间
    current_time = datetime.now().time()
    print(f"当前时间: {current_time}")
    
    # 测试每个商品的时间判断
    for symbol in config.symbols:
        print(f"\n📊 商品: {symbol}")
        
        auto_config = config.get_symbol_auto_connect_config(symbol)
        if not auto_config:
            print("  ❌ 无自动化配置")
            continue
            
        # 创建商品自动化管理器
        manager = SymbolAutoManager(symbol, auto_config, logger)
        
        print(f"  启用自动连接: {manager.enable_auto_connect}")
        print(f"  连接模式: {manager.auto_connect_mode}")
        print(f"  时间表字符串: {manager.schedule_connect_times}")
        print(f"  解析的时间段: {manager.schedule_times}")
        
        # 测试时间判断
        should_connect = manager.should_connect_at_time(current_time)
        print(f"  当前是否应该连接: {should_connect}")
        
        # 详细检查每个时间段
        for i, (start_time, end_time) in enumerate(manager.schedule_times):
            print(f"    时间段{i+1}: {start_time} - {end_time}")
            
            if start_time <= end_time:
                # 同一天的时间段
                in_range = start_time <= current_time <= end_time
                print(f"      同一天时间段，当前时间在范围内: {in_range}")
            else:
                # 跨天的时间段
                in_range = current_time >= start_time or current_time <= end_time
                print(f"      跨天时间段，当前时间在范围内: {in_range}")

if __name__ == "__main__":
    test_time_logic()
