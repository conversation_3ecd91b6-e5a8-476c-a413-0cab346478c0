#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復版本的主程序

解決事件循環退出問題
"""

import asyncio
import signal
import sys
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from main import DDEActorSystem


class FixedDDEActorSystem:
    """修復版本的 DDE Actor System"""
    
    def __init__(self, config_file):
        self.config_file = config_file
        self.system = None
        self.running = False
        
    async def start(self):
        """啟動系統"""
        try:
            print("🚀 啟動修復版本的 DDE Actor System")
            print("=" * 60)
            
            # 創建系統實例
            self.system = DDEActorSystem(self.config_file)
            
            # 初始化
            print("1. 初始化系統...")
            success = await self.system.initialize()
            if not success:
                print("❌ 系統初始化失敗")
                return False
            
            print("✅ 系統初始化成功")
            
            # 啟動
            print("2. 啟動系統...")
            success = await self.system.start()
            if not success:
                print("❌ 系統啟動失敗")
                return False
            
            print("✅ 系統啟動成功")
            
            # 設置運行標誌
            self.running = True
            
            print("\n" + "=" * 60)
            print("🎉 系統運行中...")
            print("📁 數據文件將保存到 outputs/ 目錄")
            print("📊 按 Ctrl+C 停止系統")
            print("=" * 60)
            
            # 主運行循環
            await self.run_loop()
            
            return True
            
        except Exception as e:
            print(f"❌ 啟動失敗: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    async def run_loop(self):
        """主運行循環"""
        try:
            # 設置信號處理
            def signal_handler(signum, frame):
                print(f"\n📡 接收到停止信號 ({signum})")
                self.running = False
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            # 運行循環
            loop_count = 0
            while self.running:
                try:
                    # 每10秒顯示一次狀態
                    if loop_count % 10 == 0:
                        await self.show_status()
                    
                    await asyncio.sleep(1)
                    loop_count += 1
                    
                except KeyboardInterrupt:
                    print(f"\n⏹️  用戶中斷")
                    break
                except Exception as e:
                    print(f"⚠️  運行循環錯誤: {str(e)}")
                    await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ 運行循環失敗: {str(e)}")
        finally:
            await self.stop()
    
    async def show_status(self):
        """顯示狀態"""
        try:
            # 檢查輸出文件
            output_dir = Path("outputs")
            if output_dir.exists():
                files = list(output_dir.glob("dde_data.*"))
                if files:
                    for file_path in files:
                        size = file_path.stat().st_size
                        print(f"📄 {file_path.name}: {size} bytes")
                else:
                    print("⏳ 等待數據文件生成...")
            else:
                print("📁 創建 outputs 目錄...")
                output_dir.mkdir(exist_ok=True)
                
        except Exception as e:
            print(f"⚠️  狀態檢查錯誤: {str(e)}")
    
    async def stop(self):
        """停止系統"""
        try:
            print(f"\n🛑 正在停止系統...")
            self.running = False
            
            if self.system:
                await self.system.stop()
                print("✅ 系統停止成功")
            
        except Exception as e:
            print(f"❌ 停止失敗: {str(e)}")


async def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='修復版本的 DDE Actor System')
    parser.add_argument('--config', default='config/system_config.json', 
                       help='配置文件路徑')
    
    args = parser.parse_args()
    
    # 創建並啟動系統
    system = FixedDDEActorSystem(args.config)
    
    try:
        await system.start()
    except KeyboardInterrupt:
        print(f"\n⏹️  程序被中斷")
    except Exception as e:
        print(f"❌ 程序執行失敗: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 設置事件循環策略
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 運行主程序
    asyncio.run(main())
