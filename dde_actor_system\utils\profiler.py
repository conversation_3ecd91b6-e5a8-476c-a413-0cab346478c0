#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能分析器

提供系統性能分析功能，包括：
- 函數執行時間分析
- 內存使用分析
- CPU使用率分析
- 系統資源監控
"""

import asyncio
import cProfile
import functools
import gc
import io
import logging
import pstats
import psutil
import threading
import time
import tracemalloc
from contextlib import contextmanager
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque


@dataclass
class ProfileResult:
    """性能分析結果"""
    function_name: str
    call_count: int
    total_time: float
    avg_time: float
    max_time: float
    min_time: float
    memory_usage: float = 0.0
    cpu_usage: float = 0.0


@dataclass
class SystemSnapshot:
    """系統快照"""
    timestamp: float
    cpu_percent: float
    memory_mb: float
    memory_percent: float
    disk_io_read: int
    disk_io_write: int
    network_sent: int
    network_recv: int
    thread_count: int
    process_count: int


class FunctionProfiler:
    """函數性能分析器"""
    
    def __init__(self):
        """初始化函數分析器"""
        self.profiles: Dict[str, List[float]] = defaultdict(list)
        self.memory_profiles: Dict[str, List[float]] = defaultdict(list)
        self.lock = threading.Lock()
        self.logger = logging.getLogger("FunctionProfiler")
    
    @contextmanager
    def profile(self, function_name: str):
        """性能分析上下文管理器
        
        Args:
            function_name: 函數名稱
        """
        # 記錄開始時間和內存
        start_time = time.perf_counter()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            yield
        finally:
            # 記錄結束時間和內存
            end_time = time.perf_counter()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            elapsed_time = (end_time - start_time) * 1000  # 轉換為毫秒
            memory_delta = end_memory - start_memory
            
            with self.lock:
                self.profiles[function_name].append(elapsed_time)
                self.memory_profiles[function_name].append(memory_delta)
    
    def profile_decorator(self, function_name: Optional[str] = None):
        """性能分析裝飾器
        
        Args:
            function_name: 函數名稱，None表示使用函數的實際名稱
        """
        def decorator(func):
            name = function_name or func.__name__
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                with self.profile(name):
                    return func(*args, **kwargs)
            return wrapper
        return decorator
    
    def async_profile_decorator(self, function_name: Optional[str] = None):
        """異步性能分析裝飾器
        
        Args:
            function_name: 函數名稱，None表示使用函數的實際名稱
        """
        def decorator(func):
            name = function_name or func.__name__
            
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                with self.profile(name):
                    return await func(*args, **kwargs)
            return wrapper
        return decorator
    
    def get_results(self) -> List[ProfileResult]:
        """獲取分析結果
        
        Returns:
            List[ProfileResult]: 分析結果列表
        """
        results = []
        
        with self.lock:
            for func_name, times in self.profiles.items():
                if times:
                    memory_usage = 0.0
                    if func_name in self.memory_profiles:
                        memory_data = self.memory_profiles[func_name]
                        memory_usage = sum(memory_data) / len(memory_data) if memory_data else 0.0
                    
                    result = ProfileResult(
                        function_name=func_name,
                        call_count=len(times),
                        total_time=sum(times),
                        avg_time=sum(times) / len(times),
                        max_time=max(times),
                        min_time=min(times),
                        memory_usage=memory_usage
                    )
                    results.append(result)
        
        # 按總時間排序
        results.sort(key=lambda x: x.total_time, reverse=True)
        return results
    
    def clear(self):
        """清空分析數據"""
        with self.lock:
            self.profiles.clear()
            self.memory_profiles.clear()
    
    def generate_report(self) -> str:
        """生成分析報告
        
        Returns:
            str: 分析報告
        """
        results = self.get_results()
        
        report = []
        report.append("函數性能分析報告")
        report.append("=" * 60)
        report.append(f"{'函數名稱':<30} {'調用次數':<10} {'總時間(ms)':<12} {'平均時間(ms)':<12} {'最大時間(ms)':<12}")
        report.append("-" * 60)
        
        for result in results:
            report.append(f"{result.function_name:<30} {result.call_count:<10} "
                         f"{result.total_time:<12.2f} {result.avg_time:<12.2f} {result.max_time:<12.2f}")
        
        return "\n".join(report)


class SystemProfiler:
    """系統性能分析器"""
    
    def __init__(self, sample_interval: float = 1.0, max_samples: int = 3600):
        """初始化系統分析器
        
        Args:
            sample_interval: 採樣間隔（秒）
            max_samples: 最大採樣數量
        """
        self.sample_interval = sample_interval
        self.max_samples = max_samples
        self.snapshots: deque = deque(maxlen=max_samples)
        self.running = False
        self.task: Optional[asyncio.Task] = None
        self.logger = logging.getLogger("SystemProfiler")
        
        # 初始化計數器
        self.process = psutil.Process()
        self.initial_io = psutil.disk_io_counters()
        self.initial_net = psutil.net_io_counters()
    
    async def start(self):
        """開始系統監控"""
        if self.running:
            return
        
        self.running = True
        self.task = asyncio.create_task(self._monitoring_loop())
        self.logger.info("系統性能監控已啟動")
    
    async def stop(self):
        """停止系統監控"""
        if not self.running:
            return
        
        self.running = False
        
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("系統性能監控已停止")
    
    async def _monitoring_loop(self):
        """監控循環"""
        while self.running:
            try:
                snapshot = self._take_snapshot()
                self.snapshots.append(snapshot)
                
                await asyncio.sleep(self.sample_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"系統監控錯誤: {str(e)}")
                await asyncio.sleep(self.sample_interval)
    
    def _take_snapshot(self) -> SystemSnapshot:
        """拍攝系統快照
        
        Returns:
            SystemSnapshot: 系統快照
        """
        # CPU和內存
        cpu_percent = psutil.cpu_percent()
        memory_info = self.process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        memory_percent = self.process.memory_percent()
        
        # 磁盤I/O
        disk_io = psutil.disk_io_counters()
        disk_read = disk_io.read_bytes if disk_io else 0
        disk_write = disk_io.write_bytes if disk_io else 0
        
        # 網絡I/O
        net_io = psutil.net_io_counters()
        net_sent = net_io.bytes_sent if net_io else 0
        net_recv = net_io.bytes_recv if net_io else 0
        
        # 線程和進程數
        thread_count = self.process.num_threads()
        process_count = len(psutil.pids())
        
        return SystemSnapshot(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_mb=memory_mb,
            memory_percent=memory_percent,
            disk_io_read=disk_read,
            disk_io_write=disk_write,
            network_sent=net_sent,
            network_recv=net_recv,
            thread_count=thread_count,
            process_count=process_count
        )
    
    def get_current_snapshot(self) -> SystemSnapshot:
        """獲取當前快照
        
        Returns:
            SystemSnapshot: 當前系統快照
        """
        return self._take_snapshot()
    
    def get_snapshots(self, duration_seconds: Optional[int] = None) -> List[SystemSnapshot]:
        """獲取快照列表
        
        Args:
            duration_seconds: 時間範圍（秒），None表示所有快照
            
        Returns:
            List[SystemSnapshot]: 快照列表
        """
        if duration_seconds is None:
            return list(self.snapshots)
        
        cutoff_time = time.time() - duration_seconds
        return [s for s in self.snapshots if s.timestamp >= cutoff_time]
    
    def get_average_metrics(self, duration_seconds: Optional[int] = None) -> Dict[str, float]:
        """獲取平均指標
        
        Args:
            duration_seconds: 時間範圍（秒）
            
        Returns:
            Dict[str, float]: 平均指標字典
        """
        snapshots = self.get_snapshots(duration_seconds)
        
        if not snapshots:
            return {}
        
        return {
            'avg_cpu_percent': sum(s.cpu_percent for s in snapshots) / len(snapshots),
            'avg_memory_mb': sum(s.memory_mb for s in snapshots) / len(snapshots),
            'avg_memory_percent': sum(s.memory_percent for s in snapshots) / len(snapshots),
            'avg_thread_count': sum(s.thread_count for s in snapshots) / len(snapshots),
            'max_memory_mb': max(s.memory_mb for s in snapshots),
            'max_cpu_percent': max(s.cpu_percent for s in snapshots)
        }


class MemoryProfiler:
    """內存分析器"""
    
    def __init__(self):
        """初始化內存分析器"""
        self.enabled = False
        self.snapshots: List[Any] = []
        self.logger = logging.getLogger("MemoryProfiler")
    
    def start(self):
        """開始內存追蹤"""
        if not self.enabled:
            tracemalloc.start()
            self.enabled = True
            self.logger.info("內存追蹤已啟動")
    
    def stop(self):
        """停止內存追蹤"""
        if self.enabled:
            tracemalloc.stop()
            self.enabled = False
            self.logger.info("內存追蹤已停止")
    
    def take_snapshot(self) -> Optional[Any]:
        """拍攝內存快照
        
        Returns:
            Any: 內存快照，如果未啟用則返回None
        """
        if not self.enabled:
            return None
        
        snapshot = tracemalloc.take_snapshot()
        self.snapshots.append(snapshot)
        return snapshot
    
    def get_top_stats(self, limit: int = 10) -> List[str]:
        """獲取內存使用統計
        
        Args:
            limit: 顯示條目數量
            
        Returns:
            List[str]: 統計信息列表
        """
        if not self.snapshots:
            return []
        
        snapshot = self.snapshots[-1]
        top_stats = snapshot.statistics('lineno')
        
        stats = []
        for index, stat in enumerate(top_stats[:limit], 1):
            stats.append(f"{index}. {stat}")
        
        return stats
    
    def compare_snapshots(self, snapshot1_index: int = -2, 
                         snapshot2_index: int = -1) -> List[str]:
        """比較兩個快照
        
        Args:
            snapshot1_index: 第一個快照索引
            snapshot2_index: 第二個快照索引
            
        Returns:
            List[str]: 比較結果
        """
        if len(self.snapshots) < 2:
            return ["需要至少兩個快照才能進行比較"]
        
        snapshot1 = self.snapshots[snapshot1_index]
        snapshot2 = self.snapshots[snapshot2_index]
        
        top_stats = snapshot2.compare_to(snapshot1, 'lineno')
        
        stats = []
        for stat in top_stats[:10]:
            stats.append(str(stat))
        
        return stats


class PerformanceProfiler:
    """綜合性能分析器"""
    
    def __init__(self):
        """初始化綜合分析器"""
        self.function_profiler = FunctionProfiler()
        self.system_profiler = SystemProfiler()
        self.memory_profiler = MemoryProfiler()
        self.cprofile_enabled = False
        self.cprofile_data = None
        self.logger = logging.getLogger("PerformanceProfiler")
    
    async def start_all(self):
        """啟動所有分析器"""
        self.memory_profiler.start()
        await self.system_profiler.start()
        self.logger.info("所有性能分析器已啟動")
    
    async def stop_all(self):
        """停止所有分析器"""
        self.memory_profiler.stop()
        await self.system_profiler.stop()
        self.logger.info("所有性能分析器已停止")
    
    def enable_cprofile(self):
        """啟用cProfile"""
        if not self.cprofile_enabled:
            self.cprofile_data = cProfile.Profile()
            self.cprofile_data.enable()
            self.cprofile_enabled = True
            self.logger.info("cProfile已啟用")
    
    def disable_cprofile(self):
        """禁用cProfile"""
        if self.cprofile_enabled:
            self.cprofile_data.disable()
            self.cprofile_enabled = False
            self.logger.info("cProfile已禁用")
    
    def get_cprofile_stats(self, sort_by: str = 'cumulative') -> str:
        """獲取cProfile統計
        
        Args:
            sort_by: 排序方式
            
        Returns:
            str: 統計報告
        """
        if not self.cprofile_data:
            return "cProfile未啟用"
        
        s = io.StringIO()
        ps = pstats.Stats(self.cprofile_data, stream=s)
        ps.sort_stats(sort_by)
        ps.print_stats(20)  # 顯示前20個函數
        
        return s.getvalue()
    
    def generate_comprehensive_report(self) -> str:
        """生成綜合報告
        
        Returns:
            str: 綜合性能報告
        """
        report = []
        report.append("綜合性能分析報告")
        report.append("=" * 80)
        report.append(f"生成時間: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 函數性能報告
        report.append("函數性能分析")
        report.append("-" * 40)
        report.append(self.function_profiler.generate_report())
        report.append("")
        
        # 系統性能報告
        report.append("系統性能分析")
        report.append("-" * 40)
        avg_metrics = self.system_profiler.get_average_metrics(300)  # 最近5分鐘
        for key, value in avg_metrics.items():
            report.append(f"{key}: {value:.2f}")
        report.append("")
        
        # 內存分析報告
        report.append("內存使用分析")
        report.append("-" * 40)
        memory_stats = self.memory_profiler.get_top_stats(5)
        for stat in memory_stats:
            report.append(stat)
        report.append("")
        
        # cProfile報告
        if self.cprofile_enabled:
            report.append("cProfile分析")
            report.append("-" * 40)
            report.append(self.get_cprofile_stats())
        
        return "\n".join(report)
    
    @contextmanager
    def profile_context(self, name: str):
        """性能分析上下文
        
        Args:
            name: 分析名稱
        """
        with self.function_profiler.profile(name):
            yield
    
    def profile_function(self, name: Optional[str] = None):
        """函數分析裝飾器"""
        return self.function_profiler.profile_decorator(name)
    
    def profile_async_function(self, name: Optional[str] = None):
        """異步函數分析裝飾器"""
        return self.function_profiler.async_profile_decorator(name)
