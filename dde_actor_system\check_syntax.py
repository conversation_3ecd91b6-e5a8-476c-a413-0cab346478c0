#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
語法檢查工具
"""

import ast
import sys
from pathlib import Path


def check_syntax(file_path):
    """檢查文件語法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        ast.parse(source)
        return True, None
    except SyntaxError as e:
        return False, f"語法錯誤: {e.msg} (行 {e.lineno})"
    except Exception as e:
        return False, f"其他錯誤: {str(e)}"


def main():
    """主函數"""
    print("🔍 語法檢查")
    print("=" * 40)
    
    # 檢查關鍵文件
    files_to_check = [
        "main.py",
        "actors/dde_receiver.py",
        "actors/data_processor.py",
        "actors/file_writer.py",
        "actors/gui_updater.py",
        "test_fixed_system.py"
    ]
    
    all_ok = True
    
    for file_path in files_to_check:
        path = Path(file_path)
        if path.exists():
            ok, error = check_syntax(path)
            if ok:
                print(f"✅ {file_path}")
            else:
                print(f"❌ {file_path}: {error}")
                all_ok = False
        else:
            print(f"⚪ {file_path}: 文件不存在")
    
    if all_ok:
        print(f"\n✅ 所有文件語法正確")
    else:
        print(f"\n❌ 發現語法錯誤")
    
    return all_ok


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
