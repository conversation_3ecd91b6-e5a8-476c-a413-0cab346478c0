[DDE]
service = XQTISC
topic = Quote
disconnect_on_exit = false

[Items]
item1_name = 交易時間
item1_code = FITXN07.TF-Time
item2_name = 交易日期
item2_code = FITXN07.TF-TradingDate
item3_name = 開盤價
item3_code = FITXN07.TF-Open
item4_name = 最高價
item4_code = FITXN07.TF-High
item5_name = 最低價
item5_code = FITXN07.TF-Low
item6_name = 成交價
item6_code = FITXN07.TF-Price
item7_name = 總量
item7_code = FITXN07.TF-TotalVolume
item8_name = 單量
item8_code = FITXN07.TF-Volume

[Table]
enable_time_newline = true
time_newline_interval = 0.800
enable_value_change_check = true
value_change_check_mode = single
value_change_check_items = 總量

[AutoConnect]
enable_auto_connect = True
auto_connect_mode = schedule
auto_connect_delay = 5.0
schedule_connect_times = 14:50:00-05:05:05;08:25:00-13:45:15
prevent_weekend_startup = True
schedule_end_action = unadvise_only

[FileOutput]
enable_data_file = false
enable_complete_data_file = true
enable_log_file = true

[AutoShutdown]
enable_auto_shutdown = False
shutdown_time = 05:55:05
shutdown_buffer_seconds = 30
shutdown_warning_seconds = 10
force_shutdown = True
save_data_before_shutdown = True
disconnect_before_shutdown = True
cleanup_temp_files = True

[Notifications]
enable_system_notifications = True
enable_sound_notifications = False
notify_auto_connect = True
notify_auto_disconnect = True
notify_auto_shutdown = True

[OutputPath]
log_file = ./outputs/TEMP/data/mon/XQ/FITXN07/_m//logs/FITXN07.TF_tick.log
data_file = ./outputs/TEMP/data/mon/XQ/FITXN07/_m//data_tick.csv
complete_data_file = ./outputs/TEMP/data/mon/XQ/FITXN07/_m//complete_data_tick.csv

