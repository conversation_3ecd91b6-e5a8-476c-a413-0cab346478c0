# DDE 監控程式程式碼重構說明

## 📅 重構日期
2025-06-17

## 🎯 重構目標

原始的 `dde_monitor.py` 檔案已經超過 2300 行，程式碼過於龐大，需要進行功能性分拆以提高：
- **可維護性**: 模組化設計便於維護和除錯
- **可讀性**: 每個模組職責單一，邏輯清晰
- **可擴展性**: 新功能可以獨立開發和測試
- **可重用性**: 模組可以在其他專案中重用

## 📁 新的目錄結構

```
dde_monitor/
├── core/                    # 核心功能模組
│   ├── __init__.py
│   ├── data_handler.py      # 資料處理和檔案操作
│   └── auto_manager.py      # 自動化管理器
├── gui/                     # GUI 介面模組
│   ├── __init__.py
│   ├── main_window.py       # 主視窗
│   └── settings_dialog.py   # 設定對話框
├── utils/                   # 工具模組
│   ├── __init__.py
│   ├── config_manager.py    # 設定檔管理
│   └── logger.py           # 日誌管理
├── dde_monitor_new.py      # 新的主程式入口
├── dde_monitor.py          # 原始程式 (保留)
└── config.ini              # 設定檔
```

## 🔧 模組分拆詳情

### 1. 核心功能模組 (`core/`)

#### `data_handler.py` - 資料處理模組
**功能**:
- `ItemData`: 項目資料容器
- `DataRow`: 資料行容器  
- `DataFileHandler`: 檔案處理器
- `DataProcessor`: 資料處理器

**職責**:
- 管理監控項目的資料結構
- 處理 CSV 檔案的讀寫操作
- 在獨立線程中處理 DDE 資料更新
- 處理檔案路徑變數和目錄創建

#### `auto_manager.py` - 自動化管理器
**功能**:
- `AutoConnectManager`: 自動連線管理器

**職責**:
- 自動連線功能 (立即、延遲、時間表)
- 自動結束功能
- 通知系統
- 定時器管理

### 2. GUI 介面模組 (`gui/`)

#### `main_window.py` - 主視窗
**功能**:
- `DDEMainWindow`: 主視窗類別

**職責**:
- DDE 連線控制介面
- 項目監控表格
- 資料顯示區域
- 自動化狀態顯示
- 快速開關控制

#### `settings_dialog.py` - 設定對話框
**功能**:
- `AutoSettingsDialog`: 非模態設定對話框

**職責**:
- 自動連線設定介面
- 自動結束設定介面
- 通知設定介面
- 設定的載入、保存和驗證

### 3. 工具模組 (`utils/`)

#### `config_manager.py` - 設定檔管理器
**功能**:
- `ConfigManager`: 設定檔管理器

**職責**:
- 載入和保存設定檔
- 設定值的獲取和設定
- 預設設定檔創建
- 設定檔驗證和備份

#### `logger.py` - 日誌管理器
**功能**:
- `LoggerManager`: 日誌管理器
- 便捷函數: `setup_logging()`, `get_logger()`

**職責**:
- 日誌系統初始化
- 檔案和控制台處理器管理
- 日誌級別控制
- 日誌路徑變數處理

### 4. 主程式 (`dde_monitor_new.py`)

**功能**:
- `DDEMonitorApp`: 主應用程式類別

**職責**:
- 應用程式初始化和啟動
- 模組間的信號連接
- 資源管理和清理
- 錯誤處理

## 📊 重構統計

### 原始檔案
- **dde_monitor.py**: 2372 行

### 重構後檔案
- **core/data_handler.py**: ~200 行
- **core/auto_manager.py**: ~400 行  
- **gui/main_window.py**: ~500 行
- **gui/settings_dialog.py**: ~370 行
- **utils/config_manager.py**: ~200 行
- **utils/logger.py**: ~150 行
- **dde_monitor_new.py**: ~120 行

**總計**: ~1940 行 (減少 18%)

### 模組化優勢
- **單一職責**: 每個模組只負責特定功能
- **低耦合**: 模組間依賴關係清晰
- **高內聚**: 相關功能集中在同一模組
- **易測試**: 每個模組可以獨立測試

## 🔄 遷移指南

### 1. 使用新版本
```bash
# 使用重構後的版本
python dde_monitor_new.py
```

### 2. 導入模組
```python
# 導入核心模組
from core.data_handler import ItemData, DataFileHandler
from core.auto_manager import AutoConnectManager

# 導入 GUI 模組
from gui.main_window import DDEMainWindow
from gui.settings_dialog import AutoSettingsDialog

# 導入工具模組
from utils.config_manager import ConfigManager
from utils.logger import setup_logging
```

### 3. 設定檔相容性
- 新版本完全相容現有的 `config.ini`
- 無需修改任何設定

### 4. 功能對等性
- 所有原有功能都已保留
- GUI 介面保持一致
- 自動化功能完全相同

## ✅ 測試建議

### 1. 功能測試
```bash
# 測試新版本的基本功能
python dde_monitor_new.py

# 對比原版本功能
python dde_monitor.py
```

### 2. 模組測試
```python
# 測試設定管理器
from utils.config_manager import ConfigManager
config_manager = ConfigManager()
config = config_manager.load_config()

# 測試日誌管理器
from utils.logger import setup_logging
logger = setup_logging()
logger.info("測試日誌")
```

### 3. 整合測試
- 測試 DDE 連線功能
- 測試自動化功能
- 測試設定對話框
- 測試檔案輸出功能

## 🚀 未來擴展

### 1. 新功能開發
- 可以在對應模組中添加新功能
- 例如：在 `auto_manager.py` 中添加新的自動化功能

### 2. GUI 改進
- 可以在 `gui/` 目錄下添加新的視窗
- 例如：添加資料分析視窗、圖表顯示等

### 3. 資料處理增強
- 可以在 `core/data_handler.py` 中添加新的資料格式支援
- 例如：JSON 輸出、資料庫存儲等

### 4. 工具擴展
- 可以在 `utils/` 目錄下添加新的工具模組
- 例如：資料分析工具、報告生成器等

## 📋 注意事項

### 1. 向後相容性
- 原始的 `dde_monitor.py` 仍然保留
- 可以隨時切換回原版本
- 設定檔完全相容

### 2. 依賴關係
- 新版本需要相同的依賴套件
- `dydde` 模組仍然需要
- PySide6 版本要求不變

### 3. 效能影響
- 模組化設計對效能影響極小
- 記憶體使用略有增加 (模組載入)
- 執行效率基本相同

### 4. 維護建議
- 建議使用新版本進行後續開發
- 原版本僅作為備份保留
- 新功能應該在模組化版本中實現

---
*重構版本*: v6.1 (模組化)  
*重構日期*: 2025-06-17  
*重構類型*: 功能性分拆  
*影響範圍*: 整體程式架構
