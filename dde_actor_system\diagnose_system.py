#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系統診斷工具

診斷 DDE Actor System 的運行狀態
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from dydde.dydde import DDEClient


def diagnose_config():
    """診斷配置"""
    print("🔍 診斷配置文件...")
    
    config_file = Path("config/system_config.json")
    if not config_file.exists():
        print("❌ 配置文件不存在")
        return None
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件加載成功")
        
        # 檢查產品配置
        products = config.get('products', [])
        enabled_products = [p for p in products if p.get('enabled', False)]
        
        print(f"   總產品數: {len(products)}")
        print(f"   啟用產品數: {len(enabled_products)}")
        
        for product in enabled_products:
            symbol = product.get('symbol')
            service = product.get('service')
            topic = product.get('topic')
            items = product.get('items', [])
            
            print(f"   產品: {symbol}")
            print(f"     服務: {service}.{topic}")
            print(f"     項目數: {len(items)}")
            
            # 顯示前幾個項目
            for item in items[:3]:
                print(f"       - {item}")
            if len(items) > 3:
                print(f"       ... 還有 {len(items)-3} 個項目")
        
        return config
        
    except Exception as e:
        print(f"❌ 配置文件解析失敗: {str(e)}")
        return None


def diagnose_dde_connections(config):
    """診斷 DDE 連接"""
    print(f"\n🔗 診斷 DDE 連接...")
    
    if not config:
        print("❌ 沒有配置信息")
        return False
    
    products = config.get('products', [])
    enabled_products = [p for p in products if p.get('enabled', False)]
    
    connection_results = []
    
    for product in enabled_products:
        symbol = product.get('symbol')
        service = product.get('service')
        topic = product.get('topic')
        items = product.get('items', [])
        
        print(f"\n   測試產品: {symbol}")
        print(f"   服務: {service}.{topic}")
        
        try:
            # 創建 DDE 客戶端
            client = DDEClient(service, topic)
            
            # 嘗試連接
            client.connect()
            print(f"   ✅ 連接成功")
            
            # 測試幾個項目
            successful_items = 0
            test_items = items[:3]  # 只測試前3個項目
            
            for item in test_items:
                try:
                    result = client.request(item)
                    if result and result.strip():
                        print(f"   📊 {item}: {result}")
                        successful_items += 1
                    else:
                        print(f"   ⚪ {item}: 無數據")
                    time.sleep(0.1)
                except Exception as e:
                    print(f"   ❌ {item}: {str(e)}")
            
            print(f"   成功項目: {successful_items}/{len(test_items)}")
            
            # 測試熱鏈接
            if test_items:
                test_item = test_items[0]
                print(f"   測試熱鏈接: {test_item}")
                try:
                    # 創建一個簡單的回調函數
                    def callback(item, value):
                        print(f"   🔥 回調: {item} = {value}")
                    
                    client.advise(test_item, callback)
                    print(f"   ✅ 熱鏈接建立成功")
                    
                    # 等待一下看是否有數據
                    print(f"   等待數據 (3秒)...")
                    time.sleep(3)
                    
                    client.unadvise(test_item)
                    print(f"   ✅ 熱鏈接已取消")
                    
                except Exception as e:
                    print(f"   ❌ 熱鏈接失敗: {str(e)}")
            
            client.disconnect()
            connection_results.append(True)
            
        except Exception as e:
            print(f"   ❌ 連接失敗: {str(e)}")
            connection_results.append(False)
    
    successful_connections = sum(connection_results)
    total_connections = len(connection_results)
    
    print(f"\n   連接結果: {successful_connections}/{total_connections} 成功")
    
    return successful_connections > 0


def diagnose_file_system():
    """診斷文件系統"""
    print(f"\n📁 診斷文件系統...")
    
    # 檢查目錄
    directories = ["outputs", "logs", "config"]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"   ✅ {dir_name}/ 目錄存在")
            
            # 列出文件
            files = list(dir_path.glob("*"))
            if files:
                print(f"      文件數: {len(files)}")
                for file_path in files[:5]:  # 只顯示前5個
                    size = file_path.stat().st_size if file_path.is_file() else 0
                    print(f"        {file_path.name}: {size} bytes")
                if len(files) > 5:
                    print(f"        ... 還有 {len(files)-5} 個文件")
            else:
                print(f"      目錄為空")
        else:
            print(f"   ❌ {dir_name}/ 目錄不存在")
            if dir_name == "outputs":
                print(f"      創建 outputs 目錄...")
                dir_path.mkdir(exist_ok=True)
                print(f"      ✅ outputs 目錄已創建")


def diagnose_processes():
    """診斷進程"""
    print(f"\n🔄 診斷進程...")
    
    try:
        import psutil
        
        dde_processes = []
        python_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    python_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline,
                        'cpu': proc.info['cpu_percent'],
                        'memory_mb': proc.info['memory_info'].rss / 1024 / 1024 if proc.info['memory_info'] else 0
                    })
                    
                    if 'main.py' in cmdline or 'dde_actor_system' in cmdline:
                        dde_processes.append(proc.info['pid'])
        
        print(f"   Python 進程數: {len(python_processes)}")
        print(f"   DDE 相關進程數: {len(dde_processes)}")
        
        if dde_processes:
            print(f"   ✅ 找到 DDE Actor System 進程:")
            for proc in python_processes:
                if proc['pid'] in dde_processes:
                    print(f"     PID {proc['pid']}: CPU {proc['cpu']:.1f}%, 內存 {proc['memory_mb']:.1f}MB")
                    cmdline = proc['cmdline']
                    if len(cmdline) > 80:
                        cmdline = cmdline[:77] + "..."
                    print(f"       {cmdline}")
        else:
            print(f"   ⚠️  沒有找到 DDE Actor System 進程")
            print(f"   建議啟動: python main.py --config config/system_config.json")
        
    except ImportError:
        print(f"   ❌ psutil 不可用，無法檢查進程")
    except Exception as e:
        print(f"   ❌ 進程檢查失敗: {str(e)}")


def main():
    """主函數"""
    print("🏥 DDE Actor System 系統診斷")
    print("=" * 60)
    print("此工具將全面診斷系統狀態")
    print("=" * 60)
    
    # 診斷配置
    config = diagnose_config()
    
    # 診斷 DDE 連接
    dde_ok = diagnose_dde_connections(config)
    
    # 診斷文件系統
    diagnose_file_system()
    
    # 診斷進程
    diagnose_processes()
    
    # 總結
    print(f"\n" + "=" * 60)
    print("🏥 診斷總結")
    print("=" * 60)
    
    if config:
        print("✅ 配置文件正常")
    else:
        print("❌ 配置文件有問題")
    
    if dde_ok:
        print("✅ DDE 連接正常")
    else:
        print("❌ DDE 連接有問題")
    
    print(f"\n💡 建議:")
    if config and dde_ok:
        print("1. DDE 數據源正常工作")
        print("2. 檢查 DDE Actor System 是否正在運行")
        print("3. 如果系統運行但沒有數據，可能是訂閱邏輯有問題")
        print("4. 運行 'python main.py --config config/system_config.json'")
    else:
        print("1. 檢查配置文件是否正確")
        print("2. 確保 DDE 服務正在運行")
        print("3. 檢查網絡連接")


if __name__ == "__main__":
    main()
