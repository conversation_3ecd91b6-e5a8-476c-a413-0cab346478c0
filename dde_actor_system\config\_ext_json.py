import json

list_symbols = [
"1101.TW", "1102.TW", "1103.TW", "1104.TW", "1108.TW", "1109.TW", "1110.TW", "1201.TW", "1203.TW", "1210.TW"
, "1213.TW", "1215.TW", "1216.TW", "1217.TW", "1218.TW", "1219.TW", "1220.TW", "1225.TW", "1227.TW", "1229.TW"
, "1231.TW", "1232.TW", "1233.TW", "1234.TW", "1235.TW", "1236.TW", "1256.TW", "1702.TW", "1737.TW", "3054.TW"
, "1301.TW", "1303.TW", "1304.TW", "1305.TW", "1307.TW", "1308.TW", "1309.TW", "1310.TW", "1312.TW", "1313.TW"
, "1314.TW", "1315.TW", "1321.TW", "1323.TW", "1324.TW", "1325.TW", "1326.TW", "1337.TW", "1340.TW", "1341.TW"
, "4306.TW", "1402.TW", "1409.TW", "1410.TW", "1413.TW", "1414.TW", "1417.TW", "1418.TW", "1419.TW", "1423.TW"
, "1434.TW", "1440.TW", "1441.TW", "1444.TW", "1445.TW", "1446.TW", "1447.TW", "1449.TW", "1451.TW", "1452.TW"
, "1454.TW", "1455.TW", "1457.TW", "1459.TW", "1460.TW", "1463.TW", "1464.TW", "1465.TW", "1466.TW", "1467.TW"
, "1468.TW", "1470.TW", "1473.TW", "1474.TW", "1475.TW", "1476.TW", "1477.TW", "4414.TW", "4426.TW", "4438.TW"
, "4439.TW", "4440.TW", "1503.TW", "1504.TW", "1506.TW", "1513.TW", "1514.TW", "1515.TW", "1517.TW", "1519.TW"
, "1526.TW", "1527.TW", "1528.TW", "1529.TW", "1530.TW", "1531.TW", "1532.TW", "1535.TW", "1537.TW", "1538.TW"
, "1539.TW", "1540.TW", "1541.TW", "1558.TW", "1560.TW", "1583.TW", "1589.TW", "1590.TW", "1597.TW", "2049.TW"
, "2371.TW", "3167.TW", "4526.TW", "4532.TW", "4540.TW", "4552.TW", "4555.TW", "4560.TW", "4562.TW", "4564.TW"
, "4566.TW", "4571.TW", "4572.TW", "4576.TW", "4583.TW", "5288.TW", "6606.TW", "8222.TW", "8374.TW", "8996.TW"
, "1603.TW", "1604.TW", "1605.TW", "1608.TW", "1609.TW", "1611.TW", "1612.TW", "1614.TW", "1615.TW", "1616.TW"
, "1617.TW", "1618.TW", "1626.TW", "4930.TW", "5283.TW", "1708.TW", "1709.TW", "1710.TW", "1711.TW", "1712.TW"
, "1713.TW", "1714.TW", "1717.TW", "1718.TW", "1721.TW", "1722.TW", "1723.TW", "1725.TW", "1726.TW", "1727.TW"
, "1730.TW", "1732.TW", "1735.TW", "1773.TW", "1776.TW", "4720.TW", "4722.TW", "4739.TW", "4755.TW", "4763.TW"
, "4764.TW", "4766.TW", "4770.TW", "1707.TW", "1720.TW", "1731.TW", "1733.TW", "1734.TW", "1752.TW", "1760.TW"
, "1762.TW", "1783.TW", "1786.TW", "1789.TW", "1795.TW", "3164.TW", "3705.TW", "3716.TW", "4104.TW", "4106.TW"
, "4108.TW", "4119.TW", "4133.TW", "4137.TW", "4142.TW", "4148.TW", "4155.TW", "4164.TW", "4190.TW", "4736.TW"
, "4737.TW", "4746.TW", "4771.TW", "6431.TW", "6446.TW", "6472.TW", "6491.TW", "6541.TW", "6550.TW", "6598.TW"
, "6657.TW", "6666.TW", "6782.TW", "6796.TW", "6838.TW", "6861.TW", "6885.TW", "6918.TW", "6919.TW", "6931.TW"
, "6936.TW", "1802.TW", "1806.TW", "1809.TW", "1810.TW", "1817.TW", "1903.TW", "1904.TW", "1905.TW", "1906.TW"
, "1907.TW", "1909.TW", "6790.TW", "2002.TW", "2006.TW", "2007.TW", "2008.TW", "2009.TW", "2010.TW", "2012.TW"
, "2013.TW", "2014.TW", "2015.TW", "2017.TW", "2020.TW", "2022.TW", "2023.TW", "2024.TW", "2025.TW", "2027.TW"
, "2028.TW", "2029.TW", "2030.TW", "2031.TW", "2032.TW", "2033.TW", "2034.TW", "2038.TW", "2069.TW", "2211.TW"
, "3004.TW", "5007.TW", "5538.TW", "9958.TW", "2101.TW", "2102.TW", "2103.TW", "2104.TW", "2105.TW", "2106.TW"
, "2107.TW", "2108.TW", "2109.TW", "2114.TW", "6582.TW", "1319.TW", "1338.TW", "1339.TW", "1512.TW", "1521.TW"
, "1522.TW", "1524.TW", "1525.TW", "1533.TW", "1536.TW", "1563.TW", "1568.TW", "1587.TW", "2115.TW", "2201.TW"
, "2204.TW", "2206.TW", "2207.TW", "2227.TW", "2228.TW", "2231.TW", "2233.TW", "2236.TW", "2239.TW", "2241.TW"
, "2243.TW", "2247.TW", "2248.TW", "2250.TW", "2497.TW", "3346.TW", "4551.TW", "4557.TW", "4569.TW", "4581.TW"
, "6288.TW", "6605.TW", "7732.TW", "7736.TW", "2302.TW", "2303.TW", "2329.TW", "2330.TW", "2337.TW", "2338.TW"
, "2340.TW", "2342.TW", "2344.TW", "2351.TW", "2363.TW", "2369.TW", "2379.TW", "2388.TW", "2401.TW", "2408.TW"
, "2434.TW", "2436.TW", "2441.TW", "2449.TW", "2451.TW", "2454.TW", "2458.TW", "2481.TW", "3006.TW", "3014.TW"
, "3016.TW", "3034.TW", "3035.TW", "3041.TW", "3094.TW", "3189.TW", "3257.TW", "3413.TW", "3443.TW", "3450.TW"
, "3530.TW", "3532.TW", "3545.TW", "3583.TW", "3588.TW", "3592.TW", "3661.TW", "3686.TW", "3711.TW", "4919.TW"
, "4952.TW", "4961.TW", "4967.TW", "4968.TW", "5222.TW", "5269.TW", "5285.TW", "5471.TW", "6202.TW", "6239.TW"
, "6243.TW", "6257.TW", "6271.TW", "6415.TW", "6451.TW", "6515.TW", "6525.TW", "6526.TW", "6531.TW", "6533.TW"
, "6552.TW", "6573.TW", "6695.TW", "6719.TW", "6756.TW", "6770.TW", "6789.TW", "6799.TW", "6909.TW", "6937.TW"
, "6962.TW", "7749.TW", "8016.TW", "8028.TW", "8081.TW", "8110.TW", "8131.TW", "8150.TW", "8261.TW", "8271.TW"
, "2301.TW", "2305.TW", "2324.TW", "2331.TW", "2352.TW", "2353.TW", "2356.TW", "2357.TW", "2362.TW", "2364.TW"
, "2365.TW", "2376.TW", "2377.TW", "2380.TW", "2382.TW", "2387.TW", "2395.TW", "2397.TW", "2399.TW", "2405.TW"
, "2417.TW", "2425.TW", "2465.TW", "2495.TW", "3002.TW", "3005.TW", "3013.TW", "3017.TW", "3022.TW", "3046.TW"
, "3057.TW", "3060.TW", "3231.TW", "3416.TW", "3494.TW", "3515.TW", "3652.TW", "3701.TW", "3706.TW", "3712.TW"
, "4916.TW", "4938.TW", "5215.TW", "5258.TW", "6117.TW", "6128.TW", "6166.TW", "6206.TW", "6230.TW", "6235.TW"
, "6277.TW", "6414.TW", "6579.TW", "6591.TW", "6669.TW", "6928.TW", "6933.TW", "8114.TW", "8163.TW", "8210.TW"
, "9912.TW", "2323.TW", "2349.TW", "2374.TW", "2393.TW", "2406.TW", "2409.TW", "2426.TW", "2429.TW", "2438.TW"
, "2466.TW", "2486.TW", "2489.TW", "2491.TW", "3008.TW", "3019.TW", "3024.TW", "3031.TW", "3038.TW", "3049.TW"
, "3050.TW", "3051.TW", "3059.TW", "3149.TW", "3168.TW", "3356.TW", "3406.TW", "3437.TW", "3454.TW", "3481.TW"
, "3504.TW", "3535.TW", "3543.TW", "3563.TW", "3576.TW", "3591.TW", "3622.TW", "3673.TW", "3714.TW", "4934.TW"
, "4935.TW", "4942.TW", "4949.TW", "4956.TW", "4960.TW", "4976.TW", "5234.TW", "5243.TW", "5244.TW", "5484.TW"
, "6116.TW", "6120.TW", "6164.TW", "6168.TW", "6176.TW", "6209.TW", "6225.TW", "6226.TW", "6278.TW", "6405.TW"
, "6443.TW", "6456.TW", "6477.TW", "6668.TW", "6706.TW", "6742.TW", "6916.TW", "8104.TW", "8105.TW", "8215.TW"
, "2314.TW", "2321.TW", "2332.TW", "2345.TW", "2412.TW", "2419.TW", "2424.TW", "2439.TW", "2444.TW", "2450.TW"
, "2455.TW", "2485.TW", "2498.TW", "3025.TW", "3027.TW", "3045.TW", "3047.TW", "3062.TW", "3138.TW", "3311.TW"
, "3380.TW", "3419.TW", "3447.TW", "3596.TW", "3669.TW", "3694.TW", "3704.TW", "4904.TW", "4906.TW", "4977.TW"
, "5388.TW", "6136.TW", "6142.TW", "6152.TW", "6216.TW", "6285.TW", "6416.TW", "6426.TW", "6442.TW", "6674.TW"
, "6792.TW", "6863.TW", "8011.TW", "8045.TW", "8101.TW", "1471.TW", "1582.TW", "2059.TW", "2308.TW", "2313.TW"
, "2316.TW", "2327.TW", "2328.TW", "2355.TW", "2367.TW", "2368.TW", "2375.TW", "2383.TW", "2385.TW", "2392.TW"
, "2402.TW", "2413.TW", "2415.TW", "2420.TW", "2421.TW", "2428.TW", "2431.TW", "2440.TW", "2457.TW", "2460.TW"
, "2462.TW", "2467.TW", "2472.TW", "2476.TW", "2478.TW", "2483.TW", "2484.TW", "2492.TW", "2493.TW", "3003.TW"
, "3011.TW", "3015.TW", "3021.TW", "3023.TW", "3026.TW", "3032.TW", "3037.TW", "3042.TW", "3044.TW", "3058.TW"
, "3090.TW", "3092.TW", "3229.TW", "3296.TW", "3308.TW", "3321.TW", "3338.TW", "3376.TW", "3432.TW", "3501.TW"
, "3533.TW", "3550.TW", "3593.TW", "3605.TW", "3607.TW", "3645.TW", "3653.TW", "3679.TW", "3715.TW", "4545.TW"
, "4912.TW", "4915.TW", "4927.TW", "4943.TW", "4958.TW", "4989.TW", "4999.TW", "5469.TW", "6108.TW", "6115.TW"
, "6133.TW", "6141.TW", "6153.TW", "6155.TW", "6191.TW", "6197.TW", "6205.TW", "6213.TW", "6224.TW", "6269.TW"
, "6282.TW", "6412.TW", "6449.TW", "6672.TW", "6715.TW", "6781.TW", "6805.TW", "6834.TW", "6835.TW", "6862.TW"
, "8039.TW", "8046.TW", "8103.TW", "8213.TW", "8249.TW", "2347.TW", "2414.TW", "2430.TW", "3010.TW", "3028.TW"
, "3033.TW", "3036.TW", "3048.TW", "3055.TW", "3209.TW", "3312.TW", "3528.TW", "3702.TW", "5434.TW", "6189.TW"
, "6281.TW", "6776.TW", "8070.TW", "8072.TW", "8112.TW", "2427.TW", "2453.TW", "2468.TW", "2471.TW", "2480.TW"
, "3029.TW", "4994.TW", "5203.TW", "6112.TW", "6183.TW", "6214.TW", "2312.TW", "2317.TW", "2354.TW", "2359.TW"
, "2360.TW", "2373.TW", "2390.TW", "2404.TW", "2423.TW", "2433.TW", "2459.TW", "2461.TW", "2464.TW", "2474.TW"
, "2477.TW", "2482.TW", "2488.TW", "3018.TW", "3030.TW", "3043.TW", "3305.TW", "3518.TW", "3617.TW", "3665.TW"
, "4588.TW", "5225.TW", "6139.TW", "6192.TW", "6196.TW", "6201.TW", "6215.TW", "6283.TW", "6409.TW", "6438.TW"
, "6558.TW", "6658.TW", "6691.TW", "6698.TW", "6743.TW", "6830.TW", "8021.TW", "8201.TW", "8499.TW", "1316.TW"
, "1436.TW", "1438.TW", "1439.TW", "1442.TW", "1453.TW", "1456.TW", "1472.TW", "1805.TW", "1808.TW", "2442.TW"
, "2501.TW", "2504.TW", "2505.TW", "2506.TW", "2509.TW", "2511.TW", "2515.TW", "2516.TW", "2520.TW", "2524.TW"
, "2527.TW", "2528.TW", "2530.TW", "2534.TW", "2535.TW", "2536.TW", "2537.TW", "2538.TW", "2539.TW", "2540.TW"
, "2542.TW", "2543.TW", "2545.TW", "2546.TW", "2547.TW", "2548.TW", "2597.TW", "2923.TW", "3052.TW", "3056.TW"
, "3266.TW", "3703.TW", "5515.TW", "5519.TW", "5521.TW", "5522.TW", "5525.TW", "5531.TW", "5533.TW", "5534.TW"
, "5546.TW", "6177.TW", "9906.TW", "9946.TW", "2208.TW", "2603.TW", "2605.TW", "2606.TW", "2607.TW", "2608.TW"
, "2609.TW", "2610.TW", "2611.TW", "2612.TW", "2613.TW", "2615.TW", "2617.TW", "2618.TW", "2630.TW", "2633.TW"
, "2634.TW", "2636.TW", "2637.TW", "2642.TW", "2645.TW", "2646.TW", "5607.TW", "5608.TW", "6753.TW", "6757.TW"
, "8367.TW", "2701.TW", "2702.TW", "2704.TW", "2705.TW", "2706.TW", "2707.TW", "2712.TW", "2722.TW", "2723.TW"
, "2727.TW", "2731.TW", "2739.TW", "2748.TW", "2753.TW", "5706.TW", "7705.TW", "8940.TW", "9943.TW", "2801.TW"
, "2809.TW", "2812.TW", "2816.TW", "2820.TW", "2832.TW", "2834.TW", "2836.TW", "2838.TW", "2845.TW", "2849.TW"
, "2850.TW", "2851.TW", "2852.TW", "2855.TW", "2867.TW", "2880.TW", "2881.TW", "2882.TW", "2883.TW", "2884.TW"
, "2885.TW", "2886.TW", "2887.TW", "2888.TW", "2889.TW", "2890.TW", "2891.TW", "2892.TW", "2897.TW", "5876.TW"
, "5880.TW", "6005.TW", "6024.TW", "2601.TW", "2901.TW", "2903.TW", "2905.TW", "2906.TW", "2908.TW", "2910.TW"
, "2911.TW", "2912.TW", "2913.TW", "2915.TW", "2929.TW", "2939.TW", "2945.TW", "4807.TW", "5906.TW", "5907.TW"
, "8429.TW", "8443.TW", "2616.TW", "6505.TW", "8926.TW", "9908.TW", "9918.TW", "9926.TW", "9931.TW", "9937.TW"
, "3708.TW", "5292.TW", "6581.TW", "6641.TW", "6806.TW", "6869.TW", "6873.TW", "6887.TW", "6923.TW", "6944.TW"
, "6994.TW", "8341.TW", "8422.TW", "8438.TW", "8473.TW", "8476.TW", "9930.TW", "9955.TW", "3130.TW", "6165.TW"
, "6689.TW", "6902.TW", "6906.TW", "7722.TW", "8454.TW", "1432.TW", "1598.TW", "1736.TW", "2762.TW", "4536.TW"
, "5306.TW", "6670.TW", "6768.TW", "6890.TW", "6965.TW", "8462.TW", "8467.TW", "8478.TW", "9802.TW", "9904.TW"
, "9910.TW", "9914.TW", "9921.TW", "2062.TW", "3557.TW", "6671.TW", "6754.TW", "6807.TW", "8464.TW", "8482.TW"
, "9911.TW", "9924.TW", "9934.TW", "9935.TW", "1342.TW", "1416.TW", "1435.TW", "1437.TW", "1443.TW", "1516.TW"
, "2348.TW", "2496.TW", "2514.TW", "2614.TW", "2904.TW", "3040.TW", "5284.TW", "5871.TW", "6184.TW", "6464.TW"
, "6504.TW", "6585.TW", "6592.TW", "6625.TW", "6655.TW", "6901.TW", "6914.TW", "6952.TW", "6957.TW", "6958.TW"
, "8033.TW", "8404.TW", "8411.TW", "8442.TW", "8463.TW", "8466.TW", "8481.TW", "8488.TW", "9902.TW", "9905.TW"
, "9907.TW", "9917.TW", "9919.TW", "9925.TW", "9927.TW", "9928.TW", "9929.TW", "9933.TW", "9938.TW", "9939.TW"
, "9940.TW", "9941.TW", "9942.TW", "9944.TW", "9945.TW", "1264.TW", "1294.TW", "1295.TW", "1796.TW", "4205.TW"
, "4207.TW", "6846.TW", "7743.TW", "4303.TW", "4304.TW", "4305.TW", "9950.TW", "4401.TW", "4402.TW", "4406.TW"
, "4413.TW", "4417.TW", "4420.TW", "4432.TW", "4433.TW", "4442.TW", "6506.TW", "1570.TW", "1580.TW", "1586.TW"
, "1591.TW", "1599.TW", "2066.TW", "2067.TW", "2070.TW", "2230.TW", "2235.TW", "3162.TW", "3226.TW", "3379.TW"
, "3426.TW", "3685.TW", "4502.TW", "4503.TW", "4506.TW", "4510.TW", "4513.TW", "4523.TW", "4527.TW", "4528.TW"
, "4533.TW", "4534.TW", "4535.TW", "4538.TW", "4543.TW", "4549.TW", "4550.TW", "4558.TW", "4561.TW", "4563.TW"
, "4568.TW", "4580.TW", "4584.TW", "6122.TW", "6425.TW", "6603.TW", "6609.TW", "6843.TW", "6982.TW", "7642.TW"
, "7709.TW", "8027.TW", "8083.TW", "8107.TW", "8255.TW", "9951.TW", "2061.TW", "1742.TW", "3430.TW", "4706.TW"
, "4707.TW", "4711.TW", "4714.TW", "4716.TW", "4721.TW", "4741.TW", "4754.TW", "4767.TW", "4768.TW", "4772.TW"
, "6509.TW", "1565.TW", "1777.TW", "1781.TW", "1784.TW", "1788.TW", "1799.TW", "1813.TW", "3118.TW", "3176.TW"
, "3205.TW", "3218.TW", "4102.TW", "4105.TW", "4107.TW", "4109.TW", "4111.TW", "4114.TW", "4116.TW", "4120.TW"
, "4121.TW", "4123.TW", "4126.TW", "4127.TW", "4128.TW", "4129.TW", "4130.TW", "4131.TW", "4138.TW", "4139.TW"
, "4147.TW", "4153.TW", "4157.TW", "4160.TW", "4161.TW", "4162.TW", "4163.TW", "4167.TW", "4168.TW", "4173.TW"
, "4174.TW", "4175.TW", "4183.TW", "4188.TW", "4192.TW", "4198.TW", "4726.TW", "4728.TW", "4735.TW", "4743.TW"
, "4744.TW", "4745.TW", "4747.TW", "4911.TW", "5312.TW", "6130.TW", "6242.TW", "6461.TW", "6469.TW", "6492.TW"
, "6496.TW", "6499.TW", "6523.TW", "6527.TW", "6535.TW", "6547.TW", "6569.TW", "6574.TW", "6576.TW", "6589.TW"
, "6612.TW", "6615.TW", "6617.TW", "6637.TW", "6649.TW", "6661.TW", "6662.TW", "6703.TW", "6712.TW", "6733.TW"
, "6747.TW", "6762.TW", "6767.TW", "6785.TW", "6841.TW", "6844.TW", "6872.TW", "6875.TW", "6929.TW", "7713.TW"
, "8279.TW", "8403.TW", "8409.TW", "8432.TW", "8436.TW", "2035.TW", "2063.TW", "2064.TW", "2065.TW", "2073.TW"
, "4950.TW", "5009.TW", "5011.TW", "5013.TW", "5014.TW", "5015.TW", "5016.TW", "6248.TW", "7718.TW", "8349.TW"
, "8415.TW", "8930.TW", "9962.TW", "3105.TW", "3122.TW", "3141.TW", "3169.TW", "3178.TW", "3227.TW", "3228.TW"
, "3259.TW", "3260.TW", "3264.TW", "3265.TW", "3268.TW", "3317.TW", "3372.TW", "3374.TW", "3438.TW", "3467.TW"
, "3527.TW", "3529.TW", "3555.TW", "3556.TW", "3567.TW", "3581.TW", "3675.TW", "3680.TW", "3707.TW", "4749.TW"
, "4923.TW", "4945.TW", "4951.TW", "4966.TW", "4971.TW", "4973.TW", "4991.TW", "5236.TW", "5272.TW", "5274.TW"
, "5299.TW", "5302.TW", "5344.TW", "5347.TW", "5351.TW", "5425.TW", "5443.TW", "5468.TW", "5483.TW", "5487.TW"
, "6103.TW", "6104.TW", "6129.TW", "6138.TW", "6147.TW", "6182.TW", "6187.TW", "6208.TW", "6223.TW", "6229.TW"
, "6233.TW", "6237.TW", "6261.TW", "6291.TW", "6411.TW", "6435.TW", "6462.TW", "6485.TW", "6488.TW", "6494.TW"
, "6510.TW", "6532.TW", "6548.TW", "6568.TW", "6640.TW", "6643.TW", "6651.TW", "6679.TW", "6683.TW", "6684.TW"
, "6693.TW", "6708.TW", "6716.TW", "6720.TW", "6732.TW", "6788.TW", "6823.TW", "6829.TW", "6895.TW", "6953.TW"
, "6996.TW", "7556.TW", "7704.TW", "7712.TW", "7734.TW", "8024.TW", "8040.TW", "8054.TW", "8086.TW", "8088.TW"
, "8091.TW", "8227.TW", "8277.TW", "8299.TW", "8383.TW", "1569.TW", "3071.TW", "3088.TW", "3211.TW", "3213.TW"
, "3272.TW", "3287.TW", "3323.TW", "3325.TW", "3349.TW", "3479.TW", "3483.TW", "3540.TW", "3577.TW", "3594.TW"
, "3611.TW", "3625.TW", "3693.TW", "3709.TW", "4924.TW", "4931.TW", "4987.TW", "5223.TW", "5289.TW", "5356.TW"
, "5386.TW", "5426.TW", "5438.TW", "5465.TW", "5474.TW", "5490.TW", "6121.TW", "6150.TW", "6160.TW", "6161.TW"
, "6188.TW", "6228.TW", "6276.TW", "6441.TW", "6570.TW", "6577.TW", "6680.TW", "6922.TW", "8050.TW", "8076.TW"
, "8234.TW", "8410.TW", "3066.TW", "3128.TW", "3230.TW", "3297.TW", "3339.TW", "3362.TW", "3434.TW", "3441.TW"
, "3455.TW", "3490.TW", "3516.TW", "3523.TW", "3531.TW", "3615.TW", "3623.TW", "3630.TW", "3666.TW", "3691.TW"
, "4729.TW", "4933.TW", "4972.TW", "4995.TW", "5220.TW", "5230.TW", "5245.TW", "5251.TW", "5315.TW", "5371.TW"
, "5392.TW", "6125.TW", "6167.TW", "6222.TW", "6234.TW", "6244.TW", "6246.TW", "6419.TW", "6498.TW", "6517.TW"
, "6556.TW", "6560.TW", "6859.TW", "6899.TW", "7402.TW", "8049.TW", "8064.TW", "8069.TW", "8111.TW", "8240.TW"
, "3081.TW", "3095.TW", "3152.TW", "3163.TW", "3221.TW", "3234.TW", "3306.TW", "3363.TW", "3466.TW", "3491.TW"
, "3499.TW", "3558.TW", "3564.TW", "3632.TW", "3664.TW", "3672.TW", "3684.TW", "4903.TW", "4905.TW", "4908.TW"
, "4909.TW", "4979.TW", "5353.TW", "6109.TW", "6143.TW", "6163.TW", "6170.TW", "6190.TW", "6218.TW", "6241.TW"
, "6245.TW", "6263.TW", "6417.TW", "6465.TW", "6470.TW", "6486.TW", "6530.TW", "6546.TW", "6561.TW", "6588.TW"
, "8034.TW", "8048.TW", "8059.TW", "8089.TW", "8097.TW", "8176.TW", "1336.TW", "1595.TW", "1815.TW", "3078.TW"
, "3114.TW", "3115.TW", "3191.TW", "3206.TW", "3207.TW", "3217.TW", "3236.TW", "3276.TW", "3288.TW", "3290.TW"
, "3294.TW", "3310.TW", "3322.TW", "3332.TW", "3354.TW", "3357.TW", "3388.TW", "3390.TW", "3484.TW", "3492.TW"
, "3511.TW", "3520.TW", "3526.TW", "3537.TW", "3548.TW", "3597.TW", "3609.TW", "3624.TW", "3631.TW", "3646.TW"
, "3689.TW", "3710.TW", "4542.TW", "4939.TW", "4974.TW", "5227.TW", "5228.TW", "5291.TW", "5309.TW", "5328.TW"
, "5340.TW", "5355.TW", "5381.TW", "5439.TW", "5457.TW", "5460.TW", "5464.TW", "5475.TW", "5488.TW", "5498.TW"
, "6114.TW", "6124.TW", "6126.TW", "6127.TW", "6134.TW", "6156.TW", "6158.TW", "6173.TW", "6174.TW", "6175.TW"
, "6185.TW", "6194.TW", "6203.TW", "6204.TW", "6207.TW", "6210.TW", "6217.TW", "6220.TW", "6259.TW", "6266.TW"
, "6274.TW", "6275.TW", "6279.TW", "6284.TW", "6290.TW", "6292.TW", "6418.TW", "6432.TW", "6538.TW", "6584.TW"
, "6597.TW", "6642.TW", "6664.TW", "6727.TW", "6761.TW", "6821.TW", "6913.TW", "6967.TW", "8038.TW", "8042.TW"
, "8043.TW", "8071.TW", "8074.TW", "8093.TW", "8109.TW", "8121.TW", "8147.TW", "8155.TW", "8182.TW", "8289.TW"
, "8291.TW", "8358.TW", "3224.TW", "3232.TW", "3360.TW", "3444.TW", "6113.TW", "6118.TW", "6154.TW", "6227.TW"
, "6265.TW", "6270.TW", "8032.TW", "8067.TW", "8068.TW", "8084.TW", "8096.TW", "3147.TW", "3570.TW", "4953.TW"
, "5201.TW", "5202.TW", "5210.TW", "5211.TW", "5212.TW", "5310.TW", "5403.TW", "5410.TW", "6123.TW", "6140.TW"
, "6148.TW", "6221.TW", "6231.TW", "6240.TW", "6516.TW", "6590.TW", "6593.TW", "6697.TW", "6751.TW", "6752.TW"
, "6791.TW", "6874.TW", "8099.TW", "8272.TW", "8284.TW", "8416.TW", "1785.TW", "3067.TW", "3093.TW", "3131.TW"
, "3219.TW", "3285.TW", "3289.TW", "3303.TW", "3324.TW", "3373.TW", "3402.TW", "3465.TW", "3498.TW", "3508.TW"
, "3541.TW", "3552.TW", "3580.TW", "3587.TW", "3628.TW", "3663.TW", "4554.TW", "4577.TW", "4760.TW", "5452.TW"
, "5489.TW", "5493.TW", "5536.TW", "6146.TW", "6151.TW", "6512.TW", "6613.TW", "6654.TW", "6667.TW", "6735.TW"
, "6739.TW", "6840.TW", "6855.TW", "6877.TW", "6903.TW", "7703.TW", "7728.TW", "8047.TW", "8085.TW", "8092.TW"
, "8183.TW", "8431.TW", "8455.TW", "2596.TW", "2718.TW", "3188.TW", "3489.TW", "3512.TW", "3521.TW", "4113.TW"
, "4416.TW", "4907.TW", "5206.TW", "5213.TW", "5324.TW", "5455.TW", "5508.TW", "5511.TW", "5512.TW", "5514.TW"
, "5516.TW", "5520.TW", "5523.TW", "5529.TW", "5543.TW", "5548.TW", "6171.TW", "6186.TW", "6198.TW", "6212.TW"
, "6219.TW", "6264.TW", "8080.TW", "8424.TW", "2641.TW", "2643.TW", "5601.TW", "5603.TW", "5609.TW", "1259.TW"
, "1268.TW", "2719.TW", "2726.TW", "2729.TW", "2732.TW", "2734.TW", "2736.TW", "2740.TW", "2743.TW", "2745.TW"
, "2751.TW", "2752.TW", "2754.TW", "2755.TW", "2756.TW", "3252.TW", "3522.TW", "4419.TW", "4530.TW", "4804.TW"
, "5301.TW", "5364.TW", "5701.TW", "5703.TW", "5704.TW", "5905.TW", "7708.TW", "7723.TW", "8077.TW", "5864.TW"
, "5878.TW", "6015.TW", "6016.TW", "6020.TW", "6021.TW", "6023.TW", "6026.TW", "8908.TW", "8917.TW", "8927.TW"
, "8931.TW", "3073.TW", "3551.TW", "3713.TW", "5205.TW", "5432.TW", "6624.TW", "6692.TW", "6803.TW", "6894.TW"
, "7715.TW", "8087.TW", "8171.TW", "8390.TW", "8423.TW", "8440.TW", "2640.TW", "2949.TW", "3085.TW", "3687.TW"
, "5278.TW", "5287.TW", "5321.TW", "6690.TW", "6741.TW", "6763.TW", "6811.TW", "6865.TW", "6870.TW", "6925.TW"
, "6997.TW", "7714.TW", "8044.TW", "8472.TW", "8477.TW", "1593.TW", "5348.TW", "6804.TW", "8924.TW", "8928.TW"
, "8933.TW", "8938.TW", "9960.TW", "2916.TW", "2924.TW", "2937.TW", "2941.TW", "2947.TW", "2948.TW", "3171.TW"
, "4609.TW", "4702.TW", "5902.TW", "5903.TW", "5904.TW", "6195.TW", "6616.TW", "6629.TW", "6728.TW", "6968.TW"
, "8066.TW", "8433.TW", "8941.TW", "1584.TW", "2221.TW", "2724.TW", "3284.TW", "3313.TW", "4154.TW", "4430.TW"
, "4529.TW", "4541.TW", "4556.TW", "5209.TW", "5276.TW", "5314.TW", "5345.TW", "5398.TW", "5450.TW", "5530.TW"
, "5604.TW", "6179.TW", "6199.TW", "6236.TW", "6721.TW", "6881.TW", "6904.TW", "8342.TW", "8354.TW", "8401.TW"
, "8421.TW", "8426.TW", "8435.TW", "8437.TW", "8444.TW", "8489.TW", "8905.TW", "8906.TW", "8916.TW", "8921.TW"
, "8929.TW", "8932.TW", "8935.TW", "8936.TW", "8937.TW", "8942.TW", "2926.TW", "3064.TW", "3083.TW", "3086.TW"
, "3293.TW", "3546.TW", "3629.TW", "4806.TW", "4946.TW", "5263.TW", "5478.TW", "6101.TW", "6111.TW", "6144.TW"
, "6169.TW", "6180.TW", "6294.TW", "6482.TW", "6542.TW", "6596.TW", "6856.TW", "7584.TW", "8446.TW", "8450.TW"
, "8923.TW", "9949.TW", "1240.TW", "4171.TW", "6508.TW", "6578.TW"
]

list_itemCode = [
    "Time", "TradingDate", "Open", "High", "Low", "Price", "TotalVolume", "Volume",
    "NWTotalBidContract", "NWTotalAskContract", "NWTotalBidSize", "NWTotalAskSize",
    "InSize", "OutSize", "TotalBidMatchTx", "TotalAskMatchTx",
    "BestBid1", "BestBid2", "BestBid3", "BestBid4", "BestBid5",
    "BestAsk1", "BestAsk2", "BestAsk3", "BestAsk4", "BestAsk5",
    "BestBidSize1", "BestBidSize2", "BestBidSize3", "BestBidSize4", "BestBidSize5",
    "BestAskSize1", "BestAskSize2", "BestAskSize3", "BestAskSize4", "BestAskSize5",
    "Name", "WContractDate", "SettlePrice", "UpLimit", "DownLimit", "OI",
    "TradingDate", "WRemainDate", "PreClose", "PreTotalVolume"
]

output = []

for full_symbol in list_symbols:
    parts = full_symbol.split('.')
    if len(parts) == 2:
        base_symbol, suffix = parts
        entry = {
            "symbol": base_symbol,
            "service": "XQTISC",
            "topic": "Quote",
            "items": [f"{base_symbol}.{suffix}-{code}" for code in list_itemCode],
            "enabled": True
        }
        output.append(entry)
    else:
        print(f"⚠️ 無法解析 symbol: {full_symbol}")

with open("output.json", "w", encoding="utf-8") as f:
    json.dump(output, f, indent=2, ensure_ascii=False)

print("✅ output.json 已完成，已根據每個 symbol 動態調整 item 格式。")
