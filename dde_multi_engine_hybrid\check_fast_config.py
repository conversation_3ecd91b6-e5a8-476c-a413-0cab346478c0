#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
檢查高性能配置是否正常工作
"""

import os
import time
import glob
from datetime import datetime

def check_log_files():
    """檢查日誌文件"""
    print("檢查日誌文件...")
    
    log_dir = "logs"
    if not os.path.exists(log_dir):
        print("❌ 日誌目錄不存在")
        return False
    
    # 查找最新的日誌文件
    log_files = glob.glob(os.path.join(log_dir, "*.log"))
    if not log_files:
        print("❌ 沒有找到日誌文件")
        return False
    
    # 按修改時間排序
    log_files.sort(key=os.path.getmtime, reverse=True)
    latest_log = log_files[0]
    
    print(f"✅ 找到最新日誌文件: {latest_log}")
    
    # 檢查文件是否在最近5分鐘內修改
    mtime = os.path.getmtime(latest_log)
    current_time = time.time()
    
    if current_time - mtime < 300:  # 5分鐘
        print(f"✅ 日誌文件是最新的 (修改時間: {datetime.fromtimestamp(mtime)})")
        
        # 讀取最後幾行
        try:
            with open(latest_log, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if lines:
                print(f"✅ 日誌文件有內容 ({len(lines)} 行)")
                print("最後5行內容:")
                for line in lines[-5:]:
                    print(f"  {line.strip()}")
                return True
            else:
                print("❌ 日誌文件為空")
                return False
                
        except Exception as e:
            print(f"❌ 讀取日誌文件失敗: {e}")
            return False
    else:
        print(f"❌ 日誌文件太舊 (修改時間: {datetime.fromtimestamp(mtime)})")
        return False

def check_config_file():
    """檢查配置文件"""
    print("\n檢查高性能配置文件...")
    
    config_file = "multi_config_fast.ini"
    if not os.path.exists(config_file):
        print("❌ 高性能配置文件不存在")
        return False
    
    print(f"✅ 找到配置文件: {config_file}")
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 檢查關鍵配置
        checks = [
            ("request_interval = 0.0", "DDE請求間隔設為0"),
            ("subscribe_interval = 0.0", "DDE訂閱間隔設為0"),
            ("batch_size = 100", "批次大小設為100"),
            ("console_level = WARNING", "控制台日誌級別設為WARNING"),
            ("enable_debug_logging = false", "禁用DEBUG日誌")
        ]
        
        all_good = True
        for check, desc in checks:
            if check in content:
                print(f"✅ {desc}")
            else:
                print(f"❌ {desc} - 未找到: {check}")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"❌ 讀取配置文件失敗: {e}")
        return False

def check_data_files():
    """檢查數據文件"""
    print("\n檢查數據文件...")
    
    data_dir = "data"
    if not os.path.exists(data_dir):
        print("❌ 數據目錄不存在")
        return False
    
    # 查找CSV文件
    csv_files = glob.glob(os.path.join(data_dir, "*.csv"))
    if csv_files:
        print(f"✅ 找到 {len(csv_files)} 個數據文件")
        
        # 檢查最新的文件
        csv_files.sort(key=os.path.getmtime, reverse=True)
        latest_csv = csv_files[0]
        
        mtime = os.path.getmtime(latest_csv)
        current_time = time.time()
        
        if current_time - mtime < 300:  # 5分鐘
            print(f"✅ 數據文件是最新的: {os.path.basename(latest_csv)}")
            return True
        else:
            print(f"⚠️ 數據文件較舊: {os.path.basename(latest_csv)}")
            return False
    else:
        print("⚠️ 沒有找到數據文件 (可能程序剛啟動)")
        return False

def main():
    """主函數"""
    print("=" * 60)
    print("DDE多商品監控系統 - 高性能配置檢查")
    print("=" * 60)
    
    results = []
    
    # 檢查配置文件
    config_ok = check_config_file()
    results.append(("配置文件", config_ok))
    
    # 檢查日誌文件
    log_ok = check_log_files()
    results.append(("日誌文件", log_ok))
    
    # 檢查數據文件
    data_ok = check_data_files()
    results.append(("數據文件", data_ok))
    
    # 總結
    print("\n" + "=" * 60)
    print("檢查結果總結")
    print("=" * 60)
    
    for name, status in results:
        status_text = "✅ 正常" if status else "❌ 異常"
        print(f"{name:15}: {status_text}")
    
    all_ok = all(status for _, status in results)
    
    if all_ok:
        print("\n🎉 高性能配置運行正常！")
        print("💡 提示: 由於日誌級別設為WARNING，控制台輸出很少是正常的")
        print("📊 建議: 可以通過GUI界面或日誌文件監控程序狀態")
    else:
        print("\n⚠️ 發現一些問題，請檢查上述項目")
    
    print(f"\n檢查時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
