#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
引擎包装器版多商品测试GUI窗口
简单的GUI界面，便于与其他版本进行比较
"""

import sys
import os
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional

try:
    from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                                   QTabWidget, QTableWidget, QTableWidgetItem,
                                   QPushButton, QLabel, QTextEdit, QGroupBox,
                                   QStatusBar, QMessageBox, QProgressBar)
    from PySide6.QtCore import Qt, QTimer, Signal, QThread
    from PySide6.QtGui import QFont
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'core'))

from core.multi_config_parser import MultiConfigParser
from core.engine_wrapper import MultiProductDDEWrapper


class WrapperTestWindow(QMainWindow):
    """引擎包装器版测试窗口"""
    
    def __init__(self, config_file: str):
        super().__init__()
        
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        
        # 核心组件
        self.config_parser: Optional[MultiConfigParser] = None
        self.wrapper: Optional[MultiProductDDEWrapper] = None
        
        # 数据统计
        self.total_data_received = 0
        self.data_by_symbol = {}
        self.data_by_type = {}
        self.start_time = time.time()
        
        # GUI组件
        self.symbol_tabs: Optional[QTabWidget] = None
        self.symbol_tables: Dict[str, Dict[str, QTableWidget]] = {}
        self.status_label: Optional[QLabel] = None
        self.log_text: Optional[QTextEdit] = None
        self.connect_btn: Optional[QPushButton] = None
        self.stats_labels: Dict[str, QLabel] = {}
        
        # 定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        
        self.init_ui()
        self.init_logging()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("引擎包装器版多商品DDE测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 控制面板
        self.create_control_panel(layout)
        
        # 统计信息面板
        self.create_stats_panel(layout)
        
        # 商品标签页
        self.create_symbol_tabs(layout)
        
        # 日志面板
        self.create_log_panel(layout)
        
        # 状态栏
        self.create_status_bar()
        
    def create_control_panel(self, parent_layout):
        """创建控制面板"""
        group = QGroupBox("控制面板")
        layout = QHBoxLayout(group)
        
        # 连接按钮
        self.connect_btn = QPushButton("初始化并连接")
        self.connect_btn.clicked.connect(self.toggle_connection)
        layout.addWidget(self.connect_btn)
        
        # 清除日志按钮
        clear_btn = QPushButton("清除日志")
        clear_btn.clicked.connect(self.clear_log)
        layout.addWidget(clear_btn)
        
        # 重置统计按钮
        reset_btn = QPushButton("重置统计")
        reset_btn.clicked.connect(self.reset_stats)
        layout.addWidget(reset_btn)
        
        layout.addStretch()
        parent_layout.addWidget(group)
        
    def create_stats_panel(self, parent_layout):
        """创建统计信息面板"""
        group = QGroupBox("统计信息")
        layout = QHBoxLayout(group)
        
        # 总体统计
        self.stats_labels['total'] = QLabel("总接收: 0")
        self.stats_labels['rate'] = QLabel("接收率: 0/秒")
        self.stats_labels['uptime'] = QLabel("运行时间: 0秒")
        self.stats_labels['engines'] = QLabel("引擎数: 0")
        
        for label in self.stats_labels.values():
            label.setFont(QFont("Arial", 10, QFont.Bold))
            layout.addWidget(label)
        
        layout.addStretch()
        parent_layout.addWidget(group)
        
    def create_symbol_tabs(self, parent_layout):
        """创建商品标签页"""
        self.symbol_tabs = QTabWidget()
        parent_layout.addWidget(self.symbol_tabs)
        
    def create_log_panel(self, parent_layout):
        """创建日志面板"""
        group = QGroupBox("运行日志")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        parent_layout.addWidget(group)
        
    def create_status_bar(self):
        """创建状态栏"""
        self.status_label = QLabel("就绪")
        self.statusBar().addWidget(self.status_label)
        
        # 添加时间显示
        self.time_label = QLabel()
        self.statusBar().addPermanentWidget(self.time_label)
        
        # 更新时间显示
        timer = QTimer()
        timer.timeout.connect(self.update_time)
        timer.start(1000)
        self.update_time()
        
    def init_logging(self):
        """初始化日志"""
        # 创建日志处理器，将日志输出到GUI
        class GuiLogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
                
            def emit(self, record):
                try:
                    msg = self.format(record)
                    self.text_widget.append(msg)
                    # 保持最新日志可见
                    self.text_widget.verticalScrollBar().setValue(
                        self.text_widget.verticalScrollBar().maximum()
                    )
                except:
                    pass
        
        # 添加GUI日志处理器
        gui_handler = GuiLogHandler(self.log_text)
        gui_handler.setFormatter(logging.Formatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%H:%M:%S'
        ))
        
        # 获取根日志器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(gui_handler)
        root_logger.setLevel(logging.INFO)
        
    def toggle_connection(self):
        """切换连接状态"""
        try:
            if not self.wrapper:
                # 初始化
                self.initialize_wrapper()
            else:
                # 切换连接状态
                if self.wrapper.state.name == 'RUNNING':
                    self.disconnect_wrapper()
                else:
                    self.connect_wrapper()
                    
        except Exception as e:
            self.logger.error(f"切换连接状态失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")
            
    def initialize_wrapper(self):
        """初始化包装器"""
        try:
            self.status_label.setText("正在初始化...")
            self.connect_btn.setEnabled(False)
            
            # 解析配置
            self.config_parser = MultiConfigParser(self.config_file)
            if not self.config_parser.parse_config():
                raise Exception("配置解析失败")
            
            self.logger.info("配置解析完成")
            
            # 转换配置格式
            products = self.config_parser.convert_to_wrapper_config()
            if not products:
                raise Exception("配置转换失败")
            
            # 创建包装器配置文件
            wrapper_config_file = self._create_wrapper_config(products)
            
            # 创建包装器
            self.wrapper = MultiProductDDEWrapper(wrapper_config_file)
            self.wrapper.set_multi_config_parser(self.config_parser)
            
            # 设置回调
            self.wrapper.on_data_received = self.on_data_received
            self.wrapper.on_connection_changed = self.on_connection_changed
            self.wrapper.on_error = self.on_error
            
            # 初始化包装器
            if not self.wrapper.initialize():
                raise Exception("包装器初始化失败")
            
            # 创建商品标签页
            self.create_symbol_tabs_content()
            
            self.logger.info("包装器初始化完成")
            self.status_label.setText("已初始化")
            self.connect_btn.setText("连接DDE")
            self.connect_btn.setEnabled(True)
            
            # 启动更新定时器
            self.update_timer.start(1000)  # 每秒更新一次

            # 检查是否需要自动连接
            self.check_auto_connect()
            
        except Exception as e:
            self.logger.error(f"初始化失败: {str(e)}")
            self.status_label.setText("初始化失败")
            self.connect_btn.setEnabled(True)
            raise
            
    def connect_wrapper(self):
        """连接包装器"""
        try:
            self.status_label.setText("正在连接...")
            self.connect_btn.setEnabled(False)
            
            if not self.wrapper.start():
                raise Exception("包装器启动失败")
            
            self.logger.info("DDE连接成功")
            self.status_label.setText("已连接")
            self.connect_btn.setText("断开连接")
            self.connect_btn.setEnabled(True)
            
        except Exception as e:
            self.logger.error(f"连接失败: {str(e)}")
            self.status_label.setText("连接失败")
            self.connect_btn.setEnabled(True)
            raise
            
    def disconnect_wrapper(self):
        """断开包装器"""
        try:
            self.status_label.setText("正在断开...")
            self.connect_btn.setEnabled(False)
            
            self.wrapper.stop()
            
            self.logger.info("DDE连接已断开")
            self.status_label.setText("已断开")
            self.connect_btn.setText("连接DDE")
            self.connect_btn.setEnabled(True)
            
        except Exception as e:
            self.logger.error(f"断开失败: {str(e)}")
            self.status_label.setText("断开失败")
            self.connect_btn.setEnabled(True)
            
    def create_symbol_tabs_content(self):
        """创建商品标签页内容"""
        try:
            if not self.config_parser:
                return
                
            symbols = self.config_parser.get_symbol_list()
            
            for symbol in symbols:
                # 创建商品标签页
                symbol_widget = QWidget()
                symbol_layout = QVBoxLayout(symbol_widget)
                
                # 创建数据类型标签页
                data_type_tabs = QTabWidget()
                symbol_layout.addWidget(data_type_tabs)
                
                # 获取该商品的数据类型
                symbol_config = self.config_parser.symbol_configs.get(symbol)
                if symbol_config:
                    data_types = symbol_config.enabled_types
                    
                    self.symbol_tables[symbol] = {}
                    
                    for data_type in data_types:
                        # 创建数据类型表格
                        table = self.create_data_type_table(symbol, data_type)
                        self.symbol_tables[symbol][data_type] = table
                        data_type_tabs.addTab(table, data_type.upper())
                
                self.symbol_tabs.addTab(symbol_widget, symbol)
                
        except Exception as e:
            self.logger.error(f"创建商品标签页失败: {str(e)}")
            
    def create_data_type_table(self, symbol: str, data_type: str) -> QTableWidget:
        """创建数据类型表格"""
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["项目名称", "项目代码", "当前值", "状态"])
        
        # 获取该数据类型的项目
        items = self.config_parser.get_dde_items_for_symbol(symbol, data_type)
        table.setRowCount(len(items))
        
        for row, item_code in enumerate(items):
            # 项目名称（简化显示）
            item_name = item_code.split('-')[-1] if '-' in item_code else item_code
            table.setItem(row, 0, QTableWidgetItem(item_name))
            table.setItem(row, 1, QTableWidgetItem(item_code))
            table.setItem(row, 2, QTableWidgetItem(""))
            table.setItem(row, 3, QTableWidgetItem("等待"))
        
        # 调整列宽
        table.resizeColumnsToContents()
        
        return table
        
    def _create_wrapper_config(self, products: Dict) -> str:
        """创建包装器配置文件"""
        try:
            config_content = f"""
[Global]
app_name = 引擎包装器测试
version = 1.0.0
log_level = INFO

[Engine]
max_engines = 20
engine_timeout = 30.0
restart_on_failure = true

[Products]
enabled_products = {','.join(products.keys())}

"""
            
            # 添加每个商品的配置
            for symbol, product in products.items():
                config_content += f"""
[{symbol}]
data_types = {','.join(product.data_types)}
dde_service = {product.dde_service}
dde_topic = {product.dde_topic}
output_path = {product.output_path}
auto_connect = {str(product.auto_connect).lower()}
auto_connect_times = {','.join(product.auto_connect_times)}
"""
            
            # 保存配置文件
            config_file = os.path.join(
                os.path.dirname(__file__), '..', 'config', 'wrapper_test_gui.ini'
            )
            
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            return config_file
            
        except Exception as e:
            self.logger.error(f"创建包装器配置文件失败: {str(e)}")
            return ""
            
    def on_data_received(self, symbol: str, data_type: str, item: str, value: str):
        """数据接收回调"""
        try:
            # 更新统计
            self.total_data_received += 1
            
            if symbol not in self.data_by_symbol:
                self.data_by_symbol[symbol] = 0
            self.data_by_symbol[symbol] += 1
            
            if data_type not in self.data_by_type:
                self.data_by_type[data_type] = 0
            self.data_by_type[data_type] += 1
            
            # 更新表格（在主线程中）
            self.update_table_item(symbol, data_type, item, value)
            
        except Exception as e:
            self.logger.error(f"处理数据接收失败: {str(e)}")
            
    def on_connection_changed(self, symbol: str, connected: bool):
        """连接状态变化回调"""
        status = "已连接" if connected else "已断开"
        self.logger.info(f"{symbol} {status}")
        
    def on_error(self, error_type: str, error_message: str):
        """错误回调"""
        self.logger.error(f"{error_type}: {error_message}")
        
    def update_table_item(self, symbol: str, data_type: str, item: str, value: str):
        """更新表格项目"""
        try:
            if (symbol in self.symbol_tables and 
                data_type in self.symbol_tables[symbol]):
                
                table = self.symbol_tables[symbol][data_type]
                
                # 查找对应的行
                for row in range(table.rowCount()):
                    item_code = table.item(row, 1)
                    if item_code and item_code.text() == item:
                        # 更新值和状态
                        table.setItem(row, 2, QTableWidgetItem(str(value)))
                        table.setItem(row, 3, QTableWidgetItem("已更新"))
                        break
                        
        except Exception as e:
            self.logger.error(f"更新表格失败: {str(e)}")
            
    def update_display(self):
        """更新显示"""
        try:
            # 更新统计信息
            uptime = time.time() - self.start_time
            rate = self.total_data_received / uptime if uptime > 0 else 0
            
            self.stats_labels['total'].setText(f"总接收: {self.total_data_received}")
            self.stats_labels['rate'].setText(f"接收率: {rate:.1f}/秒")
            self.stats_labels['uptime'].setText(f"运行时间: {int(uptime)}秒")
            
            if self.wrapper:
                stats = self.wrapper.get_stats()
                engine_count = stats.engine_stats.total_engines if stats.engine_stats else 0
                self.stats_labels['engines'].setText(f"引擎数: {engine_count}")
                
        except Exception as e:
            self.logger.error(f"更新显示失败: {str(e)}")
            
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        self.time_label.setText(current_time)
        
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
        
    def reset_stats(self):
        """重置统计"""
        self.total_data_received = 0
        self.data_by_symbol.clear()
        self.data_by_type.clear()
        self.start_time = time.time()
        self.logger.info("统计信息已重置")
        
    def check_auto_connect(self):
        """检查是否需要自动连接"""
        try:
            if not self.config_parser:
                return

            # 获取当前时间
            from datetime import datetime
            now = datetime.now()
            current_time = now.strftime('%H:%M:%S')

            self.logger.info(f"检查自动连接: 当前时间 {current_time}")

            # 检查每个商品的自动连接配置
            for symbol in self.config_parser.get_symbol_list():
                symbol_config = self.config_parser.symbol_configs.get(symbol)
                if not symbol_config:
                    continue

                # 获取自动连接模板
                template_name = symbol_config.auto_connect_template.replace('AutoConnect_', '')
                template = self.config_parser.auto_connect_templates.get(template_name)

                if template and template.enable_auto_connect:
                    # 检查是否在连接时间范围内
                    if self._is_in_connect_time(current_time, template.schedule_connect_times):
                        self.logger.info(f"商品 {symbol} 在自动连接时间范围内，开始连接...")
                        # 延迟几秒后自动连接
                        QTimer.singleShot(3000, self.auto_connect_wrapper)
                        return

            self.logger.info("当前时间不在自动连接范围内")

        except Exception as e:
            self.logger.error(f"检查自动连接失败: {str(e)}")

    def _is_in_connect_time(self, current_time: str, schedule_times: str) -> bool:
        """检查当前时间是否在连接时间范围内"""
        try:
            if not schedule_times:
                return False

            # 解析时间范围 (支持多个时间段，用分号分隔)
            time_ranges = schedule_times.split(';')

            for time_range in time_ranges:
                if '-' in time_range:
                    start_time, end_time = time_range.split('-', 1)
                    start_time = start_time.strip()
                    end_time = end_time.strip()

                    # 简单的时间比较 (不考虑跨日)
                    if start_time <= current_time <= end_time:
                        return True

            return False

        except Exception as e:
            self.logger.error(f"时间范围检查失败: {str(e)}")
            return False

    def auto_connect_wrapper(self):
        """自动连接包装器"""
        try:
            if self.wrapper and self.wrapper.state.name != 'RUNNING':
                self.logger.info("执行自动连接...")
                self.connect_wrapper()
            else:
                self.logger.info("包装器已连接，跳过自动连接")

        except Exception as e:
            self.logger.error(f"自动连接失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            if self.wrapper:
                self.wrapper.stop()
            event.accept()
        except Exception as e:
            self.logger.error(f"关闭窗口失败: {str(e)}")
            event.accept()
