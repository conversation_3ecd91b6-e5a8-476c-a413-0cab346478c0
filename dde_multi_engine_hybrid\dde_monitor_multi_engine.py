#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多商品DDE监控程序 - 引擎混合版本
基于多商品版本改造，集成DDE数据引擎
"""

import sys
import os
import argparse
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from utils.config_manager import MultiProductConfigManager
from utils.logger import setup_logging
from gui.multi_product_window import MultiProductMainWindow

# 导入引擎集成模块
from core.engine_integration import MultiProductEngineManager
from core.data_bridge import MultiProductDataBridge


class MultiProductDDEEngineApp:
    """多商品DDE监控程序 - 引擎混合版本"""

    def __init__(self, config_file=None):
        self.app = None
        self.main_window = None
        self.multi_config = None
        self.logger = None
        self.config_file = config_file or 'multi_config.ini'

        # 引擎组件
        self.engine_manager = None
        self.data_bridge = None

    def initialize(self):
        """初始化应用程序"""
        try:
            # 创建 QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("Multi-Product DDE Monitor")
            self.app.setApplicationVersion("7.0")

            # 初始化多商品配置管理器
            self.multi_config = MultiProductConfigManager(self.config_file)
            
            # 载入配置文件
            if not self.multi_config.load_config():
                raise Exception(f"载入多商品配置文件失败: {self.config_file}")
            
            print(f"使用多商品配置文件: {self.config_file}")
            
            # 验证配置
            errors = self.multi_config.validate_multi_config()
            if errors:
                print("配置验证发现问题:")
                for error_type, error_list in errors.items():
                    print(f"  {error_type}: {error_list}")
                if 'missing_sections' in errors or 'symbol_errors' in errors:
                    raise Exception("配置文件存在严重错误，无法继续")
            
            # 初始化日志系统 - 支持高性能配置
            system_config = self.multi_config.get_system_config()
            log_base_path = system_config.get('log_base_path', './logs/')

            # 确保日志目录存在
            os.makedirs(log_base_path, exist_ok=True)

            # 獲取日誌配置 - 修復configparser的get方法調用
            console_level = 'INFO'
            file_level = 'INFO'
            enable_debug = True

            try:
                if hasattr(self.multi_config.config, 'has_section') and self.multi_config.config.has_section('Logging'):
                    console_level = self.multi_config.config.get('Logging', 'console_level', fallback='INFO')
                    file_level = self.multi_config.config.get('Logging', 'file_level', fallback='INFO')

                    # 安全處理 enable_debug_logging 配置
                    enable_debug_raw = self.multi_config.config.get('Logging', 'enable_debug_logging', fallback='true')
                    if isinstance(enable_debug_raw, bool):
                        enable_debug = enable_debug_raw
                    elif isinstance(enable_debug_raw, str):
                        enable_debug = enable_debug_raw.lower() == 'true'
                    else:
                        enable_debug = True  # 默認啟用
            except Exception as e:
                self.logger.warning(f"讀取日誌配置失敗，使用默認值: {e}") if hasattr(self, 'logger') else None

            log_file_path = os.path.join(log_base_path, 'multi_product_monitor.log')
            self.logger = setup_logging(log_file_path, 'INFO', console_level, file_level, enable_debug)
            self.logger.info("多商品DDE监控程序启动")
            
            # 记录配置信息
            self.logger.info(f"载入商品数量: {len(self.multi_config.symbols)}")
            self.logger.info(f"商品列表: {', '.join(self.multi_config.symbols)}")
            self.logger.info(f"数据类型: {', '.join(self.multi_config.data_types)}")

            # 初始化引擎组件
            self.engine_manager = MultiProductEngineManager(self.logger)
            self.data_bridge = MultiProductDataBridge(self.logger)

            # 设置引擎回调
            self.engine_manager.on_data_received = self._on_engine_data_received
            self.data_bridge.on_global_update = self._on_data_bridge_update

            self.logger.info("引擎组件初始化完成")

            # 初始化主窗口
            self.main_window = MultiProductMainWindow(self.multi_config, self.logger)

            # 将引擎组件传递给主窗口
            if hasattr(self.main_window, 'set_engine_components'):
                self.main_window.set_engine_components(self.engine_manager, self.data_bridge)

            self.logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            import traceback
            error_msg = f"应用程序初始化失败: {str(e)}"
            if self.logger:
                self.logger.error(error_msg)
                self.logger.error("詳細錯誤信息:")
                self.logger.error(traceback.format_exc())
            else:
                print(error_msg)
                print("詳細錯誤信息:")
                traceback.print_exc()
            return False
    
    def run(self):
        """运行应用程序"""
        try:
            if not self.initialize():
                return 1
            
            # 显示主窗口
            self.main_window.show()
            
            # 运行应用程序
            return self.app.exec()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"运行应用程序失败: {str(e)}")
            else:
                print(f"运行应用程序失败: {str(e)}")
            return 1
    
    def _on_engine_data_received(self, item: str, value: str):
        """引擎数据接收回调"""
        try:
            # 解析商品和数据类型（这里需要根据实际情况调整）
            # 暂时使用简单的解析逻辑
            if '.' in item:
                symbol_part = item.split('.')[0]
                # 根据项目名称推断数据类型
                if any(keyword in item.lower() for keyword in ['time', 'price', 'volume', 'open', 'high', 'low']):
                    data_type = 'tick'
                elif any(keyword in item.lower() for keyword in ['bid', 'ask', 'contract', 'size']):
                    data_type = 'order'
                elif any(keyword in item.lower() for keyword in ['bestbid', 'bestask']):
                    data_type = 'level2'
                else:
                    data_type = 'daily'

                # 更新数据桥接器
                self.data_bridge.update_data(symbol_part, data_type, item, value)

        except Exception as e:
            if self.logger:
                self.logger.error(f"引擎数据回调失败: {item}, {str(e)}")

    def _on_data_bridge_update(self, product_symbol: str, data_type: str, item: str, value: str):
        """数据桥接器更新回调"""
        try:
            # 这里可以添加额外的处理逻辑
            self.logger.debug(f"数据更新: {product_symbol}.{data_type} {item}={value}")
        except Exception as e:
            if self.logger:
                self.logger.error(f"数据桥接回调失败: {product_symbol}.{data_type}, {item}, {str(e)}")

    def cleanup(self):
        """清理资源"""
        try:
            # 清理引擎组件
            if self.engine_manager:
                self.engine_manager.cleanup()

            if self.data_bridge:
                self.data_bridge.cleanup()

            if self.main_window:
                self.main_window.cleanup()

            if self.logger:
                self.logger.info("多商品引擎混合版本正常结束")

        except Exception as e:
            if self.logger:
                self.logger.error(f"清理资源失败: {str(e)}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='多商品DDE监控程序 v7.0',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用范例:
  python dde_monitor_multi.py                           # 使用默认配置文件 multi_config.ini
  python dde_monitor_multi.py -c my_multi_config.ini    # 使用指定配置文件
  python dde_monitor_multi.py --config /path/to/config.ini  # 使用完整路径的配置文件

多商品配置文件说明:
  1. 支持模板化配置，使用 {symbol} 占位符
  2. 可以同时监控多个商品的不同数据类型
  3. 每个商品可以有独立的输出路径和自动化策略
  4. 支持期货、股票等不同类型商品的混合监控
        """
    )

    parser.add_argument(
        '-c', '--config',
        type=str,
        help='指定多商品配置文件路径 (默认: multi_config.ini)'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='多商品DDE监控程序 v7.0'
    )

    parser.add_argument(
        '--generate-single',
        nargs=3,
        metavar=('SYMBOL', 'DATATYPE', 'OUTPUT'),
        help='生成单商品配置文件: SYMBOL DATATYPE OUTPUT_FILE'
    )

    return parser.parse_args()


def generate_single_config(multi_config, symbol, data_type, output_file):
    """生成单商品配置文件"""
    try:
        if multi_config.generate_single_product_config(symbol, data_type, output_file):
            print(f"成功生成单商品配置文件: {output_file}")
            print(f"商品: {symbol}, 数据类型: {data_type}")
            return True
        else:
            print(f"生成单商品配置文件失败")
            return False
            
    except Exception as e:
        print(f"生成单商品配置文件时出错: {str(e)}")
        return False


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 如果是生成单商品配置文件模式
    if args.generate_single:
        symbol, data_type, output_file = args.generate_single
        
        # 载入多商品配置
        config_file = args.config or 'multi_config.ini'
        multi_config = MultiProductConfigManager(config_file)
        
        if not multi_config.load_config():
            print(f"载入多商品配置文件失败: {config_file}")
            return 1
        
        # 生成单商品配置
        if generate_single_config(multi_config, symbol, data_type, output_file):
            return 0
        else:
            return 1

    # 正常运行模式
    app = MultiProductDDEEngineApp(config_file=args.config)

    try:
        exit_code = app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        exit_code = 0
    except Exception as e:
        import traceback
        print(f"程序执行失败: {str(e)}")
        print("詳細錯誤信息:")
        traceback.print_exc()
        exit_code = 1
    finally:
        app.cleanup()

    return exit_code


if __name__ == "__main__":
    sys.exit(main())
