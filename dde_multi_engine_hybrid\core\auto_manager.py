#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動化管理器模組
負責自動連線、自動斷線和自動結束功能
"""

import configparser
import logging
from datetime import datetime, time, timedelta
from typing import List, Tuple, Optional
from PySide6.QtCore import QObject, Signal, QTimer

class AutoConnectManager(QObject):
    """自動連線管理器
    負責管理自動連線、自動斷線和自動結束功能
    """
    
    # 信號定義
    auto_connect_requested = Signal()
    auto_disconnect_requested = Signal()
    auto_unadvise_only_requested = Signal()  # 僅取消訂閱信號
    auto_shutdown_requested = Signal()
    status_changed = Signal(str)
    
    def __init__(self, config: configparser.ConfigParser, logger: logging.Logger):
        super().__init__()
        self.config = config
        self.logger = logger
        
        # 自動連線設定
        self.enable_auto_connect = False
        self.auto_connect_mode = 'delay'  # immediate, delay, schedule
        self.auto_connect_delay = 3.0
        self.schedule_connect_times = ''
        self.enable_cross_day_schedule = True
        self.prevent_weekend_startup = False
        self.schedule_end_action = 'unadvise_only'  # disconnect, unadvise_only
        
        # 自動結束設定
        self.enable_auto_shutdown = False
        self.shutdown_time_str = '17:30:00'
        self.shutdown_time = None
        self.shutdown_buffer_seconds = 300
        self.shutdown_warning_seconds = 60
        self.force_shutdown = True
        self.save_data_before_shutdown = True
        self.disconnect_before_shutdown = True
        self.cleanup_temp_files = True
        
        # 通知設定
        self.enable_notifications = True
        self.notify_auto_connect = True
        self.notify_auto_disconnect = True
        self.notify_auto_shutdown = True
        
        # 內部狀態
        self.schedule_times: List[Tuple[time, time]] = []
        self.current_schedule_state = None
        self.last_schedule_check = None
        self.shutdown_warning_shown = False
        
        # 定時器
        self.auto_connect_timer = QTimer()
        self.schedule_timer = QTimer()
        self.shutdown_timer = QTimer()
        
        # 連接信號
        self.auto_connect_timer.timeout.connect(self._handle_auto_connect)
        self.schedule_timer.timeout.connect(self._check_schedule)
        self.shutdown_timer.timeout.connect(self._check_auto_shutdown)
        
        # 狀態追蹤
        self.current_schedule_state = None  # 'connected' or 'disconnected'
        self.last_schedule_check = None
        self.shutdown_warning_shown = False
        self.shutdown_warning_time = None
        
        # 載入設定
        self._load_settings()
    
    def _load_settings(self):
        """載入自動連線設定"""
        try:
            # 自動連線設定
            if 'AutoConnect' in self.config:
                self.enable_auto_connect = self.config.getboolean('AutoConnect', 'enable_auto_connect', fallback=False)
                self.auto_connect_mode = self.config.get('AutoConnect', 'auto_connect_mode', fallback='delay')
                self.auto_connect_delay = self.config.getfloat('AutoConnect', 'auto_connect_delay', fallback=3.0)
                self.schedule_connect_times = self.config.get('AutoConnect', 'schedule_connect_times', fallback='')
                self.enable_cross_day_schedule = self.config.getboolean('AutoConnect', 'enable_cross_day_schedule', fallback=True)
                self.prevent_weekend_startup = self.config.getboolean('AutoConnect', 'prevent_weekend_startup', fallback=False)
                schedule_end_action_raw = self.config.get('AutoConnect', 'schedule_end_action', fallback='unadvise_only')
                # 验证并修正 schedule_end_action 设定
                if schedule_end_action_raw.lower() in ['disconnect', 'unadvise_only']:
                    self.schedule_end_action = schedule_end_action_raw.lower()
                else:
                    self.logger.warning(f"schedule_end_action 设定值无效: '{schedule_end_action_raw}'，已更正为 'unadvise_only'")
                    self.schedule_end_action = 'unadvise_only'
                    # 更新配置文件
                    self.config.set('AutoConnect', 'schedule_end_action', 'unadvise_only')
                    try:
                        with open('config.ini', 'w', encoding='utf-8') as f:
                            self.config.write(f)
                        self.logger.info("已自动更正配置文件中的 schedule_end_action 设定")
                    except Exception as e:
                        self.logger.error(f"更新配置文件失败: {str(e)}")
            
            # 自動結束設定
            if 'AutoShutdown' in self.config:
                self.enable_auto_shutdown = self.config.getboolean('AutoShutdown', 'enable_auto_shutdown', fallback=False)
                self.shutdown_time_str = self.config.get('AutoShutdown', 'shutdown_time', fallback='17:30:00')
                self.shutdown_buffer_seconds = self.config.getint('AutoShutdown', 'shutdown_buffer_seconds', fallback=300)
                self.shutdown_warning_seconds = self.config.getint('AutoShutdown', 'shutdown_warning_seconds', fallback=60)
                self.force_shutdown = self.config.getboolean('AutoShutdown', 'force_shutdown', fallback=True)
                self.save_data_before_shutdown = self.config.getboolean('AutoShutdown', 'save_data_before_shutdown', fallback=True)
                self.disconnect_before_shutdown = self.config.getboolean('AutoShutdown', 'disconnect_before_shutdown', fallback=True)
                self.cleanup_temp_files = self.config.getboolean('AutoShutdown', 'cleanup_temp_files', fallback=True)
            
            # 通知設定
            if 'Notifications' in self.config:
                self.enable_notifications = self.config.getboolean('Notifications', 'enable_system_notifications', fallback=True)
                self.notify_auto_connect = self.config.getboolean('Notifications', 'notify_auto_connect', fallback=True)
                self.notify_auto_disconnect = self.config.getboolean('Notifications', 'notify_auto_disconnect', fallback=True)
                self.notify_auto_shutdown = self.config.getboolean('Notifications', 'notify_auto_shutdown', fallback=True)
            
            # 解析時間表和結束時間
            self.schedule_times = self._parse_schedule_times()
            self.shutdown_time = self._parse_time(self.shutdown_time_str)
            
            self.logger.info(f"自動連線設定載入完成: 啟用={self.enable_auto_connect}, 模式={self.auto_connect_mode}")
            if self.auto_connect_mode == 'schedule':
                self.logger.info(f"時間表模式結束行為: {self.schedule_end_action}")
            if self.enable_auto_shutdown:
                self.logger.info(f"自動結束設定載入完成: 啟用={self.enable_auto_shutdown}, 時間={self.shutdown_time_str}")
            
        except Exception as e:
            self.logger.error(f"載入自動連線設定失敗: {str(e)}")
            # 設定預設值
            self.enable_auto_connect = False
            self.enable_auto_shutdown = False
    
    def load_settings(self, config: configparser.ConfigParser):
        """重新載入設定 (不停止運行)"""
        try:
            old_enable_auto_connect = self.enable_auto_connect
            old_enable_auto_shutdown = self.enable_auto_shutdown
            old_auto_connect_mode = self.auto_connect_mode
            
            # 更新設定
            self.config = config
            
            # 重新載入設定
            self._load_settings()
            
            # 根據設定變更調整定時器
            self._adjust_timers(old_enable_auto_connect, old_enable_auto_shutdown, old_auto_connect_mode)
            
            self.logger.info("自動化設定已重新載入")
            self.status_changed.emit("設定已更新")
            
        except Exception as e:
            self.logger.error(f"重新載入設定失敗: {str(e)}")
    
    def _adjust_timers(self, old_enable_auto_connect, old_enable_auto_shutdown, old_auto_connect_mode):
        """根據設定變更調整定時器"""
        try:
            # 處理自動連線設定變更
            if old_enable_auto_connect != self.enable_auto_connect:
                if self.enable_auto_connect:
                    self.logger.info("自動連線已啟用")
                    if self.auto_connect_mode == 'delay':
                        # 啟動延遲連線
                        delay_ms = int(self.auto_connect_delay * 1000)
                        self.auto_connect_timer.start(delay_ms)
                        self.logger.info(f"延遲 {self.auto_connect_delay} 秒後自動連線")
                    elif self.auto_connect_mode == 'immediate':
                        # 立即連線
                        self.auto_connect_requested.emit()
                        self.logger.info("立即執行自動連線")
                else:
                    self.logger.info("自動連線已停用")
                    self.auto_connect_timer.stop()
            
            # 處理連線模式變更
            elif self.enable_auto_connect and old_auto_connect_mode != self.auto_connect_mode:
                self.auto_connect_timer.stop()
                if self.auto_connect_mode == 'delay':
                    delay_ms = int(self.auto_connect_delay * 1000)
                    self.auto_connect_timer.start(delay_ms)
                    self.logger.info(f"切換到延遲模式，{self.auto_connect_delay} 秒後連線")
                elif self.auto_connect_mode == 'immediate':
                    self.auto_connect_requested.emit()
                    self.logger.info("切換到立即模式，執行連線")
            
            # 處理時間表設定
            if self.auto_connect_mode == 'schedule' and self.schedule_times:
                if not self.schedule_timer.isActive():
                    self.schedule_timer.start(1000)
                    self.logger.info("時間表檢查已啟動")
            else:
                self.schedule_timer.stop()
            
            # 處理自動結束設定變更
            if old_enable_auto_shutdown != self.enable_auto_shutdown:
                if self.enable_auto_shutdown:
                    self.logger.info(f"自動結束已啟用，結束時間: {self.shutdown_time_str}")
                    self.shutdown_timer.start(1000)
                else:
                    self.logger.info("自動結束已停用")
                    self.shutdown_timer.stop()
                    self.shutdown_warning_shown = False
            
        except Exception as e:
            self.logger.error(f"調整定時器失敗: {str(e)}")
    
    def _parse_schedule_times(self) -> List[Tuple[time, time]]:
        """解析時間表設定"""
        times = []
        if not self.schedule_connect_times:
            return times
        
        try:
            time_ranges = self.schedule_connect_times.split(';')
            for time_range in time_ranges:
                time_range = time_range.strip()
                if not time_range:
                    continue

                if '-' not in time_range:
                    self.logger.warning(f"時間格式錯誤，缺少 '-'：'{time_range}'，正確格式：HH:MM:SS-HH:MM:SS")
                    continue

                parts = time_range.split('-')
                if len(parts) != 2:
                    self.logger.warning(f"時間格式錯誤，'-' 數量不正確：'{time_range}'")
                    continue

                start_str, end_str = parts[0].strip(), parts[1].strip()

                if not start_str:
                    self.logger.warning(f"時間格式錯誤，缺少起始時間：'{time_range}'")
                    continue

                if not end_str:
                    self.logger.warning(f"時間格式錯誤，缺少結束時間：'{time_range}'")
                    continue

                start_time = self._parse_time(start_str)
                end_time = self._parse_time(end_str)

                if start_time and end_time:
                    times.append((start_time, end_time))
                    self.logger.debug(f"成功解析時間段：{start_time} - {end_time}")
                else:
                    self.logger.warning(f"時間格式錯誤：'{time_range}'，請使用 HH:MM:SS 格式")

        except Exception as e:
            self.logger.error(f"解析時間表失敗: {str(e)}")
        
        return times
    
    def _parse_time(self, time_str: str) -> Optional[time]:
        """解析時間字串"""
        try:
            time_parts = time_str.split(':')
            if len(time_parts) >= 2:
                hour = int(time_parts[0])
                minute = int(time_parts[1])
                second = int(time_parts[2]) if len(time_parts) > 2 else 0
                return time(hour, minute, second)
        except Exception as e:
            self.logger.error(f"解析時間失敗 '{time_str}': {str(e)}")
        return None

    def start(self):
        """啟動自動連線管理器"""
        try:
            self.logger.info("自動連線管理器已啟動")
            self.status_changed.emit("自動管理器已啟動")

            # 根據設定啟動相應功能
            if self.enable_auto_connect:
                self._start_auto_connect()

            if self.enable_auto_shutdown:
                self.shutdown_timer.start(1000)
                self.logger.info(f"自動結束檢查已啟動，結束時間: {self.shutdown_time_str}")

        except Exception as e:
            self.logger.error(f"啟動自動連線管理器失敗: {str(e)}")

    def stop(self):
        """停止自動連線管理器"""
        try:
            # 停止所有定時器
            self.auto_connect_timer.stop()
            self.schedule_timer.stop()
            self.shutdown_timer.stop()

            self.logger.info("自動連線管理器已停止")
            self.status_changed.emit("自動管理器已停止")

        except Exception as e:
            self.logger.error(f"停止自動連線管理器失敗: {str(e)}")

    def _start_auto_connect(self):
        """啟動自動連線"""
        try:
            if self.auto_connect_mode == 'immediate':
                # 立即連線
                self.logger.info("立即執行自動連線")
                self.auto_connect_requested.emit()
                self._notify("自動連線", "立即連線已執行")

            elif self.auto_connect_mode == 'delay':
                # 延遲連線
                delay_ms = int(self.auto_connect_delay * 1000)
                self.auto_connect_timer.start(delay_ms)
                self.logger.info(f"延遲 {self.auto_connect_delay} 秒後自動連線")
                self._notify("自動連線", f"將在 {self.auto_connect_delay} 秒後自動連線")

            elif self.auto_connect_mode == 'schedule':
                # 時間表連線
                if self.schedule_times:
                    self.schedule_timer.start(1000)  # 每秒檢查一次
                    self.logger.info("時間表自動連線已啟動")
                    self._notify("自動連線", "時間表連線已啟動")
                else:
                    self.logger.warning("時間表模式已啟用但未設定時間表")

        except Exception as e:
            self.logger.error(f"啟動自動連線失敗: {str(e)}")

    def _handle_auto_connect(self):
        """處理自動連線定時器觸發"""
        try:
            self.auto_connect_timer.stop()
            self.logger.info("執行延遲自動連線")
            self.auto_connect_requested.emit()
            self._notify("自動連線", "延遲連線已執行")

        except Exception as e:
            self.logger.error(f"處理自動連線失敗: {str(e)}")

    def _check_schedule(self):
        """檢查時間表"""
        try:
            if not self.schedule_times:
                return

            current_time = datetime.now().time()
            current_weekday = datetime.now().weekday()

            # 檢查是否在連線時間內
            should_connect = self._should_connect_at_time(current_time)

            if should_connect and self.current_schedule_state != 'connected':
                # 檢查是否防止週末起始連線
                if self.prevent_weekend_startup and current_weekday >= 5:  # 5=Saturday, 6=Sunday
                    self.logger.info(f"週末時間，防止自動連線起始: {current_time}")
                    return

                self.logger.info(f"時間表自動連線: {current_time}")
                self.auto_connect_requested.emit()
                self.current_schedule_state = 'connected'
                self._notify("自動連線", f"時間表連線已執行: {current_time}")

            elif not should_connect and self.current_schedule_state == 'connected':
                # 根據設定選擇斷線行為
                if self.schedule_end_action == 'unadvise_only':
                    self.logger.info(f"時間表自動取消訂閱 (僅unadvise): {current_time}")
                    self.auto_unadvise_only_requested.emit()
                    self._notify("自動取消訂閱", f"時間表取消訂閱已執行: {current_time}")
                else:
                    self.logger.info(f"時間表自動斷線 (disconnect): {current_time}")
                    self.auto_disconnect_requested.emit()
                    self._notify("自動斷線", f"時間表斷線已執行: {current_time}")

                self.current_schedule_state = 'disconnected'

        except Exception as e:
            self.logger.error(f"檢查時間表失敗: {str(e)}")

    def _should_connect_at_time(self, current_time: time) -> bool:
        """檢查當前時間是否應該連線"""
        for start_time, end_time in self.schedule_times:
            if start_time <= end_time:
                # 同一天的時間範圍
                if start_time <= current_time <= end_time:
                    return True
            else:
                # 跨日的時間範圍 (永遠支援)
                if current_time >= start_time or current_time <= end_time:
                    return True
        return False

    def _check_auto_shutdown(self):
        """檢查自動結束"""
        if not self.shutdown_time:
            return

        current_datetime = datetime.now()

        # 計算目標結束時間
        target_datetime = datetime.combine(current_datetime.date(), self.shutdown_time)

        # 計算警告開始時間 (結束時間前 warning_seconds)
        warning_start_datetime = target_datetime - timedelta(seconds=self.shutdown_warning_seconds)

        # 計算緩衝時間範圍 (結束時間前後 buffer_seconds)
        buffer_start_datetime = target_datetime - timedelta(seconds=self.shutdown_buffer_seconds)
        buffer_end_datetime = target_datetime + timedelta(seconds=self.shutdown_buffer_seconds)

        self.logger.debug(f"自動結束檢查: 當前={current_datetime.strftime('%H:%M:%S')}, 目標={target_datetime.strftime('%H:%M:%S')}, 警告開始={warning_start_datetime.strftime('%H:%M:%S')}, 緩衝範圍={buffer_start_datetime.strftime('%H:%M:%S')}~{buffer_end_datetime.strftime('%H:%M:%S')}")

        # 檢查是否在緩衝時間範圍內
        if buffer_start_datetime <= current_datetime <= buffer_end_datetime:
            # 在緩衝時間內
            if current_datetime >= warning_start_datetime:
                # 到達警告時間
                if not self.shutdown_warning_shown:
                    # 首次顯示警告
                    remaining_seconds = (target_datetime - current_datetime).total_seconds()
                    self.logger.info(f"顯示自動結束警告，距離結束時間還有: {remaining_seconds:.1f} 秒")
                    self._show_shutdown_warning()
                    self.shutdown_warning_time = current_datetime
                elif current_datetime >= target_datetime:
                    # 已過結束時間，執行結束
                    elapsed_seconds = (current_datetime - target_datetime).total_seconds()
                    self.logger.info(f"已過結束時間 {elapsed_seconds:.1f} 秒，執行自動結束")
                    self._execute_auto_shutdown()
        else:
            # 不在緩衝時間內，重置狀態
            if self.shutdown_warning_shown:
                self.logger.info("超出緩衝時間範圍，重置自動結束狀態")
                self.shutdown_warning_shown = False

    def _show_shutdown_warning(self):
        """顯示結束警告"""
        if self.shutdown_warning_shown:
            return

        self.shutdown_warning_shown = True
        warning_msg = f"程式將在 {self.shutdown_warning_seconds} 秒後自動結束"
        self.logger.warning(warning_msg)
        self._notify("自動結束警告", warning_msg)

    def _execute_auto_shutdown(self):
        """執行自動結束"""
        self.logger.info("執行自動結束程式")
        self._notify("自動結束", "程式即將自動結束")
        self.auto_shutdown_requested.emit()

    def _notify(self, title: str, message: str):
        """發送通知"""
        if not self.enable_notifications:
            return

        try:
            # 這裡可以實現系統通知
            # 目前只記錄到日誌
            self.logger.info(f"[通知] {title}: {message}")
            self.status_changed.emit(f"{title}: {message}")
        except Exception as e:
            self.logger.error(f"發送通知失敗: {str(e)}")
