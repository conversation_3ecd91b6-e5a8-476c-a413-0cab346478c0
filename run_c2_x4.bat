wt -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "python dde_monitor_multi.py" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "Py_RunMe.bat config_FITXN07_01.ini" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "Py_RunMe.bat config_FITXN07_02.ini" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "Py_RunMe.bat config_FITXN07_03.ini" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "Py_RunMe.bat config_FITXN07_04.ini" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "python dde_engine_modular_test.py -c config_FITXN07_01_engine.ini" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "python dde_engine_modular_test.py -c config_FITXN07_02_engine.ini" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "python dde_engine_modular_test.py -c config_FITXN07_03_engine.ini" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "python dde_engine_modular_test.py -c config_FITXN07_04_engine.ini" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "python dde_monitor_v6.py"