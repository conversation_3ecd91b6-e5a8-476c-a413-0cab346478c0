#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE Actor System - GUI演示版本

使用模擬數據的GUI演示版本，不需要真實的DDE連接
"""

import asyncio
import argparse
import logging
import sys
import threading
import time
import random
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import QThread, Signal, QTimer
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    print("PySide6 不可用，請安裝 PySide6: pip install PySide6")
    sys.exit(1)

from gui.main_window import DDEMonitorMainWindow
from core.message_system import MessageRouter, create_dde_data_message
from actors.data_processor import DataProcessorActor
from actors.file_writer import FileWriterActor
from utils.config_manager import ConfigManager


class MockDDEDataGenerator(QThread):
    """模擬DDE數據生成器"""
    
    data_generated = Signal(str, str, str)  # item, value, source
    
    def __init__(self):
        super().__init__()
        self.running = False
        self.items = [
            "FITXN07.tick.成交價",
            "FITXN07.tick.成交量", 
            "FITXN08.tick.成交價",
            "FITXN08.tick.成交量",
            "FITXN09.tick.成交價",
            "FITXN09.tick.成交量"
        ]
        self.base_prices = {
            "FITXN07.tick.成交價": 15000,
            "FITXN08.tick.成交價": 15100,
            "FITXN09.tick.成交價": 15200
        }
        
    def run(self):
        """運行數據生成"""
        self.running = True
        counter = 0
        
        while self.running:
            try:
                # 隨機選擇一個項目
                item = random.choice(self.items)
                
                if "成交價" in item:
                    # 生成價格數據
                    base_price = self.base_prices.get(item, 15000)
                    price_change = random.randint(-50, 50)
                    value = str(base_price + price_change)
                else:
                    # 生成成交量數據
                    value = str(random.randint(1, 100))
                
                # 發送數據
                self.data_generated.emit(item, value, "MockDDE")
                
                counter += 1
                
                # 控制生成頻率 (每秒約10筆)
                self.msleep(100)
                
            except Exception as e:
                print(f"模擬數據生成錯誤: {str(e)}")
                self.msleep(1000)
    
    def stop(self):
        """停止生成"""
        self.running = False


class DemoSystemController(QThread):
    """演示系統控制器"""
    
    status_changed = Signal(str)
    log_message = Signal(str)
    metrics_updated = Signal(dict)
    actors_updated = Signal(list)
    
    def __init__(self, config_file: str):
        super().__init__()
        self.config_file = config_file
        self.running = False
        self.start_time = 0
        
        # 組件
        self.message_router = None
        self.data_processor = None
        self.file_writer = None
        self.mock_generator = None
        
        # 統計
        self.message_count = 0
        self.error_count = 0
        
    def run(self):
        """運行演示系統"""
        try:
            self.log_message.emit("正在初始化演示系統...")
            self.status_changed.emit("初始化中")
            
            # 加載配置
            config_manager = ConfigManager(self.config_file)
            if not config_manager.load_config():
                self.log_message.emit("配置加載失敗")
                self.status_changed.emit("初始化失敗")
                return
            
            self.log_message.emit("配置加載成功")
            
            # 創建事件循環
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 初始化組件
            success = loop.run_until_complete(self._initialize_components())
            if not success:
                self.log_message.emit("組件初始化失敗")
                self.status_changed.emit("初始化失敗")
                return
            
            self.log_message.emit("組件初始化成功")
            
            # 啟動系統
            self.log_message.emit("正在啟動演示系統...")
            self.status_changed.emit("啟動中")
            
            success = loop.run_until_complete(self._start_components())
            if not success:
                self.log_message.emit("系統啟動失敗")
                self.status_changed.emit("啟動失敗")
                return
            
            self.running = True
            self.start_time = time.time()
            self.log_message.emit("演示系統啟動成功")
            self.status_changed.emit("運行中")
            
            # 啟動模擬數據生成器
            self.mock_generator = MockDDEDataGenerator()
            self.mock_generator.data_generated.connect(self._handle_mock_data)
            self.mock_generator.start()
            
            # 啟動監控定時器
            self.monitor_timer = QTimer()
            self.monitor_timer.timeout.connect(self.update_metrics)
            self.monitor_timer.start(1000)  # 每秒更新
            
            # 運行事件循環
            loop.run_forever()
            
        except Exception as e:
            self.log_message.emit(f"系統運行錯誤: {str(e)}")
            self.status_changed.emit("錯誤")
        finally:
            self.running = False
            self.status_changed.emit("已停止")
            if hasattr(self, 'monitor_timer'):
                self.monitor_timer.stop()
            if self.mock_generator:
                self.mock_generator.stop()
                self.mock_generator.wait()
    
    async def _initialize_components(self):
        """初始化組件"""
        try:
            # 創建消息路由
            self.message_router = MessageRouter()
            
            # 創建數據處理Actor
            processor_config = {
                'batch_size': 10,
                'queue_size': 100,
                'backpressure': {
                    'high_watermark': 80,
                    'low_watermark': 60,
                    'strategy': 'drop_oldest'
                }
            }
            self.data_processor = DataProcessorActor('DataProcessor', processor_config)
            self.message_router.register_actor(self.data_processor)
            
            # 創建文件寫入Actor
            file_config = {
                'batch_size': 10,
                'flush_interval': 2.0,
                'files': {
                    'default': {
                        'filename': 'outputs/gui_demo_data.csv',
                        'format': 'csv',
                        'max_size_mb': 5,
                        'compress': False
                    }
                }
            }
            self.file_writer = FileWriterActor('FileWriter', file_config)
            self.message_router.register_actor(self.file_writer)
            
            return True
            
        except Exception as e:
            print(f"組件初始化失敗: {str(e)}")
            return False
    
    async def _start_components(self):
        """啟動組件"""
        try:
            await self.data_processor.start()
            await self.file_writer.start()
            return True
        except Exception as e:
            print(f"組件啟動失敗: {str(e)}")
            return False
    
    def _handle_mock_data(self, item: str, value: str, source: str):
        """處理模擬數據"""
        if not self.running or not self.message_router:
            return
        
        try:
            # 創建DDE數據消息
            message = create_dde_data_message(item, value, source)
            
            # 異步發送消息
            loop = asyncio.get_event_loop()
            if loop and not loop.is_closed():
                asyncio.run_coroutine_threadsafe(
                    self.message_router.send_message("DataProcessor", message),
                    loop
                )
                
                self.message_count += 1
                
        except Exception as e:
            self.error_count += 1
            print(f"處理模擬數據錯誤: {str(e)}")
    
    def stop_system(self):
        """停止系統"""
        if self.running:
            self.log_message.emit("正在停止演示系統...")
            self.status_changed.emit("停止中")
            
            # 停止模擬數據生成器
            if self.mock_generator:
                self.mock_generator.stop()
            
            # 停止事件循環
            loop = asyncio.get_event_loop()
            if loop and not loop.is_closed():
                loop.call_soon_threadsafe(loop.stop)
    
    def update_metrics(self):
        """更新性能指標"""
        if not self.running:
            return
        
        try:
            uptime = time.time() - self.start_time if self.start_time > 0 else 0
            
            # 計算吞吐量
            throughput = self.message_count / uptime if uptime > 0 else 0
            error_rate = (self.error_count / max(self.message_count, 1)) * 100
            
            metrics = {
                'uptime': uptime,
                'memory_mb': 35.0 + random.uniform(-5, 5),
                'cpu_percent': 8.0 + random.uniform(-3, 7),
                'throughput': throughput,
                'latency_ms': 1.2 + random.uniform(-0.5, 0.8),
                'error_rate': error_rate
            }
            
            self.metrics_updated.emit(metrics)
            
            # 更新Actor狀態
            actors = [
                {'name': 'DDEReceiver', 'status': 'running', 'messages': self.message_count, 'errors': 0},
                {'name': 'DataProcessor', 'status': 'running', 'messages': self.message_count, 'errors': self.error_count},
                {'name': 'GUIUpdater', 'status': 'running', 'messages': self.message_count, 'errors': 0},
                {'name': 'FileWriter', 'status': 'running', 'messages': self.message_count, 'errors': 0}
            ]
            
            self.actors_updated.emit(actors)
            
        except Exception as e:
            print(f"更新指標錯誤: {str(e)}")


class DDEGUIDemoApplication:
    """DDE GUI演示應用程序"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.app = None
        self.window = None
        self.controller_thread = None
        
    def run(self):
        """運行GUI演示應用程序"""
        try:
            # 創建Qt應用程序
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("DDE Actor System Demo")
            self.app.setApplicationVersion("1.0.0")
            
            # 創建主窗口
            self.window = DDEMonitorMainWindow()
            self.window.setWindowTitle("DDE Actor System Demo - 模擬數據演示")
            
            # 創建演示系統控制器
            self.controller_thread = DemoSystemController(self.config_file)
            
            # 連接信號
            self._connect_signals()
            
            # 設置窗口控制器
            self.window.set_system_controller(self.controller_thread)
            
            # 顯示窗口
            self.window.show()
            
            # 顯示歡迎消息
            self._show_welcome_message()
            
            # 運行應用程序
            return self.app.exec()
            
        except Exception as e:
            print(f"GUI演示應用程序運行失敗: {str(e)}")
            return 1
    
    def _connect_signals(self):
        """連接信號槽"""
        # 系統狀態信號
        self.controller_thread.status_changed.connect(
            self.window.monitor_widget.update_status
        )
        
        # 日誌消息信號
        self.controller_thread.log_message.connect(
            self.window.log_widget.add_log
        )
        
        # 性能指標信號
        self.controller_thread.metrics_updated.connect(
            self.window.monitor_widget.update_metrics
        )
        
        # Actor狀態信號
        self.controller_thread.actors_updated.connect(
            self.window.actor_widget.update_actors
        )
        
        # 窗口控制信號
        self.window.start_button.clicked.connect(self._start_system)
        self.window.stop_button.clicked.connect(self._stop_system)
        self.window.restart_button.clicked.connect(self._restart_system)
    
    def _start_system(self):
        """啟動系統"""
        if not self.controller_thread.isRunning():
            self.controller_thread.start()
            self.window.start_button.setEnabled(False)
            self.window.stop_button.setEnabled(True)
            self.window.restart_button.setEnabled(True)
    
    def _stop_system(self):
        """停止系統"""
        if self.controller_thread.isRunning():
            self.controller_thread.stop_system()
            self.controller_thread.wait(5000)  # 等待5秒
            
            self.window.start_button.setEnabled(True)
            self.window.stop_button.setEnabled(False)
            self.window.restart_button.setEnabled(False)
    
    def _restart_system(self):
        """重啟系統"""
        self._stop_system()
        QTimer.singleShot(2000, self._start_system)  # 2秒後重啟
    
    def _show_welcome_message(self):
        """顯示歡迎消息"""
        self.window.log_widget.add_log("歡迎使用 DDE Actor System Demo")
        self.window.log_widget.add_log("這是一個使用模擬數據的演示版本")
        self.window.log_widget.add_log(f"配置文件: {self.config_file}")
        self.window.log_widget.add_log("點擊 '啟動系統' 開始演示")


def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='DDE Actor System GUI Demo')
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/gui_test_config.json',
        help='配置文件路径'
    )
    
    return parser.parse_args()


def main():
    """主函數"""
    if not GUI_AVAILABLE:
        print("錯誤: PySide6 不可用")
        print("請安裝 PySide6: pip install PySide6")
        return 1
    
    args = parse_arguments()
    
    # 設置基本日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    
    print("🚀 DDE Actor System GUI Demo")
    print("使用模擬數據演示系統功能")
    print("=" * 50)
    
    # 創建並運行GUI演示應用程序
    demo_app = DDEGUIDemoApplication(args.config)
    return demo_app.run()


if __name__ == "__main__":
    sys.exit(main())
