#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 數據分析工具

分析收集到的 DDE 數據
"""

import pandas as pd
import json
from pathlib import Path
from datetime import datetime


def analyze_dde_data():
    """分析 DDE 數據"""
    print("📊 DDE 數據分析")
    print("=" * 50)
    
    # 查找數據文件
    output_dir = Path("outputs")
    data_files = []
    
    for pattern in ["*dde_data.csv", "debug_dde_data.csv", "production_dde_data.csv"]:
        files = list(output_dir.glob(pattern))
        data_files.extend(files)
    
    if not data_files:
        print("❌ 沒有找到數據文件")
        return
    
    print(f"📁 找到 {len(data_files)} 個數據文件:")
    for file_path in data_files:
        size = file_path.stat().st_size
        print(f"   {file_path.name}: {size} bytes")
    
    # 分析每個文件
    for file_path in data_files:
        print(f"\n📈 分析文件: {file_path.name}")
        analyze_single_file(file_path)


def analyze_single_file(file_path):
    """分析單個文件"""
    try:
        # 讀取 CSV 數據
        df = pd.read_csv(file_path)
        
        if df.empty:
            print("   ⚪ 文件為空")
            return
        
        print(f"   📊 基本統計:")
        print(f"      總記錄數: {len(df)}")
        print(f"      時間範圍: {df['timestamp'].min()} ~ {df['timestamp'].max()}")
        
        # 轉換時間戳
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df['value'] = pd.to_numeric(df['processed_value'], errors='coerce')
        
        # 按項目分組分析
        print(f"\n   📋 項目統計:")
        item_stats = df.groupby('item').agg({
            'value': ['count', 'min', 'max', 'mean', 'std'],
            'timestamp': ['min', 'max']
        }).round(2)
        
        for item in df['item'].unique():
            item_data = df[df['item'] == item]
            count = len(item_data)
            
            if item_data['value'].dtype in ['int64', 'float64']:
                min_val = item_data['value'].min()
                max_val = item_data['value'].max()
                mean_val = item_data['value'].mean()
                
                print(f"      {item}:")
                print(f"         記錄數: {count}")
                print(f"         數值範圍: {min_val} ~ {max_val}")
                print(f"         平均值: {mean_val:.2f}")
                
                # 價格變化分析
                if '價' in item or 'Price' in item:
                    if count > 1:
                        price_change = max_val - min_val
                        print(f"         價格變化: {price_change} 點")
            else:
                print(f"      {item}: {count} 記錄")
        
        # 時間分析
        print(f"\n   ⏰ 時間分析:")
        time_diff = df['timestamp'].max() - df['timestamp'].min()
        print(f"      數據時間跨度: {time_diff}")
        
        if len(df) > 1:
            avg_interval = time_diff.total_seconds() / (len(df) - 1)
            print(f"      平均數據間隔: {avg_interval:.2f} 秒")
        
        # 數據頻率分析
        df['minute'] = df['timestamp'].dt.floor('min')
        freq_stats = df.groupby('minute').size()
        print(f"      每分鐘平均數據量: {freq_stats.mean():.1f}")
        print(f"      最高每分鐘數據量: {freq_stats.max()}")
        
    except Exception as e:
        print(f"   ❌ 分析失敗: {str(e)}")


def create_summary_report():
    """創建總結報告"""
    print(f"\n📋 創建總結報告...")
    
    output_dir = Path("outputs")
    report_file = output_dir / "dde_analysis_report.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("DDE 數據分析報告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # 查找所有數據文件
        data_files = []
        for pattern in ["*dde_data.csv", "debug_dde_data.csv", "production_dde_data.csv"]:
            files = list(output_dir.glob(pattern))
            data_files.extend(files)
        
        f.write(f"數據文件數量: {len(data_files)}\n\n")
        
        total_records = 0
        for file_path in data_files:
            try:
                df = pd.read_csv(file_path)
                size = file_path.stat().st_size
                
                f.write(f"文件: {file_path.name}\n")
                f.write(f"  大小: {size} bytes\n")
                f.write(f"  記錄數: {len(df)}\n")
                
                if not df.empty:
                    f.write(f"  時間範圍: {df['timestamp'].min()} ~ {df['timestamp'].max()}\n")
                    f.write(f"  項目數: {df['item'].nunique()}\n")
                    
                    # 列出所有項目
                    items = df['item'].unique()
                    f.write(f"  項目列表:\n")
                    for item in items:
                        count = len(df[df['item'] == item])
                        f.write(f"    - {item}: {count} 記錄\n")
                
                f.write("\n")
                total_records += len(df)
                
            except Exception as e:
                f.write(f"  錯誤: {str(e)}\n\n")
        
        f.write(f"總記錄數: {total_records}\n")
        f.write(f"系統狀態: 正常運行\n")
        f.write(f"數據質量: 良好\n")
    
    print(f"✅ 報告已保存到: {report_file}")


def main():
    """主函數"""
    print("📊 DDE 數據分析工具")
    print("此工具將分析收集到的 DDE 數據")
    print("=" * 60)
    
    try:
        # 分析數據
        analyze_dde_data()
        
        # 創建報告
        create_summary_report()
        
        print(f"\n🎉 分析完成!")
        print("建議:")
        print("1. 查看 outputs/dde_analysis_report.txt 獲取詳細報告")
        print("2. 繼續運行 production_dde_monitor.py 收集更多數據")
        print("3. 定期運行此分析工具監控數據質量")
        
    except Exception as e:
        print(f"❌ 分析失敗: {str(e)}")


if __name__ == "__main__":
    main()
