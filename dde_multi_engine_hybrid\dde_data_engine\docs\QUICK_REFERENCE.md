# DDE 引擎版快速參考指南

## 快速開始

### 基本使用
```python
from dde_data_engine import DDEDataProcessor, DataProcessorConfig, ItemData

# 1. 創建項目資料
items_data = {
    "PRICE": ItemData(name="價格", code="PRICE"),
    "VOLUME": ItemData(name="總量", code="VOLUME")
}

# 2. 創建配置
config = DataProcessorConfig({
    'enable_value_change_check': True,
    'value_change_check_mode': 'single',
    'value_change_check_items': '總量'
})

# 3. 創建處理器
processor = DDEDataProcessor(config=config, items_data=items_data)

# 4. 處理資料
processor.process_dde_data("PRICE", "100.0")
processor.process_dde_data("VOLUME", "1000")
```

## 核心API參考

### DDEDataProcessor 主要方法

#### 資料處理
```python
# 處理DDE資料 (主要入口)
process_dde_data(item: str, value: str) -> bool

# 檢查時間間隔
check_time_interval() -> bool

# 設置初始值
set_initial_values(initial_values: Dict[str, str])

# 獲取當前資料
get_current_data() -> Dict[str, str]
```

#### 統計和狀態
```python
# 獲取統計資訊
get_stats() -> ProcessorStats

# 重置統計資訊
reset_stats()
```

#### 回調設定
```python
# 資料接收回調
processor.on_data_received = lambda item, value: print(f"{item}={value}")

# 資料行保存回調
processor.on_row_saved = lambda row: print("資料行已保存")

# 資料行跳過回調
processor.on_row_skipped = lambda row: print("資料行已跳過")
```

### DataFileHandler 檔案處理

#### 初始化
```python
from dde_data_engine import DataFileHandler

file_handler = DataFileHandler()
file_handler.init_file_paths(
    log_file="./logs/dde.log",
    complete_data_file="./data/complete.csv"
)
file_handler.set_items_data(items_data)
```

#### 檔案操作
```python
# 保存資料行
file_handler.save_row(row, is_complete=True)

# 初始化檔案標題
file_handler.init_file_headers()

# 清空所有檔案
file_handler.clear_files()

# 獲取檔案資訊
info = file_handler.get_file_info()
```

### DataProcessorConfig 配置管理

#### 配置創建
```python
# 使用字典創建
config = DataProcessorConfig({
    'enable_time_newline': True,
    'time_newline_interval': 0.8,
    'enable_value_change_check': True,
    'value_change_check_mode': 'single'
})

# 動態設定
config.set('value_change_check_items', '總量')
```

#### 配置存取
```python
# 基本存取
value = config.get('key', default_value)

# 類型安全存取
bool_value = config.get_bool('enable_time_newline', True)
float_value = config.get_float('time_newline_interval', 0.8)
int_value = config.get_int('max_history_rows', 10)
list_value = config.get_list('check_items', separator=',')
```

#### 配置驗證
```python
errors = config.validate()
if errors:
    print("配置錯誤:", errors)
```

## 配置參數參考

### 時間間隔設定
```python
'enable_time_newline': True,        # 啟用時間間隔換行
'time_newline_interval': 0.800,     # 時間間隔(秒)
```

### 值變化檢查設定
```python
'enable_value_change_check': True,           # 啟用值變化檢查
'value_change_check_mode': 'single',         # 檢查模式: single/multiple/all
'value_change_check_items': '總量',          # 檢查項目(依模式而定)
```

### 檔案輸出設定
```python
'enable_data_file': False,           # 啟用逐項資料檔案
'enable_complete_data_file': True,   # 啟用完整資料檔案
'enable_log_file': True,             # 啟用日誌檔案
```

### 資料處理設定
```python
'max_history_rows': 10,              # 最大歷史記錄數
'max_raw_data_queue': 1000,          # 最大原始資料佇列大小
```

## 值變化檢查模式

### single - 單項檢查
```python
config = DataProcessorConfig({
    'value_change_check_mode': 'single',
    'value_change_check_items': '總量'    # 指定單一項目名稱
})
```
**行為**: 只檢查指定項目的值變化

### multiple - 多項檢查
```python
config = DataProcessorConfig({
    'value_change_check_mode': 'multiple',
    'value_change_check_items': '價格,總量,買價'  # 逗號分隔多個項目
})
```
**行為**: 檢查指定多個項目，任一項目變化即觸發

### all - 全部檢查
```python
config = DataProcessorConfig({
    'value_change_check_mode': 'all',
    'value_change_check_items': ''      # 不需要指定項目
})
```
**行為**: 檢查所有DDE項目，任一項目變化即觸發

## 常用模式範例

### 模式1: 基本資料處理
```python
# 最簡單的配置
config = DataProcessorConfig({
    'enable_value_change_check': False  # 不檢查值變化，所有資料都保存
})

processor = DDEDataProcessor(config=config, items_data=items_data)
```

### 模式2: 高頻資料過濾
```python
# 只在特定項目變化時保存
config = DataProcessorConfig({
    'enable_value_change_check': True,
    'value_change_check_mode': 'single',
    'value_change_check_items': '總量',
    'time_newline_interval': 1.0        # 較長的時間間隔
})
```

### 模式3: 完整資料監控
```python
# 監控所有項目變化
config = DataProcessorConfig({
    'enable_value_change_check': True,
    'value_change_check_mode': 'all',
    'time_newline_interval': 0.5        # 較短的時間間隔
})
```

### 模式4: 多項目重點監控
```python
# 監控多個重要項目
config = DataProcessorConfig({
    'enable_value_change_check': True,
    'value_change_check_mode': 'multiple',
    'value_change_check_items': '價格,總量,買價,賣價'
})
```

## 檔案輸出格式

### 完整資料檔案 (complete_data.csv)
```csv
接收日期,接收時間,價格,總量,買價,賣價
2024-01-01,09:30:00.123,100.0,1000,99.5,100.5
2024-01-01,09:30:01.456,100.5,1100,100.0,101.0
```

### 逐項資料檔案 (data.csv)
```csv
接收日期,接收時間,項目代碼,項目名稱,項目值
2024-01-01,09:30:00.123,PRICE,價格,100.0
2024-01-01,09:30:00.123,VOLUME,總量,1000
```

### 日誌檔案 (log.txt)
```
2024-01-01 09:30:00.123 - 完整資料行，項目數: 4
2024-01-01 09:30:01.456 - 完整資料行，項目數: 4
```

## 錯誤處理

### 常見錯誤和解決方案

#### 配置錯誤
```python
# 檢查配置是否有效
errors = config.validate()
if errors:
    for error in errors:
        print(f"配置錯誤: {error}")
```

#### 檔案權限錯誤
```python
# 確保輸出目錄存在且有寫入權限
import os
os.makedirs("./output", exist_ok=True)
```

#### 項目不存在錯誤
```python
# 確保檢查項目存在於items_data中
check_items = config.get('value_change_check_items', '')
for item_name in check_items.split(','):
    found = any(item.name == item_name.strip() for item in items_data.values())
    if not found:
        print(f"警告: 找不到檢查項目 '{item_name}'")
```

## 效能調優

### 記憶體最佳化
```python
# 限制歷史資料數量
config.set('max_raw_data_queue', 500)  # 減少記憶體使用

# 定期重置統計資訊
processor.reset_stats()
```

### I/O 最佳化
```python
# 只啟用必要的檔案輸出
config.set('enable_data_file', False)      # 關閉逐項檔案
config.set('enable_log_file', False)       # 關閉日誌檔案
config.set('enable_complete_data_file', True)  # 只保留完整檔案
```

### 處理最佳化
```python
# 調整時間間隔以平衡效能和資料完整性
config.set('time_newline_interval', 1.0)   # 較長間隔減少處理頻率
```

## 除錯技巧

### 啟用詳細日誌
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 或者設定特定的日誌記錄器
logger = logging.getLogger('dde_data_engine')
logger.setLevel(logging.DEBUG)
```

### 使用回調監控
```python
def debug_callback(item, value):
    print(f"[DEBUG] 接收資料: {item} = {value}")

def save_callback(row):
    print(f"[DEBUG] 保存資料行: {len(row.values)} 個項目")

def skip_callback(row):
    print(f"[DEBUG] 跳過資料行: 值未變化")

processor.on_data_received = debug_callback
processor.on_row_saved = save_callback
processor.on_row_skipped = skip_callback
```

### 檢查統計資訊
```python
stats = processor.get_stats()
print(f"接收: {stats.total_received}")
print(f"處理: {stats.total_processed}")
print(f"保存: {stats.total_saved}")
print(f"跳過: {stats.total_skipped}")
print(f"最後更新: {stats.last_update_time}")
```

## 常見問題 FAQ

### Q: 為什麼資料行被跳過？
A: 檢查值變化檢查設定，確保檢查項目正確且值確實有變化。

### Q: 如何提高處理效能？
A: 調整時間間隔、限制歷史資料數量、關閉不必要的檔案輸出。

### Q: 如何確保資料完整性？
A: 使用適當的值變化檢查模式，定期檢查統計資訊。

### Q: 如何處理大量資料？
A: 使用較大的佇列大小、批次處理、適當的記憶體管理。

---

*本快速參考指南涵蓋了 DDE 引擎版的主要使用方法和常見場景，適合日常開發參考使用。*
