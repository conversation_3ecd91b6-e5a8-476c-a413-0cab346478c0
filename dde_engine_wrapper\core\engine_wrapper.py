#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多商品DDE引擎包装器
提供统一的多商品DDE监控接口
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from enum import Enum

# 导入DDE模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dydde'))
try:
    from dydde import DDEClient
except ImportError as e:
    print(f"导入DDE客户端失败: {e}")
    # 创建占位符类
    class DDEClient:
        def __init__(self, *args, **kwargs):
            pass
        def connect(self):
            pass
        def disconnect(self):
            pass

# 导入核心模块
from config_manager import WrapperConfigManager, ProductConfig, EngineConfig
from engine_manager import DDEEngineManager, EngineState, EngineStats

class WrapperState(Enum):
    """包装器状态枚举"""
    IDLE = "idle"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"

@dataclass
class DDEConnection:
    """DDE连接信息"""
    product_symbol: str
    service: str
    topic: str
    client: Optional[DDEClient] = None
    connected: bool = False
    last_activity: float = 0.0
    callback_function: Optional[callable] = None

@dataclass
class WrapperStats:
    """包装器统计信息"""
    total_products: int = 0
    connected_products: int = 0
    total_data_received: int = 0
    total_data_processed: int = 0
    uptime: float = 0.0
    engine_stats: Optional[EngineStats] = None

class MultiProductDDEWrapper:
    """多商品DDE引擎包装器
    
    提供统一的多商品DDE监控接口，支持：
    - 多商品同时监控
    - 自动连接和重连
    - 数据处理和分发
    - 引擎生命周期管理
    - 性能监控和统计
    """
    
    def __init__(self, config_file: str = 'wrapper_config.ini'):
        self.logger = logging.getLogger(__name__)
        
        # 配置管理
        self.config_manager = WrapperConfigManager(config_file)
        
        # 引擎管理
        self.engine_manager: Optional[DDEEngineManager] = None
        
        # DDE连接管理
        self.dde_connections: Dict[str, DDEConnection] = {}
        self.connection_lock = threading.RLock()
        
        # 状态管理
        self.state = WrapperState.IDLE
        self.start_time = 0.0
        self.stats = WrapperStats()
        
        # 回调函数
        self.on_data_received: Optional[Callable[[str, str, str, str], None]] = None
        self.on_connection_changed: Optional[Callable[[str, bool], None]] = None
        self.on_error: Optional[Callable[[str, str], None]] = None
        
        # 线程管理
        self.main_thread: Optional[threading.Thread] = None
        self.shutdown_event = threading.Event()
        
        self.logger.info("多商品DDE包装器初始化完成")
    
    def initialize(self) -> bool:
        """初始化包装器"""
        try:
            self.logger.info("开始初始化DDE包装器")
            
            # 载入配置
            if not self.config_manager.load_config():
                self.logger.error("载入配置失败")
                return False
            
            # 创建引擎管理器
            engine_config = self.config_manager.engine_config
            self.engine_manager = DDEEngineManager(engine_config)
            
            # 启动引擎监控
            self.engine_manager.start_monitoring()
            
            # 初始化DDE连接
            self._initialize_dde_connections()
            
            # 为每个商品和数据类型创建引擎实例
            self._create_engine_instances()
            
            self.logger.info("DDE包装器初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化DDE包装器失败: {str(e)}")
            return False
    
    def start(self) -> bool:
        """启动包装器"""
        try:
            if self.state != WrapperState.IDLE:
                self.logger.warning(f"包装器状态不正确，无法启动: {self.state}")
                return False
            
            self.logger.info("开始启动DDE包装器")
            self.state = WrapperState.STARTING
            self.start_time = time.time()
            
            # 启动主线程
            self.main_thread = threading.Thread(
                target=self._main_loop,
                name="WrapperMain",
                daemon=True
            )
            self.main_thread.start()
            
            # 连接DDE服务
            self._connect_all_dde_services()
            
            # 启动所有引擎实例
            self._start_all_engines()
            
            self.state = WrapperState.RUNNING
            self.logger.info("DDE包装器启动完成")
            return True
            
        except Exception as e:
            self.logger.error(f"启动DDE包装器失败: {str(e)}")
            self.state = WrapperState.ERROR
            return False
    
    def stop(self) -> bool:
        """停止包装器"""
        try:
            if self.state not in [WrapperState.RUNNING, WrapperState.ERROR]:
                self.logger.warning(f"包装器状态不正确，无法停止: {self.state}")
                return False
            
            self.logger.info("开始停止DDE包装器")
            self.state = WrapperState.STOPPING
            
            # 设置关闭事件
            self.shutdown_event.set()
            
            # 断开所有DDE连接
            self._disconnect_all_dde_services()
            
            # 停止引擎管理器
            if self.engine_manager:
                self.engine_manager.shutdown()
            
            # 等待主线程结束
            if self.main_thread and self.main_thread.is_alive():
                self.main_thread.join(timeout=10.0)
            
            self.state = WrapperState.STOPPED
            self.logger.info("DDE包装器已停止")
            return True
            
        except Exception as e:
            self.logger.error(f"停止DDE包装器失败: {str(e)}")
            return False
    
    def get_state(self) -> WrapperState:
        """获取包装器状态"""
        return self.state
    
    def get_stats(self) -> WrapperStats:
        """获取统计信息"""
        try:
            # 更新统计信息
            self.stats.uptime = time.time() - self.start_time if self.start_time > 0 else 0
            self.stats.total_products = len(self.config_manager.products)
            
            with self.connection_lock:
                self.stats.connected_products = sum(1 for conn in self.dde_connections.values() 
                                                  if conn.connected)
            
            if self.engine_manager:
                self.stats.engine_stats = self.engine_manager.get_stats()
            
            return self.stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            return self.stats
    
    def get_product_status(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取商品状态"""
        try:
            with self.connection_lock:
                if symbol not in self.dde_connections:
                    return None
                
                conn = self.dde_connections[symbol]
                return {
                    'symbol': symbol,
                    'connected': conn.connected,
                    'service': conn.service,
                    'topic': conn.topic,
                    'last_activity': conn.last_activity
                }
                
        except Exception as e:
            self.logger.error(f"获取商品状态失败: {str(e)}")
            return None
    
    def connect_product(self, symbol: str) -> bool:
        """连接指定商品"""
        try:
            with self.connection_lock:
                if symbol not in self.dde_connections:
                    self.logger.error(f"商品连接不存在: {symbol}")
                    return False
                
                conn = self.dde_connections[symbol]
                if conn.connected:
                    self.logger.warning(f"商品已连接: {symbol}")
                    return True
                
                return self._connect_dde_service(conn)
                
        except Exception as e:
            self.logger.error(f"连接商品失败: {symbol}, {str(e)}")
            return False
    
    def disconnect_product(self, symbol: str) -> bool:
        """断开指定商品"""
        try:
            with self.connection_lock:
                if symbol not in self.dde_connections:
                    self.logger.error(f"商品连接不存在: {symbol}")
                    return False
                
                conn = self.dde_connections[symbol]
                if not conn.connected:
                    self.logger.warning(f"商品未连接: {symbol}")
                    return True
                
                return self._disconnect_dde_service(conn)
                
        except Exception as e:
            self.logger.error(f"断开商品失败: {symbol}, {str(e)}")
            return False
    
    def process_dde_data(self, symbol: str, data_type: str, item: str, value: str):
        """处理DDE数据"""
        try:
            # 添加调试日志
            self.logger.info(f"[DEBUG] 收到DDE数据: {symbol}.{data_type} {item}={value}")

            # 更新统计信息
            self.stats.total_data_received += 1

            # 查找对应的引擎实例
            if self.engine_manager:
                engines = self.engine_manager.get_all_engines()
                for engine_id, engine in engines.items():
                    if (engine.product_symbol == symbol and
                        engine.data_type == data_type and
                        engine.processor):

                        # 处理数据
                        engine.processor.process_dde_data(item, value)
                        self.stats.total_data_processed += 1
                        break

            # 触发回调
            if self.on_data_received:
                self.logger.info(f"[DEBUG] 触发GUI回调: {symbol}.{data_type} {item}={value}")
                self.on_data_received(symbol, data_type, item, value)

        except Exception as e:
            self.logger.error(f"处理DDE数据失败: {symbol}, {data_type}, {item}, {str(e)}")
            if self.on_error:
                self.on_error(f"数据处理错误", str(e))

    def _initialize_dde_connections(self):
        """初始化DDE连接"""
        try:
            products = self.config_manager.get_all_products()

            for symbol, product_config in products.items():
                connection = DDEConnection(
                    product_symbol=symbol,
                    service=product_config.dde_service,
                    topic=product_config.dde_topic
                )

                with self.connection_lock:
                    self.dde_connections[symbol] = connection

                self.logger.info(f"初始化DDE连接: {symbol}")

        except Exception as e:
            self.logger.error(f"初始化DDE连接失败: {str(e)}")

    def _create_engine_instances(self):
        """创建引擎实例"""
        try:
            if not self.engine_manager:
                self.logger.error("引擎管理器未初始化")
                return

            products = self.config_manager.get_all_products()

            for symbol, product_config in products.items():
                for data_type in product_config.data_types:
                    engine_id = self.engine_manager.create_engine(product_config, data_type)
                    if engine_id:
                        self.logger.info(f"创建引擎实例: {symbol}_{data_type} -> {engine_id}")
                    else:
                        self.logger.error(f"创建引擎实例失败: {symbol}_{data_type}")

        except Exception as e:
            self.logger.error(f"创建引擎实例失败: {str(e)}")

    def _connect_all_dde_services(self):
        """连接所有DDE服务"""
        try:
            with self.connection_lock:
                connections = list(self.dde_connections.values())

            for connection in connections:
                self._connect_dde_service(connection)

        except Exception as e:
            self.logger.error(f"连接所有DDE服务失败: {str(e)}")

    def _disconnect_all_dde_services(self):
        """断开所有DDE服务"""
        try:
            with self.connection_lock:
                connections = list(self.dde_connections.values())

            for connection in connections:
                self._disconnect_dde_service(connection)

        except Exception as e:
            self.logger.error(f"断开所有DDE服务失败: {str(e)}")

    def _connect_dde_service(self, connection: DDEConnection) -> bool:
        """连接DDE服务"""
        try:
            if connection.connected:
                return True

            # 创建DDE客户端
            connection.client = DDEClient(connection.service, connection.topic)

            # 设置回调函数
            def on_advise_data(item, value):
                # 解析数据类型（这里需要根据实际情况调整）
                data_type = self._parse_data_type_from_item(item)
                self.process_dde_data(connection.product_symbol, data_type, item, value)

            connection.client.on_advise_data = on_advise_data
            # 保存回调函数到连接对象中，供订阅时使用
            connection.callback_function = on_advise_data

            # 连接
            connection.client.connect()
            connection.connected = True
            connection.last_activity = time.time()

            self.logger.info(f"DDE服务连接成功: {connection.product_symbol}")

            # 订阅DDE项目
            self._subscribe_dde_items(connection)

            # 触发回调
            if self.on_connection_changed:
                self.on_connection_changed(connection.product_symbol, True)

            return True

        except Exception as e:
            self.logger.error(f"连接DDE服务失败: {connection.product_symbol}, {str(e)}")
            connection.connected = False
            if self.on_error:
                self.on_error(f"DDE连接错误", str(e))
            return False

    def _disconnect_dde_service(self, connection: DDEConnection) -> bool:
        """断开DDE服务"""
        try:
            if not connection.connected or not connection.client:
                return True

            # 断开连接
            connection.client.disconnect()
            connection.client = None
            connection.connected = False

            self.logger.info(f"DDE服务断开成功: {connection.product_symbol}")

            # 触发回调
            if self.on_connection_changed:
                self.on_connection_changed(connection.product_symbol, False)

            return True

        except Exception as e:
            self.logger.error(f"断开DDE服务失败: {connection.product_symbol}, {str(e)}")
            if self.on_error:
                self.on_error(f"DDE断开错误", str(e))
            return False

    def _start_all_engines(self):
        """启动所有引擎实例"""
        try:
            if not self.engine_manager:
                self.logger.error("引擎管理器未初始化")
                return

            engines = self.engine_manager.get_all_engines()

            for engine_id in engines.keys():
                if self.engine_manager.start_engine(engine_id):
                    self.logger.info(f"启动引擎实例: {engine_id}")
                else:
                    self.logger.error(f"启动引擎实例失败: {engine_id}")

        except Exception as e:
            self.logger.error(f"启动所有引擎实例失败: {str(e)}")

    def _parse_data_type_from_item(self, item: str) -> str:
        """从项目名称解析数据类型"""
        try:
            # 这里需要根据实际的项目命名规则来解析
            # 例如: FITXN07.TF-Price -> tick
            #      FITXN07.TF-BidQty1 -> order

            if 'Price' in item or 'Volume' in item:
                return 'tick'
            elif 'Bid' in item or 'Ask' in item:
                return 'order'
            elif 'Level' in item:
                return 'level2'
            else:
                return 'daily'

        except Exception as e:
            self.logger.error(f"解析数据类型失败: {item}, {str(e)}")
            return 'unknown'

    def _main_loop(self):
        """主循环"""
        try:
            self.logger.info("包装器主循环开始")

            while not self.shutdown_event.is_set() and self.state == WrapperState.RUNNING:
                try:
                    # 更新统计信息
                    self.get_stats()

                    # 检查连接状态
                    self._check_connection_health()

                    # 短暂休眠
                    time.sleep(1.0)

                except Exception as e:
                    self.logger.error(f"主循环异常: {str(e)}")
                    time.sleep(5.0)

            self.logger.info("包装器主循环结束")

        except Exception as e:
            self.logger.error(f"主循环失败: {str(e)}")
            self.state = WrapperState.ERROR

    def _check_connection_health(self):
        """检查连接健康状态"""
        try:
            current_time = time.time()

            with self.connection_lock:
                for connection in self.dde_connections.values():
                    if connection.connected:
                        # 检查连接活跃度（这里可以添加更复杂的健康检查）
                        if current_time - connection.last_activity > 300:  # 5分钟无活动
                            self.logger.warning(f"连接可能异常: {connection.product_symbol}")

        except Exception as e:
            self.logger.error(f"检查连接健康状态失败: {str(e)}")

    def _parse_data_type_from_item(self, item: str) -> str:
        """从DDE项目名称解析数据类型"""
        # 根据DDE项目名称解析数据类型
        item_lower = item.lower()

        # Tick数据项目
        if any(keyword in item_lower for keyword in ['time', 'price', 'volume', 'open', 'high', 'low', 'totalvolume']):
            return "tick"

        # Order数据项目
        elif any(keyword in item_lower for keyword in ['bid', 'ask', 'contract', 'size', 'insize', 'outsize', 'match']):
            return "order"

        # Level2数据项目
        elif any(keyword in item_lower for keyword in ['bestbid', 'bestask', 'bidsize', 'asksize']):
            return "level2"

        # Daily数据项目
        elif any(keyword in item_lower for keyword in ['name', 'contractdate', 'settleprice', 'uplimit', 'downlimit', 'oi', 'preclose']):
            return "daily"

        # 默认返回tick
        return "tick"

    def _subscribe_dde_items(self, connection: DDEConnection):
        """订阅DDE项目"""
        try:
            if not connection.client or not connection.connected:
                self.logger.error(f"DDE连接未建立，无法订阅项目: {connection.product_symbol}")
                return

            # 获取该商品的配置
            product_config = None
            for config in self.config_manager.get_all_products().values():
                if config.symbol == connection.product_symbol:
                    product_config = config
                    break

            if not product_config:
                self.logger.error(f"未找到商品配置: {connection.product_symbol}")
                return

            # 使用多商品配置解析器获取DDE项目
            if hasattr(self, 'multi_config_parser') and self.multi_config_parser:
                subscribed_count = 0

                for data_type in product_config.data_types:
                    items = self.multi_config_parser.get_dde_items_for_symbol(
                        connection.product_symbol, data_type
                    )

                    self.logger.info(f"开始订阅 {connection.product_symbol}.{data_type}: {len(items)} 个项目")

                    for item in items:
                        try:
                            # 使用连接对象中保存的回调函数
                            callback_func = getattr(connection, 'callback_function', None)
                            if callback_func and connection.client.advise(item, callback_func):
                                subscribed_count += 1
                                self.logger.debug(f"订阅成功: {item}")
                            else:
                                self.logger.warning(f"订阅失败: {item}")
                        except Exception as e:
                            self.logger.error(f"订阅项目异常: {item}, {str(e)}")

                self.logger.info(f"DDE项目订阅完成: {connection.product_symbol}, 成功订阅 {subscribed_count} 个项目")

            else:
                # 如果没有多商品配置解析器，使用默认项目
                default_items = [
                    f"{connection.product_symbol}-Time",
                    f"{connection.product_symbol}-Price",
                    f"{connection.product_symbol}-Volume",
                    f"{connection.product_symbol}-TotalVolume"
                ]

                subscribed_count = 0
                for item in default_items:
                    try:
                        if connection.client.advise(item, connection.client.on_advise_data):
                            subscribed_count += 1
                            self.logger.debug(f"订阅成功: {item}")
                        else:
                            self.logger.warning(f"订阅失败: {item}")
                    except Exception as e:
                        self.logger.error(f"订阅项目异常: {item}, {str(e)}")

                self.logger.info(f"DDE默认项目订阅完成: {connection.product_symbol}, 成功订阅 {subscribed_count} 个项目")

        except Exception as e:
            self.logger.error(f"订阅DDE项目失败: {connection.product_symbol}, {str(e)}")

    def set_multi_config_parser(self, parser):
        """设置多商品配置解析器"""
        self.multi_config_parser = parser
        self.logger.info("多商品配置解析器已设置")
