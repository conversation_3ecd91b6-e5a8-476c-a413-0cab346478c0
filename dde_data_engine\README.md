# DDE 資料處理引擎

獨立的DDE資料處理模組，提供完整的資料接收、處理、去重和輸出功能。

## 功能特色

- **完整的資料處理流程**: 從DDE資料接收到檔案輸出的完整處理鏈
- **智能去重機制**: 支援 single、multiple、all 三種值變化檢查模式
- **時間間隔控制**: 可配置的時間間隔觸發機制
- **靈活的檔案輸出**: 支援多種檔案格式和輸出選項
- **統計監控**: 提供詳細的處理統計資訊
- **執行緒安全**: 支援多執行緒環境使用

## 核心組件

### 1. 資料結構 (data_structures.py)
- `ItemData`: DDE項目資料結構
- `RawDataRow`: 原始資料行結構
- `DataRow`: 處理後的資料行結構
- `ProcessorStats`: 處理器統計資訊

### 2. 資料處理器 (data_processor.py)
- `DDEDataProcessor`: 核心資料處理器
  - DDE資料接收和處理
  - 項目重複檢查
  - 時間間隔檢查
  - 值變化檢測
  - 資料行管理

### 3. 檔案處理器 (file_handler.py)
- `DataFileHandler`: 檔案輸出處理器
  - CSV檔案輸出
  - 完整資料檔案
  - 日誌檔案記錄

### 4. 配置處理器 (config_handler.py)
- `DataProcessorConfig`: 配置管理器
  - 參數驗證
  - 默認值設定
  - 類型轉換

## 使用方式

### 基本使用

```python
from dde_data_engine import DDEDataProcessor, DataProcessorConfig, DataFileHandler, ItemData

# 1. 準備項目資料
items_data = {
    "ITEM001": ItemData(name="價格", code="ITEM001"),
    "ITEM002": ItemData(name="數量", code="ITEM002"),
    # ... 更多項目
}

# 2. 設定配置
config = DataProcessorConfig({
    'enable_time_newline': True,
    'time_newline_interval': 0.800,
    'enable_value_change_check': True,
    'value_change_check_mode': 'all',  # single, multiple, all
    'value_change_check_items': '',
})

# 3. 設定檔案處理器
file_handler = DataFileHandler()
file_handler.init_file_paths(
    log_file="./logs/data.log",
    complete_data_file="./data/complete_data.csv"
)
file_handler.set_items_data(items_data)

# 4. 創建資料處理器
processor = DDEDataProcessor(
    config=config,
    items_data=items_data,
    file_handler=file_handler
)

# 5. 設定初始值
initial_values = {
    "ITEM001": "100.0",
    "ITEM002": "50"
}
processor.set_initial_values(initial_values)

# 6. 處理DDE資料
processor.process_dde_data("ITEM001", "101.0")
processor.process_dde_data("ITEM002", "55")

# 7. 定期檢查時間間隔
processor.check_time_interval()

# 8. 獲取統計資訊
stats = processor.get_stats()
print(f"已接收: {stats.total_received}, 已處理: {stats.total_processed}, 已保存: {stats.total_saved}")
```

### 進階使用 - 回調函數

```python
def on_data_received(item, value):
    print(f"收到資料: {item} = {value}")

def on_row_saved(row):
    print(f"保存資料行: {len(row.values)} 個項目")

def on_row_skipped(row):
    print(f"跳過資料行: 值未變化")

# 設定回調函數
processor.on_data_received = on_data_received
processor.on_row_saved = on_row_saved
processor.on_row_skipped = on_row_skipped
```

## 配置選項

### 時間間隔設定
- `enable_time_newline`: 是否啟用時間間隔換行 (預設: True)
- `time_newline_interval`: 時間間隔秒數 (預設: 0.800)

### 值變化檢查設定
- `enable_value_change_check`: 是否啟用值變化檢查 (預設: True)
- `value_change_check_mode`: 檢查模式 (預設: 'single')
  - `single`: 檢查單一指定項目
  - `multiple`: 檢查多個指定項目
  - `all`: 檢查所有項目
- `value_change_check_items`: 檢查項目名稱 (逗號分隔)

### 檔案輸出設定
- `enable_data_file`: 是否輸出資料檔案 (預設: False)
- `enable_complete_data_file`: 是否輸出完整資料檔案 (預設: True)
- `enable_log_file`: 是否輸出日誌檔案 (預設: True)

### 資料處理設定
- `max_history_rows`: 最大歷史記錄行數 (預設: 10)
- `max_raw_data_queue`: 最大原始資料隊列長度 (預設: 1000)

## 資料處理流程

1. **DDE資料接收**: 接收DDE項目更新
2. **項目重複檢查**: 檢查是否為重複項目，決定是否換行
3. **資料行更新**: 更新當前資料行的項目值
4. **時間間隔檢查**: 定期檢查是否需要根據時間間隔換行
5. **資料補全**: 補齊缺失的項目值
6. **值變化檢測**: 根據配置檢查值是否有變化
7. **資料保存**: 保存有效的完整資料行到檔案
8. **統計更新**: 更新處理統計資訊

## 執行緒安全

模組使用 `threading.RLock` 確保執行緒安全，可以在多執行緒環境中安全使用。

## 日誌記錄

模組使用標準的 Python logging 模組，可以配置不同的日誌級別：
- DEBUG: 詳細的處理過程資訊
- INFO: 一般資訊
- WARNING: 警告訊息
- ERROR: 錯誤訊息

## 版本資訊

- 版本: 1.0.0
- 相容性: Python 3.7+
- 依賴: 無外部依賴，僅使用標準庫

## 授權

本模組基於原有的DDE監控程式開發，保持相同的授權條款。
