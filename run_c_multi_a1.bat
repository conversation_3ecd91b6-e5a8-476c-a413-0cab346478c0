d:

cd D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\logs\wrapper_gui_test.log /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\outputs\TEMP\data\mon\XQ\FITXN07\_m\tick\FITXN07.TF_tick.log /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\outputs\TEMP\data\mon\XQ\FITXN07\_m\tick\ITXN07.TF_order_tick.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\outputs\TEMP\data\mon\XQ\FITXN07\_m\order\FITXN07.TF_order.log /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\outputs\TEMP\data\mon\XQ\FITXN07\_m\order\FITXN07.TF_order_complete.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\outputs\TEMP\data\mon\XQ\FITXN07\_m\level2\FITXN07.TF_level2.log /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\outputs\TEMP\data\mon\XQ\FITXN07\_m\level2\FITXN07.TF_level2_complete.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\outputs\TEMP\data\mon\XQ\FITXN07\_m\daily\FITXN07.TF_daily.log /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper\outputs\TEMP\data\mon\XQ\FITXN07\_m\daily\FITXN07.TF_daily_complete.csv /Q


cd D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\logs
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\logs\multi_product_monitor.log /Q

cd D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_tick.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_order.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_level2.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_daily.csv /Q
pause
wt -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "python dde_monitor_multi.py" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_engine_wrapper" cmd /k "python run_gui_test.py"