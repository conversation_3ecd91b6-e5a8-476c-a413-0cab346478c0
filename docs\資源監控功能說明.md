# DDE 監控程式 - 資源監控功能說明

## 🎯 功能概述

我們已經為DDE監控程式添加了完整的**自我資源監控功能**，可以即時監控程式自身的資源使用情況，就像工作管理員一樣！

### 📊 監控指標

程式可以監控以下資源指標：

1. **CPU使用率** (%)
2. **記憶體使用量** (MB)
3. **記憶體使用率** (%)
4. **線程數量**
5. **句柄數量** (Windows)

### 📈 統計功能

對每個指標都會記錄：
- ✅ **當前值**
- ✅ **最小值**
- ✅ **最大值**
- ✅ **平均值**
- ✅ **監控時間**
- ✅ **數據點數**

## 🖥️ GUI界面操作

### **資源監控控制按鈕**

在"自動化功能"區域中，您會看到：

```
[開始監控] [資源統計] [自動化設定]
```

#### 1. **開始監控按鈕**
- **點擊開始**：按鈕變為紅色"停止監控"，開始收集資源數據
- **點擊停止**：按鈕恢復原狀"開始監控"，停止收集數據
- **監控間隔**：默認每1秒收集一次數據

#### 2. **資源統計按鈕**
- 點擊後彈出**非模態對話框**顯示詳細統計
- 對話框包含：
  - 📊 **統計摘要**：各項指標的最小/最大/平均值
  - 🔄 **刷新按鈕**：更新統計數據
  - 🗑️ **重置統計**：清空歷史數據重新開始
  - 💾 **保存到文件**：將統計數據保存為文本文件
  - ❌ **關閉按鈕**：關閉對話框

## 📋 使用方法

### **基本使用流程**

1. **啟動程式**
2. **點擊"開始監控"** - 開始收集資源數據
3. **正常使用程式** - 進行DDE連接、訂閱等操作
4. **點擊"資源統計"** - 查看當前統計結果
5. **根據需要調整** - 比如調整GUI更新頻率
6. **保存統計數據** - 用於分析和比較

### **壓力測試建議**

#### **測試場景1：單程式長時間運行**
```
1. 啟動程式並開始資源監控
2. 連接DDE並訂閱數據
3. 運行4-8小時
4. 每小時查看一次資源統計
5. 記錄CPU和記憶體的變化趨勢
```

#### **測試場景2：多程式同時運行**
```
1. 啟動5個程式實例
2. 每個程式都開始資源監控
3. 設定不同的GUI更新頻率
4. 運行2-4小時
5. 比較不同設定下的資源使用情況
```

#### **測試場景3：GUI設定優化**
```
1. 開始資源監控
2. 測試不同GUI更新間隔：
   - 100ms (高頻)
   - 500ms (中頻)
   - 1000ms (低頻)
   - 2000ms (超低頻)
3. 記錄每種設定下的CPU使用率
4. 找到最佳平衡點
```

## 📊 統計數據格式

### **螢幕顯示格式**
```
=== 資源使用統計 (監控時間: 02:15:30) ===
數據點數: 8130

CPU使用率 (%)
  當前: 12.5%
  最小: 3.2%
  最大: 28.7%
  平均: 15.3%

記憶體使用 (MB)
  當前: 145.2 MB
  最小: 142.1 MB
  最大: 158.9 MB
  平均: 147.8 MB

記憶體使用率 (%)
  當前: 0.89%
  最小: 0.87%
  最大: 0.97%
  平均: 0.90%

線程數
  當前: 12
  最小: 10
  最大: 14
  平均: 11.8
```

### **文件保存格式**
統計數據會保存到 `logs/resource_stats_YYYYMMDD_HHMMSS.txt`

文件包含：
1. **統計摘要** - 如上所示的格式化統計
2. **詳細數據** - CSV格式的原始數據：
   ```
   時間,CPU(%),記憶體(MB),記憶體(%),線程數,句柄數
   14:30:15,12.5,145.2,0.89,12,245
   14:30:16,13.1,145.8,0.90,12,246
   ...
   ```

## 🔍 數據分析建議

### **關鍵指標觀察**

#### 1. **CPU使用率分析**
- **正常範圍**：5-20%（取決於數據頻率）
- **異常情況**：持續超過30%或不斷上升
- **優化目標**：通過調整GUI更新頻率降低CPU使用

#### 2. **記憶體使用分析**
- **正常範圍**：100-200MB
- **異常情況**：記憶體持續增長（可能有記憶體洩漏）
- **優化目標**：保持記憶體使用穩定

#### 3. **線程數分析**
- **正常範圍**：10-15個線程
- **異常情況**：線程數不斷增加
- **優化目標**：線程數保持穩定

### **效能比較方法**

#### **不同GUI設定比較**
```
設定A (100ms更新)：
- 平均CPU: 25.3%
- 平均記憶體: 156MB

設定B (1000ms更新)：
- 平均CPU: 12.1%
- 平均記憶體: 148MB

結論：設定B更節省資源
```

#### **長時間穩定性分析**
```
運行時間: 8小時
CPU使用率變化: 15.2% → 16.8% (增加1.6%)
記憶體使用變化: 145MB → 152MB (增加7MB)
結論：長時間運行穩定
```

## ⚠️ 注意事項

### **監控開銷**
- 資源監控本身會消耗少量CPU（約0.1-0.5%）
- 建議監控間隔不要低於0.5秒
- 長時間監控會累積歷史數據（最多保留1000個數據點）

### **數據準確性**
- CPU使用率可能有短期波動，關注平均值更有意義
- 記憶體使用包含程式的所有記憶體（代碼、數據、緩存等）
- 線程數包含主線程和所有工作線程

### **文件保存**
- 統計文件保存在 `logs/` 目錄下
- 文件名包含時間戳，避免覆蓋
- 建議定期清理舊的統計文件

## 🚀 實際應用場景

### **日常監控**
- 啟動程式後立即開始資源監控
- 定期查看統計確保程式運行正常
- 發現異常時及時調整設定

### **效能調優**
- 測試不同GUI更新頻率的效果
- 比較不同配置文件的資源使用
- 找到最適合您環境的設定

### **故障診斷**
- 程式響應慢時檢查CPU使用率
- 懷疑記憶體洩漏時監控記憶體變化
- 系統不穩定時分析線程數變化

### **壓力測試**
- 多程式同時運行時監控系統負載
- 長時間運行測試程式穩定性
- 不同工作負載下的資源使用對比

這個資源監控功能讓您可以像使用工作管理員一樣，詳細了解程式的資源使用情況，幫助您優化程式效能和診斷問題！
