#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
實時數據監控

監控 DDE 數據文件的變化
"""

import os
import time
from pathlib import Path
from datetime import datetime


def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')


def watch_data_files():
    """監控數據文件"""
    print("📊 DDE 數據實時監控")
    print("監控 outputs/ 目錄中的數據文件變化...")
    print("按 Ctrl+C 停止監控")
    print("=" * 60)
    
    output_dir = Path("outputs")
    if not output_dir.exists():
        print("❌ outputs 目錄不存在")
        return
    
    # 記錄文件的上次狀態
    last_states = {}
    
    try:
        while True:
            clear_screen()
            print("📊 DDE 數據實時監控")
            print(f"⏰ 當前時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)
            
            # 檢查所有相關文件
            files_to_watch = [
                "dde_data.csv",
                "dde_data.json",
                "clean_dde_data.csv",
                "clean_dde_data.json",
                "production_dde_data.csv",
                "production_dde_data.json",
                "debug_dde_data.csv",
                "quick_test.csv",
                "gui_demo_data.csv"
            ]
            
            current_states = {}
            changes_detected = False
            
            for filename in files_to_watch:
                file_path = output_dir / filename
                
                if file_path.exists():
                    stat = file_path.stat()
                    current_state = {
                        'size': stat.st_size,
                        'mtime': stat.st_mtime,
                        'exists': True
                    }
                    
                    # 檢查是否有變化
                    if filename in last_states:
                        if (current_state['size'] != last_states[filename]['size'] or 
                            current_state['mtime'] != last_states[filename]['mtime']):
                            changes_detected = True
                    else:
                        changes_detected = True
                    
                    current_states[filename] = current_state
                    
                    # 顯示文件信息
                    size_mb = stat.st_size / 1024 / 1024
                    modified = datetime.fromtimestamp(stat.st_mtime).strftime('%H:%M:%S')
                    
                    status = "🔥 NEW" if changes_detected and filename in last_states else "📁"
                    if filename not in last_states:
                        status = "✨ FOUND"
                    
                    print(f"{status} {filename}:")
                    print(f"    大小: {size_mb:.3f} MB ({stat.st_size} bytes)")
                    print(f"    修改: {modified}")
                    
                    # 如果是 CSV 文件，顯示行數和最新內容
                    if filename.endswith('.csv') and stat.st_size > 0:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                lines = f.readlines()
                                print(f"    行數: {len(lines)}")
                                
                                if lines:
                                    # 顯示最後一行
                                    last_line = lines[-1].strip()
                                    if len(last_line) > 80:
                                        last_line = last_line[:77] + "..."
                                    print(f"    最新: {last_line}")
                        except Exception as e:
                            print(f"    讀取錯誤: {str(e)}")
                    
                    print()
                else:
                    current_states[filename] = {'exists': False}
                    print(f"⚪ {filename}: 文件不存在")
            
            # 更新狀態
            last_states = current_states.copy()
            
            # 顯示總結
            existing_files = [f for f, s in current_states.items() if s.get('exists', False)]
            total_size = sum(current_states[f]['size'] for f in existing_files)
            
            print("=" * 60)
            print(f"📈 總結: {len(existing_files)} 個文件, 總大小: {total_size/1024/1024:.3f} MB")
            
            if changes_detected:
                print("🔥 檢測到文件變化!")
            else:
                print("⏸️  沒有檢測到變化")
            
            print("=" * 60)
            print("💡 提示:")
            print("  - 如果沒有 dde_data.* 文件，說明系統可能沒有接收到數據")
            print("  - 運行 'python trigger_subscription.py' 手動觸發訂閱")
            print("  - 檢查 DDE 服務是否在交易時間內運行")
            
            # 等待5秒
            time.sleep(5)
            
    except KeyboardInterrupt:
        clear_screen()
        print("\n👋 數據監控已停止")


def check_dde_activity():
    """檢查 DDE 活動"""
    print("\n🔍 檢查 DDE 系統活動...")
    
    # 檢查進程
    import psutil
    dde_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                if 'main.py' in cmdline:
                    dde_processes.append(proc.info['pid'])
        except:
            continue
    
    if dde_processes:
        print(f"✅ 找到 {len(dde_processes)} 個 DDE 相關進程")
    else:
        print("❌ 沒有找到 DDE 系統進程")
        print("   請先啟動: python main.py --config config/system_config.json")
    
    # 檢查日誌
    log_files = ["logs/system.log"]
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists() and log_path.stat().st_size > 0:
            print(f"📝 日誌文件: {log_file} ({log_path.stat().st_size} bytes)")
        else:
            print(f"⚪ 日誌文件: {log_file} (不存在或為空)")


def main():
    """主函數"""
    print("🚀 DDE 數據監控工具")
    print("此工具將實時監控 DDE 數據文件的變化")
    
    # 檢查 DDE 活動
    check_dde_activity()
    
    print("\n開始監控數據文件...")
    time.sleep(2)
    
    # 開始監控
    watch_data_files()


if __name__ == "__main__":
    main()
