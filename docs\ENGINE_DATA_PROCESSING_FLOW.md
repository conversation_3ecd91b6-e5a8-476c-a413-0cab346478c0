# DDE 引擎版資料處理流程文件

## 概述

本文件詳細說明 DDE 引擎版 (`dde_engine_modular_test.py` 配合 `dde_data_engine` 模組) 從接收資料到完整輸出的完整流程。引擎版採用完全模組化的設計，將資料處理邏輯抽象為獨立的引擎，提供最高的可重用性和可測試性。

## 核心架構

### 主要模組
- **dde_engine_modular_test.py**: 引擎測試主程式
- **dde_data_engine/**: 獨立的資料處理引擎模組
  - **data_processor.py**: 核心資料處理器
  - **data_structures.py**: 資料結構定義
  - **file_handler.py**: 檔案處理器
  - **config_handler.py**: 配置管理器

### 引擎化資料結構

```python
@dataclass
class ItemData:
    name: str                           # 項目顯示名稱
    code: str                           # DDE項目代碼
    value: Optional[str] = None         # 當前值
    update_time: Optional[datetime] = None  # 最後更新時間
    status: str = "未訂閱"              # 訂閱狀態

@dataclass
class RawDataRow:
    receive_date: str                   # 接收日期 (YYYY-MM-DD)
    receive_time: str                   # 接收時間 (HH:MM:S.fff)
    values: Dict[str, str]              # 項目值字典，key為項目代碼，value為項目值
    is_complete: bool = False           # 是否為完整資料行

@dataclass
class ProcessorStats:
    total_received: int = 0             # 總接收筆數
    total_processed: int = 0            # 總處理筆數
    total_saved: int = 0                # 總保存筆數
    total_skipped: int = 0              # 總跳過筆數
    last_update_time: Optional[datetime] = None  # 最後更新時間
```

## 完整資料處理流程

### 1. 引擎測試應用初始化 (`DDEEngineTestApp.initialize`)

```python
def initialize(self):
    """初始化應用程式 - 與模組化版本完全一致"""
    # 創建 QApplication - AutoConnectManager需要Qt事件循環
    self.app = QApplication(sys.argv)
    
    # 初始化設定管理器 - 重用現有模組
    self.config_manager = ConfigManager()
    self.config = self.config_manager.load_config(self.config_file)
    
    # 初始化日誌系統 - 與模組化版本一致
    self.logger = setup_logging(log_file_path, log_level, console_log_level, file_log_level)
    
    # 初始化自動化管理器 - 重用現有模組
    self.auto_manager = AutoConnectManager(self.config, self.logger)
    
    # 初始化DDE引擎 - 新的引擎化處理
    self._init_dde_engine()
    
    # 啟動自動化管理器
    self.auto_manager.start()
```

**引擎化初始化特點**：
- 重用現有的配置和日誌模組
- 保持與模組化版本的一致性
- 獨立的引擎初始化流程

### 2. DDE引擎初始化 (`_init_dde_engine`)

```python
def _init_dde_engine(self):
    """初始化DDE資料處理引擎"""
    try:
        # 載入項目配置 - 與其他版本一致
        self.items_data = self._load_items_from_config()
        
        # 創建引擎配置 - 轉換為引擎格式
        engine_config_dict = {
            'enable_time_newline': self.config.getboolean('Table', 'enable_time_newline', fallback=True),
            'time_newline_interval': self.config.getfloat('Table', 'time_newline_interval', fallback=0.800),
            'enable_value_change_check': self.config.getboolean('Table', 'enable_value_change_check', fallback=True),
            'value_change_check_mode': self.config.get('Table', 'value_change_check_mode', fallback='single'),
            'value_change_check_items': self.config.get('Table', 'value_change_check_items', fallback=''),
            'enable_complete_data_file': self.config.getboolean('FileOutput', 'enable_complete_data_file', fallback=True),
            'enable_data_file': self.config.getboolean('FileOutput', 'enable_data_file', fallback=False),
            'enable_log_file': self.config.getboolean('FileOutput', 'enable_log_file', fallback=True),
        }
        
        # 創建引擎配置物件
        self.engine_config = DataProcessorConfig(engine_config_dict)
        
        # 創建檔案處理器
        self.file_handler = DataFileHandler(self.logger)
        
        # 處理檔案路徑 - 與其他版本一致的路徑處理
        from datetime import datetime
        current_date = datetime.now().strftime('%Y%m%d')
        
        log_file = self.config.get('OutputPath', 'log_file', fallback='engine_log_{date}.txt')
        complete_data_file = self.config.get('OutputPath', 'complete_data_file', fallback='engine_complete_data_{date}.csv')
        
        log_file = log_file.replace('{date}', current_date)
        complete_data_file = complete_data_file.replace('{date}', current_date)
        
        # 初始化檔案處理器
        self.file_handler.init_file_paths(
            log_file=log_file,
            complete_data_file=complete_data_file
        )
        self.file_handler.set_items_data(self.items_data)
        
        # 創建資料處理器 - 核心引擎
        self.processor = DDEDataProcessor(
            config=self.engine_config,
            items_data=self.items_data,
            file_handler=self.file_handler,
            logger=self.logger
        )
        
        # 設置回調函數
        self.processor.on_row_saved = self._on_row_saved
        self.processor.on_row_skipped = self._on_row_skipped
        
        self.logger.info("DDE資料處理引擎初始化完成")
        
    except Exception as e:
        error_msg = f"初始化DDE引擎失敗: {str(e)}"
        self.logger.error(error_msg)
        raise
```

**引擎初始化特點**：
- 配置格式轉換和適配
- 獨立的檔案處理器配置
- 回調函數設置
- 與現有版本的路徑處理一致性

### 3. 資料接收入口 (`_on_dde_data`)

```python
def _on_dde_data(self, item: str, value: str):
    """DDE資料接收回調"""
    try:
        self.total_received += 1
        
        # 直接寫入調試檔案
        debug_file = "debug_dde_callback.txt"
        with open(debug_file, "a", encoding="utf-8") as f:
            f.write(f"[{self.total_received}] DDE回調: {item} = {value}\n")
        
        # 顯示前10筆資料
        if self.total_received <= 10:
            print(f"[DATA] 接收第{self.total_received}筆: {item} = {value}")
        
        # 每100筆顯示一次統計
        if self.total_received % 100 == 0:
            print(f"[統計] 已接收 {self.total_received} 筆, 已保存 {self.total_saved} 筆, 已跳過 {self.total_skipped} 筆")
        
        # 處理資料 - 直接調用引擎
        result = self.processor.process_dde_data(item, value)
        
        # 寫入處理結果
        with open(debug_file, "a", encoding="utf-8") as f:
            f.write(f"[{self.total_received}] 處理結果: {result}\n")
        
    except Exception as e:
        error_msg = f"處理DDE資料失敗: {item}={value}, 錯誤: {str(e)}"
        self.logger.error(error_msg)
        print(f"[ERROR] {error_msg}")
```

**引擎接收特點**：
- 直接調用引擎的 `process_dde_data` 方法
- 詳細的調試資訊記錄
- 統計資訊即時更新
- 簡化的錯誤處理

### 4. 核心引擎資料處理 (`DDEDataProcessor.process_dde_data`)

```python
def process_dde_data(self, item: str, value: str) -> bool:
    """處理DDE資料更新 - 主要的資料處理入口點"""
    try:
        with self._lock:
            self.stats.total_received += 1
            self.stats.last_update_time = datetime.now()
            
            # 觸發回調
            if self.on_data_received:
                self.on_data_received(item, value)
            
            # 1. 檢查是否需要項目重複換行（按照v6版本邏輯）
            repeat_check_result = self._check_item_repeat_newline(item, value)
            if repeat_check_result:
                return True
            
            # 2. 更新原始資料
            self._update_raw_data(item, value)
            
            # 3. 更新項目資料
            self._update_items_data(item, value)
            
            # 4. 更新最後接收時間（與v6版本一致）
            self.last_advise_time = time.time()
            
            self.stats.total_processed += 1
            return True
            
    except Exception as e:
        self.logger.error(f"處理DDE資料失敗: {str(e)}")
        return False
```

**引擎處理特點**：
- 執行緒安全的處理邏輯
- 完整的統計資訊追蹤
- 回調機制支援
- 與v6版本一致的處理順序

### 5. 引擎項目重複換行檢查 (`_check_item_repeat_newline`)

```python
def _check_item_repeat_newline(self, item: str, value: str) -> bool:
    """檢查是否需要項目重複換行"""
    try:
        if not self.raw_data:
            # 第一次接收資料，沒有當前行
            return False
        
        current_row = self.raw_data[0]
        
        # 檢查項目是否已存在於當前行
        if item in current_row.values:
            # 項目重複，觸發檢查邏輯
            
            # 補齊缺失資料
            self._fill_missing_data(current_row)
            
            # 檢查值變化
            enable_check = self.config.get_bool('enable_value_change_check', True)
            
            if enable_check:
                has_changed = self._check_value_change(current_row)
                
                if has_changed:
                    # 值有變化，儲存完整資料行
                    self._save_complete_row(current_row)
                    self._create_new_row()
                    self._add_item_to_current_row(item, value)
                    return True
                else:
                    # 值未變化，跳過資料行
                    self._skip_row(current_row)
                    self._create_new_row()
                    self._add_item_to_current_row(item, value)
                    return True
            else:
                # 未啟用值變化檢查，直接儲存資料行
                self._save_complete_row(current_row)
                self._create_new_row()
                self._add_item_to_current_row(item, value)
                return True
                
        return False
        
    except Exception as e:
        self.logger.error(f"項目重複檢查失敗: {str(e)}")
        return False
```

**引擎重複檢查特點**：
- 完全獨立的處理邏輯
- 統一的資料行操作方法
- 詳細的日誌記錄
- 異常安全處理

### 6. 引擎時間間隔檢查 (`check_time_interval`)

```python
def check_time_interval(self) -> bool:
    """檢查時間間隔（按照v6版本邏輯實現）"""
    try:
        if not self.config.get_bool('enable_time_newline', True):
            return False
        
        if self.last_advise_time is None:
            return False
        
        current_time = time.time()
        elapsed = current_time - self.last_advise_time
        
        if elapsed >= self.time_newline_interval:
            if not self.raw_data:
                return False
            
            current_row = self.raw_data[0]
            
            # 檢查行是否有資料
            if not current_row.values:
                return False
            
            # 檢查並補齊缺失資料
            if self._has_missing_data(current_row):
                self._fill_missing_data(current_row)
            
            # 檢查值變化（與模組化版本邏輯一致）
            if self.config.get_bool('enable_value_change_check', True):
                has_changed = self._check_value_change(current_row)
                
                if has_changed:
                    # 儲存完整資料行
                    self._save_complete_row(current_row)
                    self._create_new_row()
                else:
                    # 值未變化，不儲存資料行但建立新行
                    self._create_new_row()
            else:
                # 未啟用值變化檢查，直接儲存資料行
                self._save_complete_row(current_row)
                self._create_new_row()
            
            # 更新時間戳記（與模組化版本一致）
            self.last_advise_time = current_time
            return True
        
        return False
        
    except Exception as e:
        self.logger.error(f"檢查時間間隔失敗: {str(e)}")
        return False
```

**引擎時間檢查特點**：
- 獨立的定時檢查機制
- 與其他版本一致的邏輯
- 可配置的時間間隔
- 完整的錯誤處理

### 7. 引擎值變化檢查 (`_check_value_change`)

引擎版支援完整的三種值變化檢查模式：

```python
def _check_value_change(self, current_row: RawDataRow) -> bool:
    """檢查資料行的值變化"""
    try:
        # 檢查是否有歷史資料用於比較
        if len(self.raw_data) < 2:
            return True
        
        # 獲取最後一行已保存的資料進行比較
        last_saved_row = None
        for i in range(1, len(self.raw_data)):
            row = self.raw_data[i]
            if row.is_complete:
                last_saved_row = row
                break
        
        if not last_saved_row:
            return True
        
        check_mode = self.config.get('value_change_check_mode', 'single')
        check_items = self.config.get('value_change_check_items', '')
        
        if check_mode == 'single':
            # 單項檢查模式
            if not check_items:
                return True
            
            # 查找檢查項目的代碼
            check_item_code = None
            for item_code, item_data in self.items_data.items():
                if item_data.name == check_items:
                    check_item_code = item_code
                    break
            
            if check_item_code:
                current_value = current_row.values.get(check_item_code, "")
                previous_value = last_saved_row.values.get(check_item_code, "")
                has_changed = (current_value != previous_value)
                return has_changed
            else:
                return True
                
        elif check_mode == 'multiple':
            # 多項檢查模式
            if not check_items:
                return True
            
            check_items_list = [item.strip() for item in check_items.split(',') if item.strip()]
            
            for check_item_name in check_items_list:
                # 查找檢查項目的代碼
                check_item_code = None
                for item_code, item_data in self.items_data.items():
                    if item_data.name == check_item_name:
                        check_item_code = item_code
                        break
                
                if check_item_code:
                    current_value = current_row.values.get(check_item_code, "")
                    previous_value = last_saved_row.values.get(check_item_code, "")
                    if current_value != previous_value:
                        return True
            
            return False
            
        elif check_mode == 'all':
            # 全部檢查模式 - 檢查所有DDE項目（排除時間戳記）
            for item_code in current_row.values.keys():
                # 跳過時間戳記相關項目
                if item_code in ['receive_date', 'receive_time']:
                    continue
                
                current_value = current_row.values.get(item_code, "")
                previous_value = last_saved_row.values.get(item_code, "")
                if current_value != previous_value:
                    return True
            
            return False
        
        return True
        
    except Exception as e:
        self.logger.error(f"值變化檢查失敗: {str(e)}")
        return True
```

**引擎值變化檢查特點**：
- 支援所有三種檢查模式
- 智能的歷史資料查找
- 詳細的日誌記錄
- 與其他版本一致的邏輯

### 8. 引擎檔案處理 (`DataFileHandler`)

```python
class DataFileHandler:
    """資料檔案處理器"""
    
    def save_row(self, row: RawDataRow, is_complete: bool = False):
        """保存資料行"""
        try:
            # 確保標題行已初始化
            if not self._headers_initialized:
                self.init_file_headers()
            
            # 保存到完整資料檔案
            if is_complete and self.enable_complete_data_file and self.complete_data_file:
                self._save_to_complete_data_file(row)
            
            # 保存到資料檔案
            if self.enable_data_file and self.data_file:
                self._save_to_data_file(row)
            
            # 記錄到日誌檔案
            if self.enable_log_file and self.log_file:
                self._log_to_file(row, is_complete)
                
        except Exception as e:
            self.logger.error(f"保存資料行失敗: {str(e)}")

    def _save_to_complete_data_file(self, row: RawDataRow):
        """保存到完整資料檔案"""
        try:
            with open(self.complete_data_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # 構建資料行
                data_row = [row.receive_date, row.receive_time]
                
                # 按照項目順序添加值
                for item_code, item_data in self.items_data.items():
                    value = row.values.get(item_code, "")
                    data_row.append(value)
                
                writer.writerow(data_row)
                
        except Exception as e:
            self.logger.error(f"保存到完整資料檔案失敗: {str(e)}")
```

**引擎檔案處理特點**：
- 獨立的檔案處理邏輯
- 支援多種檔案格式
- 自動標題行管理
- CSV格式標準化輸出

### 9. 引擎定時檢查機制

```python
def _start_timer(self):
    """啟動定時器"""
    self._timer_running = True
    self._timer_thread()

def _timer_thread(self):
    """定時器線程"""
    def timer_loop():
        while self._timer_running:
            try:
                self._check_time_interval()
                time.sleep(0.01)  # 10ms間隔
            except Exception as e:
                print(f"[ERROR] 定時器執行失敗: {str(e)}")
    
    timer_thread = threading.Thread(target=timer_loop, daemon=True)
    timer_thread.start()

def _check_time_interval(self):
    """定時檢查時間間隔 - 與模組化版本一致的100ms間隔"""
    try:
        if self.processor:
            result = self.processor.check_time_interval()
            if result:
                # 時間間隔觸發了換行
                stats = self.processor.get_stats()
                self.total_saved = stats.total_saved
                self.total_skipped = stats.total_skipped
                
    except Exception as e:
        self.logger.error(f"定時檢查時間間隔失敗: {str(e)}")
```

**引擎定時機制特點**：
- 獨立的線程定時檢查
- 與模組化版本一致的檢查頻率
- 統計資訊同步更新
- 異常安全的定時器

## 引擎回調機制

### 回調函數設置

```python
def _on_row_saved(self, row: RawDataRow):
    """資料行保存回調"""
    self.total_saved += 1
    if self.total_saved <= 5:
        print(f"[SAVED] 第{self.total_saved}筆完整資料已保存")

def _on_row_skipped(self, row: RawDataRow):
    """資料行跳過回調"""
    self.total_skipped += 1
    if self.total_skipped <= 5:
        print(f"[SKIPPED] 第{self.total_skipped}筆資料已跳過（值未變化）")
```

**回調機制特點**：
- 事件驅動的統計更新
- 靈活的回調函數設置
- 即時的處理結果反饋

## 關鍵特性

### 1. 完全模組化
- 獨立的資料處理引擎
- 可重用的核心邏輯
- 清晰的介面定義

### 2. 執行緒安全
- 內建的執行緒安全機制
- 原子操作保證
- 安全的並發處理

### 3. 配置靈活性
- 統一的配置介面
- 類型安全的配置存取
- 動態配置更新支援

### 4. 統計追蹤
- 完整的處理統計
- 即時的效能監控
- 詳細的狀態資訊

### 5. 回調支援
- 豐富的事件回調
- 靈活的事件處理
- 可擴展的回調機制

### 6. 測試友好
- 獨立的引擎模組
- 可模擬的外部依賴
- 完整的單元測試支援

## 資料流程圖

```
引擎測試應用啟動 → 配置轉換 → 引擎初始化 → 回調設置
     ↓
DDE資料接收 → 引擎處理入口 → 執行緒安全處理 → 統計更新
     ↓
項目重複檢查 → 補齊資料 → 值變化檢查 → 保存/跳過決策
     ↓
獨立檔案處理 → 回調觸發 → 統計同步
     ↓
定時線程檢查 → 時間間隔處理 → 引擎狀態更新
```

## 與其他版本的整合

### 配置相容性
- 支援現有配置格式
- 自動配置轉換
- 向後相容性保證

### 模組重用
- 重用現有的配置管理
- 重用現有的日誌系統
- 重用現有的自動化管理

### 測試一致性
- 與模組化版本相同的環境
- 一致的處理邏輯
- 可比較的輸出結果

---

*本文件基於 DDE 引擎版的實際程式碼內容編寫，反映了引擎版的完整資料處理流程和架構設計。*
