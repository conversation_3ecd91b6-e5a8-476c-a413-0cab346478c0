[DDE]
service = XQTISC
topic = Quote
disconnect_on_exit = false

[Items]
item1_name = 交易時間
item1_code = FITXN07.TF-Time
item2_name = 交易日期
item2_code = FITXN07.TF-TradingDate
item3_name = 開盤價
item3_code = FITXN07.TF-Open
item4_name = 最高價
item4_code = FITXN07.TF-High
item5_name = 最低價
item5_code = FITXN07.TF-Low
item6_name = 成交價
item6_code = FITXN07.TF-Price
item7_name = 總量
item7_code = FITXN07.TF-TotalVolume
item8_name = 單量
item8_code = FITXN07.TF-Volume

[Table]
enable_time_newline = true
time_newline_interval = 0.800
enable_value_change_check = true
value_change_check_mode = single
value_change_check_items = 總量

[FileOutput]
enable_data_file = False
enable_complete_data_file = true
enable_log_file = true

[OutputPath]
log_file = ./logs/{date}/dde_monitor_01.log
data_file = ./outputs/TEMP/data/mon/XQ/FITXN07/_a/dde_data_01.csv
complete_data_file = ./outputs/TEMP/data/mon/XQ/FITXN07/_a/complete_data_01.csv

[Logging]
# 日誌級別: DEBUG, INFO, WARNING, ERROR, CRITICAL
# DEBUG: 顯示所有詳細調試信息（包括數據處理過程）
# INFO: 顯示一般操作信息（連接、訂閱等）
# WARNING: 只顯示警告和錯誤信息
# ERROR: 只顯示錯誤信息
log_level = INFO
console_log_level = WARNING
file_log_level = INFO

[AutoConnect]
enable_auto_connect = True
auto_connect_mode = schedule
auto_connect_delay = 5.0
schedule_connect_times = 08:25:00-13:45:15;14:50:00-05:05:05
prevent_weekend_startup = True
# schedule 模式結束時間的行為: disconnect (完整斷線+取消訂閱) 或 unadvise_only (僅取消訂閱，預設值)
# 無效設定值會自動更正為 unadvise_only
schedule_end_action = unadvise_only

[AutoShutdown]
enable_auto_shutdown = True
shutdown_time = 05:55:05
shutdown_buffer_seconds = 30
shutdown_warning_seconds = 10
force_shutdown = True
save_data_before_shutdown = True
disconnect_before_shutdown = True
cleanup_temp_files = True

[Notifications]
enable_system_notifications = True
enable_sound_notifications = False
notification_levels = WARNING,ERROR,CRITICAL
notify_auto_connect = True
notify_auto_disconnect = True
notify_auto_shutdown = True

