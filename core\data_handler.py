#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資料處理模組
包含資料容器、檔案處理器和資料處理器
"""

import os
import csv
import logging
from datetime import datetime
from dataclasses import dataclass
from collections import deque
from typing import Dict, List, Optional, Tuple
from PySide6.QtCore import QObject, Signal, QThread
from queue import Queue
import threading

@dataclass
class ItemData:
    """項目資料容器"""
    name: str
    code: str
    value: str = ""
    update_time: Optional[datetime] = None
    status: str = "未訂閱"  # 未訂閱, 已測試, 已訂閱, 訂閱失敗, 測試失敗

@dataclass
class DataRow:
    """資料行容器"""
    timestamp: datetime
    values: Dict[str, str]
    is_complete: bool = False

@dataclass
class RawDataRow:
    """原始資料行結構
    用於儲存每一筆原始資料的完整資訊

    屬性:
        receive_date (str): 接收日期 (YYYY-MM-DD)
        receive_time (str): 接收時間 (HH:MM:SS.ffffff)
        values (Dict[str, str]): 項目值字典，key為項目代碼，value為項目值
        is_complete (bool): 是否為完整資料行
    """
    receive_date: str
    receive_time: str
    values: Dict[str, str]
    is_complete: bool = False

class DataFileHandler:
    """資料檔案處理器
    負責處理資料檔案的讀寫操作
    """
    
    def __init__(self):
        self.data_file_path = None
        self.complete_data_file_path = None
        self.log_file_path = None
        self.enable_data_file = False
        self.enable_complete_data_file = False
        self.enable_log_file = False
        self.config = None
        self.items_data = None

    def init_file_paths(self, log_file=None, data_file=None, complete_data_file=None):
        """初始化文件路径（用于多商品系统）"""
        try:
            if log_file:
                self.log_file_path = log_file
                # 确保日志目录存在
                log_dir = os.path.dirname(log_file)
                if log_dir:
                    os.makedirs(log_dir, exist_ok=True)

            if data_file:
                self.data_file_path = data_file
                # 确保数据目录存在
                data_dir = os.path.dirname(data_file)
                if data_dir:
                    os.makedirs(data_dir, exist_ok=True)

            if complete_data_file:
                self.complete_data_file_path = complete_data_file
                # 确保完整数据目录存在
                complete_dir = os.path.dirname(complete_data_file)
                if complete_dir:
                    os.makedirs(complete_dir, exist_ok=True)

            logging.debug(f"文件路径初始化完成: log={log_file}, data={data_file}, complete={complete_data_file}")

        except Exception as e:
            logging.error(f"初始化文件路径失败: {str(e)}")

    def init_files(self, config, items_data: Dict[str, ItemData]):
        """初始化檔案路徑和設定"""
        try:
            # 保存配置和項目數據
            self.config = config
            self.items_data = items_data

            # 讀取檔案輸出設定
            if 'FileOutput' in config:
                self.enable_data_file = config.getboolean('FileOutput', 'enable_data_file', fallback=False)
                self.enable_complete_data_file = config.getboolean('FileOutput', 'enable_complete_data_file', fallback=True)
                self.enable_log_file = config.getboolean('FileOutput', 'enable_log_file', fallback=True)

            # 讀取檔案路徑設定
            if 'OutputPath' in config:
                self.data_file_path = config.get('OutputPath', 'data_file', fallback='./data/dde_data.csv')
                self.complete_data_file_path = config.get('OutputPath', 'complete_data_file', fallback='./data/complete_data.csv')
                self.log_file_path = config.get('OutputPath', 'log_file', fallback='./logs/dde_monitor.log')

            # 處理路徑中的變數
            self._process_path_variables()

            # 創建目錄
            self._create_directories()

            # 初始化檔案標題行
            if items_data:
                self._init_file_headers(items_data)
                logging.debug("資料檔案初始化完成")
            else:
                logging.warning("items_data 未初始化，無法寫入標題行")

        except Exception as e:
            logging.error(f"初始化檔案失敗: {str(e)}")
            raise
    
    def _process_path_variables(self):
        """處理路徑中的變數"""
        try:
            current_date = datetime.now().strftime("%Y%m%d")
            instance_id = "01"  # 可以根據需要動態生成
            
            if self.data_file_path:
                self.data_file_path = self.data_file_path.replace('{date}', current_date)
                self.data_file_path = self.data_file_path.replace('{instance}', instance_id)
            
            if self.complete_data_file_path:
                self.complete_data_file_path = self.complete_data_file_path.replace('{date}', current_date)
                self.complete_data_file_path = self.complete_data_file_path.replace('{instance}', instance_id)
            
            if self.log_file_path:
                self.log_file_path = self.log_file_path.replace('{date}', current_date)
                self.log_file_path = self.log_file_path.replace('{instance}', instance_id)
                
        except Exception as e:
            logging.error(f"處理路徑變數失敗: {str(e)}")
    
    def _create_directories(self):
        """創建必要的目錄"""
        try:
            paths = [self.data_file_path, self.complete_data_file_path, self.log_file_path]
            
            for path in paths:
                if path:
                    directory = os.path.dirname(path)
                    if directory and not os.path.exists(directory):
                        os.makedirs(directory, exist_ok=True)
                        
        except Exception as e:
            logging.error(f"創建目錄失敗: {str(e)}")
    
    def _init_file_headers(self, items_data: Dict[str, ItemData]):
        """初始化檔案標題行"""
        try:
            if not items_data:
                logging.warning("items_data 為空，無法寫入標題行")
                return

            # 準備標題行 - 與v6版本一致
            headers = ["接收日期", "接收時間"]

            # 对于多商品系统，直接从items_data获取项目名称
            if isinstance(self.config, dict):
                # 多商品系统：直接从items_data获取
                for item_code, item_data in items_data.items():
                    headers.append(item_data.name)
            else:
                # 单商品系统：按照設定檔中的順序添加項目名稱
                for i in range(1, 100):  # 假設最多100個項目
                    name_key = f'item{i}_name'
                    code_key = f'item{i}_code'

                    if name_key in self.config['Items'] and code_key in self.config['Items']:
                        name = self.config['Items'][name_key]
                        code = self.config['Items'][code_key]
                        if code in items_data:
                            headers.append(name)
                    else:
                        break

            # 寫入標題行到兩個檔案 - 使用字符串拼接與v6版本一致
            header_line = ','.join(headers) + '\n'

            # 寫入資料檔案標題行
            if self.enable_data_file and self.data_file_path:
                if not os.path.exists(self.data_file_path):
                    with open(self.data_file_path, 'w', encoding='utf-8') as f:
                        f.write(header_line)
                    logging.debug(f"資料檔案初始化完成: {self.data_file_path}")

            # 寫入完整資料檔案標題行
            if self.enable_complete_data_file and self.complete_data_file_path:
                if not os.path.exists(self.complete_data_file_path):
                    with open(self.complete_data_file_path, 'w', encoding='utf-8') as f:
                        f.write(header_line)
                    logging.debug(f"完整資料檔案初始化完成: {self.complete_data_file_path}")

        except Exception as e:
            logging.error(f"初始化檔案標題行失敗: {str(e)}")
    
    def save_row(self, data_row, is_complete: bool = False):
        """保存資料行到檔案

        參數:
            data_row: 可以是 DataRow 或 RawDataRow
            is_complete (bool): 是否為完整資料行
        """
        try:
            if hasattr(data_row, 'receive_date'):
                # RawDataRow 格式 - 按照舊版v6的邏輯
                if not hasattr(self, 'items_data') or not self.items_data:
                    logging.error("items_data 未初始化，無法儲存資料行")
                    return

                # 準備資料行
                row_data = [data_row.receive_date, data_row.receive_time]

                # 对于多商品系统，直接从items_data获取项目值
                if isinstance(self.config, dict):
                    # 多商品系统：直接从items_data按顺序获取
                    for item_code, item_data in self.items_data.items():
                        row_data.append(data_row.values.get(item_code, ""))
                else:
                    # 单商品系统：按照設定檔中的順序添加項目值
                    for i in range(1, 100):  # 假設最多100個項目
                        name_key = f'item{i}_name'
                        code_key = f'item{i}_code'

                        if name_key in self.config['Items'] and code_key in self.config['Items']:
                            code = self.config['Items'][code_key]
                            if code in self.items_data:
                                row_data.append(data_row.values.get(code, ""))
                        else:
                            break

                # 寫入檔案 - 使用字符串拼接而不是CSV writer
                # 確保所有數據都轉換為字符串
                row_str = ','.join(str(item) for item in row_data) + '\n'

                if is_complete and self.enable_complete_data_file and self.complete_data_file_path:
                    with open(self.complete_data_file_path, 'a', encoding='utf-8') as f:
                        f.write(row_str)
                elif not is_complete and self.enable_data_file and self.data_file_path:
                    with open(self.data_file_path, 'a', encoding='utf-8') as f:
                        f.write(row_str)

                logging.debug(f"已儲存資料行到 {self.complete_data_file_path if is_complete else self.data_file_path}")

            else:
                # DataRow 格式 (向後兼容)
                timestamp_str = data_row.timestamp.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                row_data = [timestamp_str] + list(data_row.values.values())

                # 保存到資料檔案
                if self.enable_data_file and self.data_file_path and not is_complete:
                    with open(self.data_file_path, 'a', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(row_data)

                # 保存到完整資料檔案
                if self.enable_complete_data_file and self.complete_data_file_path and is_complete:
                    with open(self.complete_data_file_path, 'a', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(row_data)

        except Exception as e:
            logging.error(f"保存資料行失敗: {str(e)}")
            raise

class DataProcessor(QObject):
    """資料處理器
    在獨立線程中處理DDE資料更新
    """
    
    data_processed = Signal(str, str)  # item, value
    
    def __init__(self):
        super().__init__()
        self.data_queue = Queue()
        self.running = False
        
    def add_data(self, item: str, value: str):
        """添加資料到處理佇列"""
        try:
            self.data_queue.put((item, value))
        except Exception as e:
            logging.error(f"添加資料到佇列失敗: {str(e)}")
    
    def process_data(self):
        """處理資料的主循環"""
        self.running = True
        
        while self.running:
            try:
                # 從佇列中獲取資料
                item, value = self.data_queue.get(timeout=1)
                
                # 發送處理完成信號
                self.data_processed.emit(item, value)
                
                # 標記任務完成
                self.data_queue.task_done()
                
            except:
                # 超時或其他異常，繼續循環
                continue
    
    def stop(self):
        """停止資料處理"""
        self.running = False


class MultiProductDataProcessor(QObject):
    """多商品数据处理器

    支持多个商品的数据同时处理，每个商品可以有不同的数据类型
    """

    # 信号定义
    data_processed = Signal(str, str, str, str)  # symbol, data_type, item, value

    def __init__(self, multi_config_manager):
        super().__init__()
        self.multi_config = multi_config_manager
        self.data_queue = Queue()
        self.running = False

        # 每个商品的数据容器
        self.symbol_items_data = {}  # symbol -> data_type -> item_code -> ItemData
        self.symbol_raw_data = {}    # symbol -> data_type -> deque
        self.symbol_file_handlers = {}  # symbol -> data_type -> DataFileHandler

        # 每个商品数据类型的当前数据行 - 用于实现与模块化版本相同的数据更新逻辑
        self.symbol_current_rows = {}  # symbol -> data_type -> RawDataRow

        # 时间间隔检查相关 - 实现与模块化版本相同的时间触发机制
        self.symbol_last_advise_time = {}  # symbol -> data_type -> last_time
        self.symbol_time_intervals = {}    # symbol -> data_type -> interval_seconds

        # 初始化数据容器
        self._init_symbol_data_containers()

    def _init_symbol_data_containers(self):
        """初始化各商品的数据容器"""
        try:
            all_items = self.multi_config.get_all_symbol_items()

            for symbol, symbol_data in all_items.items():
                self.symbol_items_data[symbol] = {}
                self.symbol_raw_data[symbol] = {}
                self.symbol_file_handlers[symbol] = {}
                self.symbol_current_rows[symbol] = {}  # 初始化当前数据行容器
                self.symbol_last_advise_time[symbol] = {}  # 初始化时间记录容器
                self.symbol_time_intervals[symbol] = {}   # 初始化时间间隔容器

                for data_type, items in symbol_data.items():
                    # 初始化项目数据容器
                    self.symbol_items_data[symbol][data_type] = {}
                    for item_code, item_name in items.items():
                        self.symbol_items_data[symbol][data_type][item_code] = ItemData(
                            name=item_name,
                            code=item_code
                        )

                    # 初始化原始数据队列
                    self.symbol_raw_data[symbol][data_type] = deque(maxlen=1000)

                    # 初始化时间间隔设定 - 实现与模块化版本相同的时间触发机制
                    datatype_config = self.multi_config.get_datatype_config(data_type)
                    if datatype_config:
                        time_interval = float(datatype_config.get('table_time_newline_interval', '0.800'))
                        self.symbol_time_intervals[symbol][data_type] = time_interval
                        self.symbol_last_advise_time[symbol][data_type] = None
                        logging.debug(f"设置时间间隔 {symbol}-{data_type}: {time_interval}秒")

                    # 初始化文件处理器
                    output_path = self.multi_config.get_symbol_output_path(symbol)
                    common_config = self.multi_config.get_common_config('FileOutput')

                    file_handler = DataFileHandler()
                    file_handler.init_file_paths(
                        log_file=f"{output_path}/logs/{symbol}_{data_type}.log",
                        data_file=f"{output_path}/data_{data_type}.csv",
                        complete_data_file=f"{output_path}/complete_data_{data_type}.csv"
                    )

                    # 设置文件输出选项
                    if common_config:
                        file_handler.enable_data_file = common_config.get('enable_data_file', 'false').lower() == 'true'
                        file_handler.enable_complete_data_file = common_config.get('enable_complete_data_file', 'true').lower() == 'true'
                        file_handler.enable_log_file = common_config.get('enable_log_file', 'true').lower() == 'true'

                    # 初始化文件处理器的items_data和config
                    try:
                        file_handler.items_data = self.symbol_items_data[symbol][data_type]
                        file_handler.config = datatype_config  # 这是一个字典，不是ConfigParser

                        # 创建必要的目录
                        file_handler._create_directories()

                        # 初始化文件标题行 - 这是关键步骤
                        file_handler._init_file_headers(self.symbol_items_data[symbol][data_type])

                        logging.debug(f"文件处理器初始化完成: {symbol}-{data_type}")
                    except Exception as e:
                        logging.error(f"文件处理器初始化失败 {symbol}-{data_type}: {str(e)}")

                    self.symbol_file_handlers[symbol][data_type] = file_handler

            logging.info(f"多商品数据容器初始化完成，商品数: {len(self.symbol_items_data)}")

        except Exception as e:
            logging.error(f"初始化多商品数据容器失败: {str(e)}")

    def add_data(self, item: str, value: str):
        """添加数据到处理队列

        Args:
            item: DDE项目代码
            value: 项目值
        """
        try:
            # 解析项目属于哪个商品和数据类型
            symbol, data_type = self._parse_item_symbol_and_type(item)
            if symbol and data_type:
                self.data_queue.put((symbol, data_type, item, value))
            else:
                logging.warning(f"无法解析项目的商品和数据类型: {item}")

        except Exception as e:
            logging.error(f"添加数据到队列失败: {str(e)}")

    def _parse_item_symbol_and_type(self, item: str) -> Tuple[str, str]:
        """解析项目代码，确定属于哪个商品和数据类型

        Args:
            item: DDE项目代码 (如 FITXN07.TF-Price)

        Returns:
            Tuple[symbol, data_type]: 商品代码和数据类型
        """
        try:
            for symbol in self.symbol_items_data:
                for data_type in self.symbol_items_data[symbol]:
                    if item in self.symbol_items_data[symbol][data_type]:
                        return symbol, data_type

            return None, None

        except Exception as e:
            logging.error(f"解析项目商品和数据类型失败: {str(e)}")
            return None, None

    def process_data(self):
        """处理数据的主循环"""
        self.running = True

        while self.running:
            try:
                # 从队列中获取数据
                symbol, data_type, item, value = self.data_queue.get(timeout=1)

                # 处理数据
                self._process_symbol_data(symbol, data_type, item, value)

                # 发送处理完成信号
                self.data_processed.emit(symbol, data_type, item, value)

                # 标记任务完成
                self.data_queue.task_done()

            except:
                # 超时或其他异常，检查时间间隔
                self._check_all_time_intervals()
                continue

    def _process_symbol_data(self, symbol: str, data_type: str, item: str, value: str):
        """处理特定商品的数据 - 实现与模块化版本相同的逻辑"""
        try:
            # 1. 更新项目数据
            if (symbol in self.symbol_items_data and
                data_type in self.symbol_items_data[symbol] and
                item in self.symbol_items_data[symbol][data_type]):

                item_data = self.symbol_items_data[symbol][data_type][item]
                item_data.value = value
                item_data.update_time = datetime.now()
                item_data.status = "已更新"

            # 2. 检查项目重复换行 - 实现与模块化版本相同的逻辑
            if self._check_item_repeat_newline(symbol, data_type, item, value):
                # 项目重复触发了换行，数据已保存，无需继续处理
                return

            # 3. 更新当前数据行
            self._update_current_row(symbol, data_type, item, value)

            # 4. 更新最后接收时间用于时间间隔检查
            import time
            current_time = time.time()
            if (symbol in self.symbol_last_advise_time and
                data_type in self.symbol_last_advise_time[symbol]):
                self.symbol_last_advise_time[symbol][data_type] = current_time

        except Exception as e:
            logging.error(f"处理商品数据失败 {symbol}-{data_type}: {str(e)}")

    def _check_all_time_intervals(self):
        """检查所有商品的时间间隔 - 实现与模块化版本相同的时间触发机制"""
        try:
            import time
            current_time = time.time()

            for symbol in self.symbol_last_advise_time:
                for data_type in self.symbol_last_advise_time[symbol]:
                    last_time = self.symbol_last_advise_time[symbol][data_type]
                    if last_time is None:
                        continue

                    time_interval = self.symbol_time_intervals.get(symbol, {}).get(data_type, 0.800)

                    if current_time - last_time >= time_interval:
                        logging.debug(f"时间间隔检查 {symbol}-{data_type}: 时间间隔已到，开始换行")
                        self._check_time_interval_newline(symbol, data_type)
                        self.symbol_last_advise_time[symbol][data_type] = current_time

        except Exception as e:
            logging.error(f"检查时间间隔失败: {str(e)}")

    def _check_time_interval_newline(self, symbol: str, data_type: str):
        """检查是否需要时间间隔换行 - 实现与模块化版本相同的逻辑"""
        try:
            # 获取数据类型配置
            datatype_config = self.multi_config.get_datatype_config(data_type)
            if not datatype_config:
                return

            enable_time_newline = datatype_config.get('table_enable_time_newline', 'true').lower() == 'true'
            if not enable_time_newline:
                logging.debug(f"时间间隔换行 {symbol}-{data_type}: 未启用时间间隔换行")
                return

            # 获取当前数据行
            current_row = self.symbol_current_rows.get(symbol, {}).get(data_type)
            if not current_row:
                logging.debug(f"时间间隔换行 {symbol}-{data_type}: 当前行不存在")
                return

            # 检查行是否有数据
            if not current_row.values:
                logging.debug(f"时间间隔换行 {symbol}-{data_type}: 当前行没有数据")
                return

            # 补齐缺失数据
            self._fill_missing_data_for_row(symbol, data_type, current_row)

            # 检查值变化
            enable_value_change_check = datatype_config.get('table_enable_value_change_check', 'true').lower() == 'true'

            if enable_value_change_check:
                has_changed = self._check_value_change_for_row(symbol, data_type, current_row)
                logging.debug(f"时间间隔换行 {symbol}-{data_type}: 值变化检查结果={has_changed}")

                if has_changed:
                    logging.debug(f"时间间隔换行 {symbol}-{data_type}: 值有变化，保存完整数据行")
                    # 保存完整数据行
                    self._save_data_row(symbol, data_type, current_row)
                    # 创建新行
                    self._create_new_row(symbol, data_type)
                else:
                    logging.debug(f"时间间隔换行 {symbol}-{data_type}: 值未变化，不保存数据行")
                    # 值未变化，不保存数据行，但创建新行
                    self._create_new_row(symbol, data_type)
            else:
                # 未启用值变化检查，直接保存数据行
                logging.debug(f"时间间隔换行 {symbol}-{data_type}: 未启用值变化检查，直接保存数据行")
                self._save_data_row(symbol, data_type, current_row)
                self._create_new_row(symbol, data_type)

        except Exception as e:
            logging.error(f"时间间隔换行失败 {symbol}-{data_type}: {str(e)}")

    def _update_current_row(self, symbol: str, data_type: str, item: str, value: str):
        """更新当前数据行 - 实现与模块化版本相同的逻辑"""
        try:
            current_time = datetime.now()

            # 确保当前行容器存在
            if symbol not in self.symbol_current_rows:
                self.symbol_current_rows[symbol] = {}
            if data_type not in self.symbol_current_rows[symbol]:
                self.symbol_current_rows[symbol][data_type] = None

            # 检查是否需要创建新行
            current_row = self.symbol_current_rows[symbol][data_type]
            if current_row is None:
                # 创建新的当前行
                current_row = RawDataRow(
                    receive_date=current_time.strftime('%Y-%m-%d'),
                    receive_time=current_time.strftime('%H:%M:%S.%f')[:-3],
                    values={}
                )
                self.symbol_current_rows[symbol][data_type] = current_row
                logging.debug(f"创建新的当前数据行: {symbol}-{data_type}")

            # 更新当前行的项目值和时间戳
            current_row.values[item] = value
            current_row.receive_date = current_time.strftime('%Y-%m-%d')
            current_row.receive_time = current_time.strftime('%H:%M:%S.%f')[:-3]

            logging.debug(f"更新当前行: {symbol}-{data_type}-{item} = {value}")

        except Exception as e:
            logging.error(f"更新当前数据行失败 {symbol}-{data_type}: {str(e)}")

    def _check_item_repeat_newline(self, symbol: str, data_type: str, item: str, value: str) -> bool:
        """检查是否需要项目重复换行 - 实现与模块化版本相同的逻辑
        当收到重复项目的数据时，检查是否需要换行

        参数:
            symbol: 商品代码
            data_type: 数据类型
            item: 项目代码
            value: 项目值

        返回:
            bool: 是否需要换行
        """
        try:
            # 获取当前数据行
            current_row = self.symbol_current_rows.get(symbol, {}).get(data_type)
            if not current_row:
                logging.debug(f"项目重复检查 {symbol}-{data_type}: 当前行不存在")
                return False

            # 检查项目是否已存在于当前行
            if item in current_row.values:
                logging.debug(f"项目重复检查 {symbol}-{data_type}-{item}: 项目已存在于当前行")

                # 补齐缺失数据
                self._fill_missing_data_for_row(symbol, data_type, current_row)

                # 获取数据类型配置
                datatype_config = self.multi_config.get_datatype_config(data_type)
                if not datatype_config:
                    return False

                # 检查值变化
                enable_value_change_check = datatype_config.get('table_enable_value_change_check', 'true').lower() == 'true'

                if enable_value_change_check:
                    has_changed = self._check_value_change_for_row(symbol, data_type, current_row)
                    logging.debug(f"项目重复检查 {symbol}-{data_type}: 值变化检查结果={has_changed}")

                    if has_changed:
                        logging.debug(f"项目重复检查 {symbol}-{data_type}: 值有变化，保存数据行")
                        # 保存完整数据行
                        self._save_data_row(symbol, data_type, current_row)
                        # 创建新行
                        self._create_new_row(symbol, data_type)
                        # 将收到的项目值填入新行
                        new_row = self.symbol_current_rows[symbol][data_type]
                        new_row.values[item] = value
                        new_row.receive_date = datetime.now().strftime('%Y-%m-%d')
                        new_row.receive_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        logging.debug(f"项目重复检查 {symbol}-{data_type}: 已建立新行并填入项目 {item}")
                        return True
                    else:
                        logging.debug(f"项目重复检查 {symbol}-{data_type}: 值未变化，不保存数据行")
                        # 值未变化，不保存数据行，但创建新行
                        self._create_new_row(symbol, data_type)
                        # 将收到的项目值填入新行
                        new_row = self.symbol_current_rows[symbol][data_type]
                        new_row.values[item] = value
                        new_row.receive_date = datetime.now().strftime('%Y-%m-%d')
                        new_row.receive_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        logging.debug(f"项目重复检查 {symbol}-{data_type}: 已建立新行并填入项目 {item}")
                        return True
                else:
                    # 未启用值变化检查，直接保存数据行
                    logging.debug(f"项目重复检查 {symbol}-{data_type}: 未启用值变化检查，直接保存数据行")
                    self._save_data_row(symbol, data_type, current_row)
                    self._create_new_row(symbol, data_type)
                    new_row = self.symbol_current_rows[symbol][data_type]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime('%Y-%m-%d')
                    new_row.receive_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    logging.debug(f"项目重复检查 {symbol}-{data_type}: 已建立新行并填入项目 {item}")
                    return True

            logging.debug(f"项目重复检查 {symbol}-{data_type}-{item}: 项目不存在于当前行")
            return False

        except Exception as e:
            logging.error(f"项目重复检查失败 {symbol}-{data_type}: {str(e)}")
            return False

    def _create_new_row(self, symbol: str, data_type: str):
        """创建新的数据行"""
        try:
            current_time = datetime.now()
            new_row = RawDataRow(
                receive_date=current_time.strftime('%Y-%m-%d'),
                receive_time=current_time.strftime('%H:%M:%S.%f')[:-3],
                values={}
            )
            self.symbol_current_rows[symbol][data_type] = new_row
            logging.debug(f"创建新数据行: {symbol}-{data_type}")

        except Exception as e:
            logging.error(f"创建新数据行失败 {symbol}-{data_type}: {str(e)}")

    def _save_data_row(self, symbol: str, data_type: str, row: RawDataRow):
        """保存数据行到文件"""
        try:
            if (symbol in self.symbol_file_handlers and
                data_type in self.symbol_file_handlers[symbol]):

                file_handler = self.symbol_file_handlers[symbol][data_type]
                file_handler.save_row(row, is_complete=True)
                logging.debug(f"保存数据行 {symbol}-{data_type}")

                # 保存到历史记录用于下次比较
                if (symbol in self.symbol_raw_data and
                    data_type in self.symbol_raw_data[symbol]):

                    # 创建副本用于历史记录
                    history_row = RawDataRow(
                        receive_date=row.receive_date,
                        receive_time=row.receive_time,
                        values=row.values.copy()
                    )
                    self.symbol_raw_data[symbol][data_type].append(history_row)

                    # 限制历史记录数量
                    max_history = 10
                    if len(self.symbol_raw_data[symbol][data_type]) > max_history:
                        self.symbol_raw_data[symbol][data_type].popleft()
            else:
                logging.warning(f"找不到文件处理器 {symbol}-{data_type}")

        except Exception as e:
            logging.error(f"保存数据行失败 {symbol}-{data_type}: {str(e)}")

    def _check_value_change_for_row(self, symbol: str, data_type: str, current_row: RawDataRow) -> bool:
        """检查数据行的值变化 - 支持 single、multiple、all 三种模式"""
        try:
            # 获取数据类型配置
            datatype_config = self.multi_config.get_datatype_config(data_type)
            if not datatype_config:
                return True

            enable_value_change_check = datatype_config.get('table_enable_value_change_check', 'true').lower() == 'true'
            if not enable_value_change_check:
                logging.debug(f"值变化检查 {symbol}-{data_type}: 未启用值变化检查，返回 True")
                return True

            # 检查是否有历史数据用于比较
            if (symbol not in self.symbol_raw_data or
                data_type not in self.symbol_raw_data[symbol] or
                len(self.symbol_raw_data[symbol][data_type]) == 0):
                logging.debug(f"值变化检查 {symbol}-{data_type}: 没有历史数据，这是第一笔数据，返回 True")
                return True

            # 获取最后一行数据进行比较
            last_row = self.symbol_raw_data[symbol][data_type][-1]

            check_mode = datatype_config.get('table_value_change_check_mode', 'single')
            check_items = datatype_config.get('table_value_change_check_items', '')

            logging.debug(f"值变化检查 {symbol}-{data_type}: 检查模式={check_mode}, 检查项目={check_items}")

            if check_mode == 'single':
                # 单项检查模式 - 只检查指定项目的值变化
                if not check_items:
                    logging.debug(f"值变化检查 {symbol}-{data_type}: 单项模式未指定检查项目，返回 True")
                    return True

                # 查找检查项目的代码
                check_item_code = None
                for item_code, item_data in self.symbol_items_data[symbol][data_type].items():
                    if item_data.name == check_items:
                        check_item_code = item_code
                        break

                if check_item_code:
                    current_value = current_row.values.get(check_item_code, "")
                    previous_value = last_row.values.get(check_item_code, "")
                    has_changed = (current_value != previous_value)
                    logging.debug(f"值变化检查 {symbol}-{data_type}: 单项检查 {check_items}({check_item_code}): 当前值={current_value}, 前值={previous_value}, 变化={has_changed}")
                    return has_changed
                else:
                    logging.warning(f"值变化检查 {symbol}-{data_type}: 找不到检查项目 {check_items}")
                    return True

            elif check_mode == 'multiple':
                # 多项检查模式 - 检查指定的多个项目的值变化
                if not check_items:
                    logging.debug(f"值变化检查 {symbol}-{data_type}: 多项模式未指定检查项目，返回 True")
                    return True

                check_items_list = [item.strip() for item in check_items.split(',') if item.strip()]
                if not check_items_list:
                    logging.warning(f"值变化检查 {symbol}-{data_type}: 多项模式检查项目列表为空")
                    return True

                for check_item_name in check_items_list:
                    # 查找检查项目的代码
                    check_item_code = None
                    for item_code, item_data in self.symbol_items_data[symbol][data_type].items():
                        if item_data.name == check_item_name:
                            check_item_code = item_code
                            break

                    if check_item_code:
                        current_value = current_row.values.get(check_item_code, "")
                        previous_value = last_row.values.get(check_item_code, "")
                        if current_value != previous_value:
                            logging.debug(f"值变化检查 {symbol}-{data_type}: 多项检查 {check_item_name}({check_item_code}): 值有变化 {previous_value} -> {current_value}")
                            return True
                    else:
                        logging.warning(f"值变化检查 {symbol}-{data_type}: 找不到检查项目 {check_item_name}")

                logging.debug(f"值变化检查 {symbol}-{data_type}: 多项检查所有项目值都没有变化")
                return False

            elif check_mode == 'all':
                # 全部检查模式 - 检查所有项目的值变化
                for item_code in current_row.values.keys():
                    current_value = current_row.values.get(item_code, "")
                    previous_value = last_row.values.get(item_code, "")
                    if current_value != previous_value:
                        # 找到對應的項目名稱用於日誌
                        item_name = item_code
                        if (symbol in self.symbol_items_data and
                            data_type in self.symbol_items_data[symbol] and
                            item_code in self.symbol_items_data[symbol][data_type]):
                            item_name = self.symbol_items_data[symbol][data_type][item_code].name
                        logging.debug(f"值变化检查 {symbol}-{data_type}: 全部检查 {item_name}({item_code}): 值有变化 {previous_value} -> {current_value}")
                        return True

                logging.debug(f"值变化检查 {symbol}-{data_type}: 全部检查所有项目值都没有变化")
                return False

            else:
                logging.warning(f"值变化检查 {symbol}-{data_type}: 未知的检查模式 {check_mode}")
                return True

        except Exception as e:
            logging.error(f"值变化检查失败 {symbol}-{data_type}: {str(e)}")
            return True



    def _has_missing_data(self, symbol: str, data_type: str) -> bool:
        """检查是否有缺失数据"""
        try:
            if (symbol not in self.symbol_items_data or
                data_type not in self.symbol_items_data[symbol]):
                return False

            # 检查是否有任何项目的值为 None
            for item_code, item_data in self.symbol_items_data[symbol][data_type].items():
                if item_data.value is None:
                    return True
            return False

        except Exception as e:
            logging.error(f"检查缺失数据失败 {symbol}-{data_type}: {str(e)}")
            return False

    def _fill_missing_data(self, symbol: str, data_type: str):
        """补全缺失数据 - 使用最新可用值"""
        try:
            if (symbol not in self.symbol_items_data or
                data_type not in self.symbol_items_data[symbol]):
                return

            # 对于值为 None 的项目，尝试使用历史数据或保持为空字符串
            for item_code, item_data in self.symbol_items_data[symbol][data_type].items():
                if item_data.value is None:
                    # 尝试从历史数据中获取最新值
                    if (symbol in self.symbol_raw_data and
                        data_type in self.symbol_raw_data[symbol] and
                        len(self.symbol_raw_data[symbol][data_type]) > 0):

                        # 从最新的历史记录中获取值
                        last_row = self.symbol_raw_data[symbol][data_type][-1]
                        if item_code in last_row.values:
                            item_data.value = last_row.values[item_code]
                            logging.debug(f"从历史数据补全 {symbol}-{data_type}-{item_code}: {item_data.value}")
                        else:
                            item_data.value = ""
                    else:
                        item_data.value = ""

        except Exception as e:
            logging.error(f"补全缺失数据失败 {symbol}-{data_type}: {str(e)}")

    def _fill_missing_data_for_row(self, symbol: str, data_type: str, row: RawDataRow):
        """为数据行补全缺失数据 - 实现与模块化版本相同的逻辑"""
        try:
            if (symbol not in self.symbol_items_data or
                data_type not in self.symbol_items_data[symbol]):
                return

            # 检查每个项目是否在数据行中，如果不在则从items_data中补全
            for item_code, item_data in self.symbol_items_data[symbol][data_type].items():
                if item_code not in row.values:
                    # 项目不在数据行中，需要补全
                    if item_data.value is not None:
                        row.values[item_code] = item_data.value
                        logging.debug(f"补全数据行缺失项目 {symbol}-{data_type}-{item_code}: {item_data.value}")
                    else:
                        row.values[item_code] = ""
                        logging.debug(f"补全数据行缺失项目 {symbol}-{data_type}-{item_code}: (空值)")

        except Exception as e:
            logging.error(f"补全数据行失败 {symbol}-{data_type}: {str(e)}")

    def get_symbol_items_data(self, symbol: str, data_type: str = None) -> Dict:
        """获取指定商品的项目数据"""
        try:
            if symbol not in self.symbol_items_data:
                return {}

            if data_type:
                return self.symbol_items_data[symbol].get(data_type, {})
            else:
                return self.symbol_items_data[symbol]

        except Exception as e:
            logging.error(f"获取商品项目数据失败: {str(e)}")
            return {}

    def get_symbol_raw_data(self, symbol: str, data_type: str) -> deque:
        """获取指定商品的原始数据"""
        try:
            if (symbol in self.symbol_raw_data and
                data_type in self.symbol_raw_data[symbol]):
                return self.symbol_raw_data[symbol][data_type]
            return deque()

        except Exception as e:
            logging.error(f"获取商品原始数据失败: {str(e)}")
            return deque()

    def set_initial_value(self, symbol: str, data_type: str, item_code: str, item_name: str, value: str):
        """设置项目的初始值 - 用于测试阶段获取的初始数据"""
        try:
            # 确保数据结构存在
            if symbol not in self.symbol_items_data:
                self.symbol_items_data[symbol] = {}
            if data_type not in self.symbol_items_data[symbol]:
                self.symbol_items_data[symbol][data_type] = {}

            # 创建或更新项目数据
            if item_code not in self.symbol_items_data[symbol][data_type]:
                self.symbol_items_data[symbol][data_type][item_code] = ItemData(
                    name=item_name,
                    code=item_code,
                    value=value,
                    update_time=datetime.now(),
                    status="已測試"
                )
            else:
                # 更新现有项目的初始值
                item_data = self.symbol_items_data[symbol][data_type][item_code]
                item_data.value = value
                item_data.update_time = datetime.now()
                item_data.status = "已測試"

            logging.debug(f"设置初始值: {symbol}-{data_type}-{item_code} = {value}")

        except Exception as e:
            logging.error(f"设置初始值失败 {symbol}-{data_type}-{item_code}: {str(e)}")

    def has_initial_value(self, symbol: str, data_type: str, item_code: str) -> bool:
        """检查项目是否有初始值"""
        try:
            if (symbol in self.symbol_items_data and
                data_type in self.symbol_items_data[symbol] and
                item_code in self.symbol_items_data[symbol][data_type]):

                item_data = self.symbol_items_data[symbol][data_type][item_code]
                return item_data.value is not None and item_data.status == "已測試"

            return False

        except Exception as e:
            logging.error(f"检查初始值失败 {symbol}-{data_type}-{item_code}: {str(e)}")
            return False

    def stop(self):
        """停止数据处理"""
        self.running = False
