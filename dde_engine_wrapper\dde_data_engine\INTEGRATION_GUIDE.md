# DDE 資料處理引擎整合指南

本指南說明如何將 DDE 資料處理引擎整合到現有的 DDE 監控程式中。

## 整合方式

### 方式一：替換現有多商品版本的資料處理邏輯

將現有的 `core/data_handler.py` 中的 `MultiProductDataProcessor` 替換為使用 DDE 資料處理引擎。

#### 修改步驟：

1. **導入 DDE 資料處理引擎**
```python
from dde_data_engine import DDEDataProcessor, DataProcessorConfig, DataFileHandler, ItemData
```

2. **修改 MultiProductDataProcessor 類別**
```python
class MultiProductDataProcessor(QObject):
    def __init__(self, multi_config_manager):
        super().__init__()
        self.multi_config = multi_config_manager
        
        # 為每個商品-資料類型組合創建獨立的處理器
        self.processors = {}  # (symbol, data_type) -> DDEDataProcessor
        
        self._init_processors()
    
    def _init_processors(self):
        """初始化各商品的資料處理器"""
        all_items = self.multi_config.get_all_symbol_items()
        
        for symbol, symbol_data in all_items.items():
            for data_type, items in symbol_data.items():
                # 創建項目資料
                items_data = {}
                for item_code, item_name in items.items():
                    items_data[item_code] = ItemData(name=item_name, code=item_code)
                
                # 創建配置
                datatype_config = self.multi_config.get_datatype_config(data_type)
                config = DataProcessorConfig({
                    'enable_time_newline': datatype_config.get('table_enable_time_newline', 'true').lower() == 'true',
                    'time_newline_interval': float(datatype_config.get('table_time_newline_interval', '0.800')),
                    'enable_value_change_check': datatype_config.get('table_enable_value_change_check', 'true').lower() == 'true',
                    'value_change_check_mode': datatype_config.get('table_value_change_check_mode', 'single'),
                    'value_change_check_items': datatype_config.get('table_value_change_check_items', ''),
                })
                
                # 創建檔案處理器
                output_path = self.multi_config.get_symbol_output_path(symbol)
                file_handler = DataFileHandler()
                file_handler.init_file_paths(
                    log_file=f"{output_path}/logs/{symbol}_{data_type}.log",
                    complete_data_file=f"{output_path}/complete_data_{data_type}.csv"
                )
                file_handler.set_items_data(items_data)
                
                # 創建處理器
                processor = DDEDataProcessor(
                    config=config,
                    items_data=items_data,
                    file_handler=file_handler
                )
                
                self.processors[(symbol, data_type)] = processor
    
    def add_data(self, symbol: str, data_type: str, item: str, value: str):
        """添加資料到處理器"""
        key = (symbol, data_type)
        if key in self.processors:
            self.processors[key].process_dde_data(item, value)
    
    def check_time_intervals(self):
        """檢查所有處理器的時間間隔"""
        for processor in self.processors.values():
            processor.check_time_interval()
```

### 方式二：創建新的獨立程式

基於 DDE 資料處理引擎創建全新的多商品監控程式。

#### 程式結構：
```
new_dde_monitor/
├── main.py                 # 主程式
├── config/
│   └── multi_config.ini   # 配置檔案
├── dde_data_engine/       # 資料處理引擎（複製）
├── gui/
│   └── main_window.py     # GUI介面
└── utils/
    └── dde_client.py      # DDE客戶端
```

#### 主程式範例：
```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新版 DDE 多商品監控程式
基於 DDE 資料處理引擎
"""

import sys
from PySide6.QtWidgets import QApplication
from dde_data_engine import DDEDataProcessor, DataProcessorConfig, DataFileHandler, ItemData

class NewDDEMonitor:
    def __init__(self):
        self.processors = {}
        self.dde_client = None
        
    def init_processors(self, config_file):
        """從配置檔案初始化處理器"""
        # 讀取配置並創建處理器
        pass
        
    def start_monitoring(self):
        """開始監控"""
        # 啟動DDE連接和資料處理
        pass

def main():
    app = QApplication(sys.argv)
    
    monitor = NewDDEMonitor()
    monitor.init_processors('config/multi_config.ini')
    monitor.start_monitoring()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
```

## 配置對應關係

### 原配置 → 引擎配置

| 原配置項目 | 引擎配置項目 | 說明 |
|-----------|-------------|------|
| `table_enable_time_newline` | `enable_time_newline` | 時間間隔換行 |
| `table_time_newline_interval` | `time_newline_interval` | 時間間隔秒數 |
| `table_enable_value_change_check` | `enable_value_change_check` | 值變化檢查 |
| `table_value_change_check_mode` | `value_change_check_mode` | 檢查模式 |
| `table_value_change_check_items` | `value_change_check_items` | 檢查項目 |

## 優勢

### 1. 程式碼重用性
- 核心邏輯獨立於具體應用
- 可用於其他 DDE 相關專案
- 易於維護和更新

### 2. 模組化設計
- 清晰的職責分離
- 獨立的配置管理
- 靈活的檔案處理

### 3. 測試友好
- 獨立的單元測試
- 模擬資料測試
- 回歸測試支援

### 4. 效能優化
- 執行緒安全設計
- 記憶體使用優化
- 處理效率提升

## 遷移建議

### 階段一：驗證功能
1. 在測試環境中使用引擎處理現有資料
2. 比較輸出結果與原版本的一致性
3. 驗證各種配置模式的正確性

### 階段二：逐步替換
1. 先替換單一商品的處理邏輯
2. 逐步擴展到多商品處理
3. 保留原版本作為備份

### 階段三：完全遷移
1. 全面使用新引擎
2. 移除舊的處理邏輯
3. 優化整體架構

## 注意事項

1. **相容性檢查**：確保新引擎產生的資料格式與現有系統相容
2. **效能測試**：在實際負載下測試引擎效能
3. **錯誤處理**：完善錯誤處理和恢復機制
4. **日誌記錄**：保持詳細的處理日誌用於除錯
5. **配置驗證**：確保配置參數的正確性和完整性

## 技術支援

如需技術支援或有任何問題，請參考：
- `README.md`：基本使用說明
- `example.py`：使用範例
- 原始程式碼註釋：詳細的實作說明
