@ECHO OFF
REM DDE 監控程式自動化版本啟動腳本
REM =====================================

ECHO.
ECHO ==========================================
ECHO   DDE 監控程式 v6 (自動化版) 啟動腳本
ECHO ==========================================
ECHO.

REM 設定工作目錄
set ruu_dir=%CD%
ECHO 工作目錄: %ruu_dir%

REM 檢查設定檔
IF NOT EXIST "config.ini" (
    ECHO.
    ECHO [警告] 找不到 config.ini 設定檔
    ECHO.
    IF EXIST "config_auto_example.ini" (
        ECHO 發現範例設定檔 config_auto_example.ini
        ECHO 是否要複製為 config.ini? (Y/N)
        SET /P choice=請選擇: 
        IF /I "%choice%"=="Y" (
            COPY "config_auto_example.ini" "config.ini"
            ECHO 已複製範例設定檔為 config.ini
            ECHO 請根據需要修改設定檔後重新執行
            PAUSE
            EXIT /B
        )
    )
    ECHO 請確保 config.ini 存在後重新執行
    PAUSE
    EXIT /B
)

REM 顯示自動化功能狀態
ECHO.
ECHO 檢查自動化功能設定...
ECHO.

REM 使用 Python 讀取設定檔並顯示關鍵設定
python -c "
import configparser
try:
    config = configparser.ConfigParser()
    config.read('config.ini', encoding='utf-8')
    
    print('=== 自動化功能設定 ===')
    
    # 自動連線設定
    auto_connect = config.getboolean('AutoConnect', 'enable_auto_connect', fallback=False)
    connect_mode = config.get('AutoConnect', 'auto_connect_mode', fallback='delay')
    print(f'自動連線: {\"啟用\" if auto_connect else \"停用\"} (模式: {connect_mode})')
    
    if connect_mode == 'schedule':
        schedule = config.get('AutoConnect', 'schedule_connect_times', fallback='')
        if schedule:
            print(f'連線時間表: {schedule}')
    elif connect_mode == 'delay':
        delay = config.getfloat('AutoConnect', 'auto_connect_delay', fallback=3.0)
        print(f'延遲時間: {delay} 秒')
    
    # 自動結束設定
    auto_shutdown = config.getboolean('AutoShutdown', 'enable_auto_shutdown', fallback=False)
    shutdown_time = config.get('AutoShutdown', 'shutdown_time', fallback='17:30:00')
    print(f'自動結束: {\"啟用\" if auto_shutdown else \"停用\"} (時間: {shutdown_time})')
    
    # 週末設定
    prevent_weekend_startup = config.getboolean('AutoConnect', 'prevent_weekend_startup', fallback=False)
    print(f'防止週末起始: {\"是\" if prevent_weekend_startup else \"否\"}')
    
    print('========================')
    
except Exception as e:
    print(f'讀取設定檔失敗: {e}')
" 2>NUL

IF ERRORLEVEL 1 (
    ECHO [警告] 無法讀取設定檔，請檢查 config.ini 格式
)

ECHO.
ECHO 準備啟動程式...
ECHO.

REM 啟動虛擬環境
call venv env310 1

REM 回到工作目錄
cd %ruu_dir%

REM 清除螢幕
cls

REM 顯示啟動資訊
ECHO.
ECHO ==========================================
ECHO   DDE 監控程式 v6 (自動化版) 正在啟動...
ECHO ==========================================
ECHO.
ECHO 自動化功能已啟用，程式將根據設定檔自動執行
ECHO 請查看程式視窗中的 "自動狀態" 顯示
ECHO.
ECHO 日誌檔案位置: ./logs/
ECHO 設定檔位置: ./config.ini
ECHO.
ECHO 按 Ctrl+C 可以中斷程式執行
ECHO.

REM 執行主程式
python dde_monitor.py

REM 程式結束後的處理
ECHO.
ECHO ==========================================
ECHO   程式已結束
ECHO ==========================================
ECHO.

REM 檢查是否有錯誤
IF ERRORLEVEL 1 (
    ECHO [錯誤] 程式執行時發生錯誤
    ECHO 請檢查日誌檔案以獲取詳細資訊
) ELSE (
    ECHO 程式正常結束
)

ECHO.
ECHO 按任意鍵關閉視窗...
PAUSE >NUL
