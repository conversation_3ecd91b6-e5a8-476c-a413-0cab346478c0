#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE多商品监控系统 - 性能配置管理器
提供预设的性能配置方案，用户可以根据需要选择不同的优化级别
"""

import configparser
import os
from typing import Dict, Any

class PerformanceConfigManager:
    """性能配置管理器"""
    
    def __init__(self, config_file: str = "multi_config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        
        # 预设配置方案
        self.presets = {
            "conservative": {
                "name": "保守模式",
                "description": "最稳定，适合生产环境",
                "request_interval": 0.1,
                "subscribe_interval": 0.1,
                "batch_size": 20,
                "queue_interval": 1000,
                "connection_delay": 200
            },
            "balanced": {
                "name": "平衡模式", 
                "description": "稳定性与性能的平衡",
                "request_interval": 0.05,
                "subscribe_interval": 0.05,
                "batch_size": 50,
                "queue_interval": 500,
                "connection_delay": 100
            },
            "aggressive": {
                "name": "激进模式",
                "description": "高性能，可能不稳定",
                "request_interval": 0.01,
                "subscribe_interval": 0.01,
                "batch_size": 100,
                "queue_interval": 200,
                "connection_delay": 50
            },
            "extreme": {
                "name": "极限模式",
                "description": "最高性能，仅用于测试",
                "request_interval": 0.001,
                "subscribe_interval": 0.001,
                "batch_size": 200,
                "queue_interval": 100,
                "connection_delay": 10
            }
        }
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
    
    def save_config(self):
        """保存配置文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def apply_preset(self, preset_name: str):
        """应用预设配置"""
        if preset_name not in self.presets:
            raise ValueError(f"未知的预设配置: {preset_name}")
        
        preset = self.presets[preset_name]
        
        # 确保Common_Performance段存在
        if not self.config.has_section('Common_Performance'):
            self.config.add_section('Common_Performance')

        # 应用配置
        self.config.set('Common_Performance', 'request_interval', str(preset['request_interval']))
        self.config.set('Common_Performance', 'subscribe_interval', str(preset['subscribe_interval']))
        self.config.set('Common_Performance', 'batch_size', str(preset['batch_size']))

        # 添加注释
        self.config.set('Common_Performance', '# 当前使用预设', f"{preset['name']} - {preset['description']}")
        
        print(f"已应用预设配置: {preset['name']}")
        print(f"描述: {preset['description']}")
        print(f"请求间隔: {preset['request_interval']}秒")
        print(f"订阅间隔: {preset['subscribe_interval']}秒")
        print(f"批次大小: {preset['batch_size']}")
    
    def get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        if not self.config.has_section('Common_Performance'):
            return {}

        return {
            'request_interval': self.config.getfloat('Common_Performance', 'request_interval', fallback=0.05),
            'subscribe_interval': self.config.getfloat('Common_Performance', 'subscribe_interval', fallback=0.05),
            'batch_size': self.config.getint('Common_Performance', 'batch_size', fallback=50)
        }
    
    def list_presets(self):
        """列出所有预设配置"""
        print("可用的性能预设配置:")
        print("-" * 50)
        for key, preset in self.presets.items():
            print(f"{key:12} - {preset['name']}")
            print(f"{'':12}   {preset['description']}")
            print(f"{'':12}   请求间隔: {preset['request_interval']}s, 订阅间隔: {preset['subscribe_interval']}s")
            print()

def main():
    """命令行工具"""
    import sys
    
    manager = PerformanceConfigManager()
    manager.load_config()
    
    if len(sys.argv) < 2:
        print("DDE多商品监控系统 - 性能配置管理器")
        print("用法:")
        print("  python performance_config_manager.py list          - 列出所有预设")
        print("  python performance_config_manager.py apply <preset> - 应用预设配置")
        print("  python performance_config_manager.py current       - 显示当前配置")
        return
    
    command = sys.argv[1]
    
    if command == "list":
        manager.list_presets()
    elif command == "apply" and len(sys.argv) > 2:
        preset_name = sys.argv[2]
        try:
            manager.apply_preset(preset_name)
            manager.save_config()
            print("配置已保存!")
        except ValueError as e:
            print(f"错误: {e}")
    elif command == "current":
        config = manager.get_current_config()
        print("当前性能配置:")
        for key, value in config.items():
            print(f"  {key}: {value}")
    else:
        print("未知命令，使用 'python performance_config_manager.py' 查看帮助")

if __name__ == "__main__":
    main()
