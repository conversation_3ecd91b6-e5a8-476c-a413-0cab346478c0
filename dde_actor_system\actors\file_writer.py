#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件寫入Actor實現

提供高性能的文件寫入功能，包括：
- 異步批量文件寫入
- 文件輪轉管理
- 壓縮存儲
- 多格式輸出支持
"""

import asyncio
import aiofiles
import logging
import os
import time
import gzip
import json
import csv
from datetime import datetime
from collections import deque
from typing import Dict, List, Optional, Any, TextIO
from dataclasses import dataclass
from pathlib import Path

from core.actor_base import ActorBase
from core.message_system import Message, MessageType


@dataclass
class FileConfig:
    """文件配置"""
    filename: str
    format: str = "csv"  # csv, json, txt
    max_size_mb: int = 100
    max_age_hours: int = 24
    compress: bool = True
    buffer_size: int = 8192
    flush_interval: float = 5.0


class FileRotationManager:
    """文件輪轉管理器"""
    
    def __init__(self, config: FileConfig):
        """初始化文件輪轉管理器
        
        Args:
            config: 文件配置
        """
        self.config = config
        self.current_file: Optional[TextIO] = None
        self.current_size = 0
        self.creation_time = time.time()
        self.logger = logging.getLogger("FileRotationManager")
    
    async def write_data(self, data: str) -> bool:
        """寫入數據
        
        Args:
            data: 要寫入的數據
            
        Returns:
            bool: 寫入是否成功
        """
        try:
            # 檢查是否需要輪轉
            if await self._should_rotate():
                await self._rotate_file()
            
            # 確保文件已打開
            if not self.current_file:
                await self._open_file()
            
            # 寫入數據
            if self.current_file:
                await self.current_file.write(data)
                self.current_size += len(data.encode('utf-8'))
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"寫入數據失敗: {str(e)}")
            return False
    
    async def flush(self):
        """刷新文件緩衝區"""
        try:
            if self.current_file:
                await self.current_file.flush()
        except Exception as e:
            self.logger.error(f"刷新文件失敗: {str(e)}")
    
    async def close(self):
        """關閉文件"""
        try:
            if self.current_file:
                await self.current_file.close()
                self.current_file = None
        except Exception as e:
            self.logger.error(f"關閉文件失敗: {str(e)}")
    
    async def _should_rotate(self) -> bool:
        """檢查是否需要輪轉文件
        
        Returns:
            bool: 是否需要輪轉
        """
        # 檢查文件大小
        if self.current_size > self.config.max_size_mb * 1024 * 1024:
            return True
        
        # 檢查文件年齡
        if time.time() - self.creation_time > self.config.max_age_hours * 3600:
            return True
        
        return False
    
    async def _rotate_file(self):
        """輪轉文件"""
        try:
            # 關閉當前文件
            if self.current_file:
                await self.current_file.close()
                self.current_file = None
            
            # 重命名當前文件
            if os.path.exists(self.config.filename):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                base_name = Path(self.config.filename).stem
                extension = Path(self.config.filename).suffix
                rotated_name = f"{base_name}_{timestamp}{extension}"
                
                os.rename(self.config.filename, rotated_name)
                
                # 壓縮舊文件
                if self.config.compress:
                    await self._compress_file(rotated_name)
            
            # 重置狀態
            self.current_size = 0
            self.creation_time = time.time()
            
            self.logger.info(f"文件輪轉完成: {self.config.filename}")
            
        except Exception as e:
            self.logger.error(f"文件輪轉失敗: {str(e)}")
    
    async def _open_file(self):
        """打開文件"""
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(self.config.filename), exist_ok=True)
            
            # 打開文件
            self.current_file = await aiofiles.open(
                self.config.filename, 
                'a', 
                encoding='utf-8',
                buffering=self.config.buffer_size
            )
            
        except Exception as e:
            self.logger.error(f"打開文件失敗: {str(e)}")
    
    async def _compress_file(self, filename: str):
        """壓縮文件
        
        Args:
            filename: 要壓縮的文件名
        """
        try:
            compressed_name = f"{filename}.gz"
            
            async with aiofiles.open(filename, 'rb') as f_in:
                with gzip.open(compressed_name, 'wb') as f_out:
                    async for chunk in f_in:
                        f_out.write(chunk)
            
            # 刪除原文件
            os.remove(filename)
            
            self.logger.info(f"文件壓縮完成: {compressed_name}")
            
        except Exception as e:
            self.logger.error(f"文件壓縮失敗: {str(e)}")


class DataFormatter:
    """數據格式化器"""
    
    @staticmethod
    def format_csv(items: List[Dict[str, Any]]) -> str:
        """格式化為CSV
        
        Args:
            items: 數據項目列表
            
        Returns:
            str: CSV格式字符串
        """
        if not items:
            return ""
        
        lines = []
        for item in items:
            timestamp = datetime.fromtimestamp(item.get('timestamp', time.time()))
            line = f"{timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}," \
                   f"{item.get('item', '')}," \
                   f"{item.get('value', '')}," \
                   f"{item.get('original_value', '')}\n"
            lines.append(line)
        
        return ''.join(lines)
    
    @staticmethod
    def format_json(items: List[Dict[str, Any]]) -> str:
        """格式化為JSON
        
        Args:
            items: 數據項目列表
            
        Returns:
            str: JSON格式字符串
        """
        formatted_items = []
        for item in items:
            formatted_item = {
                'timestamp': item.get('timestamp', time.time()),
                'item': item.get('item', ''),
                'value': item.get('value', ''),
                'original_value': item.get('original_value', ''),
                'metadata': item.get('metadata', {})
            }
            formatted_items.append(formatted_item)
        
        return json.dumps(formatted_items, ensure_ascii=False, separators=(',', ':')) + '\n'
    
    @staticmethod
    def format_txt(items: List[Dict[str, Any]]) -> str:
        """格式化為文本
        
        Args:
            items: 數據項目列表
            
        Returns:
            str: 文本格式字符串
        """
        lines = []
        for item in items:
            timestamp = datetime.fromtimestamp(item.get('timestamp', time.time()))
            line = f"[{timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}] " \
                   f"{item.get('item', '')}: {item.get('value', '')}\n"
            lines.append(line)
        
        return ''.join(lines)


class FileWriterActor(ActorBase):
    """文件寫入Actor
    
    負責高性能的文件寫入和管理
    """
    
    def __init__(self, name: str, config: Optional[Dict] = None):
        """初始化文件寫入Actor
        
        Args:
            name: Actor名稱
            config: 配置字典
        """
        super().__init__(name, config)
        
        # 文件配置
        self.file_configs: Dict[str, FileConfig] = {}
        self.file_managers: Dict[str, FileRotationManager] = {}
        
        # 寫入配置
        self.batch_size = self.config.get('batch_size', 1000)
        self.flush_interval = self.config.get('flush_interval', 5.0)
        self.enable_compression = self.config.get('enable_compression', True)
        
        # 寫入隊列
        self.write_queue = asyncio.Queue(maxsize=self.config.get('queue_size', 10000))
        self.write_buffer: Dict[str, List[Dict]] = {}
        
        # 統計信息
        self.stats = {
            'files_written': 0,
            'bytes_written': 0,
            'items_written': 0,
            'batches_written': 0,
            'write_errors': 0,
            'rotations_performed': 0
        }
        
        # 任務管理
        self.write_task: Optional[asyncio.Task] = None
        self.flush_task: Optional[asyncio.Task] = None
    
    async def on_start(self):
        """Actor啟動時的初始化"""
        try:
            # 解析文件配置
            await self._parse_file_configs()
            
            # 初始化文件管理器
            await self._initialize_file_managers()
            
            # 啟動寫入任務
            self.write_task = asyncio.create_task(self._write_loop())
            
            # 啟動刷新任務
            self.flush_task = asyncio.create_task(self._flush_loop())
            
            self.logger.info(f"文件寫入Actor {self.name} 啟動成功")
            
        except Exception as e:
            self.logger.error(f"文件寫入Actor啟動失敗: {str(e)}")
            raise
    
    async def on_stop(self):
        """Actor停止時的清理"""
        try:
            # 停止寫入任務
            if self.write_task:
                self.write_task.cancel()
                try:
                    await self.write_task
                except asyncio.CancelledError:
                    pass
            
            # 停止刷新任務
            if self.flush_task:
                self.flush_task.cancel()
                try:
                    await self.flush_task
                except asyncio.CancelledError:
                    pass
            
            # 刷新並關閉所有文件
            await self._flush_all_files()
            await self._close_all_files()
            
            self.logger.info(f"文件寫入Actor {self.name} 停止成功")
            
        except Exception as e:
            self.logger.error(f"文件寫入Actor停止失敗: {str(e)}")
    
    async def handle_message(self, message: Message):
        """處理接收到的消息"""
        try:
            if message.type == MessageType.FILE_WRITE:
                await self._handle_file_write_message(message)
            elif message.type == MessageType.FILE_ROTATE:
                await self._handle_file_rotate_message(message)
            else:
                self.logger.warning(f"未知消息類型: {message.type}")
                
        except Exception as e:
            self.logger.error(f"處理文件消息失敗: {str(e)}")
            self.stats['write_errors'] += 1
    
    async def _handle_file_write_message(self, message: Message):
        """處理文件寫入消息"""
        try:
            data = message.data
            
            if 'items' in data:
                # 批量寫入
                items = data['items']
                file_type = data.get('file_type', 'default')
                await self._queue_batch_write(file_type, items)
            else:
                # 單個寫入
                item = {
                    'item': data.get('item', ''),
                    'value': data.get('value', ''),
                    'timestamp': data.get('timestamp', time.time()),
                    'metadata': data.get('metadata', {})
                }
                file_type = data.get('file_type', 'default')
                await self._queue_single_write(file_type, item)
                
        except Exception as e:
            self.logger.error(f"處理文件寫入消息失敗: {str(e)}")
            self.stats['write_errors'] += 1
    
    async def _handle_file_rotate_message(self, message: Message):
        """處理文件輪轉消息"""
        try:
            file_type = message.data.get('file_type', 'all')
            
            if file_type == 'all':
                # 輪轉所有文件
                for manager in self.file_managers.values():
                    await manager._rotate_file()
                    self.stats['rotations_performed'] += 1
            else:
                # 輪轉指定文件
                if file_type in self.file_managers:
                    await self.file_managers[file_type]._rotate_file()
                    self.stats['rotations_performed'] += 1
                    
        except Exception as e:
            self.logger.error(f"處理文件輪轉消息失敗: {str(e)}")
    
    async def _queue_single_write(self, file_type: str, item: Dict):
        """隊列單個寫入
        
        Args:
            file_type: 文件類型
            item: 數據項目
        """
        try:
            await self.write_queue.put((file_type, [item]))
        except asyncio.QueueFull:
            self.logger.warning("寫入隊列已滿，丟棄數據")
            self.stats['write_errors'] += 1
    
    async def _queue_batch_write(self, file_type: str, items: List[Dict]):
        """隊列批量寫入
        
        Args:
            file_type: 文件類型
            items: 數據項目列表
        """
        try:
            await self.write_queue.put((file_type, items))
        except asyncio.QueueFull:
            self.logger.warning("寫入隊列已滿，丟棄數據")
            self.stats['write_errors'] += len(items)
    
    async def _write_loop(self):
        """寫入循環"""
        self.logger.info("文件寫入循環開始")
        
        while self.running:
            try:
                # 獲取寫入任務
                file_type, items = await self.write_queue.get()
                
                # 添加到緩衝區
                if file_type not in self.write_buffer:
                    self.write_buffer[file_type] = []
                
                self.write_buffer[file_type].extend(items)
                
                # 檢查是否需要刷新
                if len(self.write_buffer[file_type]) >= self.batch_size:
                    await self._flush_file_buffer(file_type)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"寫入循環錯誤: {str(e)}")
                self.stats['write_errors'] += 1
                await asyncio.sleep(0.1)
        
        self.logger.info("文件寫入循環結束")
    
    async def _flush_loop(self):
        """刷新循環"""
        while self.running:
            try:
                await asyncio.sleep(self.flush_interval)
                await self._flush_all_buffers()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"刷新循環錯誤: {str(e)}")
    
    async def _flush_file_buffer(self, file_type: str):
        """刷新文件緩衝區
        
        Args:
            file_type: 文件類型
        """
        try:
            if file_type not in self.write_buffer or not self.write_buffer[file_type]:
                return
            
            items = self.write_buffer[file_type]
            self.write_buffer[file_type] = []
            
            # 獲取文件管理器
            if file_type not in self.file_managers:
                self.logger.warning(f"未找到文件管理器: {file_type}")
                return
            
            manager = self.file_managers[file_type]
            config = self.file_configs[file_type]
            
            # 格式化數據
            if config.format == 'csv':
                formatted_data = DataFormatter.format_csv(items)
            elif config.format == 'json':
                formatted_data = DataFormatter.format_json(items)
            else:
                formatted_data = DataFormatter.format_txt(items)
            
            # 寫入文件
            success = await manager.write_data(formatted_data)
            
            if success:
                self.stats['items_written'] += len(items)
                self.stats['bytes_written'] += len(formatted_data.encode('utf-8'))
                self.stats['batches_written'] += 1
            else:
                self.stats['write_errors'] += len(items)
                
        except Exception as e:
            self.logger.error(f"刷新文件緩衝區失敗: {str(e)}")
            self.stats['write_errors'] += len(items) if 'items' in locals() else 1
    
    async def _flush_all_buffers(self):
        """刷新所有緩衝區"""
        for file_type in list(self.write_buffer.keys()):
            await self._flush_file_buffer(file_type)
    
    async def _flush_all_files(self):
        """刷新所有文件"""
        await self._flush_all_buffers()
        
        for manager in self.file_managers.values():
            await manager.flush()
    
    async def _close_all_files(self):
        """關閉所有文件"""
        for manager in self.file_managers.values():
            await manager.close()
    
    async def _parse_file_configs(self):
        """解析文件配置"""
        files_config = self.config.get('files', {})
        
        for file_type, file_config in files_config.items():
            config = FileConfig(
                filename=file_config['filename'],
                format=file_config.get('format', 'csv'),
                max_size_mb=file_config.get('max_size_mb', 100),
                max_age_hours=file_config.get('max_age_hours', 24),
                compress=file_config.get('compress', True),
                buffer_size=file_config.get('buffer_size', 8192),
                flush_interval=file_config.get('flush_interval', 5.0)
            )
            
            self.file_configs[file_type] = config
            self.logger.info(f"配置文件: {file_type} -> {config.filename}")
    
    async def _initialize_file_managers(self):
        """初始化文件管理器"""
        for file_type, config in self.file_configs.items():
            manager = FileRotationManager(config)
            self.file_managers[file_type] = manager
    
    def get_file_stats(self) -> Dict[str, Any]:
        """獲取文件統計信息
        
        Returns:
            Dict: 文件統計信息
        """
        buffer_stats = {}
        for file_type, buffer in self.write_buffer.items():
            buffer_stats[file_type] = len(buffer)
        
        return {
            'write_stats': self.stats.copy(),
            'buffer_stats': buffer_stats,
            'queue_size': self.write_queue.qsize(),
            'file_configs': {
                file_type: {
                    'filename': config.filename,
                    'format': config.format,
                    'max_size_mb': config.max_size_mb
                }
                for file_type, config in self.file_configs.items()
            }
        }
