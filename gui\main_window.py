#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主視窗模組
包含 DDE 監控程式的主要 GUI 介面
"""

import os
import time
import glob
import logging
import configparser
from datetime import datetime
from collections import deque
from typing import Dict, Optional

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QLineEdit, QTableWidget,
                             QTableWidgetItem, QTextEdit, QSplitter, QGroupBox,
                             QFormLayout, QCheckBox, QMessageBox, QApplication, QDialog, QSpinBox, QStatusBar)
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QFont

# 導入自定義模組
from dydde.dydde import DDEClient
from core.data_handler import ItemData, DataRow, RawDataRow, DataFileHandler, DataProcessor
from core.auto_manager import AutoConnectManager
from gui.settings_dialog import AutoSettingsDialog
from utils.resource_monitor import ResourceMonitor


class QTextEditLogHandler(logging.Handler):
    """自定義日誌處理器，將日誌輸出到QTextEdit"""

    def __init__(self, text_edit):
        super().__init__()
        self.text_edit = text_edit

    def emit(self, record):
        """發送日誌記錄到QTextEdit"""
        try:
            msg = self.format(record)
            # 直接在主線程中執行，因為GUI操作必須在主線程
            if self.text_edit:
                self.text_edit.append(msg)
                self._limit_lines()

        except Exception as e:
            # 忽略日誌處理錯誤，避免無限循環
            print(f"日誌處理器錯誤: {e}")

    def _limit_lines(self):
        """限制日誌顯示行數"""
        try:
            if self.text_edit and self.text_edit.document().lineCount() > 500:
                cursor = self.text_edit.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                # 刪除前100行
                for _ in range(100):
                    cursor.select(cursor.SelectionType.LineUnderCursor)
                    cursor.removeSelectedText()
                    if cursor.hasSelection():
                        cursor.deleteChar()  # 刪除換行符

        except Exception as e:
            print(f"限制日誌行數錯誤: {e}")


class NonModalConfirmDialog(QDialog):
    """非模態確認對話框"""

    def __init__(self, parent, title, message, on_confirm_callback):
        super().__init__(parent)
        self.on_confirm_callback = on_confirm_callback

        self.setWindowTitle(title)
        self.setModal(False)  # 設為非模態
        self.setFixedSize(300, 120)

        # 佈局
        layout = QVBoxLayout(self)

        # 訊息標籤
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        layout.addWidget(message_label)

        # 按鈕區域
        button_layout = QHBoxLayout()

        confirm_btn = QPushButton("確認")
        confirm_btn.clicked.connect(self.on_confirm)

        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(confirm_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # 設定樣式
        confirm_btn.setStyleSheet("background-color: #d32f2f; color: white; font-weight: bold;")
        cancel_btn.setStyleSheet("background-color: #757575; color: white;")

    def on_confirm(self):
        """確認按鈕點擊處理"""
        self.accept()
        if self.on_confirm_callback:
            self.on_confirm_callback()

class DDEMainWindow(QMainWindow):
    """DDE 監控主視窗"""
    
    # 信號定義
    settings_changed = Signal(dict)
    
    def __init__(self, config: configparser.ConfigParser, logger: logging.Logger, 
                 auto_manager: AutoConnectManager):
        super().__init__()
        
        # 基本屬性
        self.config = config
        self.logger = logger
        self.auto_manager = auto_manager
        
        # DDE 相關
        self.dde_client = None
        
        # 資料處理
        self.items_data: Dict[str, ItemData] = {}
        self.raw_data: deque = deque(maxlen=1000)
        self.file_handler = DataFileHandler()
        self.data_processor = DataProcessor()
        self.process_thread = QThread()
        
        # 計時器和狀態
        self.last_advise_time = None
        self.timer = QTimer()
        self._auto_shutdown_in_progress = False
        self._manual_exit_confirmed = False

        # GUI更新控制
        self.gui_update_timer = QTimer()
        self.gui_update_interval = 10  # 默認10ms更新一次
        self.pending_raw_data = []
        self.pending_processed_data = []
        self.raw_data_paused = False
        self.processed_data_paused = False
        self.log_data_paused = False

        # 配置變數
        self.enable_time_newline = False
        self.time_newline_interval = 0.8
        self.enable_value_change_check = True
        self.value_change_check_item = ""

        # GUI 元件
        self.auto_status_label = None
        self.quick_auto_connect_cb = None
        self.quick_auto_shutdown_cb = None
        self.auto_settings_dialog = None

        # 資源監控
        self.resource_monitor = ResourceMonitor(self.logger)
        
        # 初始化
        self.load_table_config()
        self.init_data_containers()
        self.init_ui()
        self.init_status_bar()  # 初始化状态栏
        self.init_log_handler()  # 初始化日誌處理器
        self.init_data_processor()
        self.init_file_handler()
        self.init_timer()
        self.init_gui_update_timer()  # 初始化GUI更新定時器
        
        # 更新 GUI 狀態
        self.update_quick_switches()
        self.update_auto_status_display()
        
        self.logger.info("主視窗初始化完成")

    def load_table_config(self):
        """載入表格相關配置"""
        try:
            if 'Table' in self.config:
                self.enable_time_newline = self.config.getboolean('Table', 'enable_time_newline', fallback=False)
                self.time_newline_interval = self.config.getfloat('Table', 'time_newline_interval', fallback=0.8)
                self.enable_value_change_check = self.config.getboolean('Table', 'enable_value_change_check', fallback=True)
                self.value_change_check_mode = self.config.get('Table', 'value_change_check_mode', fallback='single')
                self.value_change_check_item = self.config.get('Table', 'value_change_check_item', fallback='')
                self.value_change_check_items = self.config.get('Table', 'value_change_check_items', fallback='')

                self.logger.info(f"載入表格配置: 時間換行={self.enable_time_newline}, 間隔={self.time_newline_interval}, 值變化檢查={self.enable_value_change_check}")
                self.logger.info(f"值變化檢查模式: {self.value_change_check_mode}, 檢查項目: {self.value_change_check_items}")
            else:
                self.logger.warning("設定檔中未找到 Table 區塊")

        except Exception as e:
            self.logger.error(f"載入表格配置失敗: {str(e)}")

    def init_data_containers(self):
        """初始化資料容器"""
        try:
            self.items_data.clear()
            self.raw_data = deque(maxlen=1000)  # 初始化原始資料佇列

            if 'Items' in self.config:
                items_section = self.config['Items']
                
                # 讀取所有項目
                item_names = {}
                item_codes = {}
                
                for key in items_section:
                    if key.endswith('_name'):
                        item_num = key.replace('_name', '')
                        item_names[item_num] = items_section[key]
                    elif key.endswith('_code'):
                        item_num = key.replace('_code', '')
                        item_codes[item_num] = items_section[key]
                
                # 建立項目資料
                for item_num in item_names:
                    if item_num in item_codes:
                        name = item_names[item_num]
                        code = item_codes[item_num]
                        self.items_data[code] = ItemData(name=name, code=code)
                
                self.logger.info(f"載入 {len(self.items_data)} 個監控項目")
            else:
                self.logger.warning("設定檔中未找到 Items 區塊")
                
        except Exception as e:
            self.logger.error(f"初始化資料容器失敗: {str(e)}")
    
    def init_ui(self):
        """初始化使用者介面"""
        self.setWindowTitle("DDE 監控程式 v6.1 (模組化版)")
        self.setGeometry(100, 100, 1200, 800)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主佈局
        main_layout = QVBoxLayout(central_widget)
        
        # 創建各個區域
        self.create_connection_area(main_layout)
        self.create_auto_status_area(main_layout)
        self.create_items_area(main_layout)
        self.create_data_area(main_layout)
        
        # 設定字體
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
    
    def create_connection_area(self, parent_layout):
        """創建連線控制區域"""
        group = QGroupBox("DDE 連線設定")
        layout = QFormLayout(group)
        
        # 服務和主題輸入
        self.service_edit = QLineEdit()
        self.topic_edit = QLineEdit()
        
        # 從設定檔載入預設值
        if 'DDE' in self.config:
            self.service_edit.setText(self.config.get('DDE', 'service', fallback=''))
            self.topic_edit.setText(self.config.get('DDE', 'topic', fallback=''))
        
        layout.addRow("服務名稱:", self.service_edit)
        layout.addRow("主題:", self.topic_edit)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        self.service_connect_btn = QPushButton("服務連線")
        self.service_connect_btn.clicked.connect(self.toggle_service_connection)
        
        self.test_items_btn = QPushButton("測試項目")
        self.test_items_btn.clicked.connect(self.test_items)
        self.test_items_btn.setEnabled(False)
        
        self.subscribe_btn = QPushButton("訂閱項目")
        self.subscribe_btn.clicked.connect(self.toggle_subscription)
        self.subscribe_btn.setEnabled(False)
        
        self.connect_btn = QPushButton("連線並訂閱")
        self.connect_btn.clicked.connect(self.toggle_connection)
        
        button_layout.addWidget(self.service_connect_btn)
        button_layout.addWidget(self.test_items_btn)
        button_layout.addWidget(self.subscribe_btn)
        button_layout.addWidget(self.connect_btn)
        button_layout.addStretch()
        
        # 狀態標籤
        self.status_label = QLabel("尚未連線")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        button_layout.addWidget(self.status_label)
        
        layout.addRow(button_layout)
        parent_layout.addWidget(group)
    
    def create_auto_status_area(self, parent_layout):
        """創建自動化狀態區域"""
        group = QGroupBox("自動化功能")
        layout = QHBoxLayout(group)

        # 自動狀態顯示
        self.auto_status_label = QLabel("⚫ 手動連線 | ⚫ 手動結束")
        self.auto_status_label.setStyleSheet("color: blue; font-weight: bold;")
        layout.addWidget(self.auto_status_label)

        layout.addStretch()

        # GUI更新頻率控制
        gui_control_layout = QHBoxLayout()
        gui_control_layout.addWidget(QLabel("GUI更新間隔:"))

        self.gui_update_spinbox = QSpinBox()
        self.gui_update_spinbox.setRange(5, 5000)  # 5ms 到 5秒
        self.gui_update_spinbox.setValue(self.gui_update_interval)
        self.gui_update_spinbox.setSuffix(" ms")
        self.gui_update_spinbox.valueChanged.connect(self.on_gui_update_interval_changed)
        gui_control_layout.addWidget(self.gui_update_spinbox)

        layout.addLayout(gui_control_layout)
        layout.addStretch()

        # 快速開關
        self.quick_auto_connect_cb = QCheckBox("自動連線")
        self.quick_auto_connect_cb.stateChanged.connect(self.on_quick_auto_connect_changed)
        layout.addWidget(self.quick_auto_connect_cb)

        self.quick_auto_shutdown_cb = QCheckBox("自動結束")
        self.quick_auto_shutdown_cb.stateChanged.connect(self.on_quick_auto_shutdown_changed)
        layout.addWidget(self.quick_auto_shutdown_cb)

        # 資源監控控制
        resource_layout = QHBoxLayout()
        self.resource_monitor_btn = QPushButton("開始監控")
        self.resource_monitor_btn.setCheckable(True)
        self.resource_monitor_btn.clicked.connect(self.toggle_resource_monitoring)
        self.resource_stats_btn = QPushButton("資源統計")
        self.resource_stats_btn.clicked.connect(self.show_resource_stats)
        resource_layout.addWidget(self.resource_monitor_btn)
        resource_layout.addWidget(self.resource_stats_btn)
        layout.addLayout(resource_layout)

        # 設定按鈕
        self.auto_settings_btn = QPushButton("自動化設定")
        self.auto_settings_btn.setStyleSheet("background-color: green; color: white; font-weight: bold;")
        self.auto_settings_btn.clicked.connect(self.show_auto_settings)
        layout.addWidget(self.auto_settings_btn)

        parent_layout.addWidget(group)
    
    def create_items_area(self, parent_layout):
        """創建項目監控區域"""
        group = QGroupBox("商品監控項目")
        layout = QVBoxLayout(group)
        
        # 項目表格
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(4)
        self.items_table.setHorizontalHeaderLabels(["項目名稱", "項目代碼", "當前值", "狀態"])
        
        # 設定表格屬性
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.items_table)
        parent_layout.addWidget(group)
        
        # 更新項目表格
        self.update_items_table()
    
    def create_data_area(self, parent_layout):
        """創建資料顯示區域"""
        splitter = QSplitter(Qt.Horizontal)

        # 原始資料顯示
        raw_group = QGroupBox("原始資料")
        raw_layout = QVBoxLayout(raw_group)

        # 原始資料控制按鈕
        raw_control_layout = QHBoxLayout()
        self.raw_pause_btn = QPushButton("暫停")
        self.raw_pause_btn.setCheckable(True)
        self.raw_pause_btn.clicked.connect(self.toggle_raw_data_pause)
        self.raw_clear_btn = QPushButton("清除")
        self.raw_clear_btn.clicked.connect(self.clear_raw_data)
        raw_control_layout.addWidget(self.raw_pause_btn)
        raw_control_layout.addWidget(self.raw_clear_btn)
        raw_control_layout.addStretch()
        raw_layout.addLayout(raw_control_layout)

        self.raw_data_text = QTextEdit()
        self.raw_data_text.setMaximumHeight(200)
        self.raw_data_text.setFont(QFont("Consolas", 9))
        raw_layout.addWidget(self.raw_data_text)

        splitter.addWidget(raw_group)

        # 處理後資料顯示
        processed_group = QGroupBox("處理後資料")
        processed_layout = QVBoxLayout(processed_group)

        # 處理後資料控制按鈕
        processed_control_layout = QHBoxLayout()
        self.processed_pause_btn = QPushButton("暫停")
        self.processed_pause_btn.setCheckable(True)
        self.processed_pause_btn.clicked.connect(self.toggle_processed_data_pause)
        self.processed_clear_btn = QPushButton("清除")
        self.processed_clear_btn.clicked.connect(self.clear_processed_data)
        processed_control_layout.addWidget(self.processed_pause_btn)
        processed_control_layout.addWidget(self.processed_clear_btn)
        processed_control_layout.addStretch()
        processed_layout.addLayout(processed_control_layout)

        self.processed_data_text = QTextEdit()
        self.processed_data_text.setMaximumHeight(200)
        self.processed_data_text.setFont(QFont("Consolas", 9))
        processed_layout.addWidget(self.processed_data_text)

        splitter.addWidget(processed_group)

        # 程式日誌顯示
        log_group = QGroupBox("程式日誌")
        log_layout = QVBoxLayout(log_group)

        # 程式日誌控制按鈕
        log_control_layout = QHBoxLayout()
        self.log_pause_btn = QPushButton("暫停")
        self.log_pause_btn.setCheckable(True)
        self.log_pause_btn.clicked.connect(self.toggle_log_data_pause)
        self.log_clear_btn = QPushButton("清除")
        self.log_clear_btn.clicked.connect(self.clear_log_data)
        log_control_layout.addWidget(self.log_pause_btn)
        log_control_layout.addWidget(self.log_clear_btn)
        log_control_layout.addStretch()
        log_layout.addLayout(log_control_layout)

        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)

        splitter.addWidget(log_group)
        parent_layout.addWidget(splitter)

    def init_status_bar(self):
        """初始化状态栏"""
        try:
            # 创建状态栏
            self.status_bar = QStatusBar()
            self.setStatusBar(self.status_bar)

            # 状态栏左侧信息
            self.status_bar.showMessage("DDE監控程序已啟動")

            # 状态栏右侧时间显示
            self.datetime_label = QLabel()
            self.datetime_label.setStyleSheet("color: blue; font-weight: bold;")
            self.status_bar.addPermanentWidget(self.datetime_label)

            # 启动时间更新定时器
            self.datetime_timer = QTimer()
            self.datetime_timer.timeout.connect(self.update_datetime_display)
            self.datetime_timer.start(100)  # 每100ms更新一次，显示毫秒

            self.logger.info("狀態欄初始化完成")

        except Exception as e:
            self.logger.error(f"初始化狀態欄失敗: {str(e)}")

    def update_datetime_display(self):
        """更新日期时间显示"""
        try:
            current_time = datetime.now()
            time_str = current_time.strftime('%Y/%m/%d %H:%M:%S.%f')[:-3]  # 显示毫秒
            self.datetime_label.setText(time_str)
        except Exception as e:
            self.logger.error(f"更新日期時間顯示失敗: {str(e)}")

    def init_log_handler(self):
        """初始化日誌處理器"""
        try:
            # 創建自定義日誌處理器
            self.gui_log_handler = QTextEditLogHandler(self.log_text)

            # 設定格式器
            formatter = logging.Formatter(
                '[%(asctime)s] [%(levelname)s] %(message)s',
                datefmt='%H:%M:%S'
            )
            self.gui_log_handler.setFormatter(formatter)

            # 設定日誌級別（顯示INFO及以上級別）
            self.gui_log_handler.setLevel(logging.INFO)

            # 添加到logger
            self.logger.addHandler(self.gui_log_handler)

            self.logger.info("GUI日誌處理器初始化完成")

        except Exception as e:
            # 如果GUI日誌處理器初始化失敗，記錄到原有日誌
            self.logger.error(f"初始化GUI日誌處理器失敗: {str(e)}")

    def init_data_processor(self):
        """初始化資料處理器"""
        try:
            # 移動資料處理器到獨立線程
            self.data_processor.moveToThread(self.process_thread)

            # 連接信號
            self.data_processor.data_processed.connect(self._process_advise_data)
            self.process_thread.started.connect(self.data_processor.process_data)

            # 啟動線程
            self.process_thread.start()

            self.logger.info("資料處理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化資料處理器失敗: {str(e)}")

    def init_file_handler(self):
        """初始化檔案處理器"""
        try:
            self.file_handler.init_files(self.config, self.items_data)
            self.logger.info("檔案處理器初始化完成")

        except Exception as e:
            self.logger.error(f"初始化檔案處理器失敗: {str(e)}")

    def init_timer(self):
        """初始化定時器"""
        try:
            self.timer.timeout.connect(self.check_time_interval)
            self.timer.start(10)  # 每 10ms檢查一次

        except Exception as e:
            self.logger.error(f"初始化定時器失敗: {str(e)}")

    def init_gui_update_timer(self):
        """初始化GUI更新定時器"""
        try:
            self.gui_update_timer.timeout.connect(self.update_gui_displays)
            self.gui_update_timer.start(self.gui_update_interval)
            self.logger.info(f"GUI更新定時器已啟動，間隔: {self.gui_update_interval}ms")

        except Exception as e:
            self.logger.error(f"初始化GUI更新定時器失敗: {str(e)}")

    def update_items_table(self):
        """更新項目表格"""
        try:
            self.items_table.setRowCount(len(self.items_data))

            for row, (code, item_data) in enumerate(self.items_data.items()):
                self.items_table.setItem(row, 0, QTableWidgetItem(item_data.name))
                self.items_table.setItem(row, 1, QTableWidgetItem(item_data.code))
                self.items_table.setItem(row, 2, QTableWidgetItem(str(item_data.value)))
                self.items_table.setItem(row, 3, QTableWidgetItem(item_data.status))

                # 根據狀態設定顏色
                status_item = self.items_table.item(row, 3)
                if item_data.status == "已訂閱":
                    status_item.setBackground(Qt.green)
                elif item_data.status == "已測試":
                    status_item.setBackground(Qt.yellow)
                elif "失敗" in item_data.status:
                    status_item.setBackground(Qt.red)

            # 調整列寬
            self.items_table.resizeColumnsToContents()

        except Exception as e:
            self.logger.error(f"更新項目表格失敗: {str(e)}")

    def on_gui_update_interval_changed(self, value):
        """GUI更新間隔變更處理"""
        try:
            self.gui_update_interval = value
            self.gui_update_timer.stop()
            self.gui_update_timer.start(self.gui_update_interval)
            self.logger.info(f"GUI更新間隔已調整為: {value}ms")
        except Exception as e:
            self.logger.error(f"調整GUI更新間隔失敗: {str(e)}")

    def update_gui_displays(self):
        """批量更新GUI顯示"""
        try:
            # 更新原始資料顯示
            if not self.raw_data_paused and self.pending_raw_data:
                for raw_text in self.pending_raw_data:
                    self.raw_data_text.append(raw_text)
                self.pending_raw_data.clear()
                self._limit_raw_data_lines()

            # 更新處理後資料顯示
            if not self.processed_data_paused and self.pending_processed_data:
                for processed_text in self.pending_processed_data:
                    self.processed_data_text.append(processed_text)
                self.pending_processed_data.clear()
                self._limit_processed_data_lines()

        except Exception as e:
            self.logger.error(f"批量更新GUI顯示失敗: {str(e)}")

    def _limit_raw_data_lines(self):
        """限制原始資料顯示行數"""
        try:
            if self.raw_data_text.document().lineCount() > 100: # 限制原始資料顯示行數為100行
                cursor = self.raw_data_text.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                cursor.select(cursor.SelectionType.LineUnderCursor)
                cursor.removeSelectedText()
                if cursor.hasSelection():
                    cursor.deleteChar()
        except Exception as e:
            self.logger.debug(f"限制原始資料行數失敗: {str(e)}")

    def _limit_processed_data_lines(self):
        """限制處理後資料顯示行數"""
        try:
            if self.processed_data_text.document().lineCount() > 100: # 限制處理後資料顯示行數為100行
                cursor = self.processed_data_text.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                cursor.select(cursor.SelectionType.LineUnderCursor)
                cursor.removeSelectedText()
                if cursor.hasSelection():
                    cursor.deleteChar()
        except Exception as e:
            self.logger.debug(f"限制處理後資料行數失敗: {str(e)}")

    # 資訊框控制方法
    def toggle_raw_data_pause(self):
        """切換原始資料暫停/恢復"""
        self.raw_data_paused = not self.raw_data_paused
        if self.raw_data_paused:
            self.raw_pause_btn.setText("恢復")
            self.raw_pause_btn.setStyleSheet("background-color: orange; color: white;")
            self.logger.info("原始資料顯示已暫停")
        else:
            self.raw_pause_btn.setText("暫停")
            self.raw_pause_btn.setStyleSheet("")
            self.logger.info("原始資料顯示已恢復")

    def clear_raw_data(self):
        """清除原始資料"""
        self.raw_data_text.clear()
        self.pending_raw_data.clear()
        self.logger.info("原始資料已清除")

    def toggle_processed_data_pause(self):
        """切換處理後資料暫停/恢復"""
        self.processed_data_paused = not self.processed_data_paused
        if self.processed_data_paused:
            self.processed_pause_btn.setText("恢復")
            self.processed_pause_btn.setStyleSheet("background-color: orange; color: white;")
            self.logger.info("處理後資料顯示已暫停")
        else:
            self.processed_pause_btn.setText("暫停")
            self.processed_pause_btn.setStyleSheet("")
            self.logger.info("處理後資料顯示已恢復")

    def clear_processed_data(self):
        """清除處理後資料"""
        self.processed_data_text.clear()
        self.pending_processed_data.clear()
        self.logger.info("處理後資料已清除")

    def toggle_log_data_pause(self):
        """切換程式日誌暫停/恢復"""
        self.log_data_paused = not self.log_data_paused
        if self.log_data_paused:
            self.log_pause_btn.setText("恢復")
            self.log_pause_btn.setStyleSheet("background-color: orange; color: white;")
            # 暫停日誌處理器
            if hasattr(self, 'gui_log_handler'):
                self.gui_log_handler.setLevel(logging.CRITICAL + 1)  # 設定為最高級別，實際上停止輸出
            self.logger.info("程式日誌顯示已暫停")
        else:
            self.log_pause_btn.setText("暫停")
            self.log_pause_btn.setStyleSheet("")
            # 恢復日誌處理器
            if hasattr(self, 'gui_log_handler'):
                self.gui_log_handler.setLevel(logging.INFO)
            self.logger.info("程式日誌顯示已恢復")

    def clear_log_data(self):
        """清除程式日誌"""
        self.log_text.clear()
        self.logger.info("程式日誌已清除")

    # 資源監控控制方法
    def toggle_resource_monitoring(self):
        """切換資源監控開關"""
        try:
            if self.resource_monitor_btn.isChecked():
                # 開始監控
                self.resource_monitor.start_monitoring(interval=1.0)
                self.resource_monitor_btn.setText("停止監控")
                self.resource_monitor_btn.setStyleSheet("background-color: red; color: white;")
                self.logger.info("資源監控已啟動")
            else:
                # 停止監控
                self.resource_monitor.stop_monitoring()
                self.resource_monitor_btn.setText("開始監控")
                self.resource_monitor_btn.setStyleSheet("")
                self.logger.info("資源監控已停止")
        except Exception as e:
            self.logger.error(f"切換資源監控失敗: {str(e)}")

    def show_resource_stats(self):
        """顯示資源統計"""
        try:
            stats_text = self.resource_monitor.get_formatted_stats()

            # 創建非模態對話框顯示統計信息
            dialog = QDialog(self)
            dialog.setWindowTitle("資源使用統計")
            dialog.setModal(False)
            dialog.resize(500, 600)

            layout = QVBoxLayout(dialog)

            # 統計信息顯示
            stats_display = QTextEdit()
            stats_display.setPlainText(stats_text)
            stats_display.setReadOnly(True)
            stats_display.setFont(QFont("Consolas", 9))
            layout.addWidget(stats_display)

            # 按鈕區域
            button_layout = QHBoxLayout()

            refresh_btn = QPushButton("刷新")
            refresh_btn.clicked.connect(lambda: stats_display.setPlainText(
                self.resource_monitor.get_formatted_stats()))

            reset_btn = QPushButton("重置統計")
            reset_btn.clicked.connect(lambda: self._reset_resource_stats(stats_display))

            save_btn = QPushButton("保存到文件")
            save_btn.clicked.connect(self._save_resource_stats)

            close_btn = QPushButton("關閉")
            close_btn.clicked.connect(dialog.close)

            button_layout.addWidget(refresh_btn)
            button_layout.addWidget(reset_btn)
            button_layout.addWidget(save_btn)
            button_layout.addStretch()
            button_layout.addWidget(close_btn)

            layout.addLayout(button_layout)

            dialog.show()
            self.logger.info("顯示資源統計對話框")

        except Exception as e:
            self.logger.error(f"顯示資源統計失敗: {str(e)}")

    def _reset_resource_stats(self, stats_display):
        """重置資源統計"""
        try:
            self.resource_monitor.reset_stats()
            stats_display.setPlainText(self.resource_monitor.get_formatted_stats())
            self.logger.info("資源統計已重置")
        except Exception as e:
            self.logger.error(f"重置資源統計失敗: {str(e)}")

    def _save_resource_stats(self):
        """保存資源統計到文件"""
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"resource_stats_{timestamp}.txt"

            # 保存到logs目錄
            log_dir = "logs"
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            file_path = os.path.join(log_dir, filename)
            self.resource_monitor.save_stats_to_file(file_path)

            # 顯示保存成功消息
            QMessageBox.information(self, "保存成功", f"資源統計已保存到:\n{file_path}")

        except Exception as e:
            self.logger.error(f"保存資源統計失敗: {str(e)}")
            QMessageBox.critical(self, "保存失敗", f"保存資源統計失敗:\n{str(e)}")

    def toggle_service_connection(self):
        """切換服務連線狀態"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                self.connect_service()
            else:
                self.disconnect_service_with_confirmation()
        except Exception as e:
            self.logger.error(f"切換服務連線狀態失敗: {str(e)}")

    def connect_service(self):
        """連接 DDE 服務"""
        try:
            service = self.service_edit.text()
            topic = self.topic_edit.text()

            if not service or not topic:
                QMessageBox.warning(self, "警告", "請輸入服務名稱和主題")
                return

            # 從設定檔讀取 disconnect_on_exit 設定
            disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)

            # 建立 DDE 客戶端
            self.dde_client = DDEClient(service, topic, disconnect_on_exit=disconnect_on_exit)
            self.dde_client.connect()

            self.status_label.setText("已連線")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
            self.service_connect_btn.setText("斷離服務")
            self.connect_btn.setText("退訂並斷線")
            self.test_items_btn.setEnabled(True)

            self.logger.info(f"已連接到 DDE 服務: {service}.{topic}")

        except Exception as e:
            self.logger.error(f"連接 DDE 服務失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"連接 DDE 服務失敗: {str(e)}")

    def disconnect_service(self):
        """斷開 DDE 服務連接"""
        try:
            if self.dde_client:
                disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
                self.dde_client.disconnect(terminate_dde=disconnect_on_exit)
                self.dde_client = None

            # 更新連線狀態
            self.status_label.setText("服務已斷線")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.service_connect_btn.setText("服務連線")
            self.connect_btn.setText("連線並訂閱")
            self.test_items_btn.setEnabled(False)
            self.subscribe_btn.setEnabled(False)
            self.subscribe_btn.setText("訂閱項目")

            # 更新所有監控項目狀態為已斷線
            self.update_items_status_on_disconnect()

            self.logger.info("已斷開DDE服務連接")

        except Exception as e:
            self.logger.error(f"斷開DDE服務連接失敗: {str(e)}")

    def disconnect_service_with_confirmation(self):
        """帶確認的斷開服務連接（手動操作）"""
        try:
            dialog = NonModalConfirmDialog(
                self,
                "確認斷離服務",
                "確定要斷離DDE服務嗎？",
                self.disconnect_service
            )
            dialog.show()
            self.logger.info("顯示斷離服務確認對話框")
        except Exception as e:
            self.logger.error(f"顯示斷離服務確認對話框失敗: {str(e)}")

    def test_items(self):
        """測試所有項目"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                QMessageBox.warning(self, "警告", "請先連接DDE服務")
                return

            # 測試所有項目
            for code, item_data in self.items_data.items():
                value = self.dde_client.request(code)
                if value is not None:
                    item_data.value = value
                    item_data.update_time = datetime.now()
                    item_data.status = "已測試"
                else:
                    item_data.status = "測試失敗"

            self.update_items_table()
            self.subscribe_btn.setEnabled(True)
            self.logger.info("項目測試完成")

        except Exception as e:
            self.logger.error(f"測試項目失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"測試項目失敗: {str(e)}")

    def toggle_subscription(self):
        """切換項目訂閱狀態（手動操作）"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                QMessageBox.warning(self, "警告", "請先連接DDE服務")
                return

            # 檢查當前訂閱狀態
            is_subscribed = any(item_data.status == "已訂閱" for item_data in self.items_data.values())

            if is_subscribed:
                # 手動取消訂閱需要確認
                self.unsubscribe_with_confirmation()
            else:
                # 訂閱所有項目（不需要確認）
                self.subscribe_all_items()

        except Exception as e:
            self.logger.error(f"切換訂閱狀態失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"切換訂閱狀態失敗: {str(e)}")

    def unsubscribe_with_confirmation(self):
        """帶確認的取消訂閱（手動操作）"""
        try:
            dialog = NonModalConfirmDialog(
                self,
                "確認取消訂閱",
                "確定要取消所有項目的訂閱嗎？",
                self.unsubscribe_all_items
            )
            dialog.show()
            self.logger.info("顯示取消訂閱確認對話框")
        except Exception as e:
            self.logger.error(f"顯示取消訂閱確認對話框失敗: {str(e)}")

    def subscribe_all_items(self):
        """訂閱所有項目（不需要確認）"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                QMessageBox.warning(self, "警告", "請先連接DDE服務")
                return

            # 訂閱所有項目
            for code, item_data in self.items_data.items():
                if item_data.status == "已測試":
                    if self.dde_client.advise(code, self.on_advise_data):
                        item_data.status = "已訂閱"
                    else:
                        item_data.status = "訂閱失敗"

            self.subscribe_btn.setText("取消訂閱")
            self.update_items_table()
            self.logger.info("已訂閱所有項目")

        except Exception as e:
            self.logger.error(f"訂閱所有項目失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"訂閱所有項目失敗: {str(e)}")

    def unsubscribe_all_items(self):
        """取消訂閱所有項目（不需要確認）"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                return

            # 取消訂閱所有項目
            for code, item_data in self.items_data.items():
                if item_data.status == "已訂閱":
                    self.dde_client.unadvise(code)
                    item_data.status = "已測試"

            self.subscribe_btn.setText("訂閱項目")
            self.update_items_table()
            self.logger.info("已取消所有項目訂閱")

        except Exception as e:
            self.logger.error(f"取消訂閱所有項目失敗: {str(e)}")

    def toggle_connection(self):
        """一鍵連線/斷線"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                self.connect_dde()
            else:
                self.disconnect_dde_with_confirmation()
        except Exception as e:
            self.logger.error(f"切換連接狀態失敗: {str(e)}")

    def connect_dde(self):
        """一鍵連線"""
        try:
            service = self.service_edit.text()
            topic = self.topic_edit.text()

            if not service or not topic:
                QMessageBox.warning(self, "警告", "請輸入服務和主題")
                return

            self.dde_client = DDEClient(service, topic)
            self.dde_client.connect()

            # 測試並訂閱所有項目
            self.test_and_subscribe_items()

            self.status_label.setText("已連線")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
            self.service_connect_btn.setText("斷離服務")
            self.connect_btn.setText("退訂並斷線")
            self.subscribe_btn.setText("取消訂閱")
            self.subscribe_btn.setEnabled(True)
            self.logger.info(f"成功連接到DDE服務: {service}, 主題: {topic}")

        except Exception as e:
            self.logger.error(f"連接DDE失敗: {str(e)}")
            self.status_label.setText("連線失敗")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            QMessageBox.critical(self, "錯誤", f"連接DDE失敗: {str(e)}")

    def disconnect_dde(self):
        """一鍵斷線"""
        try:
            if self.dde_client:
                disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
                self.dde_client.disconnect(terminate_dde=disconnect_on_exit)
                self.dde_client = None

            # 更新連線狀態
            self.status_label.setText("尚未連線")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.service_connect_btn.setText("服務連線")
            self.connect_btn.setText("連線並訂閱")
            self.subscribe_btn.setText("訂閱項目")
            self.subscribe_btn.setEnabled(False)

            # 更新所有監控項目狀態為已斷線
            self.update_items_status_on_disconnect()

            self.logger.info("已斷開DDE連接")

        except Exception as e:
            self.logger.error(f"斷開DDE連接失敗: {str(e)}")

    def disconnect_dde_with_confirmation(self):
        """帶確認的一鍵斷線（手動操作）"""
        try:
            dialog = NonModalConfirmDialog(
                self,
                "確認退訂並斷線",
                "確定要退訂所有項目並斷線嗎？",
                self.disconnect_dde
            )
            dialog.show()
            self.logger.info("顯示退訂並斷線確認對話框")
        except Exception as e:
            self.logger.error(f"顯示退訂並斷線確認對話框失敗: {str(e)}")

    def update_items_status_on_disconnect(self):
        """斷線時更新所有監控項目狀態"""
        try:
            for code, item_data in self.items_data.items():
                if item_data.status in ["已訂閱", "已測試"]:
                    item_data.status = "已斷線"
                    item_data.value = ""  # 清空值

            # 更新表格顯示
            self.update_items_table()
            self.logger.debug("已更新所有監控項目狀態為已斷線")

        except Exception as e:
            self.logger.error(f"更新監控項目斷線狀態失敗: {str(e)}")

    def test_and_subscribe_items(self):
        """測試並訂閱所有項目"""
        try:
            # 1. 先測試所有項目
            for code, item_data in self.items_data.items():
                value = self.dde_client.request(code)
                if value is not None:
                    item_data.value = value
                    item_data.update_time = datetime.now()
                    item_data.status = "已測試"
                else:
                    item_data.status = "測試失敗"

            self.update_items_table()

            # 2. 再訂閱所有項目
            for code, item_data in self.items_data.items():
                if item_data.status == "已測試":
                    if self.dde_client.advise(code, self.on_advise_data):
                        item_data.status = "已訂閱"
                    else:
                        item_data.status = "訂閱失敗"

            self.update_items_table()

        except Exception as e:
            self.logger.error(f"測試並訂閱項目失敗: {str(e)}")
            raise

    def on_advise_data(self, item: str, value: str):
        """處理DDE資料更新"""
        try:
            self.data_processor.add_data(item, value)
        except Exception as e:
            self.logger.error(f"加入資料到佇列失敗: {str(e)}")

    def _process_advise_data(self, item: str, value: str):
        """實際處理資料的方法"""
        try:
            self.logger.debug(f"[處理] 開始處理數據: {item} = {value}")

            # 1. 檢查是否需要項目重複換行
            if self.check_item_repeat_newline(item, value):
                return

            # 2. 更新原始資料
            self.update_raw_data(item, value)

            # 3. 更新項目資料
            if item in self.items_data:
                self.items_data[item].value = value
                self.items_data[item].update_time = datetime.now()

            # 4. 更新表格
            self.update_items_table()

            # 5. 添加到原始資料待更新佇列（而不是即時更新）
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            raw_text = f"[{timestamp}] {item}: {value}"
            self.pending_raw_data.append(raw_text)

            # 6. 更新最後接收時間
            self.last_advise_time = time.time()

        except Exception as e:
            self.logger.error(f"處理資料失敗: {str(e)}")

    def check_item_repeat_newline(self, item: str, value: str) -> bool:
        """檢查是否需要項目重複換行"""
        try:
            self.logger.debug(f"check_item_repeat_newline() - 檢查項目: {item}")

            if not self.raw_data:
                self.logger.debug("check_item_repeat_newline() - 無原始資料，不需要換行")
                return False

            current_row = self.raw_data[0]
            self.logger.debug(f"check_item_repeat_newline() - 當前行項目: {list(current_row.values.keys())}")

            # 如果項目已存在於當前行，則需要換行
            if item in current_row.values:
                self.logger.debug(f"check_item_repeat_newline() - 項目 {item} 已存在於當前行")

                # 補齊缺失資料
                if self.has_missing_data(current_row):
                    self.logger.debug("check_item_repeat_newline() - 當前行缺少資料，開始補齊")
                    self.fill_missing_data(current_row)

                # 如果啟用了值變化檢查，則檢查值是否有變化
                if self.enable_value_change_check:
                    has_changed = self.check_value_change(current_row)
                    self.logger.debug(f"check_item_repeat_newline() - 值變化檢查結果: {has_changed}")

                    if has_changed:
                        self.logger.debug("check_item_repeat_newline() - 值有變化，儲存完整資料行")
                        # 儲存完整資料行
                        self.file_handler.save_row(current_row, is_complete=True)
                        # 顯示處理後資料
                        self.display_processed_data(current_row)
                        # 建立新行
                        self.create_new_row()
                        # 將收到的項目值填入新行
                        new_row = self.raw_data[0]
                        new_row.values[item] = value
                        new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                        new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                        self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                        return True
                    else:
                        self.logger.debug("check_item_repeat_newline() - 值未變化，不儲存資料行")
                        # 值未變化，不儲存資料行，但建立新行
                        self.create_new_row()
                        # 將收到的項目值填入新行
                        new_row = self.raw_data[0]
                        new_row.values[item] = value
                        new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                        new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                        self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                        return True
                else:
                    # 未啟用值變化檢查，直接儲存資料行
                    self.logger.debug("check_item_repeat_newline() - 未啟用值變化檢查，直接儲存資料行")
                    self.file_handler.save_row(current_row, is_complete=True)
                    # 顯示處理後資料
                    self.display_processed_data(current_row)
                    self.create_new_row()
                    new_row = self.raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                    return True

            self.logger.debug(f"check_item_repeat_newline() - 項目 {item} 不存在於當前行")
            return False

        except Exception as e:
            self.logger.error(f"檢查項目重複換行失敗: {str(e)}")
            return False

    def update_raw_data(self, item: str, value: str):
        """更新原始資料
        更新原始資料佇列中的資料

        參數:
            item (str): 項目代碼
            value (str): 項目值
        """
        try:
            self.logger.debug(f"update_raw_data() - 更新原始資料: {item} = {value}")
            now = datetime.now()
            current_row = None

            # 檢查是否需要建立新行
            if not self.raw_data:
                self.logger.debug("update_raw_data() - 建立新行")
                current_row = RawDataRow(
                    receive_date=now.strftime("%Y-%m-%d"),
                    receive_time=now.strftime("%H:%M:%S.%f"),
                    values={}
                )
                self.raw_data.appendleft(current_row)
            else:
                current_row = self.raw_data[0]

            # 更新值
            current_row.values[item] = value
            current_row.receive_date = now.strftime("%Y-%m-%d")
            current_row.receive_time = now.strftime("%H:%M:%S.%f")

            # 儲存原始資料行到 data_file (與v6版本保持一致)
            self.file_handler.save_row(current_row, is_complete=False)

            self.logger.debug(f"update_raw_data() - 更新後的值: {current_row.values}")

        except Exception as e:
            self.logger.error(f"更新原始資料失敗: {str(e)}")
            raise

    def create_new_row(self):
        """建立新的資料行
        在原始資料佇列中建立新的資料行
        """
        now = datetime.now()
        new_row = RawDataRow(
            receive_date=now.strftime("%Y-%m-%d"),
            receive_time=now.strftime("%H:%M:%S.%f"),
            values={}
        )
        self.raw_data.appendleft(new_row)
        self.logger.debug("create_new_row() - 已建立新行")

    def has_missing_data(self, row: RawDataRow) -> bool:
        """檢查是否有缺失資料
        檢查資料行是否缺少任何項目的資料

        參數:
            row (RawDataRow): 要檢查的資料行

        回傳:
            bool: 是否有缺失資料
        """
        has_missing = len(row.values) < len(self.items_data)
        self.logger.debug(f"has_missing_data() - 檢查缺失資料: {has_missing}")
        return has_missing

    def fill_missing_data(self, row: RawDataRow):
        """補齊缺失資料
        使用項目資料容器中的最新值補齊缺失資料

        參數:
            row (RawDataRow): 要補齊的資料行
        """
        try:
            for code, item_data in self.items_data.items():
                if code not in row.values and item_data.value is not None:
                    row.values[code] = item_data.value
                    self.logger.debug(f"fill_missing_data() - 補齊缺失資料: {code} = {item_data.value}")

        except Exception as e:
            self.logger.error(f"補齊缺失資料失敗: {str(e)}")
            raise

    def check_value_change(self, current_row: RawDataRow) -> bool:
        """檢查值變化
        根據設定檢查指定項目的值是否發生變化

        參數:
            current_row (RawDataRow): 當前的資料行

        回傳:
            bool: 值是否發生變化
        """
        try:
            # 如果未啟用值變化檢查，直接返回 True
            if not self.enable_value_change_check:
                self.logger.debug("check_value_change() - 未啟用值變化檢查，返回 True")
                return True

            # 獲取檢查模式
            check_mode = self.value_change_check_mode
            self.logger.debug(f"check_value_change() - 檢查模式: {check_mode}")

            # 如果沒有前一行，直接返回 True
            if len(self.raw_data) <= 1:
                self.logger.debug("check_value_change() - 沒有前一行資料，這是第一筆資料，返回 True")
                return True

            previous_row = self.raw_data[1]

            # 根據不同模式進行檢查
            if check_mode == 'single':
                # 單一項目檢查
                check_item_name = self.value_change_check_items
                if not check_item_name:
                    self.logger.debug("check_value_change() - 未指定檢查項目，返回 True")
                    return True

                # 獲取檢查項目的代碼
                check_item_code = None
                for code, item_data in self.items_data.items():
                    if item_data.name == check_item_name:
                        check_item_code = code
                        break

                if not check_item_code:
                    self.logger.debug(f"check_value_change() - 找不到檢查項目 {check_item_name} 的代碼")
                    return True

                # 檢查值是否變化
                current_value = current_row.values.get(check_item_code, "")
                previous_value = previous_row.values.get(check_item_code, "")
                has_changed = current_value != previous_value

                self.logger.debug(f"check_value_change() - 單一項目檢查:")
                self.logger.debug(f"  項目名稱: {check_item_name}")
                self.logger.debug(f"  項目代碼: {check_item_code}")
                self.logger.debug(f"  當前值: {current_value}")
                self.logger.debug(f"  前一個值: {previous_value}")
                self.logger.debug(f"  是否有變化: {has_changed}")

                return has_changed

            elif check_mode == 'multiple':
                # 多個項目檢查
                check_items_str = self.value_change_check_items
                if not check_items_str:
                    self.logger.debug("check_value_change() - 未指定檢查項目，返回 True")
                    return True

                check_item_names = [item.strip() for item in check_items_str.split(',')]
                self.logger.debug(f"check_value_change() - 多個項目檢查: {check_item_names}")

                # 檢查每個項目
                for check_item_name in check_item_names:
                    # 獲取檢查項目的代碼
                    check_item_code = None
                    for code, item_data in self.items_data.items():
                        if item_data.name == check_item_name:
                            check_item_code = code
                            break

                    if not check_item_code:
                        self.logger.debug(f"check_value_change() - 找不到檢查項目 {check_item_name} 的代碼")
                        continue

                    # 檢查值是否變化
                    current_value = current_row.values.get(check_item_code, "")
                    previous_value = previous_row.values.get(check_item_code, "")

                    self.logger.debug(f"check_value_change() - 檢查項目 {check_item_name}:")
                    self.logger.debug(f"  項目代碼: {check_item_code}")
                    self.logger.debug(f"  當前值: {current_value}")
                    self.logger.debug(f"  前一個值: {previous_value}")

                    # 只要有一個項目值變化就返回 True
                    if current_value != previous_value:
                        self.logger.debug(f"check_value_change() - 項目 {check_item_name} 值有變化")
                        return True

                self.logger.debug("check_value_change() - 所有檢查項目值都沒有變化")
                return False

            elif check_mode == 'all':
                # 檢查所有項目（除了接收日期和接收時間）
                self.logger.debug("check_value_change() - 檢查所有項目")

                # 檢查每個項目
                for code, item_data in self.items_data.items():
                    # 跳過 receive_date 和 receive_time（這些不應該影響值變化檢查）
                    if code in ['receive_date', 'receive_time']:
                        continue

                    current_value = current_row.values.get(code, "")
                    previous_value = previous_row.values.get(code, "")

                    self.logger.debug(f"check_value_change() - 檢查項目 {item_data.name}:")
                    self.logger.debug(f"  項目代碼: {code}")
                    self.logger.debug(f"  當前值: {current_value}")
                    self.logger.debug(f"  前一個值: {previous_value}")

                    # 只要有一個項目值變化就返回 True
                    if current_value != previous_value:
                        self.logger.debug(f"check_value_change() - 項目 {item_data.name} 值有變化")
                        return True

                self.logger.debug("check_value_change() - 所有項目值都沒有變化")
                return False

            else:
                self.logger.warning(f"check_value_change() - 未知的檢查模式: {check_mode}")
                return True

        except Exception as e:
            self.logger.error(f"檢查值變化失敗: {str(e)}")
            return True

    def check_time_interval(self):
        """檢查時間間隔"""
        try:
            if not self.enable_time_newline or not self.last_advise_time:
                return

            current_time = time.time()
            if current_time - self.last_advise_time >= self.time_newline_interval:
                self.logger.debug("check_time_interval() - 時間間隔到達，檢查是否需要換行")

                if not self.raw_data:
                    self.logger.debug("check_time_interval() - 無原始資料，不需要處理")
                    return

                current_row = self.raw_data[0]
                self.logger.debug(f"check_time_interval() - 當前行項目數: {len(current_row.values)}")

                # 檢查行是否有資料
                if not current_row.values:
                    self.logger.debug("check_time_interval() - 當前行沒有資料")
                    return

                # 檢查並補齊缺失資料
                if self.has_missing_data(current_row):
                    self.logger.debug("check_time_interval() - 當前行缺少資料，開始補齊")
                    self.fill_missing_data(current_row)

                # 如果啟用了值變化檢查，則檢查值是否有變化
                if self.enable_value_change_check:
                    self.logger.debug(f"check_time_interval() - raw_data 長度: {len(self.raw_data)}")
                    has_changed = self.check_value_change(current_row)
                    self.logger.debug(f"check_time_interval() - 值變化檢查結果: {has_changed}")

                    if has_changed:
                        self.logger.debug("check_time_interval() - 值有變化，儲存完整資料行")
                        # 儲存完整資料行
                        self.file_handler.save_row(current_row, is_complete=True)
                        # 顯示處理後資料
                        self.display_processed_data(current_row)
                        # 建立新行
                        self.create_new_row()
                    else:
                        self.logger.debug("check_time_interval() - 值未變化，不儲存資料行")
                        # 值未變化，不儲存資料行，但建立新行
                        self.create_new_row()
                else:
                    # 未啟用值變化檢查，直接儲存資料行
                    self.logger.debug("check_time_interval() - 未啟用值變化檢查，直接儲存資料行")
                    self.file_handler.save_row(current_row, is_complete=True)
                    # 顯示處理後資料
                    self.display_processed_data(current_row)
                    self.create_new_row()

        except Exception as e:
            self.logger.error(f"檢查時間間隔失敗: {str(e)}")

    def on_quick_auto_connect_changed(self, state):
        """快速自動連線開關變更處理"""
        try:
            self.quick_auto_connect_cb.blockSignals(True)

            enabled = state == Qt.Checked

            # 更新設定檔
            if 'AutoConnect' not in self.config:
                self.config.add_section('AutoConnect')

            self.config.set('AutoConnect', 'enable_auto_connect', str(enabled))

            # 儲存設定檔
            with open('config.ini', 'w', encoding='utf-8') as f:
                self.config.write(f)

            # 重新載入自動管理器設定
            if hasattr(self, 'auto_manager'):
                self.auto_manager.load_settings(self.config)

            # 更新狀態顯示
            self.update_auto_status_display()

            self.logger.info(f"自動連線功能已{'啟用' if enabled else '停用'}")

        except Exception as e:
            self.logger.error(f"快速自動連線開關變更失敗: {str(e)}")
        finally:
            self.quick_auto_connect_cb.blockSignals(False)

    def on_quick_auto_shutdown_changed(self, state):
        """快速自動結束開關變更處理"""
        try:
            self.quick_auto_shutdown_cb.blockSignals(True)

            enabled = state == Qt.Checked

            # 更新設定檔
            if 'AutoShutdown' not in self.config:
                self.config.add_section('AutoShutdown')

            self.config.set('AutoShutdown', 'enable_auto_shutdown', str(enabled))

            # 儲存設定檔
            with open('config.ini', 'w', encoding='utf-8') as f:
                self.config.write(f)

            # 重新載入自動管理器設定
            if hasattr(self, 'auto_manager'):
                self.auto_manager.load_settings(self.config)

            # 更新狀態顯示
            self.update_auto_status_display()

            self.logger.info(f"自動結束功能已{'啟用' if enabled else '停用'}")

        except Exception as e:
            self.logger.error(f"快速自動結束開關變更失敗: {str(e)}")
        finally:
            self.quick_auto_shutdown_cb.blockSignals(False)

    def update_quick_switches(self):
        """更新快速開關狀態"""
        try:
            self.quick_auto_connect_cb.blockSignals(True)
            self.quick_auto_shutdown_cb.blockSignals(True)

            # 更新自動連線開關
            auto_connect_enabled = False
            if 'AutoConnect' in self.config:
                auto_connect_enabled = self.config.getboolean('AutoConnect', 'enable_auto_connect', fallback=False)
            self.quick_auto_connect_cb.setChecked(auto_connect_enabled)

            # 更新自動結束開關
            auto_shutdown_enabled = False
            if 'AutoShutdown' in self.config:
                auto_shutdown_enabled = self.config.getboolean('AutoShutdown', 'enable_auto_shutdown', fallback=False)
            self.quick_auto_shutdown_cb.setChecked(auto_shutdown_enabled)

        except Exception as e:
            self.logger.error(f"更新快速開關狀態失敗: {str(e)}")
        finally:
            self.quick_auto_connect_cb.blockSignals(False)
            self.quick_auto_shutdown_cb.blockSignals(False)

    def update_auto_status_display(self):
        """更新自動狀態顯示"""
        try:
            status_parts = []

            # 檢查自動連線狀態
            if 'AutoConnect' in self.config and self.config.getboolean('AutoConnect', 'enable_auto_connect', fallback=False):
                mode = self.config.get('AutoConnect', 'auto_connect_mode', fallback='delay')
                if mode == 'immediate':
                    status_parts.append("🟢 立即連線")
                elif mode == 'delay':
                    delay = self.config.getfloat('AutoConnect', 'auto_connect_delay', fallback=30.0)
                    status_parts.append(f"🟠 延遲連線({delay}秒)")
                elif mode == 'schedule':
                    status_parts.append("🔵 時間表連線")
            else:
                status_parts.append("⚫ 手動連線")

            # 檢查自動結束狀態
            if 'AutoShutdown' in self.config and self.config.getboolean('AutoShutdown', 'enable_auto_shutdown', fallback=False):
                shutdown_time = self.config.get('AutoShutdown', 'shutdown_time', fallback='17:30:00')
                status_parts.append(f"🟢 自動結束({shutdown_time})")
            else:
                status_parts.append("⚫ 手動結束")

            status_text = " | ".join(status_parts)
            self.update_auto_status(status_text)

        except Exception as e:
            self.logger.error(f"更新自動狀態顯示失敗: {str(e)}")
            self.update_auto_status("❌ 狀態更新失敗")

    def update_auto_status(self, status: str):
        """更新自動狀態顯示"""
        try:
            self.auto_status_label.setText(status)

            # 根據狀態設置不同顏色
            if "錯誤" in status or "失敗" in status:
                self.auto_status_label.setStyleSheet("color: red; font-weight: bold;")
            elif "警告" in status:
                self.auto_status_label.setStyleSheet("color: orange; font-weight: bold;")
            elif "成功" in status or "完成" in status:
                self.auto_status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.auto_status_label.setStyleSheet("color: blue; font-weight: bold;")

        except Exception as e:
            self.logger.error(f"更新自動狀態顯示失敗: {str(e)}")

    def show_auto_settings(self):
        """顯示自動化設定對話框"""
        try:
            # 檢查是否已經有對話框開啟
            if hasattr(self, 'auto_settings_dialog') and self.auto_settings_dialog is not None and self.auto_settings_dialog.isVisible():
                self.auto_settings_dialog.raise_()
                self.auto_settings_dialog.activateWindow()
                return

            # 創建新的對話框
            self.auto_settings_dialog = AutoSettingsDialog(self.config, self)

            # 連接設定變更信號
            self.auto_settings_dialog.settings_changed.connect(self.on_auto_settings_changed)

            # 顯示對話框 (非模態)
            self.auto_settings_dialog.show()

        except Exception as e:
            self.logger.error(f"顯示自動化設定對話框失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"無法開啟設定對話框: {str(e)}")

    def on_auto_settings_changed(self, settings):
        """處理自動化設定變更"""
        try:
            self.logger.info("自動化設定已變更，重新載入設定")

            # 重新載入設定
            from utils.config_manager import ConfigManager
            config_manager = ConfigManager()
            self.config = config_manager.load_config()

            # 重新載入自動管理器設定 (不停止運行)
            if hasattr(self, 'auto_manager'):
                self.auto_manager.load_settings(self.config)

            # 更新快速開關狀態
            self.update_quick_switches()

            # 更新自動狀態顯示
            self.update_auto_status_display()

            # 發送設定變更信號
            self.settings_changed.emit(settings)

        except Exception as e:
            self.logger.error(f"處理自動化設定變更失敗: {str(e)}")

    def auto_connect(self):
        """自動連線"""
        try:
            self.logger.info("執行自動連線")
            self.connect_dde()
        except Exception as e:
            self.logger.error(f"自動連線失敗: {str(e)}")

    def auto_disconnect(self):
        """自動斷線"""
        try:
            self.logger.info("執行自動斷線")
            if self.dde_client:
                # 先自動取消訂閱（不需要確認）
                self.unsubscribe_all_items()

                # 自動斷線時，根據 disconnect_before_shutdown 設定決定是否終止 DDE
                # 這樣可以避免與 disconnect_on_exit 設定衝突
                terminate_dde = self.auto_manager.disconnect_before_shutdown
                self.dde_client.disconnect(terminate_dde=terminate_dde)
                self.dde_client = None

                # 更新連線狀態
                self.status_label.setText("尚未連線")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
                self.service_connect_btn.setText("服務連線")
                self.connect_btn.setText("連線並訂閱")
                self.subscribe_btn.setText("訂閱項目")
                self.subscribe_btn.setEnabled(False)
                self.logger.info(f"已自動斷開DDE連接 (terminate_dde={terminate_dde})")
        except Exception as e:
            self.logger.error(f"自動斷線失敗: {str(e)}")

    def auto_unadvise_only(self):
        """自動取消訂閱（僅unadvise，不斷開連線）"""
        try:
            self.logger.info("執行自動取消訂閱（僅unadvise）")
            if self.dde_client:
                # 只取消訂閱，不斷開連線
                self.unsubscribe_all_items()

                # 更新按鈕狀態，但保持連線狀態
                self.subscribe_btn.setText("訂閱項目")
                self.subscribe_btn.setEnabled(True)
                self.connect_btn.setText("連線並訂閱")

                self.logger.info("已自動取消所有訂閱（保持連線）")
            else:
                self.logger.warning("未連線，無法執行取消訂閱操作")
        except Exception as e:
            self.logger.error(f"自動取消訂閱失敗: {str(e)}")

    def auto_shutdown(self):
        """自動結束程式"""
        try:
            self.logger.info("開始執行自動結束程式")

            # 設定自動結束標誌
            self._auto_shutdown_in_progress = True

            # 保存資料
            if self.auto_manager.save_data_before_shutdown:
                self.logger.info("結束前保存資料")
                self.save_current_data()

            # 斷開連線
            if self.auto_manager.disconnect_before_shutdown:
                self.logger.info("結束前斷開連線")
                self.auto_disconnect()
                time.sleep(2)

            # 清理暫存檔案
            if self.auto_manager.cleanup_temp_files:
                self.logger.info("結束前清理暫存檔案")
                self.cleanup_temp_files()

            self.logger.info("自動結束程式執行完成，程式即將關閉")

            # 強制結束或顯示非模態確認對話框
            if self.auto_manager.force_shutdown:
                QApplication.quit()
            else:
                self.show_auto_shutdown_confirmation()

        except Exception as e:
            self.logger.error(f"自動結束程式過程發生錯誤: {str(e)}")
            if hasattr(self.auto_manager, 'force_shutdown') and self.auto_manager.force_shutdown:
                QApplication.quit()

    def show_auto_shutdown_confirmation(self):
        """顯示自動結束確認對話框 (非模態)"""
        try:
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("自動結束確認")
            dialog.setModal(False)  # 非模態
            dialog.setWindowFlags(dialog.windowFlags() | Qt.WindowStaysOnTopHint)
            dialog.resize(350, 120)

            layout = QVBoxLayout(dialog)

            # 訊息標籤
            message_label = QLabel("程式已到達預設結束時間，是否要結束程式？")
            message_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(message_label)

            # 按鈕區域
            button_layout = QHBoxLayout()

            yes_btn = QPushButton("確定")
            no_btn = QPushButton("取消")

            button_layout.addWidget(yes_btn)
            button_layout.addWidget(no_btn)
            layout.addLayout(button_layout)

            # 連接信號
            yes_btn.clicked.connect(lambda: self._confirm_auto_shutdown(dialog))
            no_btn.clicked.connect(lambda: self._cancel_auto_shutdown(dialog))

            # 顯示對話框
            dialog.show()

        except Exception as e:
            self.logger.error(f"顯示自動結束確認對話框失敗: {str(e)}")
            QApplication.quit()

    def _confirm_auto_shutdown(self, dialog):
        """確認自動結束"""
        try:
            dialog.close()
            QApplication.quit()
        except Exception as e:
            self.logger.error(f"確認自動結束失敗: {str(e)}")
            QApplication.quit()

    def _cancel_auto_shutdown(self, dialog):
        """取消自動結束"""
        try:
            dialog.close()
            self._auto_shutdown_in_progress = False
            self.logger.info("使用者取消自動結束")
        except Exception as e:
            self.logger.error(f"取消自動結束失敗: {str(e)}")

    def save_current_data(self):
        """保存當前資料"""
        try:
            if self.raw_data:
                # 這裡可以實現資料保存邏輯
                self.logger.info("已保存當前資料")
        except Exception as e:
            self.logger.error(f"保存當前資料失敗: {str(e)}")

    def cleanup_temp_files(self):
        """清理暫存檔案"""
        try:
            temp_patterns = ["*.tmp", "*.temp", "*.lock", "*.pid"]

            for pattern in temp_patterns:
                for file_path in glob.glob(pattern):
                    try:
                        os.remove(file_path)
                        self.logger.debug(f"已刪除暫存檔案: {file_path}")
                    except Exception as e:
                        self.logger.warning(f"刪除暫存檔案失敗 {file_path}: {str(e)}")

        except Exception as e:
            self.logger.error(f"清理暫存檔案失敗: {str(e)}")

    def cleanup(self):
        """清理資源"""
        try:
            # 停止資料處理器
            if hasattr(self, 'data_processor'):
                self.data_processor.stop()

            # 停止線程
            if hasattr(self, 'process_thread') and self.process_thread.isRunning():
                self.process_thread.quit()
                self.process_thread.wait()

            # 斷開 DDE 連線
            if self.dde_client:
                self.disconnect_dde()

            # 停止資源監控
            if hasattr(self, 'resource_monitor'):
                self.resource_monitor.stop_monitoring()

            # 清理GUI日誌處理器
            if hasattr(self, 'gui_log_handler'):
                self.logger.removeHandler(self.gui_log_handler)
                self.gui_log_handler.close()

            self.logger.info("主視窗資源清理完成")

        except Exception as e:
            self.logger.error(f"清理資源失敗: {str(e)}")

    def closeEvent(self, event):
        """視窗關閉事件"""
        try:
            # 檢查是否為自動結束
            if hasattr(self, '_auto_shutdown_in_progress') and self._auto_shutdown_in_progress:
                # 自動結束，直接關閉
                self.cleanup()
                event.accept()
                return

            # 檢查是否已經確認手動退出
            if hasattr(self, '_manual_exit_confirmed') and self._manual_exit_confirmed:
                # 已確認手動退出，直接關閉
                event.accept()
                QApplication.quit()
                return

            # 手動關閉，顯示確認對話框
            self.show_exit_confirmation(event)

        except Exception as e:
            self.logger.error(f"關閉視窗失敗: {str(e)}")
            event.accept()

    def show_exit_confirmation(self, event):
        """顯示退出確認對話框 (非模態)"""
        try:
            # 先忽略關閉事件
            event.ignore()

            # 創建非模態確認對話框
            from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton

            dialog = QDialog(self)
            dialog.setWindowTitle("確認退出")
            dialog.setModal(False)  # 非模態
            dialog.setWindowFlags(dialog.windowFlags() | Qt.WindowStaysOnTopHint)
            dialog.resize(300, 120)

            layout = QVBoxLayout(dialog)

            # 訊息標籤
            message_label = QLabel("確定要退出 DDE 監控程式嗎？")
            message_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(message_label)

            # 按鈕區域
            button_layout = QHBoxLayout()

            yes_btn = QPushButton("確定")
            no_btn = QPushButton("取消")

            button_layout.addWidget(yes_btn)
            button_layout.addWidget(no_btn)
            layout.addLayout(button_layout)

            # 連接信號
            yes_btn.clicked.connect(lambda: self._confirm_exit(dialog))
            no_btn.clicked.connect(dialog.close)

            # 顯示對話框
            dialog.show()

        except Exception as e:
            self.logger.error(f"顯示退出確認對話框失敗: {str(e)}")
            # 如果對話框失敗，直接關閉
            self.cleanup()
            QApplication.quit()

    def display_processed_data(self, row):
        """顯示處理後資料

        參數:
            row: 要顯示的完整資料行
        """
        try:
            # 格式化顯示處理後的完整資料行
            timestamp = f"{row.receive_date} {row.receive_time}"

            # 按照項目順序顯示所有值
            values_text = []
            for code, item_data in self.items_data.items():
                value = row.values.get(code, "")
                values_text.append(f"{item_data.name}: {value}")

            processed_text = f"[{timestamp}] {', '.join(values_text)}"
            # 添加到處理後資料待更新佇列（而不是即時更新）
            self.pending_processed_data.append(processed_text)

        except Exception as e:
            self.logger.error(f"顯示處理後資料失敗: {str(e)}")

    def _confirm_exit(self, dialog):
        """確認退出"""
        try:
            dialog.close()
            # 設定標誌避免重複確認
            self._manual_exit_confirmed = True
            self.cleanup()
            # 直接關閉主視窗，避免觸發 closeEvent
            self.close()
        except Exception as e:
            self.logger.error(f"確認退出失敗: {str(e)}")
            QApplication.quit()
