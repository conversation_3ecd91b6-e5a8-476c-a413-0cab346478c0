#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件循環管理器

提供Actor系統的事件循環管理，包括：
- 主事件循環管理
- 多線程事件循環協調
- 定時任務調度
- 系統生命週期管理
"""

import asyncio
import logging
import signal
import threading
import time
from typing import Dict, List, Optional, Callable, Any
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

from .actor_base import ActorBase, ActorState
from .message_system import MessageRouter, Message, MessageType


@dataclass
class EventLoopConfig:
    """事件循環配置"""
    max_workers: int = 4
    enable_uvloop: bool = False
    debug_mode: bool = False
    shutdown_timeout: float = 30.0
    health_check_interval: float = 60.0


class EventLoopManager:
    """事件循環管理器
    
    負責管理整個Actor系統的事件循環和生命週期
    """
    
    def __init__(self, config: Optional[EventLoopConfig] = None):
        """初始化事件循環管理器
        
        Args:
            config: 事件循環配置
        """
        self.config = config or EventLoopConfig()
        self.logger = logging.getLogger("EventLoopManager")
        
        # 事件循環管理
        self.main_loop: Optional[asyncio.AbstractEventLoop] = None
        self.worker_loops: Dict[str, asyncio.AbstractEventLoop] = {}
        self.thread_pool = ThreadPoolExecutor(max_workers=self.config.max_workers)
        
        # Actor管理
        self.actors: Dict[str, ActorBase] = {}
        self.message_router = MessageRouter()
        
        # 系統狀態
        self.running = False
        self.shutdown_event = asyncio.Event()
        self.startup_complete = asyncio.Event()
        
        # 定時任務
        self.scheduled_tasks: Dict[str, asyncio.Task] = {}
        self.health_check_task: Optional[asyncio.Task] = None
        
        # 信號處理
        self.signal_handlers_installed = False
        
    async def start(self) -> bool:
        """啟動事件循環管理器
        
        Returns:
            bool: 啟動是否成功
        """
        try:
            self.logger.info("正在啟動事件循環管理器")
            
            # 設置事件循環
            await self._setup_event_loop()
            
            # 安裝信號處理器
            self._install_signal_handlers()
            
            # 啟動所有Actor
            await self._start_all_actors()
            
            # 啟動定時任務
            await self._start_scheduled_tasks()
            
            self.running = True
            self.startup_complete.set()
            
            self.logger.info("事件循環管理器啟動成功")
            return True
            
        except Exception as e:
            self.logger.error(f"啟動事件循環管理器失敗: {str(e)}")
            return False
    
    async def stop(self) -> bool:
        """停止事件循環管理器
        
        Returns:
            bool: 停止是否成功
        """
        try:
            self.logger.info("正在停止事件循環管理器")
            
            # 設置停止標誌
            self.running = False
            self.shutdown_event.set()
            
            # 停止定時任務
            await self._stop_scheduled_tasks()
            
            # 停止所有Actor
            await self._stop_all_actors()
            
            # 清理資源
            await self._cleanup_resources()
            
            self.logger.info("事件循環管理器停止成功")
            return True
            
        except Exception as e:
            self.logger.error(f"停止事件循環管理器失敗: {str(e)}")
            return False
    
    def register_actor(self, actor: ActorBase) -> bool:
        """註冊Actor
        
        Args:
            actor: 要註冊的Actor
            
        Returns:
            bool: 註冊是否成功
        """
        try:
            if actor.name in self.actors:
                self.logger.warning(f"Actor {actor.name} 已經註冊")
                return False
            
            self.actors[actor.name] = actor
            self.message_router.register_actor(actor)
            
            # 如果系統已經運行，立即啟動Actor
            if self.running:
                asyncio.create_task(actor.start())
            
            self.logger.info(f"註冊Actor: {actor.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"註冊Actor失敗: {str(e)}")
            return False
    
    def unregister_actor(self, actor_name: str) -> bool:
        """取消註冊Actor
        
        Args:
            actor_name: Actor名稱
            
        Returns:
            bool: 取消註冊是否成功
        """
        try:
            if actor_name not in self.actors:
                self.logger.warning(f"Actor {actor_name} 未註冊")
                return False
            
            actor = self.actors[actor_name]
            
            # 如果Actor正在運行，先停止它
            if actor.state == ActorState.RUNNING:
                asyncio.create_task(actor.stop())
            
            del self.actors[actor_name]
            self.message_router.unregister_actor(actor_name)
            
            self.logger.info(f"取消註冊Actor: {actor_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"取消註冊Actor失敗: {str(e)}")
            return False
    
    def schedule_task(self, name: str, coro_func: Callable, 
                     interval: float, immediate: bool = False) -> bool:
        """調度定時任務
        
        Args:
            name: 任務名稱
            coro_func: 協程函數
            interval: 執行間隔（秒）
            immediate: 是否立即執行
            
        Returns:
            bool: 調度是否成功
        """
        try:
            if name in self.scheduled_tasks:
                self.logger.warning(f"定時任務 {name} 已存在")
                return False
            
            async def task_wrapper():
                if immediate:
                    await coro_func()
                
                while self.running:
                    try:
                        await asyncio.sleep(interval)
                        if self.running:
                            await coro_func()
                    except asyncio.CancelledError:
                        break
                    except Exception as e:
                        self.logger.error(f"定時任務 {name} 執行失敗: {str(e)}")
            
            task = asyncio.create_task(task_wrapper())
            self.scheduled_tasks[name] = task
            
            self.logger.info(f"調度定時任務: {name}, 間隔: {interval}秒")
            return True
            
        except Exception as e:
            self.logger.error(f"調度定時任務失敗: {str(e)}")
            return False
    
    def cancel_task(self, name: str) -> bool:
        """取消定時任務
        
        Args:
            name: 任務名稱
            
        Returns:
            bool: 取消是否成功
        """
        try:
            if name not in self.scheduled_tasks:
                self.logger.warning(f"定時任務 {name} 不存在")
                return False
            
            task = self.scheduled_tasks[name]
            task.cancel()
            del self.scheduled_tasks[name]
            
            self.logger.info(f"取消定時任務: {name}")
            return True
            
        except Exception as e:
            self.logger.error(f"取消定時任務失敗: {str(e)}")
            return False
    
    async def run_forever(self):
        """運行事件循環直到收到停止信號"""
        try:
            self.logger.info("事件循環開始運行")
            
            # 等待啟動完成
            await self.startup_complete.wait()
            
            # 等待停止信號
            await self.shutdown_event.wait()
            
            self.logger.info("收到停止信號，準備關閉")
            
        except Exception as e:
            self.logger.error(f"事件循環運行錯誤: {str(e)}")
        finally:
            await self.stop()
    
    async def _setup_event_loop(self):
        """設置事件循環"""
        self.main_loop = asyncio.get_running_loop()
        
        # 設置調試模式
        if self.config.debug_mode:
            self.main_loop.set_debug(True)
        
        # 嘗試使用uvloop（如果可用且啟用）
        if self.config.enable_uvloop:
            try:
                import uvloop
                if not isinstance(self.main_loop, uvloop.Loop):
                    self.logger.info("使用uvloop加速事件循環")
            except ImportError:
                self.logger.warning("uvloop不可用，使用默認事件循環")
    
    def _install_signal_handlers(self):
        """安裝信號處理器"""
        if self.signal_handlers_installed:
            return
        
        try:
            # 只在主線程中安裝信號處理器
            if threading.current_thread() is threading.main_thread():
                def signal_handler(signum, frame):
                    self.logger.info(f"收到信號 {signum}，準備關閉系統")
                    if self.main_loop and not self.main_loop.is_closed():
                        self.main_loop.call_soon_threadsafe(self.shutdown_event.set)
                
                signal.signal(signal.SIGINT, signal_handler)
                signal.signal(signal.SIGTERM, signal_handler)
                
                self.signal_handlers_installed = True
                self.logger.info("信號處理器安裝成功")
                
        except Exception as e:
            self.logger.warning(f"安裝信號處理器失敗: {str(e)}")
    
    async def _start_all_actors(self):
        """啟動所有Actor"""
        start_tasks = []
        
        for actor in self.actors.values():
            if actor.state == ActorState.CREATED:
                task = asyncio.create_task(actor.start())
                start_tasks.append(task)
        
        if start_tasks:
            results = await asyncio.gather(*start_tasks, return_exceptions=True)
            
            success_count = 0
            for i, result in enumerate(results):
                if result is True:
                    success_count += 1
                elif isinstance(result, Exception):
                    actor_name = list(self.actors.keys())[i]
                    self.logger.error(f"啟動Actor {actor_name} 失敗: {str(result)}")
            
            self.logger.info(f"成功啟動 {success_count}/{len(start_tasks)} 個Actor")
    
    async def _stop_all_actors(self):
        """停止所有Actor"""
        stop_tasks = []
        
        for actor in self.actors.values():
            if actor.state == ActorState.RUNNING:
                task = asyncio.create_task(actor.stop())
                stop_tasks.append(task)
        
        if stop_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*stop_tasks, return_exceptions=True),
                    timeout=self.config.shutdown_timeout
                )
            except asyncio.TimeoutError:
                self.logger.warning("停止Actor超時，強制關閉")
    
    async def _start_scheduled_tasks(self):
        """啟動定時任務"""
        # 啟動健康檢查任務
        if self.config.health_check_interval > 0:
            self.schedule_task(
                "health_check",
                self._health_check,
                self.config.health_check_interval,
                immediate=True
            )
    
    async def _stop_scheduled_tasks(self):
        """停止定時任務"""
        for name, task in list(self.scheduled_tasks.items()):
            task.cancel()
        
        # 等待所有任務完成
        if self.scheduled_tasks:
            await asyncio.gather(*self.scheduled_tasks.values(), return_exceptions=True)
        
        self.scheduled_tasks.clear()
    
    async def _health_check(self):
        """健康檢查"""
        try:
            unhealthy_actors = []
            
            for name, actor in self.actors.items():
                if actor.state == ActorState.ERROR:
                    unhealthy_actors.append(name)
                elif actor.state == ActorState.RUNNING:
                    # 檢查Actor是否響應
                    stats = actor.get_stats()
                    if time.time() - stats.last_activity_time > 300:  # 5分鐘無活動
                        self.logger.warning(f"Actor {name} 可能無響應")
            
            if unhealthy_actors:
                self.logger.warning(f"發現不健康的Actor: {unhealthy_actors}")
                
                # 發送健康檢查消息
                health_message = Message(
                    type=MessageType.SYSTEM_HEALTH_CHECK,
                    data={'unhealthy_actors': unhealthy_actors},
                    sender="EventLoopManager"
                )
                await self.message_router.broadcast_message(health_message)
            
        except Exception as e:
            self.logger.error(f"健康檢查失敗: {str(e)}")
    
    async def _cleanup_resources(self):
        """清理資源"""
        try:
            # 關閉線程池
            self.thread_pool.shutdown(wait=True)
            
            # 清理其他資源
            self.actors.clear()
            
        except Exception as e:
            self.logger.error(f"清理資源失敗: {str(e)}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """獲取系統信息
        
        Returns:
            Dict: 系統信息
        """
        actor_states = {}
        for name, actor in self.actors.items():
            actor_states[name] = actor.state.value
        
        return {
            'running': self.running,
            'actor_count': len(self.actors),
            'actor_states': actor_states,
            'scheduled_tasks': list(self.scheduled_tasks.keys()),
            'message_router_stats': self.message_router.get_stats()
        }
