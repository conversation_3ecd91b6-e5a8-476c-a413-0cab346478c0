#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多商品自动化管理器
支持不同商品的不同自动化策略
"""

import logging
from datetime import datetime, time
from typing import Dict, List, Optional
from PySide6.QtCore import QObject, Signal, QTimer

from utils.config_manager import MultiProductConfigManager


class SymbolAutoManager:
    """单个商品的自动化管理器"""
    
    def __init__(self, symbol: str, auto_config: Dict, logger: logging.Logger):
        self.symbol = symbol
        self.config = auto_config
        self.logger = logger
        
        # 自动连接设置
        self.enable_auto_connect = auto_config.get('enable_auto_connect', 'false').lower() == 'true'
        self.auto_connect_mode = auto_config.get('auto_connect_mode', 'delay')
        self.auto_connect_delay = float(auto_config.get('auto_connect_delay', '30.0'))
        self.schedule_connect_times = auto_config.get('schedule_connect_times', '')
        self.prevent_weekend_startup = auto_config.get('prevent_weekend_startup', 'true').lower() == 'true'
        self.schedule_end_action = auto_config.get('schedule_end_action', 'unadvise_only')
        
        # 解析时间表
        self.schedule_times = self._parse_schedule_times()
        
        # 状态 - 初始化时根据当前时间设置状态
        current_time = datetime.now().time()
        if self.should_connect_at_time(current_time):
            self.current_schedule_state = 'should_connect'  # 应该连接但还未连接
        else:
            self.current_schedule_state = 'disconnected'  # 不在交易时间内

        self.last_connect_time = None
        self.last_disconnect_time = None
        
    def _parse_schedule_times(self) -> List[tuple]:
        """解析时间表字符串"""
        try:
            schedule_times = []
            if not self.schedule_connect_times:
                return schedule_times
            
            # 支持多个时间段，用分号分隔
            time_ranges = self.schedule_connect_times.split(';')
            
            for time_range in time_ranges:
                time_range = time_range.strip()
                if '-' in time_range:
                    start_str, end_str = time_range.split('-', 1)
                    start_time = datetime.strptime(start_str.strip(), '%H:%M:%S').time()
                    end_time = datetime.strptime(end_str.strip(), '%H:%M:%S').time()
                    schedule_times.append((start_time, end_time))
            
            return schedule_times
            
        except Exception as e:
            self.logger.error(f"解析商品 {self.symbol} 时间表失败: {str(e)}")
            return []
    
    def should_connect_at_time(self, current_time: time) -> bool:
        """检查当前时间是否应该连接"""
        try:
            # 如果没有时间表，则不自动连接
            if not self.schedule_times:
                return False

            for start_time, end_time in self.schedule_times:
                if start_time <= end_time:
                    # 同一天的时间段
                    if start_time <= current_time <= end_time:
                        return True
                else:
                    # 跨天的时间段
                    if current_time >= start_time or current_time <= end_time:
                        return True
            return False

        except Exception as e:
            self.logger.error(f"检查商品 {self.symbol} 连接时间失败: {str(e)}")
            return False
    

    
    def get_next_connect_time(self) -> Optional[time]:
        """获取下一个连接时间"""
        try:
            current_time = datetime.now().time()
            next_times = []
            
            for start_time, end_time in self.schedule_times:
                if start_time > current_time:
                    next_times.append(start_time)
                elif start_time <= end_time and current_time > end_time:
                    # 明天的开始时间
                    next_times.append(start_time)
                elif start_time > end_time and current_time <= end_time:
                    # 今天已经在跨天时间段内
                    continue
                elif start_time > end_time and current_time > end_time:
                    # 明天的开始时间
                    next_times.append(start_time)
            
            return min(next_times) if next_times else None
            
        except Exception as e:
            self.logger.error(f"获取商品 {self.symbol} 下一个连接时间失败: {str(e)}")
            return None
    
    def get_status_info(self) -> Dict:
        """获取状态信息"""
        return {
            'symbol': self.symbol,
            'enable_auto_connect': self.enable_auto_connect,
            'auto_connect_mode': self.auto_connect_mode,
            'current_state': self.current_schedule_state,
            'schedule_times': self.schedule_times,
            'next_connect_time': self.get_next_connect_time(),
            'last_connect_time': self.last_connect_time,
            'last_disconnect_time': self.last_disconnect_time
        }


class MultiProductAutoManager(QObject):
    """多商品自动化管理器"""
    
    # 信号定义
    symbol_auto_connect_requested = Signal(str)  # symbol
    symbol_auto_disconnect_requested = Signal(str)  # symbol
    symbol_auto_unadvise_only_requested = Signal(str)  # symbol
    auto_shutdown_requested = Signal()
    status_changed = Signal(str)
    
    def __init__(self, multi_config: MultiProductConfigManager, logger: logging.Logger):
        super().__init__()
        self.multi_config = multi_config
        self.logger = logger
        
        # 商品自动化管理器
        self.symbol_managers: Dict[str, SymbolAutoManager] = {}

        # 连接队列管理 - 避免多商品同时连接造成DDE冲突
        self.connection_queue: List[str] = []  # 待连接的商品队列
        self.current_connecting_symbol: Optional[str] = None  # 当前正在连接的商品
        self.is_processing_queue = False  # 是否正在处理队列

        # 全局自动结束设置
        self.global_auto_shutdown_config = multi_config.get_common_config('AutoShutdown')
        self.enable_auto_shutdown = self.global_auto_shutdown_config.get('enable_auto_shutdown', 'false').lower() == 'true'
        self.shutdown_time_str = self.global_auto_shutdown_config.get('shutdown_time', '05:55:05')
        self.shutdown_buffer_seconds = int(self.global_auto_shutdown_config.get('shutdown_buffer_seconds', '30'))
        self.shutdown_warning_seconds = int(self.global_auto_shutdown_config.get('shutdown_warning_seconds', '10'))
        
        # 解析结束时间
        try:
            self.shutdown_time = datetime.strptime(self.shutdown_time_str, '%H:%M:%S').time()
        except:
            self.shutdown_time = time(5, 55, 5)
            self.logger.warning(f"解析自动结束时间失败，使用默认值: 05:55:05")
        
        # 定时器
        self.schedule_timer = QTimer()
        self.schedule_timer.timeout.connect(self._check_all_schedules)

        self.shutdown_timer = QTimer()
        self.shutdown_timer.timeout.connect(self._check_auto_shutdown)

        # 队列处理定时器 - 串行化处理连接请求
        self.queue_timer = QTimer()
        self.queue_timer.timeout.connect(self._process_connection_queue)
        self.queue_timer.start(2000)  # 每2秒检查一次队列
        
        # 初始化商品管理器
        self._init_symbol_managers()
        
        self.logger.info(f"多商品自动化管理器初始化完成，管理商品数: {len(self.symbol_managers)}")
    
    def _init_symbol_managers(self):
        """初始化各商品的自动化管理器"""
        try:
            for symbol in self.multi_config.symbols:
                auto_config = self.multi_config.get_symbol_auto_connect_config(symbol)
                if auto_config:
                    manager = SymbolAutoManager(symbol, auto_config, self.logger)
                    self.symbol_managers[symbol] = manager
                    self.logger.info(f"初始化商品 {symbol} 自动化管理器")
                else:
                    self.logger.warning(f"商品 {symbol} 没有自动化配置")
                    
        except Exception as e:
            self.logger.error(f"初始化商品自动化管理器失败: {str(e)}")
    
    def start(self):
        """启动多商品自动化管理器"""
        try:
            self.logger.info("多商品自动化管理器已启动")
            self.status_changed.emit("多商品自动管理器已启动")
            
            # 检查是否有需要自动连接的商品
            auto_connect_symbols = []
            for symbol, manager in self.symbol_managers.items():
                if manager.enable_auto_connect:
                    auto_connect_symbols.append(symbol)
                    
                    if manager.auto_connect_mode == 'immediate':
                        # 立即连接
                        self.logger.info(f"商品 {symbol} 立即执行自动连接")
                        self.symbol_auto_connect_requested.emit(symbol)
                        manager.current_schedule_state = 'connected'
                        manager.last_connect_time = datetime.now()
                    elif manager.auto_connect_mode == 'delay':
                        # 延迟连接 - 这里简化处理，实际可以为每个商品设置独立的延迟定时器
                        self.logger.info(f"商品 {symbol} 将延迟 {manager.auto_connect_delay} 秒后自动连接")
            
            if auto_connect_symbols:
                # 启动时间表检查
                self.schedule_timer.start(1000)  # 每秒检查一次
                self.logger.info(f"启动时间表检查，自动连接商品: {', '.join(auto_connect_symbols)}")
            
            # 启动全局自动结束检查
            if self.enable_auto_shutdown:
                self.shutdown_timer.start(1000)
                self.logger.info(f"自动结束检查已启动，结束时间: {self.shutdown_time_str}")
            
        except Exception as e:
            self.logger.error(f"启动多商品自动化管理器失败: {str(e)}")
    
    def stop(self):
        """停止多商品自动化管理器"""
        try:
            # 停止所有定时器
            self.schedule_timer.stop()
            self.shutdown_timer.stop()
            
            self.logger.info("多商品自动化管理器已停止")
            self.status_changed.emit("多商品自动管理器已停止")
            
        except Exception as e:
            self.logger.error(f"停止多商品自动化管理器失败: {str(e)}")
    
    def _check_all_schedules(self):
        """检查所有商品的时间表"""
        try:
            current_time = datetime.now().time()
            current_weekday = datetime.now().weekday()
            
            for symbol, manager in self.symbol_managers.items():
                if not manager.enable_auto_connect or manager.auto_connect_mode != 'schedule':
                    continue
                
                should_connect = manager.should_connect_at_time(current_time)

                # 只有在应该连接且当前未连接时才连接
                if should_connect and manager.current_schedule_state in ['disconnected', 'should_connect']:
                    # 检查是否防止周末起始连接
                    if manager.prevent_weekend_startup and current_weekday >= 5:  # 5=Saturday, 6=Sunday
                        self.logger.info(f"商品 {symbol} 周末时间，防止自动连接起始: {current_time}")
                        continue

                    self.logger.info(f"商品 {symbol} 时间表自动连接: {current_time}")
                    # 添加到连接队列而不是直接连接
                    self._add_to_connection_queue(symbol)
                    manager.current_schedule_state = 'connecting'  # 标记为连接中
                    manager.last_connect_time = datetime.now()

                # 只有在不应该连接且当前已连接时才断开
                elif not should_connect and manager.current_schedule_state == 'connected':
                    self.logger.info(f"商品 {symbol} 时间表自动断开: {current_time}")

                    if manager.schedule_end_action == 'disconnect':
                        self.symbol_auto_disconnect_requested.emit(symbol)
                    else:  # unadvise_only
                        self.symbol_auto_unadvise_only_requested.emit(symbol)

                    manager.current_schedule_state = 'disconnected'
                    manager.last_disconnect_time = datetime.now()
            
        except Exception as e:
            self.logger.error(f"检查时间表失败: {str(e)}")

    def _add_to_connection_queue(self, symbol: str):
        """添加商品到连接队列"""
        try:
            if symbol not in self.connection_queue and symbol != self.current_connecting_symbol:
                self.connection_queue.append(symbol)
                self.logger.info(f"商品 {symbol} 已添加到连接队列，队列长度: {len(self.connection_queue)}")
        except Exception as e:
            self.logger.error(f"添加商品到连接队列失败 {symbol}: {str(e)}")

    def _process_connection_queue(self):
        """处理连接队列 - 串行化连接"""
        try:
            # 如果当前有商品正在连接，等待完成
            if self.current_connecting_symbol is not None:
                return

            # 如果队列为空，无需处理
            if not self.connection_queue:
                return

            # 取出队列中的第一个商品进行连接
            symbol = self.connection_queue.pop(0)
            self.current_connecting_symbol = symbol

            self.logger.info(f"开始处理连接队列，连接商品: {symbol}，剩余队列: {len(self.connection_queue)}")
            self.symbol_auto_connect_requested.emit(symbol)

        except Exception as e:
            self.logger.error(f"处理连接队列失败: {str(e)}")

    def notify_connection_completed(self, symbol: str, success: bool):
        """通知连接完成 - 由GUI调用"""
        try:
            if self.current_connecting_symbol == symbol:
                self.current_connecting_symbol = None

                if success:
                    # 更新商品状态为已连接
                    if symbol in self.symbol_managers:
                        self.symbol_managers[symbol].current_schedule_state = 'connected'
                    self.logger.info(f"商品 {symbol} 连接完成，继续处理队列")
                else:
                    # 连接失败，重置状态
                    if symbol in self.symbol_managers:
                        self.symbol_managers[symbol].current_schedule_state = 'disconnected'
                    self.logger.warning(f"商品 {symbol} 连接失败")

        except Exception as e:
            self.logger.error(f"通知连接完成失败 {symbol}: {str(e)}")

    def _check_auto_shutdown(self):
        """检查自动结束"""
        try:
            if not self.enable_auto_shutdown:
                return
            
            current_time = datetime.now().time()
            
            # 计算目标结束时间和缓冲时间
            shutdown_datetime = datetime.combine(datetime.now().date(), self.shutdown_time)
            buffer_start_time = (shutdown_datetime - 
                               datetime.timedelta(seconds=self.shutdown_buffer_seconds)).time()
            buffer_end_time = (shutdown_datetime + 
                             datetime.timedelta(seconds=self.shutdown_buffer_seconds)).time()
            
            # 检查是否在缓冲时间范围内
            if buffer_start_time <= current_time <= buffer_end_time:
                self.logger.info(f"触发自动结束: 当前时间 {current_time}, 目标时间 {self.shutdown_time}")
                self.auto_shutdown_requested.emit()
                
                # 停止自动结束检查，避免重复触发
                self.shutdown_timer.stop()
            
        except Exception as e:
            self.logger.error(f"检查自动结束失败: {str(e)}")
    
    def get_all_status(self) -> Dict[str, Dict]:
        """获取所有商品的状态信息"""
        try:
            status_info = {}
            for symbol, manager in self.symbol_managers.items():
                status_info[symbol] = manager.get_status_info()
            return status_info
            
        except Exception as e:
            self.logger.error(f"获取状态信息失败: {str(e)}")
            return {}
    
    def get_symbol_status(self, symbol: str) -> Optional[Dict]:
        """获取指定商品的状态信息"""
        try:
            if symbol in self.symbol_managers:
                return self.symbol_managers[symbol].get_status_info()
            return None
            
        except Exception as e:
            self.logger.error(f"获取商品 {symbol} 状态信息失败: {str(e)}")
            return None
    
    def update_symbol_state(self, symbol: str, state: str):
        """更新商品状态"""
        try:
            if symbol in self.symbol_managers:
                self.symbol_managers[symbol].current_schedule_state = state
                if state == 'connected':
                    self.symbol_managers[symbol].last_connect_time = datetime.now()
                elif state in ['disconnected', 'ended']:
                    self.symbol_managers[symbol].last_disconnect_time = datetime.now()
                    
        except Exception as e:
            self.logger.error(f"更新商品 {symbol} 状态失败: {str(e)}")
