#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多商品DDE监控程序 - 主程序
基于模块化版本改造，支持多商品同时监控
"""

import sys
import os
import argparse
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入自定义模块
from utils.config_manager import MultiProductConfigManager
from utils.logger import setup_logging
from gui.multi_product_window import MultiProductMainWindow


class MultiProductDDEApp:
    """多商品DDE监控程序主应用类别"""

    def __init__(self, config_file=None):
        self.app = None
        self.main_window = None
        self.multi_config = None
        self.logger = None
        self.config_file = config_file or 'multi_config.ini'

    def initialize(self):
        """初始化应用程序"""
        try:
            # 创建 QApplication
            self.app = QApplication(sys.argv)
            self.app.setApplicationName("Multi-Product DDE Monitor")
            self.app.setApplicationVersion("7.0")

            # 初始化多商品配置管理器
            self.multi_config = MultiProductConfigManager(self.config_file)
            
            # 载入配置文件
            if not self.multi_config.load_config():
                raise Exception(f"载入多商品配置文件失败: {self.config_file}")
            
            print(f"使用多商品配置文件: {self.config_file}")
            
            # 验证配置
            errors = self.multi_config.validate_multi_config()
            if errors:
                print("配置验证发现问题:")
                for error_type, error_list in errors.items():
                    print(f"  {error_type}: {error_list}")
                if 'missing_sections' in errors or 'symbol_errors' in errors:
                    raise Exception("配置文件存在严重错误，无法继续")
            
            # 初始化日志系统
            system_config = self.multi_config.get_system_config()
            log_base_path = system_config.get('log_base_path', './logs/')
            
            # 确保日志目录存在
            os.makedirs(log_base_path, exist_ok=True)
            
            log_file_path = os.path.join(log_base_path, 'multi_product_monitor.log')
            self.logger = setup_logging(log_file_path, 'INFO', 'INFO', 'DEBUG')
            self.logger.info("多商品DDE监控程序启动")
            
            # 记录配置信息
            self.logger.info(f"载入商品数量: {len(self.multi_config.symbols)}")
            self.logger.info(f"商品列表: {', '.join(self.multi_config.symbols)}")
            self.logger.info(f"数据类型: {', '.join(self.multi_config.data_types)}")
            
            # 初始化主窗口
            self.main_window = MultiProductMainWindow(self.multi_config, self.logger)
            
            self.logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"应用程序初始化失败: {str(e)}")
            else:
                print(f"应用程序初始化失败: {str(e)}")
            return False
    
    def run(self):
        """运行应用程序"""
        try:
            if not self.initialize():
                return 1
            
            # 显示主窗口
            self.main_window.show()
            
            # 运行应用程序
            return self.app.exec()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"运行应用程序失败: {str(e)}")
            else:
                print(f"运行应用程序失败: {str(e)}")
            return 1
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.main_window:
                self.main_window.cleanup()
            
            if self.logger:
                self.logger.info("多商品应用程序正常结束")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"清理资源失败: {str(e)}")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='多商品DDE监控程序 v7.0',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用范例:
  python dde_monitor_multi.py                           # 使用默认配置文件 multi_config.ini
  python dde_monitor_multi.py -c my_multi_config.ini    # 使用指定配置文件
  python dde_monitor_multi.py --config /path/to/config.ini  # 使用完整路径的配置文件

多商品配置文件说明:
  1. 支持模板化配置，使用 {symbol} 占位符
  2. 可以同时监控多个商品的不同数据类型
  3. 每个商品可以有独立的输出路径和自动化策略
  4. 支持期货、股票等不同类型商品的混合监控
        """
    )

    parser.add_argument(
        '-c', '--config',
        type=str,
        help='指定多商品配置文件路径 (默认: multi_config.ini)'
    )

    parser.add_argument(
        '--version',
        action='version',
        version='多商品DDE监控程序 v7.0'
    )

    parser.add_argument(
        '--generate-single',
        nargs=3,
        metavar=('SYMBOL', 'DATATYPE', 'OUTPUT'),
        help='生成单商品配置文件: SYMBOL DATATYPE OUTPUT_FILE'
    )

    return parser.parse_args()


def generate_single_config(multi_config, symbol, data_type, output_file):
    """生成单商品配置文件"""
    try:
        if multi_config.generate_single_product_config(symbol, data_type, output_file):
            print(f"成功生成单商品配置文件: {output_file}")
            print(f"商品: {symbol}, 数据类型: {data_type}")
            return True
        else:
            print(f"生成单商品配置文件失败")
            return False
            
    except Exception as e:
        print(f"生成单商品配置文件时出错: {str(e)}")
        return False


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    # 如果是生成单商品配置文件模式
    if args.generate_single:
        symbol, data_type, output_file = args.generate_single
        
        # 载入多商品配置
        config_file = args.config or 'multi_config.ini'
        multi_config = MultiProductConfigManager(config_file)
        
        if not multi_config.load_config():
            print(f"载入多商品配置文件失败: {config_file}")
            return 1
        
        # 生成单商品配置
        if generate_single_config(multi_config, symbol, data_type, output_file):
            return 0
        else:
            return 1

    # 正常运行模式
    app = MultiProductDDEApp(config_file=args.config)

    try:
        exit_code = app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        exit_code = 0
    except Exception as e:
        print(f"程序执行失败: {str(e)}")
        exit_code = 1
    finally:
        app.cleanup()

    return exit_code


if __name__ == "__main__":
    sys.exit(main())
