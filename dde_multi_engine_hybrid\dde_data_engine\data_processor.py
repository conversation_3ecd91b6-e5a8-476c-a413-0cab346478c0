#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 資料處理器
核心的DDE資料處理邏輯，包含完整的資料處理流程
"""

import time
import logging
from datetime import datetime
from collections import deque
from typing import Dict, List, Optional, Callable, Any
from queue import Queue
import threading

from .data_structures import ItemData, RawDataRow, DataRow, ProcessorStats
from .config_handler import DataProcessorConfig
from .file_handler import DataFileHandler


class DDEDataProcessor:
    """DDE 資料處理器
    
    提供完整的DDE資料處理功能:
    1. DDE 資料接收和初始值建立
    2. 資料補全和完整行建立  
    3. 時間間隔和重複項目檢查
    4. 值變化檢測和有效行識別
    5. 資料輸出和記錄
    """
    
    def __init__(self, 
                 config: DataProcessorConfig,
                 items_data: Dict[str, ItemData],
                 file_handler: Optional[DataFileHandler] = None,
                 logger: Optional[logging.Logger] = None):
        """初始化資料處理器
        
        Args:
            config: 處理器配置
            items_data: 項目資料字典 {項目代碼: ItemData}
            file_handler: 檔案處理器
            logger: 日誌記錄器
        """
        self.config = config
        self.items_data = items_data
        self.file_handler = file_handler
        self.logger = logger or logging.getLogger(__name__)

        # 測試日誌系統
        print(f"[DEBUG] 資料處理器初始化，logger: {self.logger}")
        print(f"[DEBUG] logger handlers: {self.logger.handlers}")
        print(f"[DEBUG] logger level: {self.logger.level}")
        self.logger.info("資料處理器初始化完成")
        self.logger.debug("這是資料處理器的DEBUG訊息")
        print(f"[DEBUG] 資料處理器日誌測試完成")
        
        # 資料容器
        self.raw_data: deque = deque(maxlen=config.get_int('max_raw_data_queue', 1000))
        self.current_row: Optional[RawDataRow] = None
        
        # 時間管理
        self.last_advise_time: Optional[float] = None
        self.time_newline_interval = config.get_float('time_newline_interval', 0.800)
        
        # 統計資訊
        self.stats = ProcessorStats()
        
        # 回調函數
        self.on_data_received: Optional[Callable[[str, str], None]] = None
        self.on_row_saved: Optional[Callable[[RawDataRow], None]] = None
        self.on_row_skipped: Optional[Callable[[RawDataRow], None]] = None
        
        # 執行緒安全
        self._lock = threading.RLock()
        
        self.logger.info("DDE資料處理器初始化完成")
    
    def process_dde_data(self, item: str, value: str) -> bool:
        """處理DDE資料更新
        
        這是主要的資料處理入口點，實現完整的處理流程:
        1. 檢查項目重複換行
        2. 更新原始資料
        3. 更新項目資料
        4. 更新時間記錄
        
        Args:
            item: DDE項目代碼
            value: 項目值
            
        Returns:
            是否成功處理
        """
        try:
            with self._lock:
                self.stats.total_received += 1
                self.stats.last_update_time = datetime.now()
                
                self.logger.debug(f"處理DDE資料: {item} = {value}")
                
                # 觸發回調
                if self.on_data_received:
                    self.on_data_received(item, value)
                
                # 1. 檢查是否需要項目重複換行（按照v6版本邏輯）
                repeat_check_result = self._check_item_repeat_newline(item, value)

                if repeat_check_result:
                    return True

                # 2. 更新原始資料
                self._update_raw_data(item, value)

                # 3. 更新項目資料
                self._update_items_data(item, value)

                # 4. 更新最後接收時間（與v6版本一致）
                self.last_advise_time = time.time()
                
                self.stats.total_processed += 1
                return True
                
        except Exception as e:
            self.logger.error(f"處理DDE資料失敗: {str(e)}")
            return False
    
    def check_time_interval(self) -> bool:
        """檢查時間間隔（按照v6版本邏輯實現）

        定期檢查是否需要根據時間間隔進行換行

        Returns:
            是否觸發了時間間隔換行
        """
        try:
            if not self.config.get_bool('enable_time_newline', True):
                return False

            if self.last_advise_time is None:
                return False

            current_time = time.time()
            elapsed = current_time - self.last_advise_time

            if elapsed >= self.time_newline_interval:
                self.logger.debug(f"時間間隔已到，開始換行 (間隔: {elapsed:.3f}秒)")

                if not self.raw_data:
                    return False

                current_row = self.raw_data[0]

                # 檢查行是否有資料
                if not current_row.values:
                    return False

                # 檢查並補齊缺失資料
                if self._has_missing_data(current_row):
                    self._fill_missing_data(current_row)

                # 檢查值變化（與模組化版本邏輯一致）
                if self.config.get_bool('enable_value_change_check', True):
                    has_changed = self._check_value_change(current_row)

                    if has_changed:
                        self.logger.debug("時間間隔換行: 值有變化，儲存完整資料行")
                        # 儲存完整資料行
                        self._save_complete_row(current_row)
                        # 建立新行
                        self._create_new_row()
                    else:
                        self.logger.debug("時間間隔換行: 值未變化，不儲存資料行但建立新行")
                        # 值未變化，不儲存資料行，但建立新行
                        self._create_new_row()
                else:
                    # 未啟用值變化檢查，直接儲存資料行
                    self.logger.debug("時間間隔換行: 未啟用值變化檢查，直接儲存資料行")
                    self._save_complete_row(current_row)
                    self._create_new_row()

                # 更新時間戳記（與模組化版本一致）
                self.last_advise_time = current_time
                return True

            return False

        except Exception as e:
            self.logger.error(f"檢查時間間隔失敗: {str(e)}")
            return False
    
    def set_initial_values(self, initial_values: Dict[str, str]):
        """設置初始值
        
        用於在開始監控前設置項目的初始值
        
        Args:
            initial_values: 初始值字典 {項目代碼: 值}
        """
        try:
            with self._lock:
                for item_code, value in initial_values.items():
                    if item_code in self.items_data:
                        self.items_data[item_code].value = value
                        self.items_data[item_code].update_time = datetime.now()
                        self.items_data[item_code].status = "已測試"
                        
                self.logger.info(f"設置初始值完成，項目數: {len(initial_values)}")
                
        except Exception as e:
            self.logger.error(f"設置初始值失敗: {str(e)}")
    
    def get_current_data(self) -> Dict[str, str]:
        """獲取當前資料
        
        Returns:
            當前所有項目的值字典
        """
        try:
            with self._lock:
                return {code: item.value or "" for code, item in self.items_data.items()}
                
        except Exception as e:
            self.logger.error(f"獲取當前資料失敗: {str(e)}")
            return {}
    
    def get_stats(self) -> ProcessorStats:
        """獲取統計資訊
        
        Returns:
            處理器統計資訊
        """
        return self.stats
    
    def reset_stats(self):
        """重置統計資訊"""
        with self._lock:
            self.stats.reset()
            self.logger.info("統計資訊已重置")
    
    def _check_item_repeat_newline(self, item: str, value: str) -> bool:
        """檢查是否需要項目重複換行

        當收到重複項目的資料時，檢查是否需要換行

        Args:
            item: 項目代碼
            value: 項目值

        Returns:
            是否需要換行
        """
        try:
            if not self.raw_data:
                # 第一次接收資料，沒有當前行
                return False

            current_row = self.raw_data[0]

            # 檢查項目是否已存在於當前行
            if item in current_row.values:
                # 項目重複，觸發檢查邏輯
                self.logger.debug(f"項目 {item} 已存在於當前行")

                # 補齊缺失資料
                self._fill_missing_data(current_row)

                # 檢查值變化
                enable_check = self.config.get_bool('enable_value_change_check', True)

                if enable_check:
                    has_changed = self._check_value_change(current_row)

                    if has_changed:
                        self.logger.debug("值有變化，儲存完整資料行")
                        self._save_complete_row(current_row)
                        self._create_new_row()
                        self._add_item_to_current_row(item, value)
                        return True
                    else:
                        self.logger.debug("值未變化，跳過資料行")
                        self._skip_row(current_row)
                        self._create_new_row()
                        self._add_item_to_current_row(item, value)
                        return True
                else:
                    # 未啟用值變化檢查，直接儲存資料行
                    self.logger.debug("未啟用值變化檢查，直接儲存資料行")
                    self._save_complete_row(current_row)
                    self._create_new_row()
                    self._add_item_to_current_row(item, value)
                    return True
            return False
            
        except Exception as e:
            self.logger.error(f"項目重複檢查失敗: {str(e)}")
            return False



    def _has_missing_data(self, row: RawDataRow) -> bool:
        """檢查是否有缺失資料（按照memo流程實現）"""
        return len(row.values) < len(self.items_data)

    def _update_raw_data(self, item: str, value: str):
        """更新原始資料

        Args:
            item: 項目代碼
            value: 項目值
        """
        try:
            # 如果沒有當前行，創建新行
            if not self.raw_data:
                self._create_new_row()

            # 將項目值加入當前行
            self._add_item_to_current_row(item, value)

        except Exception as e:
            self.logger.error(f"更新原始資料失敗: {str(e)}")

    def _update_items_data(self, item: str, value: str):
        """更新項目資料

        Args:
            item: 項目代碼
            value: 項目值
        """
        try:
            if item in self.items_data:
                self.items_data[item].value = value
                self.items_data[item].update_time = datetime.now()
                self.items_data[item].status = "已更新"

        except Exception as e:
            self.logger.error(f"更新項目資料失敗: {str(e)}")

    def _create_new_row(self):
        """創建新的資料行"""
        try:
            current_time = datetime.now()
            new_row = RawDataRow(
                receive_date=current_time.strftime('%Y-%m-%d'),
                receive_time=current_time.strftime('%H:%M:%S.%f')[:-3],
                values={},
                is_complete=False
            )

            # 將新行加入到隊列前端
            if len(self.raw_data) >= self.raw_data.maxlen:
                self.raw_data.popleft()
            self.raw_data.appendleft(new_row)

            self.logger.debug("創建新資料行")

        except Exception as e:
            self.logger.error(f"創建新資料行失敗: {str(e)}")

    def _add_item_to_current_row(self, item: str, value: str):
        """將項目值加入當前行

        Args:
            item: 項目代碼
            value: 項目值
        """
        try:
            if self.raw_data:
                current_row = self.raw_data[0]
                current_row.values[item] = value

                # 更新時間戳記
                current_time = datetime.now()
                current_row.receive_date = current_time.strftime('%Y-%m-%d')
                current_row.receive_time = current_time.strftime('%H:%M:%S.%f')[:-3]

        except Exception as e:
            self.logger.error(f"加入項目到當前行失敗: {str(e)}")

    def _fill_missing_data(self, row: RawDataRow):
        """補齊缺失的資料項目

        Args:
            row: 要補齊的資料行
        """
        try:
            for item_code, item_data in self.items_data.items():
                if item_code not in row.values:
                    row.values[item_code] = item_data.value or ""

            row.is_complete = True
            self.logger.debug("資料行補齊完成")

        except Exception as e:
            self.logger.error(f"補齊資料失敗: {str(e)}")

    def _check_value_change(self, current_row: RawDataRow) -> bool:
        """檢查資料行的值變化

        Args:
            current_row: 當前資料行

        Returns:
            是否有值變化
        """
        try:
            # 檢查是否有歷史資料用於比較
            if len(self.raw_data) < 2:
                self.logger.debug("沒有歷史資料，這是第一筆資料")
                return True

            # 獲取最後一行已保存的資料進行比較 - 修復：應該從最新的開始找
            last_saved_row = None
            # 從最新的資料開始往前找，跳過當前行（索引0）
            for i in range(1, len(self.raw_data)):
                row = self.raw_data[i]
                if row.is_complete:
                    last_saved_row = row
                    break

            if not last_saved_row:
                self.logger.debug("沒有已保存的歷史資料")
                return True

            check_mode = self.config.get('value_change_check_mode', 'single')
            check_items = self.config.get('value_change_check_items', '')

            self.logger.debug(f"值變化檢查: 模式={check_mode}, 檢查項目={check_items}")

            if check_mode == 'single':
                # 單項檢查模式
                if not check_items:
                    self.logger.debug("單項模式未指定檢查項目")
                    return True

                # 查找檢查項目的代碼
                check_item_code = None
                for item_code, item_data in self.items_data.items():
                    if item_data.name == check_items:
                        check_item_code = item_code
                        break

                if check_item_code:
                    current_value = current_row.values.get(check_item_code, "")
                    previous_value = last_saved_row.values.get(check_item_code, "")
                    has_changed = (current_value != previous_value)
                    self.logger.debug(f"單項檢查 {check_items}({check_item_code}): 當前值={current_value}, 前值={previous_value}, 變化={has_changed}")
                    return has_changed
                else:
                    self.logger.warning(f"找不到檢查項目 {check_items}")
                    return True

            elif check_mode == 'multiple':
                # 多項檢查模式
                if not check_items:
                    self.logger.debug("多項模式未指定檢查項目")
                    return True

                check_items_list = [item.strip() for item in check_items.split(',') if item.strip()]

                if not check_items_list:
                    self.logger.warning("多項模式檢查項目列表為空")
                    return True

                for check_item_name in check_items_list:
                    # 查找檢查項目的代碼
                    check_item_code = None
                    for item_code, item_data in self.items_data.items():
                        if item_data.name == check_item_name:
                            check_item_code = item_code
                            break

                    if check_item_code:
                        current_value = current_row.values.get(check_item_code, "")
                        previous_value = last_saved_row.values.get(check_item_code, "")
                        if current_value != previous_value:
                            self.logger.debug(f"多項檢查 {check_item_name}({check_item_code}): 值有變化 {previous_value} -> {current_value}")
                            return True
                    else:
                        self.logger.warning(f"找不到檢查項目 {check_item_name}")

                self.logger.debug("多項檢查所有項目值都沒有變化")
                return False

            elif check_mode == 'all':
                # 全部檢查模式 - 檢查所有DDE項目（排除時間戳記）
                for item_code in current_row.values.keys():
                    # 跳過時間戳記相關項目（雖然values中通常不包含，但為了安全起見）
                    if item_code in ['receive_date', 'receive_time']:
                        continue

                    current_value = current_row.values.get(item_code, "")
                    previous_value = last_saved_row.values.get(item_code, "")
                    if current_value != previous_value:
                        # 找到對應的項目名稱用於日誌
                        item_name = item_code
                        if item_code in self.items_data:
                            item_name = self.items_data[item_code].name
                        self.logger.debug(f"全部檢查 {item_name}({item_code}): 值有變化 {previous_value} -> {current_value}")
                        return True

                self.logger.debug("全部檢查所有項目值都沒有變化")
                return False

            else:
                self.logger.warning(f"未知的檢查模式: {check_mode}")
                return True

        except Exception as e:
            self.logger.error(f"值變化檢查失敗: {str(e)}")
            return True

    def _save_complete_row(self, row: RawDataRow):
        """保存完整資料行

        Args:
            row: 要保存的資料行
        """
        try:
            # 確保資料行已補齊
            if not row.is_complete:
                self._fill_missing_data(row)

            # 使用檔案處理器保存（濾除邏輯已在值變化檢查中處理）
            if self.file_handler:
                self.file_handler.save_row(row, is_complete=True)

            # 觸發回調
            if self.on_row_saved:
                self.on_row_saved(row)

            self.stats.total_saved += 1
            self.logger.debug("保存完整資料行")

        except Exception as e:
            self.logger.error(f"保存完整資料行失敗: {str(e)}")



    def _skip_row(self, row: RawDataRow):
        """跳過資料行

        Args:
            row: 被跳過的資料行
        """
        try:
            # 觸發回調
            if self.on_row_skipped:
                self.on_row_skipped(row)

            self.stats.total_skipped += 1
            self.logger.debug("跳過資料行")

        except Exception as e:
            self.logger.error(f"跳過資料行失敗: {str(e)}")
