# DDE多商品引擎混合版本

## 📋 项目概述

这是基于**原版多商品版本**改造的**引擎包装器混合版本**，用于与原版多商品版本进行对比测试。

## 🎯 设计目标

1. **保持多商品版本的GUI和用户体验**
2. **使用引擎包装器的数据处理核心**
3. **实现完全一致的数据输出**
4. **便于性能和功能对比**

## 📁 文件结构

```
dde_multi_engine_hybrid/
├── README.md                    # 本文件
├── dde_monitor_multi_engine.py  # 主程序（基于多商品版改造）
├── config/
│   └── multi_config.ini         # 配置文件（复制自原版）
├── core/
│   ├── engine_integration.py   # 引擎集成模块
│   └── data_bridge.py          # 数据桥接模块
├── gui/
│   └── hybrid_window.py        # 混合GUI窗口
└── logs/                       # 日志输出
```

## 🔧 实现方案

### 阶段1: 基础框架
- [x] 复制多商品版本代码
- [ ] 集成引擎包装器核心
- [ ] 修改数据处理流程

### 阶段2: 数据桥接
- [ ] 实现DDE数据桥接
- [ ] 保持GUI更新机制
- [ ] 确保输出文件一致

### 阶段3: 测试验证
- [ ] 与原版多商品版本对比
- [ ] 验证数据一致性
- [ ] 性能测试

## 🚀 使用方法

### 单独运行混合版本
```bash
cd dde_multi_engine_hybrid
python run_hybrid_test.py
```

### 对比测试（推荐）
```bash
# 在主目录运行对比测试
python run_comparison_hybrid.py

# 只运行原版
python run_comparison_hybrid.py --original-only

# 只运行混合版
python run_comparison_hybrid.py --hybrid-only

# 自定义监控时间（秒）
python run_comparison_hybrid.py -d 600
```

## 📊 对比测试

| 功能 | 原版多商品 | 混合版本 | 状态 |
|------|------------|----------|------|
| GUI界面 | ✅ | 🔄 | 开发中 |
| DDE连接 | ✅ | 🔄 | 开发中 |
| 数据处理 | ✅ | 🔄 | 开发中 |
| 文件输出 | ✅ | 🔄 | 开发中 |
| 性能表现 | ✅ | 🔄 | 待测试 |

## 🎯 预期优势

1. **更好的数据处理**: 使用引擎版本的数据处理逻辑
2. **更高的稳定性**: 引擎版本的错误处理机制
3. **更强的扩展性**: 支持更多商品和数据类型
4. **保持兼容性**: GUI和配置文件完全兼容

## 🔧 当前状态

### ✅ 已完成
- [x] 基础框架搭建
- [x] 引擎集成模块 (`core/engine_integration.py`)
- [x] 数据桥接模块 (`core/data_bridge.py`)
- [x] 主程序改造 (`dde_monitor_multi_engine.py`)
- [x] 测试脚本 (`run_hybrid_test.py`)
- [x] 对比测试脚本 (`../run_comparison_hybrid.py`)

### 🔄 开发中
- [ ] GUI窗口引擎集成
- [ ] DDE数据流集成
- [ ] 文件输出一致性

### 📋 待测试
- [ ] 基础启动测试
- [ ] DDE连接测试
- [ ] 数据处理测试
- [ ] 与原版对比测试

## 📝 开发日志

- 2025-06-26 13:47: 项目创建，完成基础框架开发
- 2025-06-26 13:47: 引擎集成和数据桥接模块开发完成
- 2025-06-26 13:47: 对比测试脚本创建完成
