# DDE 監控程式 - GUI效能優化功能說明

## 🎯 新增功能概述

為了解決長時間運行時GUI響應性問題和資源使用過高的問題，我們新增了以下手動控制功能：

### 1. **GUI更新頻率控制**
- 可在程式運行中動態調整GUI更新間隔
- 範圍：100ms ~ 5000ms (5秒)
- 默認值：500ms

### 2. **資訊框獨立控制**
每個資訊框都有獨立的控制按鈕：
- **暫停/恢復** - 暫停或恢復該資訊框的更新
- **清除** - 清空該資訊框的內容

## 🖥️ GUI界面說明

### **自動化功能區域**
在原有的自動化功能區域中，新增了：
```
GUI更新間隔: [500] ms  [自動連線] [自動結束] [自動化設定]
```

### **資料顯示區域**
每個資訊框上方都新增了控制按鈕：

#### 原始資料框
```
[暫停] [清除]
原始資料內容...
```

#### 處理後資料框
```
[暫停] [清除]
處理後資料內容...
```

#### 程式日誌框
```
[暫停] [清除]
程式日誌內容...
```

## 🔧 使用方法

### **調整GUI更新頻率**
1. 在"自動化功能"區域找到"GUI更新間隔"
2. 調整數值（100-5000ms）
3. 設定會立即生效

**建議設定：**
- **高頻監控**：100-200ms（更即時，但CPU使用較高）
- **一般監控**：500-1000ms（平衡效能和即時性）
- **低頻監控**：1000-3000ms（節省資源，適合長時間運行）

### **控制資訊框更新**

#### 暫停/恢復更新
1. 點擊對應資訊框的"暫停"按鈕
2. 按鈕會變成橙色並顯示"恢復"
3. 該資訊框停止更新，但資料處理繼續進行
4. 點擊"恢復"按鈕重新開始更新

#### 清除內容
1. 點擊對應資訊框的"清除"按鈕
2. 該資訊框的所有內容會被清空
3. 新的資料會繼續顯示（如果未暫停）

## 📊 效能優化原理

### **批量更新機制**
- **舊方式**：每收到一筆資料就立即更新GUI
- **新方式**：將資料暫存，定時批量更新GUI

### **減少GUI操作頻率**
- 降低文字控件的append操作頻率
- 減少行數檢查和清理操作
- 避免GUI主線程阻塞

### **可選擇性更新**
- 可以暫停不需要的資訊框更新
- 減少不必要的GUI渲染
- 保持核心功能正常運行

## 🧪 壓力測試建議

### **測試場景1：高頻更新**
```
GUI更新間隔：100ms
所有資訊框：啟用
運行時間：2-4小時
觀察：CPU使用率、記憶體使用、GUI響應性
```

### **測試場景2：中頻更新**
```
GUI更新間隔：500ms
所有資訊框：啟用
運行時間：4-8小時
觀察：長時間穩定性
```

### **測試場景3：低頻更新**
```
GUI更新間隔：2000ms
原始資料框：暫停
處理後資料框：啟用
程式日誌框：啟用
運行時間：8小時以上
觀察：最低資源使用情況
```

### **測試場景4：多程式壓力測試**
```
同時運行5個程式實例
每個程式設定不同的GUI更新間隔
觀察系統整體負載
```

## 📈 預期效果

### **CPU使用率改善**
- **高頻設定**：可能與原版相近，但GUI響應更穩定
- **中頻設定**：CPU使用率降低30-50%
- **低頻設定**：CPU使用率降低50-70%

### **記憶體使用改善**
- 減少GUI控件的頻繁操作
- 降低記憶體碎片化
- 更好的長時間穩定性

### **GUI響應性改善**
- 避免"沒有回應"狀態
- 更流暢的使用者互動
- 批量更新減少卡頓

## 🔍 監控和調試

### **觀察指標**
1. **CPU使用率**：工作管理員中的CPU%
2. **記憶體使用**：程式的記憶體佔用
3. **GUI響應性**：視窗標題是否顯示"沒有回應"
4. **資料完整性**：CSV檔案是否正常生成

### **調試技巧**
1. **逐步調整**：從高頻開始，逐步降低更新頻率
2. **分別測試**：先測試單一資訊框暫停的效果
3. **對比測試**：與原版程式對比資源使用情況
4. **長時間觀察**：至少運行4-6小時觀察穩定性

## ⚠️ 注意事項

1. **資料處理不受影響**：暫停GUI更新不會影響資料處理和檔案輸出
2. **日誌暫停**：暫停程式日誌會停止GUI日誌顯示，但檔案日誌正常
3. **設定保存**：GUI更新間隔設定不會自動保存，重啟後恢復默認值
4. **記憶體累積**：長時間暫停可能導致待更新資料累積，恢復時會一次性顯示

## 🚀 使用建議

### **日常使用**
- GUI更新間隔：500-1000ms
- 保持所有資訊框啟用
- 定期清除不需要的歷史資料

### **長時間監控**
- GUI更新間隔：1000-2000ms
- 可暫停原始資料框（減少資源使用）
- 保持處理後資料和日誌框啟用

### **多程式運行**
- 每個程式使用不同的更新間隔
- 錯開啟動時間
- 監控系統整體負載

這些新功能讓您可以根據實際需求靈活調整程式的效能表現，在資源使用和即時性之間找到最佳平衡點。
