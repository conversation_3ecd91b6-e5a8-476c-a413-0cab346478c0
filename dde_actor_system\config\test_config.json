{"system": {"system_name": "DDE Actor System Test", "version": "1.0.0", "debug_mode": true, "log_level": "DEBUG", "log_file": "logs/test_system.log", "log_max_size_mb": 10, "log_backup_count": 3, "enable_monitoring": true, "monitoring_interval": 10.0, "health_check_interval": 60.0, "enable_remote_api": false, "api_host": "localhost", "api_port": 8080}, "performance": {"dde_buffer_size": 65536, "dde_batch_size": 100, "dde_batch_timeout": 0.1, "processing_batch_size": 100, "processing_queue_size": 1000, "processing_workers": 2, "gui_update_interval_ms": 100, "gui_max_batch_size": 100, "gui_virtual_rows": 50, "file_batch_size": 100, "file_flush_interval": 2.0, "file_buffer_size": 4096, "backpressure_high_watermark": 800, "backpressure_low_watermark": 600, "backpressure_strategy": "drop_oldest", "memory_pool_initial_size": 100, "gc_threshold": 1000}, "products": [{"symbol": "TEST_PRODUCT", "data_types": ["tick"], "service": "TEST_SERVICE", "topic": "TEST_TOPIC", "items": ["TEST_PRODUCT.tick.價格", "TEST_PRODUCT.tick.數量"], "enabled": true, "priority": 1, "custom_settings": {"description": "測試產品", "exchange": "TEST", "contract_type": "test"}}]}