#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
強制數據流

直接向 DDE Actor System 發送模擬數據，測試數據處理流程
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from core.message_system import MessageRouter, create_dde_data_message
from actors.data_processor import DataProcessorActor
from actors.file_writer import FileWriterActor


async def test_data_flow():
    """測試數據流"""
    print("🚀 強制數據流測試")
    print("=" * 50)
    
    try:
        print("1. 創建組件...")
        
        # 創建消息路由
        router = MessageRouter()
        
        # 創建數據處理Actor
        processor_config = {
            'batch_size': 5,
            'queue_size': 100,
            'backpressure': {
                'high_watermark': 80,
                'low_watermark': 60,
                'strategy': 'drop_oldest'
            }
        }
        data_processor = DataProcessorActor('DataProcessor', processor_config)
        router.register_actor(data_processor)
        
        # 創建文件寫入Actor
        file_config = {
            'batch_size': 5,
            'flush_interval': 1.0,
            'files': {
                'default': {
                    'filename': 'outputs/force_test_data.csv',
                    'format': 'csv',
                    'max_size_mb': 5,
                    'compress': False
                },
                'json': {
                    'filename': 'outputs/force_test_data.json',
                    'format': 'json',
                    'max_size_mb': 5,
                    'compress': False
                }
            }
        }
        file_writer = FileWriterActor('FileWriter', file_config)
        router.register_actor(file_writer)
        
        print("✅ 組件創建成功")
        
        print("\n2. 啟動組件...")
        await data_processor.start()
        await file_writer.start()
        print("✅ 組件啟動成功")
        
        print("\n3. 發送測試數據...")
        
        # 模擬真實的 DDE 數據
        test_data = [
            ("FITXN07.tick.成交價", "22272"),
            ("FITXN07.tick.成交量", "1"),
            ("FITXN08.tick.成交價", "22091"),
            ("FITXN08.tick.成交量", "2"),
            ("FITXN09.tick.成交價", "22150"),
            ("FITXN09.tick.成交量", "3"),
            ("FITXN07.tick.成交價", "22275"),
            ("FITXN07.tick.成交量", "1"),
            ("FITXN08.tick.成交價", "22095"),
            ("FITXN08.tick.成交量", "2"),
        ]
        
        for i, (item, value) in enumerate(test_data):
            # 創建 DDE 數據消息
            message = create_dde_data_message(item, value, "ForceDataFlow")
            
            # 發送到數據處理器
            success = await router.send_message("DataProcessor", message)
            print(f"   發送 {i+1}: {item} = {value} {'✅' if success else '❌'}")
            
            # 稍微延遲模擬真實數據流
            await asyncio.sleep(0.2)
        
        print("✅ 測試數據發送完成")
        
        print("\n4. 等待處理完成...")
        await asyncio.sleep(3.0)
        
        print("\n5. 檢查處理統計...")
        processor_stats = data_processor.get_processing_stats()
        print(f"   處理的項目數: {processor_stats['processing_stats']['items_processed']}")
        print(f"   處理的批次數: {processor_stats['processing_stats']['batches_processed']}")
        
        file_stats = file_writer.get_file_stats()
        print(f"   寫入的項目數: {file_stats['write_stats']['items_written']}")
        print(f"   寫入的批次數: {file_stats['write_stats']['batches_written']}")
        
        print("\n6. 檢查輸出文件...")
        output_files = [
            "outputs/force_test_data.csv",
            "outputs/force_test_data.json"
        ]
        
        for file_path in output_files:
            path = Path(file_path)
            if path.exists():
                size = path.stat().st_size
                print(f"   ✅ {path.name}: {size} bytes")
                
                if path.suffix == '.csv' and size > 0:
                    try:
                        with open(path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            print(f"      行數: {len(lines)}")
                            if lines:
                                print(f"      最後一行: {lines[-1].strip()}")
                    except Exception as e:
                        print(f"      讀取錯誤: {str(e)}")
            else:
                print(f"   ❌ {path.name}: 文件不存在")
        
        print("\n7. 停止組件...")
        await data_processor.stop()
        await file_writer.stop()
        print("✅ 組件停止成功")
        
        print("\n🎉 強制數據流測試完成!")
        return True
        
    except Exception as e:
        print(f"\n❌ 測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_real_dde_integration():
    """測試真實 DDE 整合"""
    print(f"\n" + "=" * 50)
    print("🔗 真實 DDE 整合測試")
    print("=" * 50)
    
    try:
        from dydde.dydde import DDEClient
        
        print("1. 創建 DDE 客戶端...")
        client = DDEClient("XQTISC", "Quote")
        client.connect()
        print("✅ DDE 連接成功")
        
        print("\n2. 創建數據處理組件...")
        router = MessageRouter()
        
        processor_config = {'batch_size': 3, 'queue_size': 50}
        data_processor = DataProcessorActor('DataProcessor', processor_config)
        router.register_actor(data_processor)
        
        file_config = {
            'batch_size': 3,
            'flush_interval': 1.0,
            'files': {
                'default': {
                    'filename': 'outputs/real_dde_data.csv',
                    'format': 'csv',
                    'max_size_mb': 5,
                    'compress': False
                }
            }
        }
        file_writer = FileWriterActor('FileWriter', file_config)
        router.register_actor(file_writer)
        
        await data_processor.start()
        await file_writer.start()
        print("✅ 組件啟動成功")
        
        print("\n3. 獲取真實 DDE 數據並處理...")
        test_items = [
            "FITXN07.TF-Price",
            "FITXN07.TF-Volume",
            "FITXN08.TF-Price",
            "FITXN08.TF-Volume"
        ]
        
        for item in test_items:
            try:
                # 從 DDE 獲取數據
                value = client.request(item)
                
                if value and value.strip():
                    # 創建消息並發送到處理器
                    message = create_dde_data_message(item, value, "RealDDE")
                    success = await router.send_message("DataProcessor", message)
                    print(f"   處理 {item}: {value} {'✅' if success else '❌'}")
                else:
                    print(f"   ⚪ {item}: 無數據")
                
                await asyncio.sleep(0.5)
                
            except Exception as e:
                print(f"   ❌ {item}: {str(e)}")
        
        print("\n4. 等待處理完成...")
        await asyncio.sleep(2.0)
        
        print("\n5. 檢查結果...")
        output_file = Path("outputs/real_dde_data.csv")
        if output_file.exists():
            size = output_file.stat().st_size
            print(f"   ✅ 真實數據文件: {size} bytes")
            
            if size > 0:
                with open(output_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print(f"   行數: {len(lines)}")
                    for line in lines:
                        print(f"   數據: {line.strip()}")
        else:
            print(f"   ❌ 真實數據文件未生成")
        
        await data_processor.stop()
        await file_writer.stop()
        client.disconnect()
        
        print("✅ 真實 DDE 整合測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 真實 DDE 整合測試失敗: {str(e)}")
        return False


async def main():
    """主函數"""
    print("🧪 DDE Actor System - 強制數據流測試")
    print("此工具將直接測試數據處理流程")
    print("=" * 60)
    
    try:
        # 測試強制數據流
        force_result = await test_data_flow()
        
        if force_result:
            # 測試真實 DDE 整合
            real_result = await test_real_dde_integration()
            
            if real_result:
                print(f"\n🎉 所有測試通過!")
                print("數據處理流程正常工作")
                print("\n建議:")
                print("1. 檢查 outputs/ 目錄中的測試文件")
                print("2. 如果測試成功但主系統沒有數據，可能是訂閱邏輯問題")
                print("3. 考慮修改 DDE Receiver 的訂閱邏輯")
            else:
                print(f"\n⚠️  強制數據流成功，但真實 DDE 整合失敗")
        else:
            print(f"\n❌ 強制數據流測試失敗")
        
    except Exception as e:
        print(f"\n❌ 測試執行失敗: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
