#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享 DDE 客戶端

解決 127 個連接限制問題 - 使用共享 DDE 實例
"""

import threading
from typing import Dict, Optional, Callable
import sys
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

# 導入 DDE 相關模組
try:
    from dydde.dydde import DDE, timestamp
except ImportError:
    print("❌ 無法導入 dydde 模組")
    sys.exit(1)


class SharedDDEManager:
    """共享 DDE 管理器 - 單例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self._idInst = None
        self._conversations: Dict[str, int] = {}  # key: "service.topic", value: hConv
        self._callbacks: Dict[str, Dict[str, Callable]] = {}  # key: "service.topic", value: {item: callback}
        self._lock = threading.Lock()
        self._connection_count = 0
        
        print(f"[{timestamp()}] [INFO] [SharedDDE] 初始化共享 DDE 管理器")
    
    def initialize(self):
        """初始化 DDE 系統"""
        with self._lock:
            if self._idInst is None:
                try:
                    self._idInst = DDE.Initialize(None, DDE.APPCLASS_STANDARD | DDE.APPCMD_CLIENTONLY)
                    if self._idInst:
                        print(f"[{timestamp()}] [INFO] [SharedDDE] DDE 系統初始化成功: idInst={self._idInst}")
                        return True
                    else:
                        print(f"[{timestamp()}] [ERROR] [SharedDDE] DDE 系統初始化失敗")
                        return False
                except Exception as e:
                    print(f"[{timestamp()}] [ERROR] [SharedDDE] DDE 初始化異常: {str(e)}")
                    return False
            return True
    
    def connect(self, service: str, topic: str) -> Optional[int]:
        """連接到 DDE 服務"""
        conn_key = f"{service}.{topic}"
        
        with self._lock:
            # 檢查是否已有連接
            if conn_key in self._conversations:
                print(f"[{timestamp()}] [INFO] [SharedDDE] 復用現有連接: {conn_key}")
                return self._conversations[conn_key]
            
            # 確保 DDE 已初始化
            if not self.initialize():
                return None
            
            try:
                # 建立新連接
                print(f"[{timestamp()}] [INFO] [SharedDDE] 建立新連接: {conn_key}")
                
                hszService = DDE.CreateStringHandle(self._idInst, service, DDE.CP_WINANSI)
                hszTopic = DDE.CreateStringHandle(self._idInst, topic, DDE.CP_WINANSI)
                
                hConv = DDE.Connect(self._idInst, hszService, hszTopic, None)
                
                DDE.FreeStringHandle(self._idInst, hszService)
                DDE.FreeStringHandle(self._idInst, hszTopic)
                
                if hConv:
                    self._conversations[conn_key] = hConv
                    self._callbacks[conn_key] = {}
                    self._connection_count += 1
                    print(f"[{timestamp()}] [INFO] [SharedDDE] 連接成功: {conn_key}, hConv={hConv}, 總連接數={self._connection_count}")
                    return hConv
                else:
                    print(f"[{timestamp()}] [ERROR] [SharedDDE] 連接失敗: {conn_key}")
                    return None
                    
            except Exception as e:
                print(f"[{timestamp()}] [ERROR] [SharedDDE] 連接異常: {conn_key}: {str(e)}")
                return None
    
    def request(self, service: str, topic: str, item: str) -> Optional[str]:
        """請求數據"""
        conn_key = f"{service}.{topic}"
        
        # 確保連接存在
        hConv = self.connect(service, topic)
        if not hConv:
            return None
        
        try:
            hszItem = DDE.CreateStringHandle(self._idInst, item, DDE.CP_WINANSI)
            hData = DDE.ClientTransaction(None, 0, hConv, hszItem, DDE.CF_TEXT, DDE.XTYP_REQUEST, 5000, None)
            DDE.FreeStringHandle(self._idInst, hszItem)
            
            if hData:
                data = DDE.AccessData(hData, None)
                if data:
                    result = data.decode('utf-8', errors='ignore').rstrip('\x00')
                    DDE.UnaccessData(hData)
                    DDE.FreeDataHandle(hData)
                    return result
                else:
                    DDE.FreeDataHandle(hData)
            
            return None
            
        except Exception as e:
            print(f"[{timestamp()}] [ERROR] [SharedDDE] 請求數據失敗: {item}: {str(e)}")
            return None
    
    def disconnect(self, service: str, topic: str):
        """斷開特定連接"""
        conn_key = f"{service}.{topic}"
        
        with self._lock:
            if conn_key in self._conversations:
                try:
                    hConv = self._conversations[conn_key]
                    DDE.Disconnect(hConv)
                    del self._conversations[conn_key]
                    del self._callbacks[conn_key]
                    self._connection_count -= 1
                    print(f"[{timestamp()}] [INFO] [SharedDDE] 斷開連接: {conn_key}, 剩餘連接數={self._connection_count}")
                except Exception as e:
                    print(f"[{timestamp()}] [ERROR] [SharedDDE] 斷開連接失敗: {conn_key}: {str(e)}")
    
    def disconnect_all(self):
        """斷開所有連接"""
        with self._lock:
            print(f"[{timestamp()}] [INFO] [SharedDDE] 斷開所有連接...")
            
            for conn_key, hConv in list(self._conversations.items()):
                try:
                    DDE.Disconnect(hConv)
                    print(f"[{timestamp()}] [INFO] [SharedDDE] 已斷開: {conn_key}")
                except Exception as e:
                    print(f"[{timestamp()}] [ERROR] [SharedDDE] 斷開失敗: {conn_key}: {str(e)}")
            
            self._conversations.clear()
            self._callbacks.clear()
            self._connection_count = 0
            
            # 清理 DDE 系統
            if self._idInst:
                try:
                    DDE.Uninitialize(self._idInst)
                    self._idInst = None
                    print(f"[{timestamp()}] [INFO] [SharedDDE] DDE 系統已清理")
                except Exception as e:
                    print(f"[{timestamp()}] [ERROR] [SharedDDE] DDE 清理失敗: {str(e)}")
    
    def get_stats(self) -> Dict:
        """獲取統計信息"""
        with self._lock:
            return {
                'total_connections': self._connection_count,
                'active_conversations': list(self._conversations.keys()),
                'dde_initialized': self._idInst is not None
            }


class SharedDDEClient:
    """共享 DDE 客戶端"""
    
    def __init__(self, service: str, topic: str):
        self.service = service
        self.topic = topic
        self.manager = SharedDDEManager()
        self._connected = False
    
    def connect(self):
        """連接"""
        hConv = self.manager.connect(self.service, self.topic)
        self._connected = hConv is not None
        return self._connected
    
    def disconnect(self):
        """斷開連接"""
        if self._connected:
            self.manager.disconnect(self.service, self.topic)
            self._connected = False
    
    def request(self, item: str) -> Optional[str]:
        """請求數據"""
        if not self._connected:
            if not self.connect():
                return None
        
        return self.manager.request(self.service, self.topic, item)
    
    def is_connected(self) -> bool:
        """檢查連接狀態"""
        return self._connected


def test_shared_dde():
    """測試共享 DDE 客戶端"""
    print("🧪 測試共享 DDE 客戶端")
    print("=" * 60)
    
    clients = []
    manager = SharedDDEManager()
    
    try:
        # 測試建立大量連接
        test_products = []
        
        # 期貨產品
        futures = ["FITXN07", "FITXN08", "FITXN09"]
        for symbol in futures:
            test_products.append((symbol, "XQTISC", "Quote", f"{symbol}.TF-Price"))
        
        # 股票產品
        stocks = ["2330", "2317", "2454"]
        for symbol in stocks:
            test_products.append((symbol, "XQTISC", "Quote", f"{symbol}.TW-Price"))
        
        print(f"測試 {len(test_products)} 個產品...")
        
        successful_connections = 0
        successful_requests = 0
        
        for i, (symbol, service, topic, item) in enumerate(test_products, 1):
            print(f"\n測試產品 {i}: {symbol}")
            
            try:
                client = SharedDDEClient(service, topic)
                if client.connect():
                    clients.append(client)
                    successful_connections += 1
                    print(f"   ✅ 連接成功")
                    
                    # 測試數據請求
                    result = client.request(item)
                    if result:
                        successful_requests += 1
                        print(f"   ✅ 數據: {item} = {result}")
                    else:
                        print(f"   ⚪ 無數據: {item}")
                else:
                    print(f"   ❌ 連接失敗")
                    
            except Exception as e:
                print(f"   ❌ 異常: {str(e)}")
        
        # 顯示統計
        stats = manager.get_stats()
        print(f"\n📊 測試結果:")
        print(f"   嘗試連接: {len(test_products)}")
        print(f"   成功連接: {successful_connections}")
        print(f"   成功請求: {successful_requests}")
        print(f"   實際 DDE 連接數: {stats['total_connections']}")
        print(f"   活躍對話: {stats['active_conversations']}")
        
        # 關鍵測試：檢查是否復用了連接
        unique_services = set((service, topic) for _, service, topic, _ in test_products)
        print(f"\n🔍 連接復用分析:")
        print(f"   不同服務數: {len(unique_services)}")
        print(f"   實際連接數: {stats['total_connections']}")
        print(f"   復用效果: {'✅ 成功復用' if stats['total_connections'] <= len(unique_services) else '❌ 沒有復用'}")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  測試被中斷")
    except Exception as e:
        print(f"\n❌ 測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理所有連接
        print(f"\n🧹 清理資源...")
        manager.disconnect_all()


if __name__ == "__main__":
    test_shared_dde()
