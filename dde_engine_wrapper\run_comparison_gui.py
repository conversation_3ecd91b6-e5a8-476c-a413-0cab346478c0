#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI版本比较测试启动程序
同时启动原版多商品和引擎包装器版进行比较
"""

import sys
import os
import subprocess
import time
import logging

def setup_logging():
    """设置日志"""
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, 'comparison_gui.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file, encoding='utf-8')
        ]
    )
    
    return logging.getLogger(__name__)

def check_prerequisites(logger):
    """检查前置条件"""
    logger.info("检查前置条件...")
    
    # 检查PySide6
    try:
        import PySide6
        logger.info("✓ PySide6 可用")
    except ImportError:
        logger.error("✗ PySide6 不可用，请安装: pip install PySide6")
        return False
    
    # 检查配置文件
    config_file = os.path.join(os.path.dirname(__file__), 'config', 'multi_config.ini')
    if not os.path.exists(config_file):
        logger.error(f"✗ 配置文件不存在: {config_file}")
        return False
    logger.info("✓ 配置文件存在")
    
    # 检查原版多商品程序
    original_path = os.path.join(os.path.dirname(__file__), '..', 'dde_monitor_multi.py')
    if not os.path.exists(original_path):
        logger.warning(f"⚠ 原版多商品程序不存在: {original_path}")
        logger.info("将只启动引擎包装器版")
        return "wrapper_only"
    logger.info("✓ 原版多商品程序存在")
    
    # 检查引擎包装器GUI
    wrapper_gui = os.path.join(os.path.dirname(__file__), 'gui', 'wrapper_test_window.py')
    if not os.path.exists(wrapper_gui):
        logger.error(f"✗ 引擎包装器GUI不存在: {wrapper_gui}")
        return False
    logger.info("✓ 引擎包装器GUI存在")
    
    return True

def start_original_version(logger):
    """启动原版多商品程序"""
    try:
        original_dir = os.path.join(os.path.dirname(__file__), '..')
        config_file = "multi_config.ini"
        
        command = [
            sys.executable,
            'dde_monitor_multi.py',
            '--config', config_file
        ]
        
        logger.info("启动原版多商品程序...")
        logger.info(f"命令: {' '.join(command)}")
        logger.info(f"工作目录: {original_dir}")
        
        process = subprocess.Popen(
            command,
            cwd=original_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        logger.info(f"✓ 原版多商品程序已启动 (PID: {process.pid})")
        return process
        
    except Exception as e:
        logger.error(f"✗ 启动原版多商品程序失败: {str(e)}")
        return None

def start_wrapper_version(logger):
    """启动引擎包装器版程序"""
    try:
        wrapper_dir = os.path.dirname(__file__)
        
        command = [
            sys.executable,
            'run_gui_test.py'
        ]
        
        logger.info("启动引擎包装器版程序...")
        logger.info(f"命令: {' '.join(command)}")
        logger.info(f"工作目录: {wrapper_dir}")
        
        process = subprocess.Popen(
            command,
            cwd=wrapper_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8'
        )
        
        logger.info(f"✓ 引擎包装器版程序已启动 (PID: {process.pid})")
        return process
        
    except Exception as e:
        logger.error(f"✗ 启动引擎包装器版程序失败: {str(e)}")
        return None

def monitor_processes(original_process, wrapper_process, logger):
    """监控进程状态"""
    logger.info("开始监控进程状态...")
    logger.info("按 Ctrl+C 停止监控并结束所有进程")
    
    try:
        while True:
            # 检查进程状态
            original_running = original_process and original_process.poll() is None
            wrapper_running = wrapper_process and wrapper_process.poll() is None
            
            if not original_running and not wrapper_running:
                logger.info("所有进程都已结束")
                break
            elif not original_running and original_process:
                logger.warning("原版多商品程序已结束")
                original_process = None
            elif not wrapper_running and wrapper_process:
                logger.warning("引擎包装器版程序已结束")
                wrapper_process = None
            
            time.sleep(5)  # 每5秒检查一次
            
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在结束进程...")
        
        # 结束进程
        if original_process and original_process.poll() is None:
            try:
                original_process.terminate()
                original_process.wait(timeout=10)
                logger.info("✓ 原版多商品程序已结束")
            except:
                original_process.kill()
                logger.info("✓ 原版多商品程序已强制结束")
        
        if wrapper_process and wrapper_process.poll() is None:
            try:
                wrapper_process.terminate()
                wrapper_process.wait(timeout=10)
                logger.info("✓ 引擎包装器版程序已结束")
            except:
                wrapper_process.kill()
                logger.info("✓ 引擎包装器版程序已强制结束")

def main():
    """主函数"""
    print("GUI版本比较测试启动程序")
    print("=" * 50)
    
    logger = setup_logging()
    
    # 检查前置条件
    check_result = check_prerequisites(logger)
    if check_result is False:
        logger.error("前置条件检查失败，无法启动")
        return False
    
    wrapper_only = check_result == "wrapper_only"
    
    # 启动程序
    original_process = None
    wrapper_process = None
    
    try:
        if not wrapper_only:
            # 启动原版多商品程序
            original_process = start_original_version(logger)
            if original_process:
                logger.info("等待2秒后启动引擎包装器版...")
                time.sleep(2)
        
        # 启动引擎包装器版程序
        wrapper_process = start_wrapper_version(logger)
        
        if not wrapper_process:
            logger.error("引擎包装器版启动失败")
            return False
        
        # 显示启动信息
        print("\n" + "=" * 50)
        if original_process:
            print("✓ 原版多商品程序已启动")
        print("✓ 引擎包装器版程序已启动")
        print("\n现在可以在两个GUI窗口中进行比较测试:")
        if original_process:
            print("1. 原版多商品DDE监控窗口")
        print("2. 引擎包装器版测试窗口")
        print("\n比较要点:")
        print("- 连接速度和稳定性")
        print("- 数据接收率和准确性") 
        print("- 界面响应速度")
        print("- 资源使用情况")
        print("- 错误处理能力")
        print("=" * 50)
        
        # 监控进程
        monitor_processes(original_process, wrapper_process, logger)
        
        logger.info("比较测试结束")
        return True
        
    except Exception as e:
        logger.error(f"运行比较测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    print("\n🎉 比较测试完成" if success else "\n❌ 比较测试失败")
    sys.exit(0 if success else 1)
