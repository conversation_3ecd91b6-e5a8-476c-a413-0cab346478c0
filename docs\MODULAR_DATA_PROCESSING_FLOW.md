# DDE 模組化版資料處理流程文件

## 概述

本文件詳細說明 DDE 模組化版 (`dde_monitor_new.py` 及相關模組) 從接收資料到完整輸出的完整流程。模組化版採用分層架構設計，將功能分拆到不同模組中，提供更好的可維護性和擴展性。

## 核心架構

### 主要模組
- **dde_monitor_new.py**: 主程式入口
- **gui/main_window.py**: 主視窗GUI模組
- **core/data_handler.py**: 資料處理核心模組
- **utils/config_manager.py**: 配置管理模組
- **utils/logger.py**: 日誌管理模組
- **core/auto_manager.py**: 自動化管理模組

### 資料結構
```python
@dataclass
class ItemData:
    name: str                           # 項目名稱
    code: str                           # DDE項目代碼
    value: str = ""                     # 當前值
    update_time: Optional[datetime] = None  # 最後更新時間
    status: str = "未訂閱"              # 訂閱狀態

@dataclass
class RawDataRow:
    receive_date: str                   # 接收日期 (YYYY-MM-DD)
    receive_time: str                   # 接收時間 (HH:MM:SS.ffffff)
    values: Dict[str, str]              # 項目值字典，key為項目代碼，value為項目值
    is_complete: bool = False           # 是否為完整資料行
```

## 完整資料處理流程

### 1. 應用程式初始化 (`DDEMonitorApp.initialize`)

```python
def initialize(self):
    """初始化應用程式"""
    # 創建 QApplication
    self.app = QApplication(sys.argv)
    
    # 初始化設定管理器
    self.config_manager = ConfigManager()
    self.config = self.config_manager.load_config(self.config_file)
    
    # 初始化日誌系統
    self.logger = setup_logging(log_file_path, log_level, console_log_level, file_log_level)
    
    # 初始化自動化管理器
    self.auto_manager = AutoConnectManager(self.config, self.logger)
    
    # 初始化主視窗
    self.main_window = DDEMainWindow(self.config, self.logger, self.auto_manager)
    
    # 連接信號
    self._connect_signals()
    
    # 啟動自動化管理器
    self.auto_manager.start()
```

**模組化特點**：
- 清晰的職責分離
- 獨立的配置和日誌管理
- 信號槽機制連接各模組

### 2. 資料接收入口 (`gui/main_window.py: on_advise_data`)

```python
def on_advise_data(self, item: str, value: str):
    """處理DDE資料更新"""
    try:
        self.data_processor.add_data(item, value)
    except Exception as e:
        self.logger.error(f"加入資料到佇列失敗: {str(e)}")
```

**佇列處理機制**：
- 使用 `DataProcessor` 類別處理資料佇列
- 非同步處理避免阻塞DDE回調
- 信號槽機制傳遞處理結果

### 3. 資料處理器 (`core/data_handler.py: DataProcessor`)

```python
class DataProcessor(QObject):
    """資料處理器 - 在獨立線程中處理DDE資料更新"""
    
    data_processed = Signal(str, str)  # item, value
    
    def add_data(self, item: str, value: str):
        """添加資料到處理佇列"""
        self.data_queue.put((item, value))
    
    def process_data(self):
        """處理資料的主循環"""
        self.running = True
        
        while self.running:
            try:
                # 從佇列中獲取資料
                item, value = self.data_queue.get(timeout=1)
                
                # 發送處理完成信號
                self.data_processed.emit(item, value)
                
                # 標記任務完成
                self.data_queue.task_done()
                
            except:
                # 超時或其他異常，繼續循環
                continue
```

**處理器特點**：
- 繼承自QObject，支援信號槽機制
- 獨立的處理循環
- 異常安全的佇列處理

### 4. 實際資料處理 (`gui/main_window.py: _process_advise_data`)

```python
def _process_advise_data(self, item: str, value: str):
    """實際處理資料的方法"""
    try:
        # 1. 檢查是否需要項目重複換行
        if self.check_item_repeat_newline(item, value):
            return

        # 2. 更新原始資料
        self.update_raw_data(item, value)

        # 3. 更新項目資料
        if item in self.items_data:
            self.items_data[item].value = value
            self.items_data[item].update_time = datetime.now()

        # 4. 更新表格
        self.update_items_table()

        # 5. 添加到原始資料待更新佇列（而不是即時更新）
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        raw_text = f"[{timestamp}] {item}: {value}"
        self.pending_raw_data.append(raw_text)

        # 6. 更新最後接收時間
        self.last_advise_time = time.time()

    except Exception as e:
        self.logger.error(f"處理資料失敗: {str(e)}")
```

**處理順序**：
1. 項目重複換行檢查
2. 原始資料更新
3. 項目資料更新
4. GUI表格更新
5. 待更新佇列管理
6. 時間記錄更新

### 5. 項目重複換行檢查 (`check_item_repeat_newline`)

```python
def check_item_repeat_newline(self, item: str, value: str) -> bool:
    """檢查是否需要項目重複換行"""
    try:
        if not self.raw_data:
            return False

        current_row = self.raw_data[0]

        # 如果項目已存在於當前行，則需要換行
        if item in current_row.values:
            # 補齊缺失資料
            if self.has_missing_data(current_row):
                self.fill_missing_data(current_row)

            # 如果啟用了值變化檢查，則檢查值是否有變化
            if self.enable_value_change_check:
                has_changed = self.check_value_change(current_row)

                if has_changed:
                    # 儲存完整資料行
                    self.file_handler.save_row(current_row, is_complete=True)
                    # 顯示處理後資料
                    self.display_processed_data(current_row)
                    # 建立新行
                    self.create_new_row()
                    # 將收到的項目值填入新行
                    new_row = self.raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    return True
                else:
                    # 值未變化，不儲存資料行，但建立新行
                    self.create_new_row()
                    # 將收到的項目值填入新行
                    new_row = self.raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    return True
            else:
                # 未啟用值變化檢查，直接儲存資料行
                self.file_handler.save_row(current_row, is_complete=True)
                # 顯示處理後資料
                self.display_processed_data(current_row)
                # 建立新行並填入新項目值
                self.create_new_row()
                new_row = self.raw_data[0]
                new_row.values[item] = value
                new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                return True

        return False

    except Exception as e:
        self.logger.error(f"項目重複檢查失敗: {str(e)}")
        return False
```

**模組化版特點**：
- 包含GUI更新邏輯 (`display_processed_data`)
- 詳細的日誌記錄
- 完整的異常處理

### 6. 時間間隔檢查機制

模組化版使用定時器機制進行時間間隔檢查：

```python
# 在主視窗初始化中設置定時器
self.interval_timer = QTimer()
self.interval_timer.timeout.connect(self.check_time_interval)
self.interval_timer.start(100)  # 100ms間隔檢查

def check_time_interval(self):
    """檢查時間間隔"""
    if not self.enable_time_newline:
        return
        
    now = time.time()
    
    if (self.last_advise_time is not None and 
        now - self.last_advise_time >= self.time_newline_interval):
        
        self.check_time_interval_newline()
        self.last_advise_time = now

def check_time_interval_newline(self):
    """檢查是否需要時間間隔換行"""
    if not self.enable_time_newline:
        return
        
    if not self.raw_data:
        return
        
    current_row = self.raw_data[0]
    
    # 檢查行是否有資料
    if not current_row.values:
        return
        
    # 檢查並補齊缺失資料
    if self.has_missing_data(current_row):
        self.fill_missing_data(current_row)
        
    # 如果啟用了值變化檢查，則檢查值是否有變化
    if self.enable_value_change_check:
        has_changed = self.check_value_change(current_row)
        
        if has_changed:
            # 儲存完整資料行
            self.file_handler.save_row(current_row, is_complete=True)
            # 顯示處理後資料
            self.display_processed_data(current_row)
            # 建立新行
            self.create_new_row()
        else:
            # 值未變化，不儲存資料行，但建立新行
            self.create_new_row()
    else:
        # 未啟用值變化檢查，直接儲存資料行
        self.file_handler.save_row(current_row, is_complete=True)
        # 顯示處理後資料
        self.display_processed_data(current_row)
        self.create_new_row()
```

**定時器特點**：
- 使用QTimer進行定期檢查
- 100ms的檢查間隔
- 與GUI事件循環整合

### 7. 值變化檢查

模組化版的值變化檢查邏輯與v6版基本相同，但整合了更好的日誌記錄：

```python
def check_value_change(self, current_row: RawDataRow) -> bool:
    """檢查值變化"""
    # 如果未啟用值變化檢查，直接返回 True
    if not self.enable_value_change_check:
        return True
        
    # 獲取檢查模式
    check_mode = self.config.get('Table', 'value_change_check_mode', fallback='single')
    
    # 如果沒有前一行，直接返回 True
    if len(self.raw_data) <= 1:
        return True
        
    previous_row = self.raw_data[1]
    
    # 根據不同模式進行檢查
    if check_mode == 'single':
        # 單一項目檢查邏輯
        pass
    elif check_mode == 'multiple':
        # 多個項目檢查邏輯
        pass
    elif check_mode == 'all':
        # 全部項目檢查邏輯
        pass
```

### 8. 檔案處理機制 (`core/data_handler.py: DataFileHandler`)

```python
class DataFileHandler:
    """資料檔案處理器"""
    
    def __init__(self):
        self.data_file_path = None
        self.complete_data_file_path = None
        self.log_file_path = None
        self.enable_data_file = False
        self.enable_complete_data_file = False
        self.enable_log_file = False
        self.config = None
        self.items_data = None

    def init_files(self, config, items_data: Dict[str, ItemData]):
        """初始化檔案路徑和設定"""
        # 保存配置和項目數據
        self.config = config
        self.items_data = items_data
        
        # 讀取檔案輸出控制設定
        self.enable_data_file = config.getboolean('FileOutput', 'enable_data_file', fallback=False)
        self.enable_complete_data_file = config.getboolean('FileOutput', 'enable_complete_data_file', fallback=True)
        self.enable_log_file = config.getboolean('FileOutput', 'enable_log_file', fallback=True)
        
        # 處理檔案路徑
        self._setup_file_paths(config)
        
        # 初始化檔案標題
        self._init_file_headers()

    def save_row(self, row: RawDataRow, is_complete: bool = False):
        """保存資料行"""
        try:
            if is_complete and self.enable_complete_data_file:
                self._save_complete_data_row(row)
                
            if self.enable_data_file:
                self._save_data_row(row)
                
            if self.enable_log_file:
                self._log_data_row(row, is_complete)
                
        except Exception as e:
            logging.error(f"保存資料行失敗: {str(e)}")
```

**檔案處理特點**：
- 支援多種檔案格式輸出
- 可配置的檔案輸出控制
- 自動檔案路徑處理和目錄創建

## 配置管理 (`utils/config_manager.py`)

```python
class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config = None
        self.config_file_path = None
    
    def load_config(self, config_file='config.ini'):
        """載入配置檔案"""
        self.config = configparser.ConfigParser()
        self.config_file_path = config_file
        
        if os.path.exists(config_file):
            self.config.read(config_file, encoding='utf-8')
        else:
            # 創建默認配置
            self._create_default_config()
            self.save_config()
        
        return self.config
    
    def _create_default_config(self):
        """創建默認配置"""
        # 設置各種默認配置項
        pass
```

**配置管理特點**：
- 獨立的配置管理模組
- 支援默認配置創建
- UTF-8編碼支援

## 自動化管理 (`core/auto_manager.py`)

```python
class AutoConnectManager(QObject):
    """自動連接管理器"""
    
    # 信號定義
    auto_connect_requested = Signal()
    auto_disconnect_requested = Signal()
    auto_unadvise_only_requested = Signal()
    auto_shutdown_requested = Signal()
    status_changed = Signal(str)
    
    def __init__(self, config, logger):
        super().__init__()
        self.config = config
        self.logger = logger
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_schedule)
    
    def start(self):
        """啟動自動化管理"""
        self.timer.start(1000)  # 每秒檢查一次
    
    def check_schedule(self):
        """檢查排程"""
        # 檢查是否需要自動連接、斷開等操作
        pass
```

**自動化特點**：
- 基於信號槽的事件驅動
- 定時排程檢查
- 與主視窗解耦

## 模組間通信

### 信號槽機制
```python
# 在主程式中連接信號
def _connect_signals(self):
    """連接信號"""
    # 連接自動化管理器信號到主視窗
    self.auto_manager.auto_connect_requested.connect(self.main_window.auto_connect)
    self.auto_manager.auto_disconnect_requested.connect(self.main_window.auto_disconnect)
    
    # 連接資料處理器信號
    self.main_window.data_processor.data_processed.connect(self.main_window._process_advise_data)
```

## 關鍵特性

### 1. 模組化架構
- 清晰的職責分離
- 獨立的模組可單獨測試
- 易於維護和擴展

### 2. 異步處理
- 佇列機制處理DDE資料
- 信號槽非阻塞通信
- GUI響應性保證

### 3. 配置管理
- 獨立的配置管理模組
- 支援動態配置載入
- 默認配置自動創建

### 4. 日誌系統
- 分層日誌記錄
- 可配置的日誌級別
- 檔案和控制台雙輸出

### 5. 自動化功能
- 排程自動連接/斷開
- 事件驅動的自動化邏輯
- 與主功能解耦

## 資料流程圖

```
應用啟動 → 模組初始化 → 信號連接 → 自動化啟動
     ↓
DDE資料接收 → 佇列處理 → 信號發送 → 實際處理
     ↓
項目重複檢查 → 補齊資料 → 值變化檢查 → 保存/跳過決策
     ↓
GUI更新 → 檔案輸出 → 日誌記錄
     ↓
定時檢查 → 時間間隔處理 → 自動化排程檢查
```

---

*本文件基於 DDE 模組化版的實際程式碼內容編寫，反映了模組化版的完整資料處理流程和架構設計。*
