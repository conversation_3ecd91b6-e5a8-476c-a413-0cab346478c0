#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多商品DDE监控程序对比测试脚本
同时启动原版多商品版本和引擎混合版本进行对比
"""

import sys
import os
import subprocess
import time
import argparse
from datetime import datetime

def print_header():
    """打印标题"""
    print("=" * 80)
    print("多商品DDE监控程序 - 对比测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def start_original_version():
    """启动原版多商品版本"""
    try:
        print("🚀 启动原版多商品版本...")
        cmd = [sys.executable, "dde_monitor_multi.py", "-c", "multi_config.ini"]
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        print(f"   进程ID: {process.pid}")
        return process
    except Exception as e:
        print(f"❌ 启动原版失败: {str(e)}")
        return None

def start_hybrid_version():
    """启动引擎混合版本"""
    try:
        print("🚀 启动引擎混合版本...")
        cmd = [sys.executable, "run_hybrid_test.py"]
        process = subprocess.Popen(
            cmd,
            cwd="dde_multi_engine_hybrid",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        print(f"   进程ID: {process.pid}")
        return process
    except Exception as e:
        print(f"❌ 启动混合版本失败: {str(e)}")
        return None

def monitor_processes(original_proc, hybrid_proc, duration=300):
    """监控进程运行状态"""
    print(f"\n📊 监控进程运行状态 (持续 {duration} 秒)...")
    print("-" * 60)
    
    start_time = time.time()
    check_interval = 10  # 每10秒检查一次
    
    while time.time() - start_time < duration:
        try:
            # 检查原版进程
            if original_proc:
                original_status = "运行中" if original_proc.poll() is None else "已停止"
            else:
                original_status = "未启动"
            
            # 检查混合版进程
            if hybrid_proc:
                hybrid_status = "运行中" if hybrid_proc.poll() is None else "已停止"
            else:
                hybrid_status = "未启动"
            
            # 显示状态
            elapsed = int(time.time() - start_time)
            print(f"[{elapsed:3d}s] 原版: {original_status:8s} | 混合版: {hybrid_status:8s}")
            
            # 如果两个进程都停止了，退出监控
            if (original_proc and original_proc.poll() is not None and
                hybrid_proc and hybrid_proc.poll() is not None):
                print("两个进程都已停止，结束监控")
                break
            
            time.sleep(check_interval)
            
        except KeyboardInterrupt:
            print("\n用户中断监控")
            break
        except Exception as e:
            print(f"监控异常: {str(e)}")
            break

def cleanup_processes(original_proc, hybrid_proc):
    """清理进程"""
    print("\n🧹 清理进程...")
    
    processes = [
        ("原版多商品版本", original_proc),
        ("引擎混合版本", hybrid_proc)
    ]
    
    for name, proc in processes:
        if proc and proc.poll() is None:
            try:
                print(f"   终止 {name} (PID: {proc.pid})")
                proc.terminate()
                proc.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"   强制终止 {name}")
                proc.kill()
            except Exception as e:
                print(f"   清理 {name} 失败: {str(e)}")

def print_summary():
    """打印测试总结"""
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    print("1. 检查两个版本的GUI界面是否正常显示")
    print("2. 对比DDE连接和订阅状态")
    print("3. 观察数据接收和处理情况")
    print("4. 比较输出文件的内容和格式")
    print("5. 评估性能和稳定性差异")
    print()
    print("📁 输出文件位置:")
    print("   原版: ./data/")
    print("   混合版: ./dde_multi_engine_hybrid/data/")
    print()
    print("📋 日志文件位置:")
    print("   原版: ./logs/multi_product_monitor.log")
    print("   混合版: ./dde_multi_engine_hybrid/logs/multi_product_monitor.log")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='多商品DDE监控程序对比测试',
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '-d', '--duration',
        type=int,
        default=300,
        help='监控持续时间（秒），默认300秒'
    )
    
    parser.add_argument(
        '--original-only',
        action='store_true',
        help='只启动原版多商品版本'
    )
    
    parser.add_argument(
        '--hybrid-only',
        action='store_true',
        help='只启动引擎混合版本'
    )
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_arguments()
    
    print_header()
    
    original_proc = None
    hybrid_proc = None
    
    try:
        # 启动进程
        if not args.hybrid_only:
            original_proc = start_original_version()
            time.sleep(2)  # 等待2秒
        
        if not args.original_only:
            hybrid_proc = start_hybrid_version()
            time.sleep(2)  # 等待2秒
        
        # 监控进程
        if original_proc or hybrid_proc:
            monitor_processes(original_proc, hybrid_proc, args.duration)
        else:
            print("❌ 没有成功启动任何进程")
            return 1
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试异常: {str(e)}")
    finally:
        cleanup_processes(original_proc, hybrid_proc)
    
    print_summary()
    return 0

if __name__ == "__main__":
    sys.exit(main())
