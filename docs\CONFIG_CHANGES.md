# DDE 監控程式設定檔變更說明

## 📅 變更日期
2025-06-17

## 🎯 變更目的
移除自動重連功能，簡化設定檔結構

## 📋 設定檔變更詳情

### [AutoConnect] 區塊變更

#### 移除的設定項目
```ini
# 以下設定項目已從 config.ini 中移除:
auto_reconnect_on_disconnect = true
max_reconnect_attempts = 5
reconnect_interval = 10.0
```

#### 保留的設定項目
```ini
[AutoConnect]
enable_auto_connect = true
auto_connect_mode = delay
auto_connect_delay = 5.0
schedule_connect_times = 09:00:00-12:00:00;13:30:00-15:00:00
enable_cross_day_schedule = true
skip_weekends = false
```

### 完整的更新後設定檔

```ini
[DDE]
service = XQTISC
topic = Quote
disconnect_on_exit = false

[Items]
item1_name = 交易時間
item1_code = FITXN06.TF-Time
item2_name = 交易日期
item2_code = FITXN06.TF-TradingDate
item3_name = 開盤價
item3_code = FITXN06.TF-Open
item4_name = 最高價
item4_code = FITXN06.TF-High
item5_name = 最低價
item5_code = FITXN06.TF-Low
item6_name = 成交價
item6_code = FITXN06.TF-Price
item7_name = 總量
item7_code = FITXN06.TF-TotalVolume
item8_name = 單量
item8_code = FITXN06.TF-Volume

[Table]
enable_time_newline = true
time_newline_interval = 0.800
enable_value_change_check = true
value_change_check_mode = single
value_change_check_items = 總量

[FileOutput]
enable_data_file = false
enable_complete_data_file = true
enable_log_file = true

[OutputPath]
log_file = ./logs/{date}/dde_monitor_01.log
data_file = R:/TEMP/data/mon/XQ/FITXN06/_a/dde_data_01.csv
complete_data_file = R:/TEMP/data/mon/XQ/FITXN06/_a/complete_data_01.csv

[AutoConnect]
enable_auto_connect = true
auto_connect_mode = delay
auto_connect_delay = 5.0
schedule_connect_times = 09:00:00-12:00:00;13:30:00-15:00:00
enable_cross_day_schedule = true
skip_weekends = false

[AutoShutdown]
enable_auto_shutdown = true
shutdown_time = 05:55:00
shutdown_buffer_seconds = 5
shutdown_warning_seconds = 60
force_shutdown = true
save_data_before_shutdown = true
disconnect_before_shutdown = true
cleanup_temp_files = true

[Notifications]
enable_system_notifications = true
enable_sound_notifications = false
notification_levels = INFO,WARNING,ERROR
notify_auto_connect = true
notify_auto_disconnect = true
notify_auto_shutdown = true
```

## 🔍 變更影響

### 1. 功能影響
- **移除自動重連**: 程式不再自動重連斷開的連線
- **簡化操作**: 使用者不需要配置複雜的重連參數
- **專注核心**: 專注於自動連線和自動結束功能

### 2. GUI 影響
- **簡化介面**: 自動化設定對話框中不再顯示重連設定群組
- **狀態顯示**: 自動狀態顯示中不再顯示重連資訊
- **設定載入**: GUI 載入設定時會忽略不存在的重連設定

### 3. 向後相容性
- **舊設定檔**: 如果舊設定檔包含重連設定，程式會忽略這些設定
- **無錯誤**: 不會因為缺少重連設定而產生錯誤
- **平滑升級**: 使用者可以無縫升級到新版本

## ⚠️ 注意事項

### 1. 備份建議
在更新前，建議備份原始設定檔：
```bash
cp config.ini config.ini.backup
```

### 2. 手動更新
如果您有自訂的設定檔，請手動移除以下行：
```ini
auto_reconnect_on_disconnect = true
max_reconnect_attempts = 5
reconnect_interval = 10.0
```

### 3. 連線管理
- 程式不再自動重連，如需重新連線請手動操作
- 建議確保 DDE 服務的穩定性
- 可以使用自動連線功能在程式啟動時建立連線

## 🚀 升級步驟

### 自動升級 (推薦)
1. 啟動更新後的程式
2. 程式會自動忽略不存在的重連設定
3. 使用 GUI 設定對話框調整其他設定

### 手動升級
1. 備份現有設定檔
2. 手動編輯 config.ini，移除重連相關設定
3. 重新啟動程式

### 驗證升級
1. 啟動程式，檢查是否有錯誤訊息
2. 開啟自動化設定對話框，確認不再顯示重連設定
3. 測試自動連線和自動結束功能

## 📊 設定檔大小變化

### 變更前
- 總行數: 70 行
- AutoConnect 區塊: 10 行設定

### 變更後
- 總行數: 67 行 (-3 行)
- AutoConnect 區塊: 7 行設定 (-3 行)

### 減少的內容
- 移除 3 行重連相關設定
- 簡化了 30% 的自動連線設定複雜度

## ✅ 驗證清單

- [x] 移除 `auto_reconnect_on_disconnect` 設定
- [x] 移除 `max_reconnect_attempts` 設定  
- [x] 移除 `reconnect_interval` 設定
- [x] 保留所有其他自動連線設定
- [x] 保留所有自動結束設定
- [x] 保留所有通知設定
- [x] 設定檔格式正確
- [x] 程式可以正常載入設定檔

---
*變更版本*: v6.1  
*變更日期*: 2025-06-17  
*變更類型*: 功能移除  
*影響範圍*: AutoConnect 設定區塊
