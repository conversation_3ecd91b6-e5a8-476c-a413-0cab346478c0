# DDE 監控程式故障排除指南

## 常見問題診斷

### 1. DDE 連接問題

#### 問題: 無法連接到 DDE 服務
**錯誤訊息**: "連接 DDE 服務失敗" 或 "DMLERR_NO_CONV_ESTABLISHED"

**可能原因**:
- DDE 服務程式未啟動
- 服務名稱或主題設定錯誤
- Windows DDE 服務被禁用
- 防火牆或安全軟體阻擋

**診斷步驟**:
1. 檢查 DDE 服務程式是否運行
2. 驗證 config.ini 中的服務名稱和主題
3. 檢查 Windows 事件日誌
4. 測試其他 DDE 客戶端工具

**解決方案**:
```ini
# 確認 config.ini 設定正確
[DDE]
service = XQTISC    # 確認服務名稱
topic = Quote       # 確認主題名稱
```

#### 問題: 連接後立即斷開
**錯誤訊息**: "DMLERR_SERVER_DIED" 或連接狀態頻繁變化

**可能原因**:
- DDE 服務不穩定
- 網路連接問題
- 系統資源不足
- 多個客戶端衝突

**解決方案**:
1. 重啟 DDE 服務程式
2. 檢查系統資源使用情況
3. 確保只有一個監控程式實例運行
4. 調整重連參數

```python
# 在 dde_monitor.py 中調整重連設定
self._auto_reconnect = True
self._max_reconnect_attempts = 5
self._reconnect_interval = 2.0
```

### 2. 資料接收問題

#### 問題: 無法接收資料更新
**症狀**: 項目顯示為"已訂閱"但沒有資料更新

**診斷步驟**:
1. 檢查項目代碼是否正確
2. 測試單個項目的 request 功能
3. 檢查 DDE 服務是否有資料推送
4. 查看日誌檔案中的錯誤訊息

**解決方案**:
```python
# 手動測試項目
def test_single_item(self, item_code):
    try:
        result = self.dde_client.request(item_code)
        self.logger.info(f"測試項目 {item_code}: {result}")
        return result is not None
    except Exception as e:
        self.logger.error(f"測試項目失敗: {str(e)}")
        return False
```

#### 問題: 資料更新延遲
**症狀**: 資料更新不及時，存在明顯延遲

**可能原因**:
- 資料處理佇列堆積
- UI 更新頻率設定不當
- 系統效能不足

**解決方案**:
```python
# 調整處理參數
class DataProcessor(QObject):
    def process_data(self):
        batch = []
        while self.running:
            # 減少批次大小，提高處理頻率
            while len(batch) < 50 and not self.data_queue.empty():
                batch.append(self.data_queue.get())
            
            # 縮短休眠時間
            time.sleep(0.005)  # 從 0.01 改為 0.005
```

### 3. 檔案輸出問題

#### 問題: 無法建立輸出檔案
**錯誤訊息**: "建立目錄失敗" 或 "Permission denied"

**可能原因**:
- 輸出路徑不存在或無權限
- 磁碟空間不足
- 檔案被其他程式佔用

**解決方案**:
1. 檢查輸出路徑權限
2. 確保磁碟空間充足
3. 關閉可能佔用檔案的程式

```python
# 增強檔案權限檢查
def check_file_permissions(self, file_path):
    try:
        directory = os.path.dirname(file_path)
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
        
        # 測試寫入權限
        test_file = os.path.join(directory, 'test_write.tmp')
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        return True
    except Exception as e:
        self.logger.error(f"檔案權限檢查失敗: {str(e)}")
        return False
```

#### 問題: CSV 檔案格式錯誤
**症狀**: 生成的 CSV 檔案無法正確開啟或資料錯亂

**可能原因**:
- 編碼問題
- 資料中包含特殊字符
- CSV 分隔符衝突

**解決方案**:
```python
def save_row_safe(self, row: RawDataRow, is_complete: bool = False):
    """安全的 CSV 寫入方法"""
    try:
        # 清理資料中的特殊字符
        cleaned_data = []
        for value in row_data:
            if isinstance(value, str):
                # 移除或替換問題字符
                cleaned_value = value.replace(',', '，').replace('\n', ' ').replace('\r', '')
                cleaned_data.append(f'"{cleaned_value}"')  # 加上引號保護
            else:
                cleaned_data.append(str(value))
        
        # 寫入檔案
        line = ','.join(cleaned_data) + '\n'
        with open(target_file, 'a', encoding='utf-8-sig') as f:  # 使用 BOM
            f.write(line)
            
    except Exception as e:
        self.logger.error(f"CSV 寫入失敗: {str(e)}")
```

### 4. UI 介面問題

#### 問題: 介面無回應或凍結
**症狀**: 點擊按鈕無反應，視窗無法操作

**可能原因**:
- 主線程被阻塞
- 大量資料更新導致 UI 卡頓
- 記憶體不足

**解決方案**:
```python
# 確保 UI 操作在主線程
def update_ui_safe(self):
    """線程安全的 UI 更新"""
    if threading.current_thread() == threading.main_thread():
        self.update_items_table()
    else:
        # 使用信號槽機制
        self.ui_update_signal.emit()

# 限制 UI 更新頻率
def __init__(self):
    self.last_ui_update = 0
    self.ui_update_interval = 0.1  # 100ms 更新一次
    
def update_ui(self):
    current_time = time.time()
    if current_time - self.last_ui_update >= self.ui_update_interval:
        self.update_items_table()
        self.last_ui_update = current_time
```

#### 問題: 資料顯示不正確
**症狀**: 表格中的資料與實際接收的資料不符

**可能原因**:
- 資料更新順序問題
- 線程同步問題
- UI 更新邏輯錯誤

**解決方案**:
```python
# 加入資料驗證
def update_items_table(self):
    """更新項目表格並驗證資料"""
    try:
        self.items_table.setRowCount(len(self.items_data))
        for row, (code, item_data) in enumerate(self.items_data.items()):
            # 驗證資料完整性
            if not self._validate_item_data(item_data):
                self.logger.warning(f"項目資料異常: {code}")
                continue
                
            # 安全更新 UI
            self._update_table_row(row, item_data)
            
    except Exception as e:
        self.logger.error(f"更新表格失敗: {str(e)}")

def _validate_item_data(self, item_data):
    """驗證項目資料"""
    return (item_data.name and 
            item_data.code and 
            item_data.status in ['未訂閱', '已測試', '已訂閱', '測試失敗', '訂閱失敗'])
```

### 5. 效能問題

#### 問題: 記憶體使用持續增長
**症狀**: 程式運行時間越長，記憶體使用越多

**診斷工具**:
```python
import psutil
import gc

def monitor_memory_usage(self):
    """監控記憶體使用"""
    process = psutil.Process()
    memory_info = process.memory_info()
    
    self.logger.info(f"記憶體使用: {memory_info.rss / 1024 / 1024:.2f} MB")
    self.logger.info(f"物件數量: {len(gc.get_objects())}")
    
    # 檢查大型物件
    large_objects = [obj for obj in gc.get_objects() 
                    if hasattr(obj, '__sizeof__') and obj.__sizeof__() > 1024]
    self.logger.info(f"大型物件數量: {len(large_objects)}")
```

**解決方案**:
```python
# 定期清理資源
def cleanup_old_data(self):
    """清理過期資料"""
    current_time = time.time()
    
    # 清理原始資料佇列
    while (len(self.raw_data) > 100 and 
           self.raw_data[-1].receive_time < current_time - 3600):
        self.raw_data.pop()
    
    # 強制垃圾回收
    gc.collect()
```

#### 問題: CPU 使用率過高
**症狀**: 程式佔用大量 CPU 資源

**可能原因**:
- 無限循環或忙等待
- 資料處理效率低
- UI 更新過於頻繁

**解決方案**:
```python
# 優化資料處理循環
def process_data(self):
    """優化的資料處理循環"""
    while self.running:
        try:
            # 使用阻塞式佇列操作，避免忙等待
            try:
                item, value = self.data_queue.get(timeout=0.1)
                self.data_processed.emit(item, value)
                self.data_queue.task_done()
            except:
                # 佇列為空，短暫休眠
                time.sleep(0.01)
                
        except Exception as e:
            self.logger.error(f"資料處理異常: {str(e)}")
            time.sleep(0.1)  # 異常時休眠更長時間
```

## 日誌分析

### 日誌級別說明
- **DEBUG**: 詳細的調試資訊，正常運行時不顯示
- **INFO**: 一般操作資訊，如連接建立、資料接收
- **WARNING**: 警告資訊，不影響正常運行但需要注意
- **ERROR**: 錯誤資訊，影響功能但程式可繼續運行
- **CRITICAL**: 嚴重錯誤，可能導致程式崩潰

### 重要日誌模式
```bash
# 連接相關
grep "連接\|connect\|disconnect" logs/*/dde_monitor.log

# 錯誤相關
grep "ERROR\|CRITICAL" logs/*/dde_monitor.log

# 效能相關
grep "處理時間\|記憶體\|CPU" logs/*/dde_monitor.log
```

### 日誌分析腳本
```python
def analyze_log_file(log_file_path):
    """分析日誌檔案"""
    stats = {
        'total_lines': 0,
        'error_count': 0,
        'warning_count': 0,
        'connection_events': 0,
        'data_events': 0
    }
    
    with open(log_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            stats['total_lines'] += 1
            
            if 'ERROR' in line:
                stats['error_count'] += 1
            elif 'WARNING' in line:
                stats['warning_count'] += 1
            elif '連接' in line or 'connect' in line.lower():
                stats['connection_events'] += 1
            elif '資料' in line or 'data' in line.lower():
                stats['data_events'] += 1
    
    return stats
```

## 緊急處理程序

### 程式崩潰處理
1. **保存現場**: 複製日誌檔案和設定檔
2. **重啟程式**: 使用 Py_RunMe.bat 重新啟動
3. **檢查狀態**: 確認 DDE 連接和資料接收正常
4. **分析原因**: 查看日誌檔案找出崩潰原因

### 資料遺失處理
1. **停止程式**: 立即停止可能造成更多資料遺失的操作
2. **檢查檔案**: 確認現有資料檔案的完整性
3. **恢復資料**: 從備份或日誌中恢復遺失的資料
4. **修復問題**: 解決導致資料遺失的根本問題

### 效能問題處理
1. **監控資源**: 使用工作管理員監控 CPU 和記憶體使用
2. **調整參數**: 臨時調整處理參數降低負載
3. **重啟服務**: 重啟 DDE 服務和監控程式
4. **優化設定**: 根據系統能力調整設定參數

## 預防措施

### 定期維護
- 每週檢查日誌檔案
- 每月清理過期資料檔案
- 定期更新系統和依賴套件
- 備份重要設定和資料

### 監控設置
- 設置磁碟空間警告
- 監控程式運行狀態
- 定期檢查 DDE 服務狀態
- 記錄效能指標變化

### 備份策略
- 每日備份設定檔案
- 每週備份重要資料
- 保留多個版本的備份
- 測試備份恢復程序

---
*文件版本*: v1.0  
*建立日期*: 2025-06-16  
*最後更新*: 2025-06-16
