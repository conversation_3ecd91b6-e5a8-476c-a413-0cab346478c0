#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器
运行所有单元测试并生成报告
"""

import unittest
import sys
import os
import time
from io import StringIO

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# 导入测试模块
from tests.test_config_manager import TestWrapperConfigManager, TestProductConfig, TestEngineConfig
from tests.test_parallel_processor import TestProcessingTask, TestParallelProcessor, TestProcessorStats
from tests.test_monitoring_system import (
    TestAlert, TestSystemMetrics, TestEngineMetrics, 
    TestMonitoringConfig, TestMonitoringSystem
)


class TestResult:
    """测试结果统计"""
    
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.error_tests = 0
        self.skipped_tests = 0
        self.test_details = []
        self.start_time = 0
        self.end_time = 0
    
    @property
    def duration(self):
        return self.end_time - self.start_time
    
    @property
    def success_rate(self):
        if self.total_tests == 0:
            return 0.0
        return (self.passed_tests / self.total_tests) * 100


def run_test_suite(test_suite, suite_name):
    """运行测试套件"""
    print(f"\n{'='*60}")
    print(f"运行测试套件: {suite_name}")
    print(f"{'='*60}")
    
    # 创建测试运行器
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        buffer=True
    )
    
    # 运行测试
    start_time = time.time()
    result = runner.run(test_suite)
    end_time = time.time()
    
    # 统计结果
    test_result = TestResult()
    test_result.start_time = start_time
    test_result.end_time = end_time
    test_result.total_tests = result.testsRun
    test_result.passed_tests = result.testsRun - len(result.failures) - len(result.errors) - len(result.skipped)
    test_result.failed_tests = len(result.failures)
    test_result.error_tests = len(result.errors)
    test_result.skipped_tests = len(result.skipped)
    
    # 显示结果
    print(f"测试数量: {test_result.total_tests}")
    print(f"通过: {test_result.passed_tests}")
    print(f"失败: {test_result.failed_tests}")
    print(f"错误: {test_result.error_tests}")
    print(f"跳过: {test_result.skipped_tests}")
    print(f"成功率: {test_result.success_rate:.1f}%")
    print(f"耗时: {test_result.duration:.2f}秒")
    
    # 显示失败和错误详情
    if result.failures:
        print(f"\n失败的测试 ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"  ✗ {test}")
            print(f"    {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n错误的测试 ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"  ✗ {test}")
            print(f"    {traceback.split('Exception:')[-1].strip()}")
    
    return test_result


def create_test_suites():
    """创建测试套件"""
    suites = {}
    
    # 配置管理器测试套件
    config_suite = unittest.TestSuite()
    config_suite.addTest(unittest.makeSuite(TestWrapperConfigManager))
    config_suite.addTest(unittest.makeSuite(TestProductConfig))
    config_suite.addTest(unittest.makeSuite(TestEngineConfig))
    suites['配置管理器'] = config_suite
    
    # 并行处理器测试套件
    processor_suite = unittest.TestSuite()
    processor_suite.addTest(unittest.makeSuite(TestProcessingTask))
    processor_suite.addTest(unittest.makeSuite(TestParallelProcessor))
    processor_suite.addTest(unittest.makeSuite(TestProcessorStats))
    suites['并行处理器'] = processor_suite
    
    # 监控系统测试套件
    monitoring_suite = unittest.TestSuite()
    monitoring_suite.addTest(unittest.makeSuite(TestAlert))
    monitoring_suite.addTest(unittest.makeSuite(TestSystemMetrics))
    monitoring_suite.addTest(unittest.makeSuite(TestEngineMetrics))
    monitoring_suite.addTest(unittest.makeSuite(TestMonitoringConfig))
    monitoring_suite.addTest(unittest.makeSuite(TestMonitoringSystem))
    suites['监控系统'] = monitoring_suite
    
    return suites


def generate_summary_report(results):
    """生成总结报告"""
    print(f"\n{'='*60}")
    print("测试总结报告")
    print(f"{'='*60}")
    
    total_tests = sum(r.total_tests for r in results.values())
    total_passed = sum(r.passed_tests for r in results.values())
    total_failed = sum(r.failed_tests for r in results.values())
    total_errors = sum(r.error_tests for r in results.values())
    total_skipped = sum(r.skipped_tests for r in results.values())
    total_duration = sum(r.duration for r in results.values())
    
    overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0
    
    print(f"总测试数量: {total_tests}")
    print(f"总通过数量: {total_passed}")
    print(f"总失败数量: {total_failed}")
    print(f"总错误数量: {total_errors}")
    print(f"总跳过数量: {total_skipped}")
    print(f"总体成功率: {overall_success_rate:.1f}%")
    print(f"总耗时: {total_duration:.2f}秒")
    
    print(f"\n各模块测试结果:")
    print(f"{'模块名称':<15} {'测试数':<8} {'通过':<8} {'失败':<8} {'错误':<8} {'成功率':<10}")
    print("-" * 70)
    
    for suite_name, result in results.items():
        print(f"{suite_name:<15} {result.total_tests:<8} {result.passed_tests:<8} "
              f"{result.failed_tests:<8} {result.error_tests:<8} {result.success_rate:<10.1f}%")
    
    # 评估结果
    print(f"\n测试评估:")
    if overall_success_rate >= 95:
        print("🎉 优秀！所有测试基本通过，代码质量很高。")
    elif overall_success_rate >= 85:
        print("✅ 良好！大部分测试通过，有少量问题需要修复。")
    elif overall_success_rate >= 70:
        print("⚠️  一般！有一些测试失败，需要重点关注。")
    else:
        print("❌ 需要改进！测试失败较多，需要大量修复工作。")
    
    return overall_success_rate >= 85


def main():
    """主函数"""
    print("DDE引擎包装器单元测试")
    print("=" * 60)
    
    # 设置测试环境
    os.environ['TESTING'] = '1'
    
    # 创建测试套件
    test_suites = create_test_suites()
    
    # 运行所有测试套件
    results = {}
    overall_start_time = time.time()
    
    for suite_name, test_suite in test_suites.items():
        try:
            result = run_test_suite(test_suite, suite_name)
            results[suite_name] = result
        except Exception as e:
            print(f"运行测试套件 {suite_name} 时发生错误: {str(e)}")
            # 创建一个失败的结果
            failed_result = TestResult()
            failed_result.total_tests = 1
            failed_result.error_tests = 1
            results[suite_name] = failed_result
    
    overall_end_time = time.time()
    
    # 生成总结报告
    success = generate_summary_report(results)
    
    print(f"\n总运行时间: {overall_end_time - overall_start_time:.2f}秒")
    print(f"测试完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 返回退出码
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
