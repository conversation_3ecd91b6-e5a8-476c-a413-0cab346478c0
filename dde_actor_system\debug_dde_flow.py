#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
調試 DDE 數據流

診斷為什麼 DDE 數據沒有流入文件
"""

import asyncio
import time
import sys
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from dydde.dydde import DDEClient
from core.message_system import MessageRouter, create_dde_data_message
from actors.data_processor import DataProcessorActor
from actors.file_writer import FileWriterActor


class DDEFlowDebugger:
    """DDE 數據流調試器"""
    
    def __init__(self):
        self.router = None
        self.data_processor = None
        self.file_writer = None
        self.dde_client = None
        self.data_count = 0
        
    async def setup_components(self):
        """設置組件"""
        print("🔧 設置組件...")
        
        # 創建消息路由
        self.router = MessageRouter()
        
        # 創建數據處理器
        processor_config = {
            'batch_size': 3,
            'queue_size': 100
        }
        self.data_processor = DataProcessorActor('DataProcessor', processor_config)
        self.router.register_actor(self.data_processor)
        
        # 創建文件寫入器
        file_config = {
            'batch_size': 3,
            'flush_interval': 2.0,  # 2秒刷新
            'files': {
                'default': {
                    'filename': 'outputs/debug_dde_data.csv',
                    'format': 'csv',
                    'max_size_mb': 5,
                    'compress': False
                }
            }
        }
        self.file_writer = FileWriterActor('FileWriter', file_config)
        self.router.register_actor(self.file_writer)
        
        # 啟動組件
        await self.data_processor.start()
        await self.file_writer.start()
        
        print("✅ 組件設置完成")
    
    async def test_dde_connection(self):
        """測試 DDE 連接"""
        print("\n🔗 測試 DDE 連接...")
        
        try:
            self.dde_client = DDEClient("XQTISC", "Quote")
            self.dde_client.connect()
            print("✅ DDE 連接成功")
            
            # 測試項目
            test_items = [
                "FITXN07.TF-Price",
                "FITXN07.TF-Volume",
                "FITXN08.TF-Price",
                "FITXN08.TF-Volume"
            ]
            
            print("📊 測試數據請求...")
            for item in test_items:
                try:
                    value = self.dde_client.request(item)
                    if value and value.strip():
                        print(f"   ✅ {item}: {value}")
                        
                        # 發送到處理器
                        message = create_dde_data_message(item, value, "DebugTest")
                        success = await self.router.send_message("DataProcessor", message)
                        if success:
                            self.data_count += 1
                            print(f"      📤 已發送到處理器")
                        else:
                            print(f"      ❌ 發送失敗")
                    else:
                        print(f"   ⚪ {item}: 無數據")
                except Exception as e:
                    print(f"   ❌ {item}: {str(e)}")
            
            return True
            
        except Exception as e:
            print(f"❌ DDE 連接失敗: {str(e)}")
            return False
    
    async def test_hot_links(self):
        """測試熱鏈接"""
        print("\n🔥 測試熱鏈接...")
        
        if not self.dde_client:
            print("❌ 沒有 DDE 連接")
            return False
        
        try:
            # 創建回調函數
            def data_callback(item, value):
                print(f"🔥 熱鏈接回調: {item} = {value}")
                
                # 創建異步任務發送數據
                async def send_data():
                    message = create_dde_data_message(item, value, "HotLink")
                    success = await self.router.send_message("DataProcessor", message)
                    if success:
                        self.data_count += 1
                        print(f"   📤 熱鏈接數據已發送")
                    else:
                        print(f"   ❌ 熱鏈接數據發送失敗")
                
                # 在事件循環中執行
                asyncio.create_task(send_data())
            
            # 建立熱鏈接
            test_item = "FITXN07.TF-Price"
            print(f"建立熱鏈接: {test_item}")
            
            self.dde_client.advise(test_item, data_callback)
            print("✅ 熱鏈接建立成功")
            
            # 等待數據
            print("⏰ 等待熱鏈接數據 (10秒)...")
            for i in range(10):
                print(f"   等待中... {i+1}/10", end='\r')
                await asyncio.sleep(1)
            print()
            
            # 取消熱鏈接
            self.dde_client.unadvise(test_item)
            print("✅ 熱鏈接已取消")
            
            return True
            
        except Exception as e:
            print(f"❌ 熱鏈接測試失敗: {str(e)}")
            return False
    
    async def test_polling_mode(self):
        """測試輪詢模式"""
        print("\n🔄 測試輪詢模式...")
        
        if not self.dde_client:
            print("❌ 沒有 DDE 連接")
            return False
        
        try:
            test_items = [
                "FITXN07.TF-Price",
                "FITXN07.TF-Volume",
                "FITXN08.TF-Price",
                "FITXN08.TF-Volume"
            ]
            
            print("🔄 開始輪詢...")
            for round_num in range(3):
                print(f"\n   第 {round_num + 1} 輪輪詢:")
                
                for item in test_items:
                    try:
                        value = self.dde_client.request(item)
                        if value and value.strip():
                            print(f"     📊 {item}: {value}")
                            
                            # 發送到處理器
                            message = create_dde_data_message(item, value, f"Polling-R{round_num+1}")
                            success = await self.router.send_message("DataProcessor", message)
                            if success:
                                self.data_count += 1
                            
                        await asyncio.sleep(0.2)
                    except Exception as e:
                        print(f"     ❌ {item}: {str(e)}")
                
                await asyncio.sleep(2)  # 輪詢間隔
            
            print("✅ 輪詢測試完成")
            return True
            
        except Exception as e:
            print(f"❌ 輪詢測試失敗: {str(e)}")
            return False
    
    async def check_results(self):
        """檢查結果"""
        print(f"\n📊 檢查結果...")
        
        # 等待處理完成
        print("⏰ 等待數據處理完成...")
        await asyncio.sleep(5)
        
        # 檢查統計
        try:
            processor_stats = self.data_processor.get_processing_stats()
            print(f"📈 數據處理器統計:")
            if 'processing_stats' in processor_stats:
                ps = processor_stats['processing_stats']
                print(f"   處理項目: {ps.get('items_processed', 0)}")
                print(f"   處理批次: {ps.get('batches_processed', 0)}")
        except Exception as e:
            print(f"⚠️  處理器統計獲取失敗: {str(e)}")
        
        try:
            file_stats = self.file_writer.get_file_stats()
            print(f"📁 文件寫入器統計:")
            if 'write_stats' in file_stats:
                ws = file_stats['write_stats']
                print(f"   寫入項目: {ws.get('items_written', 0)}")
                print(f"   寫入批次: {ws.get('batches_written', 0)}")
        except Exception as e:
            print(f"⚠️  文件寫入器統計獲取失敗: {str(e)}")
        
        # 檢查文件
        debug_file = Path("outputs/debug_dde_data.csv")
        if debug_file.exists():
            size = debug_file.stat().st_size
            print(f"📄 調試文件: {size} bytes")
            
            if size > 0:
                with open(debug_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print(f"   行數: {len(lines)}")
                    if lines:
                        print(f"   最後一行: {lines[-1].strip()}")
        else:
            print(f"❌ 調試文件不存在")
        
        print(f"\n📊 總發送數據: {self.data_count} 筆")
    
    async def cleanup(self):
        """清理資源"""
        print(f"\n🧹 清理資源...")
        
        if self.dde_client:
            self.dde_client.disconnect()
        
        if self.data_processor:
            await self.data_processor.stop()
        
        if self.file_writer:
            await self.file_writer.stop()
        
        print("✅ 清理完成")


async def main():
    """主函數"""
    print("🔍 DDE 數據流調試器")
    print("=" * 60)
    
    debugger = DDEFlowDebugger()
    
    try:
        # 設置組件
        await debugger.setup_components()
        
        # 測試 DDE 連接
        dde_ok = await debugger.test_dde_connection()
        
        if dde_ok:
            # 測試熱鏈接
            await debugger.test_hot_links()
            
            # 測試輪詢模式
            await debugger.test_polling_mode()
        
        # 檢查結果
        await debugger.check_results()
        
    except Exception as e:
        print(f"❌ 調試失敗: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        await debugger.cleanup()
    
    print(f"\n🎉 調試完成!")


if __name__ == "__main__":
    asyncio.run(main())
