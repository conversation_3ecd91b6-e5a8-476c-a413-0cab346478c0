#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
觸發 DDE 訂閱

手動觸發 DDE 數據訂閱，測試數據流
"""

import sys
import time
import json
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from dydde.dydde import DDEClient


def load_config():
    """加載配置文件"""
    config_file = Path("config/system_config.json")
    if not config_file.exists():
        print("❌ 配置文件不存在")
        return None
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ 加載配置文件失敗: {str(e)}")
        return None


def trigger_subscriptions():
    """觸發訂閱"""
    print("🚀 DDE 訂閱觸發器")
    print("=" * 50)
    
    # 加載配置
    config = load_config()
    if not config:
        return False
    
    # 獲取產品配置
    products = config.get('products', [])
    enabled_products = [p for p in products if p.get('enabled', False)]
    
    if not enabled_products:
        print("❌ 沒有啟用的產品")
        return False
    
    print(f"找到 {len(enabled_products)} 個啟用的產品")
    
    # 為每個產品創建 DDE 連接並訂閱
    for product in enabled_products:
        symbol = product.get('symbol')
        service = product.get('service')
        topic = product.get('topic')
        items = product.get('items', [])
        
        print(f"\n📡 處理產品: {symbol}")
        print(f"   服務: {service}.{topic}")
        print(f"   項目數: {len(items)}")
        
        try:
            # 創建 DDE 客戶端
            client = DDEClient(service, topic)

            # 連接
            print(f"   連接中...")
            success = client.connect()
            
            if not success:
                print(f"   ❌ 連接失敗")
                continue
            
            print(f"   ✅ 連接成功")
            
            # 訂閱項目
            successful_subscriptions = 0
            for item in items:
                try:
                    print(f"   📊 訂閱: {item}")
                    
                    # 嘗試請求數據
                    result = client.request(item)
                    if result:
                        print(f"      結果: {result}")
                        successful_subscriptions += 1
                    else:
                        print(f"      無數據")
                    
                    # 嘗試建立熱鏈接 (如果支持)
                    try:
                        client.advise(item)
                        print(f"      ✅ 熱鏈接建立")
                    except:
                        print(f"      ⚠️  熱鏈接失敗")
                    
                    time.sleep(0.1)  # 避免過快請求
                    
                except Exception as e:
                    print(f"      ❌ 訂閱失敗: {str(e)}")
            
            print(f"   📈 成功訂閱: {successful_subscriptions}/{len(items)}")
            
            # 保持連接一段時間讓數據流動
            if successful_subscriptions > 0:
                print(f"   ⏰ 保持連接 30 秒...")
                for i in range(30):
                    print(f"      等待數據... {i+1}/30", end='\r')
                    time.sleep(1)
                print()  # 換行
            
            # 斷開連接
            client.disconnect()
            print(f"   🔌 連接已斷開")
            
        except Exception as e:
            print(f"   ❌ 處理失敗: {str(e)}")
    
    return True


def check_results():
    """檢查結果"""
    print(f"\n📁 檢查結果文件...")
    
    output_dir = Path("outputs")
    if not output_dir.exists():
        print("❌ outputs 目錄不存在")
        return
    
    files_found = False
    for file_path in output_dir.glob("dde_data.*"):
        if file_path.exists() and file_path.stat().st_size > 0:
            files_found = True
            size = file_path.stat().st_size
            print(f"✅ {file_path.name}: {size} bytes")
            
            if file_path.suffix == '.csv':
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        print(f"   行數: {len(lines)}")
                        if lines:
                            print(f"   最後一行: {lines[-1].strip()}")
                except Exception as e:
                    print(f"   讀取錯誤: {str(e)}")
    
    if not files_found:
        print("⚠️  沒有找到 DDE 數據文件")
        print("   可能原因:")
        print("   - DDE Actor System 沒有運行")
        print("   - 數據沒有成功傳遞到文件寫入器")
        print("   - 市場沒有數據更新")


def main():
    """主函數"""
    print("🎯 DDE 數據訂閱觸發器")
    print("此工具將手動觸發 DDE 數據訂閱")
    print("建議在運行 DDE Actor System 的同時使用")
    print("=" * 60)
    
    try:
        # 觸發訂閱
        success = trigger_subscriptions()
        
        if success:
            # 檢查結果
            check_results()
            
            print(f"\n🎉 訂閱觸發完成!")
            print("建議:")
            print("1. 檢查 outputs/ 目錄中的文件")
            print("2. 運行 'python check_status.py' 查看系統狀態")
            print("3. 如果沒有數據，可能需要在交易時間內測試")
        else:
            print(f"\n❌ 訂閱觸發失敗")
    
    except KeyboardInterrupt:
        print(f"\n⏹️  用戶中斷")
    except Exception as e:
        print(f"\n❌ 執行失敗: {str(e)}")


if __name__ == "__main__":
    main()
