#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消息系統實現

提供Actor間通信的消息機制，包括：
- 消息定義和類型
- 消息路由和分發
- 消息序列化和反序列化
- 消息優先級和過濾
"""

import asyncio
import json
import time
import uuid
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Any, Dict, Optional, List, Callable, Union
import logging


class MessageType(Enum):
    """消息類型枚舉"""
    # 系統消息
    SYSTEM_START = "system.start"
    SYSTEM_STOP = "system.stop"
    SYSTEM_HEALTH_CHECK = "system.health_check"
    SYSTEM_CONFIG_UPDATE = "system.config_update"
    
    # DDE相關消息
    DDE_DATA = "dde.data"
    DDE_CONNECT = "dde.connect"
    DDE_DISCONNECT = "dde.disconnect"
    DDE_SUBSCRIBE = "dde.subscribe"
    DDE_UNSUBSCRIBE = "dde.unsubscribe"
    
    # 數據處理消息
    DATA_PROCESS = "data.process"
    DATA_VALIDATE = "data.validate"
    DATA_TRANSFORM = "data.transform"
    DATA_AGGREGATE = "data.aggregate"
    
    # GUI更新消息
    GUI_UPDATE = "gui.update"
    GUI_BATCH_UPDATE = "gui.batch_update"
    GUI_CLEAR = "gui.clear"
    GUI_REFRESH = "gui.refresh"
    
    # 文件操作消息
    FILE_WRITE = "file.write"
    FILE_READ = "file.read"
    FILE_ROTATE = "file.rotate"
    
    # 性能監控消息
    PERF_METRICS = "perf.metrics"
    PERF_ALERT = "perf.alert"
    
    # 自定義消息
    CUSTOM = "custom"


class MessagePriority(Enum):
    """消息優先級"""
    CRITICAL = 0
    HIGH = 1
    NORMAL = 2
    LOW = 3


@dataclass
class Message:
    """消息類"""
    type: MessageType
    data: Any
    sender: str = ""
    target: str = ""
    message_id: str = ""
    timestamp: float = 0.0
    priority: MessagePriority = MessagePriority.NORMAL
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None
    ttl: Optional[float] = None  # 生存時間（秒）
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化後處理"""
        if not self.message_id:
            self.message_id = str(uuid.uuid4())
        if self.timestamp == 0.0:
            self.timestamp = time.time()
        if self.metadata is None:
            self.metadata = {}
    
    def is_expired(self) -> bool:
        """檢查消息是否過期"""
        if self.ttl is None:
            return False
        return time.time() - self.timestamp > self.ttl
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        result = asdict(self)
        result['type'] = self.type.value
        result['priority'] = self.priority.value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Message':
        """從字典創建消息"""
        data['type'] = MessageType(data['type'])
        data['priority'] = MessagePriority(data['priority'])
        return cls(**data)
    
    def to_json(self) -> str:
        """轉換為JSON字符串"""
        return json.dumps(self.to_dict(), default=str)
    
    @classmethod
    def from_json(cls, json_str: str) -> 'Message':
        """從JSON字符串創建消息"""
        data = json.loads(json_str)
        return cls.from_dict(data)


class MessageFilter:
    """消息過濾器"""
    
    def __init__(self):
        self.filters: List[Callable[[Message], bool]] = []
    
    def add_filter(self, filter_func: Callable[[Message], bool]):
        """添加過濾函數"""
        self.filters.append(filter_func)
    
    def should_process(self, message: Message) -> bool:
        """檢查消息是否應該被處理"""
        for filter_func in self.filters:
            if not filter_func(message):
                return False
        return True


class MessageRouter:
    """消息路由器
    
    負責在Actor之間路由和分發消息
    """
    
    def __init__(self):
        self.actors: Dict[str, 'ActorBase'] = {}
        self.routes: Dict[MessageType, List[str]] = {}
        self.filters: Dict[str, MessageFilter] = {}
        self.logger = logging.getLogger("MessageRouter")
        
        # 統計信息
        self.stats = {
            'messages_routed': 0,
            'messages_failed': 0,
            'messages_filtered': 0,
            'messages_expired': 0
        }
    
    def register_actor(self, actor: 'ActorBase'):
        """註冊Actor
        
        Args:
            actor: 要註冊的Actor實例
        """
        self.actors[actor.name] = actor
        actor.set_message_router(self)
        self.logger.info(f"註冊Actor: {actor.name}")
    
    def unregister_actor(self, actor_name: str):
        """取消註冊Actor
        
        Args:
            actor_name: Actor名稱
        """
        if actor_name in self.actors:
            del self.actors[actor_name]
            self.logger.info(f"取消註冊Actor: {actor_name}")
    
    def add_route(self, message_type: MessageType, target_actor: str):
        """添加消息路由
        
        Args:
            message_type: 消息類型
            target_actor: 目標Actor名稱
        """
        if message_type not in self.routes:
            self.routes[message_type] = []
        
        if target_actor not in self.routes[message_type]:
            self.routes[message_type].append(target_actor)
            self.logger.debug(f"添加路由: {message_type.value} -> {target_actor}")
    
    def remove_route(self, message_type: MessageType, target_actor: str):
        """移除消息路由
        
        Args:
            message_type: 消息類型
            target_actor: 目標Actor名稱
        """
        if message_type in self.routes and target_actor in self.routes[message_type]:
            self.routes[message_type].remove(target_actor)
            self.logger.debug(f"移除路由: {message_type.value} -> {target_actor}")
    
    def set_filter(self, actor_name: str, message_filter: MessageFilter):
        """設置Actor的消息過濾器
        
        Args:
            actor_name: Actor名稱
            message_filter: 消息過濾器
        """
        self.filters[actor_name] = message_filter
    
    async def send_message(self, target: str, message: Message) -> bool:
        """發送消息到指定Actor
        
        Args:
            target: 目標Actor名稱
            message: 要發送的消息
            
        Returns:
            bool: 發送是否成功
        """
        try:
            # 檢查消息是否過期
            if message.is_expired():
                self.stats['messages_expired'] += 1
                self.logger.warning(f"消息已過期: {message.message_id}")
                return False
            
            # 檢查目標Actor是否存在
            if target not in self.actors:
                self.logger.error(f"目標Actor不存在: {target}")
                self.stats['messages_failed'] += 1
                return False
            
            # 應用消息過濾器
            if target in self.filters:
                if not self.filters[target].should_process(message):
                    self.stats['messages_filtered'] += 1
                    self.logger.debug(f"消息被過濾: {message.message_id}")
                    return False
            
            # 發送消息
            message.target = target
            actor = self.actors[target]
            success = await actor.receive_message(message)
            
            if success:
                self.stats['messages_routed'] += 1
            else:
                self.stats['messages_failed'] += 1
                
            return success
            
        except Exception as e:
            self.logger.error(f"發送消息失敗: {str(e)}")
            self.stats['messages_failed'] += 1
            return False
    
    async def broadcast_message(self, message: Message, 
                              targets: Optional[List[str]] = None) -> int:
        """廣播消息到多個Actor
        
        Args:
            message: 要廣播的消息
            targets: 目標Actor列表，None表示廣播到所有Actor
            
        Returns:
            int: 成功發送的數量
        """
        if targets is None:
            targets = list(self.actors.keys())
        
        success_count = 0
        tasks = []
        
        for target in targets:
            if target != message.sender:  # 不發送給自己
                task = self.send_message(target, message)
                tasks.append(task)
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for result in results if result is True)
        
        return success_count
    
    async def route_by_type(self, message: Message) -> int:
        """根據消息類型路由消息
        
        Args:
            message: 要路由的消息
            
        Returns:
            int: 成功路由的數量
        """
        if message.type not in self.routes:
            self.logger.warning(f"沒有找到消息類型的路由: {message.type.value}")
            return 0
        
        targets = self.routes[message.type]
        return await self.broadcast_message(message, targets)
    
    async def send_reply(self, original_message: Message, reply_data: Any) -> bool:
        """發送回復消息
        
        Args:
            original_message: 原始消息
            reply_data: 回復數據
            
        Returns:
            bool: 發送是否成功
        """
        if not original_message.reply_to:
            self.logger.warning("原始消息沒有指定回復地址")
            return False
        
        reply_message = Message(
            type=MessageType.CUSTOM,
            data=reply_data,
            target=original_message.reply_to,
            correlation_id=original_message.message_id
        )
        
        return await self.send_message(original_message.reply_to, reply_message)
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取路由器統計信息
        
        Returns:
            Dict: 統計信息
        """
        return {
            'registered_actors': len(self.actors),
            'active_routes': sum(len(targets) for targets in self.routes.values()),
            'message_stats': self.stats.copy()
        }
    
    def get_actor_info(self, actor_name: str) -> Optional[Dict[str, Any]]:
        """獲取Actor信息
        
        Args:
            actor_name: Actor名稱
            
        Returns:
            Dict: Actor信息，如果不存在則返回None
        """
        if actor_name in self.actors:
            return self.actors[actor_name].get_info()
        return None
    
    def list_actors(self) -> List[str]:
        """列出所有註冊的Actor
        
        Returns:
            List[str]: Actor名稱列表
        """
        return list(self.actors.keys())
    
    def list_routes(self) -> Dict[str, List[str]]:
        """列出所有路由
        
        Returns:
            Dict: 路由映射
        """
        return {msg_type.value: targets for msg_type, targets in self.routes.items()}


# 便利函數
def create_message(msg_type: MessageType, data: Any, 
                  sender: str = "", target: str = "",
                  priority: MessagePriority = MessagePriority.NORMAL,
                  **kwargs) -> Message:
    """創建消息的便利函數
    
    Args:
        msg_type: 消息類型
        data: 消息數據
        sender: 發送者
        target: 目標
        priority: 優先級
        **kwargs: 其他參數
        
    Returns:
        Message: 創建的消息
    """
    return Message(
        type=msg_type,
        data=data,
        sender=sender,
        target=target,
        priority=priority,
        **kwargs
    )


def create_dde_data_message(item: str, value: str, sender: str = "") -> Message:
    """創建DDE數據消息的便利函數
    
    Args:
        item: DDE項目
        value: DDE值
        sender: 發送者
        
    Returns:
        Message: DDE數據消息
    """
    return create_message(
        MessageType.DDE_DATA,
        {'item': item, 'value': value, 'timestamp': time.time()},
        sender=sender,
        priority=MessagePriority.HIGH
    )


def create_gui_update_message(item: str, value: str, sender: str = "") -> Message:
    """創建GUI更新消息的便利函數

    Args:
        item: 數據項目名稱
        value: 數據值
        sender: 發送者

    Returns:
        Message: GUI更新消息
    """
    return create_message(
        MessageType.GUI_UPDATE,
        {'item': item, 'value': value, 'timestamp': time.time()},
        sender=sender,
        priority=MessagePriority.NORMAL
    )
