#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行处理器
负责DDE数据的并行处理和任务调度
"""

import logging
import threading
import time
import queue
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
import asyncio
from collections import defaultdict

class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class ProcessingTask:
    """处理任务数据类"""
    task_id: str
    symbol: str
    data_type: str
    item: str
    value: str
    priority: TaskPriority = TaskPriority.NORMAL
    created_time: float = 0.0
    callback: Optional[Callable] = None

    def __post_init__(self):
        if self.created_time == 0.0:
            self.created_time = time.time()

    def __lt__(self, other):
        """支持任务比较（用于优先级队列）"""
        if not isinstance(other, ProcessingTask):
            return NotImplemented
        # 优先级高的任务排在前面，时间早的任务排在前面
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.created_time < other.created_time

@dataclass
class ProcessorStats:
    """处理器统计信息"""
    total_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    pending_tasks: int = 0
    average_processing_time: float = 0.0
    throughput_per_second: float = 0.0
    active_workers: int = 0

class ParallelProcessor:
    """并行处理器
    
    负责DDE数据的并行处理，提供：
    - 多线程任务处理
    - 优先级队列管理
    - 负载均衡
    - 性能监控
    """
    
    def __init__(self, max_workers: int = 10, queue_size: int = 10000):
        self.logger = logging.getLogger(__name__)
        
        # 线程池配置
        self.max_workers = max_workers
        self.queue_size = queue_size
        
        # 任务队列 - 按优先级分组
        self.task_queues: Dict[TaskPriority, queue.PriorityQueue] = {
            TaskPriority.CRITICAL: queue.PriorityQueue(maxsize=queue_size//4),
            TaskPriority.HIGH: queue.PriorityQueue(maxsize=queue_size//4),
            TaskPriority.NORMAL: queue.PriorityQueue(maxsize=queue_size//2),
            TaskPriority.LOW: queue.PriorityQueue(maxsize=queue_size//4)
        }
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(
            max_workers=max_workers,
            thread_name_prefix="DDEProcessor"
        )
        
        # 工作线程管理
        self.worker_threads: List[threading.Thread] = []
        self.running = False
        self.shutdown_event = threading.Event()
        
        # 统计信息
        self.stats = ProcessorStats()
        self.stats_lock = threading.Lock()
        
        # 性能监控
        self.processing_times: List[float] = []
        self.last_stats_update = time.time()
        
        # 负载均衡
        self.worker_loads: Dict[int, int] = defaultdict(int)
        self.load_balancer_lock = threading.Lock()
        
        self.logger.info(f"并行处理器初始化完成: {max_workers} 工作线程")
    
    def start(self):
        """启动并行处理器"""
        try:
            if self.running:
                self.logger.warning("并行处理器已经在运行")
                return
            
            self.running = True
            self.shutdown_event.clear()
            
            # 启动工作线程
            for i in range(self.max_workers):
                worker_thread = threading.Thread(
                    target=self._worker_loop,
                    name=f"DDEWorker-{i}",
                    args=(i,),
                    daemon=True
                )
                worker_thread.start()
                self.worker_threads.append(worker_thread)
            
            # 启动统计监控线程
            stats_thread = threading.Thread(
                target=self._stats_monitor_loop,
                name="StatsMonitor",
                daemon=True
            )
            stats_thread.start()
            
            self.logger.info(f"并行处理器启动成功: {len(self.worker_threads)} 个工作线程")
            
        except Exception as e:
            self.logger.error(f"启动并行处理器失败: {str(e)}")
            self.running = False
    
    def stop(self, timeout: float = 10.0):
        """停止并行处理器"""
        try:
            if not self.running:
                self.logger.warning("并行处理器未在运行")
                return
            
            self.logger.info("开始停止并行处理器")
            
            # 设置停止标志
            self.running = False
            self.shutdown_event.set()
            
            # 等待工作线程结束
            for worker_thread in self.worker_threads:
                worker_thread.join(timeout=timeout/len(self.worker_threads))
            
            # 关闭线程池
            self.thread_pool.shutdown(wait=True)
            
            # 清空队列
            self._clear_all_queues()
            
            self.logger.info("并行处理器已停止")
            
        except Exception as e:
            self.logger.error(f"停止并行处理器失败: {str(e)}")
    
    def submit_task(self, task: ProcessingTask) -> bool:
        """提交处理任务"""
        try:
            if not self.running:
                self.logger.error("并行处理器未运行，无法提交任务")
                return False
            
            # 选择对应优先级的队列
            target_queue = self.task_queues[task.priority]
            
            # 尝试添加到队列
            try:
                # 使用优先级作为排序键（数值越大优先级越高）
                priority_value = task.priority.value
                target_queue.put((priority_value, task.created_time, task), timeout=1.0)
                
                with self.stats_lock:
                    self.stats.total_tasks += 1
                    self.stats.pending_tasks += 1
                
                self.logger.debug(f"任务提交成功: {task.task_id}")
                return True
                
            except queue.Full:
                self.logger.warning(f"队列已满，任务提交失败: {task.task_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"提交任务失败: {str(e)}")
            return False
    
    def submit_dde_data(self, symbol: str, data_type: str, item: str, value: str,
                       priority: TaskPriority = TaskPriority.NORMAL,
                       callback: Optional[Callable] = None) -> bool:
        """提交DDE数据处理任务"""
        try:
            task_id = f"{symbol}_{data_type}_{item}_{int(time.time()*1000000)}"
            
            task = ProcessingTask(
                task_id=task_id,
                symbol=symbol,
                data_type=data_type,
                item=item,
                value=value,
                priority=priority,
                callback=callback
            )
            
            return self.submit_task(task)
            
        except Exception as e:
            self.logger.error(f"提交DDE数据失败: {str(e)}")
            return False
    
    def get_stats(self) -> ProcessorStats:
        """获取统计信息"""
        with self.stats_lock:
            # 更新活跃工作线程数
            self.stats.active_workers = sum(1 for t in self.worker_threads if t.is_alive())
            return self.stats
    
    def get_queue_status(self) -> Dict[str, int]:
        """获取队列状态"""
        try:
            status = {}
            for priority, task_queue in self.task_queues.items():
                status[priority.name] = task_queue.qsize()
            return status
            
        except Exception as e:
            self.logger.error(f"获取队列状态失败: {str(e)}")
            return {}
    
    def _worker_loop(self, worker_id: int):
        """工作线程主循环"""
        try:
            self.logger.info(f"工作线程启动: Worker-{worker_id}")
            
            while self.running and not self.shutdown_event.is_set():
                try:
                    # 按优先级顺序检查队列
                    task = self._get_next_task()
                    
                    if task:
                        # 处理任务
                        self._process_task(task, worker_id)
                    else:
                        # 没有任务时短暂休眠
                        time.sleep(0.01)
                        
                except Exception as e:
                    self.logger.error(f"工作线程异常: Worker-{worker_id}, {str(e)}")
                    time.sleep(0.1)
            
            self.logger.info(f"工作线程结束: Worker-{worker_id}")
            
        except Exception as e:
            self.logger.error(f"工作线程失败: Worker-{worker_id}, {str(e)}")
    
    def _get_next_task(self) -> Optional[ProcessingTask]:
        """获取下一个任务（按优先级）"""
        try:
            # 按优先级顺序检查队列
            for priority in [TaskPriority.CRITICAL, TaskPriority.HIGH, 
                           TaskPriority.NORMAL, TaskPriority.LOW]:
                task_queue = self.task_queues[priority]
                
                try:
                    # 非阻塞获取任务
                    priority_value, created_time, task = task_queue.get_nowait()
                    
                    with self.stats_lock:
                        self.stats.pending_tasks -= 1
                    
                    return task
                    
                except queue.Empty:
                    continue
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取下一个任务失败: {str(e)}")
            return None
    
    def _process_task(self, task: ProcessingTask, worker_id: int):
        """处理单个任务"""
        start_time = time.time()
        
        try:
            self.logger.debug(f"开始处理任务: {task.task_id} by Worker-{worker_id}")
            
            # 更新负载统计
            with self.load_balancer_lock:
                self.worker_loads[worker_id] += 1
            
            # 这里是实际的数据处理逻辑
            # 在实际实现中，这里会调用对应的引擎实例进行数据处理
            self._simulate_data_processing(task)
            
            # 调用回调函数
            if task.callback:
                task.callback(task)
            
            # 更新统计信息
            processing_time = time.time() - start_time
            
            with self.stats_lock:
                self.stats.completed_tasks += 1
                self.processing_times.append(processing_time)
                
                # 保持处理时间列表大小
                if len(self.processing_times) > 1000:
                    self.processing_times = self.processing_times[-500:]
            
            self.logger.debug(f"任务处理完成: {task.task_id}, 耗时: {processing_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"处理任务失败: {task.task_id}, {str(e)}")
            
            with self.stats_lock:
                self.stats.failed_tasks += 1
        
        finally:
            # 更新负载统计
            with self.load_balancer_lock:
                self.worker_loads[worker_id] -= 1
    
    def _simulate_data_processing(self, task: ProcessingTask):
        """模拟数据处理（实际实现中会调用引擎实例）"""
        # 这里模拟数据处理的耗时
        # 在实际实现中，这里会：
        # 1. 找到对应的引擎实例
        # 2. 调用引擎的数据处理方法
        # 3. 处理结果和错误
        
        processing_time = 0.001  # 模拟1ms的处理时间
        time.sleep(processing_time)
        
        # 模拟处理逻辑
        self.logger.debug(f"处理数据: {task.symbol}.{task.data_type}.{task.item} = {task.value}")
    
    def _stats_monitor_loop(self):
        """统计监控循环"""
        try:
            self.logger.info("统计监控线程启动")
            
            while self.running and not self.shutdown_event.is_set():
                try:
                    self._update_stats()
                    time.sleep(5.0)  # 每5秒更新一次统计
                    
                except Exception as e:
                    self.logger.error(f"统计监控异常: {str(e)}")
                    time.sleep(10.0)
            
            self.logger.info("统计监控线程结束")
            
        except Exception as e:
            self.logger.error(f"统计监控失败: {str(e)}")
    
    def _update_stats(self):
        """更新统计信息"""
        try:
            current_time = time.time()
            time_diff = current_time - self.last_stats_update
            
            with self.stats_lock:
                # 计算平均处理时间
                if self.processing_times:
                    self.stats.average_processing_time = sum(self.processing_times) / len(self.processing_times)
                
                # 计算吞吐量
                if time_diff > 0:
                    completed_in_period = self.stats.completed_tasks
                    self.stats.throughput_per_second = completed_in_period / time_diff
            
            self.last_stats_update = current_time
            
        except Exception as e:
            self.logger.error(f"更新统计信息失败: {str(e)}")
    
    def _clear_all_queues(self):
        """清空所有队列"""
        try:
            for priority, task_queue in self.task_queues.items():
                while not task_queue.empty():
                    try:
                        task_queue.get_nowait()
                    except queue.Empty:
                        break
            
            self.logger.info("所有队列已清空")
            
        except Exception as e:
            self.logger.error(f"清空队列失败: {str(e)}")
