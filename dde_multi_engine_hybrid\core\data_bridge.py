#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据桥接模块
在多商品GUI和引擎之间建立数据桥接
"""

import logging
import threading
import time
from typing import Dict, List, Optional, Callable
from datetime import datetime

from dde_data_engine.data_structures import ItemData


class DataBridge:
    """数据桥接器
    
    负责在多商品GUI和引擎管理器之间传递数据
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self._lock = threading.Lock()
        
        # 数据存储
        self.items_data: Dict[str, ItemData] = {}
        self.raw_data: List[dict] = []
        
        # 回调函数
        self.on_gui_update: Optional[Callable] = None
        self.on_data_processed: Optional[Callable] = None
        
        # 统计信息
        self.total_received = 0
        self.total_processed = 0
        self.start_time = time.time()
        
    def initialize_items(self, items_config: Dict[str, dict]):
        """初始化项目数据"""
        try:
            with self._lock:
                self.items_data.clear()
                
                for item_code, item_info in items_config.items():
                    item_data = ItemData(
                        name=item_info.get('name', item_code),
                        code=item_code,
                        value=None,
                        update_time=None,
                        status="未订阅"
                    )
                    self.items_data[item_code] = item_data
                
                self.logger.info(f"初始化项目数据: {len(self.items_data)} 个项目")
                
        except Exception as e:
            self.logger.error(f"初始化项目数据失败: {str(e)}")
    
    def update_item_data(self, item_code: str, value: str):
        """更新项目数据"""
        try:
            with self._lock:
                self.total_received += 1
                
                if item_code in self.items_data:
                    item_data = self.items_data[item_code]
                    item_data.value = value
                    item_data.update_time = datetime.now()
                    item_data.status = "已更新"
                    
                    self.logger.debug(f"更新项目数据: {item_code} = {value}")
                    
                    # 触发GUI更新
                    if self.on_gui_update:
                        self.on_gui_update(item_code, value)
                    
                    self.total_processed += 1
                else:
                    self.logger.warning(f"未知项目代码: {item_code}")
                    
        except Exception as e:
            self.logger.error(f"更新项目数据失败: {item_code}, {str(e)}")
    
    def get_item_data(self, item_code: str) -> Optional[ItemData]:
        """获取项目数据"""
        with self._lock:
            return self.items_data.get(item_code)
    
    def get_all_items(self) -> Dict[str, ItemData]:
        """获取所有项目数据"""
        with self._lock:
            return self.items_data.copy()
    
    def update_item_status(self, item_code: str, status: str):
        """更新项目状态"""
        try:
            with self._lock:
                if item_code in self.items_data:
                    self.items_data[item_code].status = status
                    self.logger.debug(f"更新项目状态: {item_code} -> {status}")
                    
        except Exception as e:
            self.logger.error(f"更新项目状态失败: {item_code}, {str(e)}")
    
    def add_raw_data(self, data_row: dict):
        """添加原始数据行"""
        try:
            with self._lock:
                self.raw_data.insert(0, data_row)
                
                # 保持最近1000行数据
                if len(self.raw_data) > 1000:
                    self.raw_data = self.raw_data[:1000]
                
                # 触发数据处理回调
                if self.on_data_processed:
                    self.on_data_processed(data_row)
                    
        except Exception as e:
            self.logger.error(f"添加原始数据失败: {str(e)}")
    
    def get_recent_data(self, count: int = 10) -> List[dict]:
        """获取最近的数据"""
        with self._lock:
            return self.raw_data[:count]
    
    def get_statistics(self) -> dict:
        """获取统计信息"""
        with self._lock:
            uptime = time.time() - self.start_time
            rate = self.total_received / uptime if uptime > 0 else 0
            
            return {
                'total_received': self.total_received,
                'total_processed': self.total_processed,
                'uptime': uptime,
                'receive_rate': rate,
                'items_count': len(self.items_data),
                'active_items': len([item for item in self.items_data.values() 
                                   if item.status == "已更新"])
            }
    
    def reset_statistics(self):
        """重置统计信息"""
        with self._lock:
            self.total_received = 0
            self.total_processed = 0
            self.start_time = time.time()
            self.logger.info("统计信息已重置")
    
    def clear_data(self):
        """清理数据"""
        try:
            with self._lock:
                # 重置项目状态但保留结构
                for item_data in self.items_data.values():
                    item_data.value = None
                    item_data.update_time = None
                    item_data.status = "未订阅"
                
                # 清理原始数据
                self.raw_data.clear()
                
                self.logger.info("数据桥接器已清理")
                
        except Exception as e:
            self.logger.error(f"清理数据失败: {str(e)}")


class MultiProductDataBridge:
    """多商品数据桥接器
    
    管理多个商品的数据桥接
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.bridges: Dict[str, DataBridge] = {}
        
        # 全局回调
        self.on_global_update: Optional[Callable] = None
    
    def create_bridge(self, product_symbol: str, data_type: str) -> DataBridge:
        """创建数据桥接器"""
        try:
            bridge_id = f"{product_symbol}_{data_type}"
            
            if bridge_id in self.bridges:
                return self.bridges[bridge_id]
            
            bridge = DataBridge(self.logger)
            
            # 设置回调
            bridge.on_gui_update = lambda item, value: self._on_bridge_update(
                product_symbol, data_type, item, value
            )
            
            self.bridges[bridge_id] = bridge
            self.logger.info(f"创建数据桥接器: {bridge_id}")
            
            return bridge
            
        except Exception as e:
            self.logger.error(f"创建数据桥接器失败: {product_symbol}.{data_type}, {str(e)}")
            return None
    
    def get_bridge(self, product_symbol: str, data_type: str) -> Optional[DataBridge]:
        """获取数据桥接器"""
        bridge_id = f"{product_symbol}_{data_type}"
        return self.bridges.get(bridge_id)
    
    def update_data(self, product_symbol: str, data_type: str, item: str, value: str):
        """更新数据"""
        try:
            bridge = self.get_bridge(product_symbol, data_type)
            if bridge:
                bridge.update_item_data(item, value)
            else:
                self.logger.warning(f"数据桥接器不存在: {product_symbol}.{data_type}")
                
        except Exception as e:
            self.logger.error(f"更新数据失败: {product_symbol}.{data_type}, {item}, {str(e)}")
    
    def get_global_statistics(self) -> dict:
        """获取全局统计信息"""
        try:
            total_received = 0
            total_processed = 0
            total_items = 0
            active_items = 0
            
            for bridge in self.bridges.values():
                stats = bridge.get_statistics()
                total_received += stats['total_received']
                total_processed += stats['total_processed']
                total_items += stats['items_count']
                active_items += stats['active_items']
            
            return {
                'total_bridges': len(self.bridges),
                'total_received': total_received,
                'total_processed': total_processed,
                'total_items': total_items,
                'active_items': active_items
            }
            
        except Exception as e:
            self.logger.error(f"获取全局统计失败: {str(e)}")
            return {}
    
    def _on_bridge_update(self, product_symbol: str, data_type: str, item: str, value: str):
        """桥接器更新回调"""
        try:
            if self.on_global_update:
                self.on_global_update(product_symbol, data_type, item, value)
        except Exception as e:
            self.logger.error(f"桥接器回调失败: {product_symbol}.{data_type}, {item}, {str(e)}")
    
    def cleanup(self):
        """清理资源"""
        try:
            for bridge in self.bridges.values():
                bridge.clear_data()
            self.bridges.clear()
            self.logger.info("多商品数据桥接器清理完成")
        except Exception as e:
            self.logger.error(f"清理多商品数据桥接器失败: {str(e)}")
