#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 實現方式分析

分析為什麼 Excel 可以連接2000檔，我們只能127個
"""

import time
import sys
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from dydde.dydde import DDEClient


class DDEImplementationAnalyzer:
    """DDE 實現方式分析器"""
    
    def __init__(self):
        self.test_results = {}
        
    def test_single_connection_multiple_items(self):
        """測試單一連接處理多個項目（模擬 Excel 方式）"""
        print("🔍 測試方案1: 單一連接處理多個項目")
        print("-" * 50)
        
        try:
            # 創建單一連接
            client = DDEClient("XQTISC", "Quote")
            client.connect()
            print("✅ 建立單一 DDE 連接成功")
            
            # 測試大量項目
            test_items = []
            
            # 期貨項目
            futures = ["FITXN07", "FITXN08", "FITXN09"]
            for symbol in futures:
                test_items.extend([
                    f"{symbol}.TF-Price",
                    f"{symbol}.TF-Volume",
                    f"{symbol}.TF-TotalVolume"
                ])
            
            # 股票項目
            stocks = ["2330", "2317", "2454", "2382", "2881"]
            for symbol in stocks:
                test_items.extend([
                    f"{symbol}.TW-Price",
                    f"{symbol}.TW-Volume",
                    f"{symbol}.TW-TotalVolume"
                ])
            
            print(f"測試 {len(test_items)} 個項目...")
            
            successful_items = 0
            failed_items = 0
            
            for i, item in enumerate(test_items, 1):
                try:
                    result = client.request(item)
                    if result and result.strip():
                        successful_items += 1
                        if i <= 5:  # 只顯示前5個結果
                            print(f"   ✅ {item}: {result}")
                    else:
                        failed_items += 1
                        if failed_items <= 3:  # 只顯示前3個失敗
                            print(f"   ⚪ {item}: 無數據")
                except Exception as e:
                    failed_items += 1
                    if failed_items <= 3:
                        print(f"   ❌ {item}: {str(e)}")
                
                # 每50個項目顯示進度
                if i % 50 == 0:
                    print(f"   進度: {i}/{len(test_items)}, 成功: {successful_items}, 失敗: {failed_items}")
            
            client.disconnect()
            
            self.test_results['single_connection'] = {
                'total_items': len(test_items),
                'successful': successful_items,
                'failed': failed_items,
                'success_rate': successful_items / len(test_items) * 100
            }
            
            print(f"\n📊 單一連接測試結果:")
            print(f"   總項目: {len(test_items)}")
            print(f"   成功: {successful_items}")
            print(f"   失敗: {failed_items}")
            print(f"   成功率: {successful_items / len(test_items) * 100:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 單一連接測試失敗: {str(e)}")
            return False
    
    def test_multiple_connections_single_item(self):
        """測試多個連接各處理單個項目（當前方式）"""
        print(f"\n🔍 測試方案2: 多個連接各處理單個項目")
        print("-" * 50)
        
        clients = []
        successful_connections = 0
        failed_connections = 0
        
        try:
            # 測試建立多個連接
            test_items = [
                "FITXN07.TF-Price",
                "FITXN08.TF-Price", 
                "FITXN09.TF-Price",
                "2330.TW-Price",
                "2317.TW-Price"
            ]
            
            print(f"嘗試為 {len(test_items)} 個項目各建立獨立連接...")
            
            for i, item in enumerate(test_items, 1):
                try:
                    client = DDEClient("XQTISC", "Quote")
                    client.connect()
                    
                    result = client.request(item)
                    if result:
                        clients.append(client)
                        successful_connections += 1
                        print(f"   ✅ 連接 {i}: {item} = {result}")
                    else:
                        client.disconnect()
                        failed_connections += 1
                        print(f"   ⚪ 連接 {i}: {item} 無數據")
                        
                except Exception as e:
                    failed_connections += 1
                    print(f"   ❌ 連接 {i}: {item} 失敗 - {str(e)}")
            
            self.test_results['multiple_connections'] = {
                'total_attempts': len(test_items),
                'successful': successful_connections,
                'failed': failed_connections,
                'success_rate': successful_connections / len(test_items) * 100
            }
            
            print(f"\n📊 多連接測試結果:")
            print(f"   嘗試連接: {len(test_items)}")
            print(f"   成功: {successful_connections}")
            print(f"   失敗: {failed_connections}")
            print(f"   成功率: {successful_connections / len(test_items) * 100:.1f}%")
            
        finally:
            # 清理連接
            for client in clients:
                try:
                    client.disconnect()
                except:
                    pass
    
    def test_connection_reuse(self):
        """測試連接復用"""
        print(f"\n🔍 測試方案3: 連接復用")
        print("-" * 50)
        
        try:
            # 建立連接字典
            connections = {}
            
            test_products = [
                ("FITXN07", "XQTISC", "Quote"),
                ("FITXN08", "XQTISC", "Quote"),
                ("2330", "XQTISC", "Quote"),
                ("2317", "XQTISC", "Quote")
            ]
            
            successful_products = 0
            total_items = 0
            successful_items = 0
            
            for symbol, service, topic in test_products:
                conn_key = f"{service}.{topic}"
                
                # 復用或創建連接
                if conn_key not in connections:
                    try:
                        client = DDEClient(service, topic)
                        client.connect()
                        connections[conn_key] = client
                        print(f"   ✅ 創建新連接: {conn_key}")
                    except Exception as e:
                        print(f"   ❌ 創建連接失敗: {conn_key} - {str(e)}")
                        continue
                
                client = connections[conn_key]
                
                # 測試該產品的多個項目
                if symbol.startswith("FITX"):
                    items = [f"{symbol}.TF-Price", f"{symbol}.TF-Volume"]
                else:
                    items = [f"{symbol}.TW-Price", f"{symbol}.TW-Volume"]
                
                product_success = 0
                for item in items:
                    try:
                        result = client.request(item)
                        total_items += 1
                        if result and result.strip():
                            successful_items += 1
                            product_success += 1
                    except Exception as e:
                        total_items += 1
                        print(f"     ❌ {item}: {str(e)}")
                
                if product_success > 0:
                    successful_products += 1
                    print(f"   ✅ {symbol}: {product_success}/{len(items)} 項目成功")
            
            # 清理連接
            for client in connections.values():
                try:
                    client.disconnect()
                except:
                    pass
            
            self.test_results['connection_reuse'] = {
                'total_connections': len(connections),
                'total_products': len(test_products),
                'successful_products': successful_products,
                'total_items': total_items,
                'successful_items': successful_items
            }
            
            print(f"\n📊 連接復用測試結果:")
            print(f"   總連接數: {len(connections)}")
            print(f"   成功產品: {successful_products}/{len(test_products)}")
            print(f"   成功項目: {successful_items}/{total_items}")
            
        except Exception as e:
            print(f"❌ 連接復用測試失敗: {str(e)}")
    
    def analyze_dydde_implementation(self):
        """分析 dydde 實現"""
        print(f"\n🔍 分析 dydde 實現")
        print("-" * 50)
        
        try:
            # 檢查 dydde 模組
            import inspect
            
            # 檢查 DDEClient 類
            client_methods = [method for method in dir(DDEClient) if not method.startswith('_')]
            print(f"DDEClient 可用方法: {client_methods}")
            
            # 創建測試客戶端並檢查內部狀態
            client = DDEClient("XQTISC", "Quote")
            print(f"客戶端初始狀態:")
            print(f"   服務: {getattr(client, 'service', 'N/A')}")
            print(f"   主題: {getattr(client, 'topic', 'N/A')}")
            
            # 檢查是否有連接句柄相關屬性
            handle_attrs = [attr for attr in dir(client) if 'handle' in attr.lower() or 'conv' in attr.lower()]
            print(f"句柄相關屬性: {handle_attrs}")
            
        except Exception as e:
            print(f"❌ dydde 分析失敗: {str(e)}")
    
    def generate_recommendations(self):
        """生成建議"""
        print(f"\n" + "=" * 60)
        print(f"💡 分析結論和建議")
        print(f"=" * 60)
        
        print(f"🔍 問題分析:")
        print(f"1. Excel 可以處理2000檔股票 → DDE 服務端沒有限制")
        print(f"2. 我們的程式只能127個 → 實現方式有問題")
        print(f"3. 可能原因:")
        print(f"   - 每個產品建立獨立連接（資源浪費）")
        print(f"   - 沒有正確復用連接")
        print(f"   - dydde 包裝層的限制")
        
        print(f"\n💡 建議解決方案:")
        print(f"1. 連接復用：同一個 service.topic 只建立一個連接")
        print(f"2. 批量處理：一個連接處理多個商品的多個項目")
        print(f"3. 資源管理：正確清理和復用連接")
        print(f"4. 考慮直接使用 Windows DDE API")
        
        if self.test_results:
            print(f"\n📊 測試結果對比:")
            for test_name, results in self.test_results.items():
                print(f"   {test_name}: {results}")


def main():
    """主函數"""
    print("🔍 DDE 實現方式分析工具")
    print("分析為什麼 Excel 可以連接2000檔，我們只能127個")
    print("=" * 60)
    
    analyzer = DDEImplementationAnalyzer()
    
    try:
        # 執行各種測試
        analyzer.test_single_connection_multiple_items()
        analyzer.test_multiple_connections_single_item()
        analyzer.test_connection_reuse()
        analyzer.analyze_dydde_implementation()
        
        # 生成建議
        analyzer.generate_recommendations()
        
    except KeyboardInterrupt:
        print(f"\n⏹️  分析被中斷")
    except Exception as e:
        print(f"\n❌ 分析失敗: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
