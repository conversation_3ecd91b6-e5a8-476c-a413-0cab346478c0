# 動態生成的漸進式測試配置

[System]
dde_service = XQTISC
dde_topic = Quote
output_path = ./data/
log_level = INFO

[Symbols]
symbol_list = FITXN07.TF

[FITXN07.TF]
enabled_types = tick
auto_connect_template = AutoConnect_FuturesFullDay
output_path = ./data/

[DataTypes]
tick = Time,Price,Volume,TotalVolume,NWTotalBidContract,NWTotalAskContract,NWTotalBidSize,NWTotalAskSize

[Template_Tick_Items]
Time = {symbol}-Time
Price = {symbol}-Price
Volume = {symbol}-Volume
TotalVolume = {symbol}-TotalVolume
NWTotalBidContract = {symbol}-NWTotalBidContract
NWTotalAskContract = {symbol}-NWTotalAskContract
NWTotalBidSize = {symbol}-NWTotalBidSize
NWTotalAskSize = {symbol}-NWTotalAskSize

[AutoConnect_FuturesFullDay]
connect_times = 15:00-05:00;08:45-13:45
weekdays = 1,2,3,4,5
skip_weekends = true
schedule_end_action = unadvise_only
