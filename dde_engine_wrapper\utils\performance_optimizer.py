#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化器
提供系统性能优化和调优功能
"""

import logging
import time
import threading
import gc
import psutil
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from collections import deque
import weakref

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    memory_peak: float
    thread_count: int
    gc_collections: int
    processing_latency: float
    throughput: float

@dataclass
class OptimizationConfig:
    """优化配置"""
    # 内存优化
    enable_memory_optimization: bool = True
    memory_threshold_mb: float = 1024.0  # 1GB
    gc_threshold_ratio: float = 0.8
    
    # 线程优化
    enable_thread_optimization: bool = True
    max_thread_count: int = 50
    thread_pool_size_ratio: float = 0.8
    
    # 缓存优化
    enable_cache_optimization: bool = True
    cache_size_limit: int = 10000
    cache_ttl_seconds: float = 300.0
    
    # 监控配置
    metrics_collection_interval: float = 5.0
    optimization_check_interval: float = 30.0

class PerformanceOptimizer:
    """性能优化器
    
    提供系统性能监控和自动优化功能：
    - 内存使用优化
    - 线程池调优
    - 缓存管理
    - 垃圾回收优化
    """
    
    def __init__(self, config: OptimizationConfig = None):
        self.config = config or OptimizationConfig()
        self.logger = logging.getLogger(__name__)
        
        # 性能指标历史
        self.metrics_history: deque = deque(maxlen=1000)
        self.metrics_lock = threading.RLock()
        
        # 优化状态
        self.running = False
        self.optimization_thread: Optional[threading.Thread] = None
        self.shutdown_event = threading.Event()
        
        # 缓存管理
        self.cache_registry: Dict[str, weakref.WeakValueDictionary] = {}
        self.cache_stats: Dict[str, Dict[str, int]] = {}
        
        # 线程池引用
        self.thread_pools: List[Any] = []
        
        self.logger.info("性能优化器初始化完成")
    
    def start(self):
        """启动性能优化器"""
        try:
            if self.running:
                self.logger.warning("性能优化器已经在运行")
                return
            
            self.running = True
            self.shutdown_event.clear()
            
            # 启动优化线程
            self.optimization_thread = threading.Thread(
                target=self._optimization_loop,
                name="PerformanceOptimizer",
                daemon=True
            )
            self.optimization_thread.start()
            
            self.logger.info("性能优化器启动成功")
            
        except Exception as e:
            self.logger.error(f"启动性能优化器失败: {str(e)}")
            self.running = False
    
    def stop(self):
        """停止性能优化器"""
        try:
            if not self.running:
                self.logger.warning("性能优化器未在运行")
                return
            
            self.logger.info("开始停止性能优化器")
            
            # 设置停止标志
            self.running = False
            self.shutdown_event.set()
            
            # 等待优化线程结束
            if self.optimization_thread and self.optimization_thread.is_alive():
                self.optimization_thread.join(timeout=5.0)
            
            self.logger.info("性能优化器已停止")
            
        except Exception as e:
            self.logger.error(f"停止性能优化器失败: {str(e)}")
    
    def collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            # 获取系统指标
            process = psutil.Process()
            
            cpu_usage = process.cpu_percent()
            memory_info = process.memory_info()
            memory_usage = memory_info.rss / 1024 / 1024  # MB
            memory_peak = memory_info.peak_wss / 1024 / 1024 if hasattr(memory_info, 'peak_wss') else memory_usage
            thread_count = process.num_threads()
            
            # 获取垃圾回收统计
            gc_stats = gc.get_stats()
            gc_collections = sum(stat['collections'] for stat in gc_stats)
            
            # 创建性能指标
            metrics = PerformanceMetrics(
                timestamp=time.time(),
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                memory_peak=memory_peak,
                thread_count=thread_count,
                gc_collections=gc_collections,
                processing_latency=0.0,  # 需要从其他组件获取
                throughput=0.0  # 需要从其他组件获取
            )
            
            # 添加到历史记录
            with self.metrics_lock:
                self.metrics_history.append(metrics)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集性能指标失败: {str(e)}")
            return None
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前性能指标"""
        with self.metrics_lock:
            if self.metrics_history:
                return self.metrics_history[-1]
            return None
    
    def get_metrics_history(self, count: int = 100) -> List[PerformanceMetrics]:
        """获取性能指标历史"""
        with self.metrics_lock:
            return list(self.metrics_history)[-count:]
    
    def optimize_memory(self) -> bool:
        """内存优化"""
        try:
            if not self.config.enable_memory_optimization:
                return True
            
            current_metrics = self.get_current_metrics()
            if not current_metrics:
                return False
            
            # 检查内存使用是否超过阈值
            if current_metrics.memory_usage > self.config.memory_threshold_mb:
                self.logger.warning(f"内存使用过高: {current_metrics.memory_usage:.1f}MB")
                
                # 强制垃圾回收
                collected = gc.collect()
                self.logger.info(f"垃圾回收完成，回收对象数: {collected}")
                
                # 清理缓存
                self._cleanup_caches()
                
                return True
            
            # 检查垃圾回收阈值
            if self._should_trigger_gc():
                gc.collect()
                self.logger.debug("触发垃圾回收")
            
            return True
            
        except Exception as e:
            self.logger.error(f"内存优化失败: {str(e)}")
            return False
    
    def optimize_threads(self) -> bool:
        """线程优化"""
        try:
            if not self.config.enable_thread_optimization:
                return True
            
            current_metrics = self.get_current_metrics()
            if not current_metrics:
                return False
            
            # 检查线程数量
            if current_metrics.thread_count > self.config.max_thread_count:
                self.logger.warning(f"线程数量过多: {current_metrics.thread_count}")
                
                # 这里可以添加线程池调优逻辑
                # 例如减少线程池大小或清理空闲线程
                
            return True
            
        except Exception as e:
            self.logger.error(f"线程优化失败: {str(e)}")
            return False
    
    def register_cache(self, cache_name: str, cache_dict: weakref.WeakValueDictionary):
        """注册缓存"""
        try:
            self.cache_registry[cache_name] = cache_dict
            self.cache_stats[cache_name] = {
                'hits': 0,
                'misses': 0,
                'size': 0,
                'cleanups': 0
            }
            self.logger.info(f"注册缓存: {cache_name}")
            
        except Exception as e:
            self.logger.error(f"注册缓存失败: {str(e)}")
    
    def update_cache_stats(self, cache_name: str, hit: bool):
        """更新缓存统计"""
        try:
            if cache_name in self.cache_stats:
                if hit:
                    self.cache_stats[cache_name]['hits'] += 1
                else:
                    self.cache_stats[cache_name]['misses'] += 1
                    
        except Exception as e:
            self.logger.error(f"更新缓存统计失败: {str(e)}")
    
    def get_cache_stats(self) -> Dict[str, Dict[str, int]]:
        """获取缓存统计"""
        try:
            # 更新缓存大小
            for cache_name, cache_dict in self.cache_registry.items():
                if cache_name in self.cache_stats:
                    self.cache_stats[cache_name]['size'] = len(cache_dict)
            
            return self.cache_stats.copy()
            
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {str(e)}")
            return {}
    
    def register_thread_pool(self, thread_pool):
        """注册线程池"""
        try:
            self.thread_pools.append(weakref.ref(thread_pool))
            self.logger.info("注册线程池")
            
        except Exception as e:
            self.logger.error(f"注册线程池失败: {str(e)}")
    
    def get_optimization_recommendations(self) -> List[str]:
        """获取优化建议"""
        recommendations = []
        
        try:
            current_metrics = self.get_current_metrics()
            if not current_metrics:
                return recommendations
            
            # 内存优化建议
            if current_metrics.memory_usage > self.config.memory_threshold_mb * 0.8:
                recommendations.append(f"内存使用较高 ({current_metrics.memory_usage:.1f}MB)，建议优化内存使用")
            
            # 线程优化建议
            if current_metrics.thread_count > self.config.max_thread_count * 0.8:
                recommendations.append(f"线程数量较多 ({current_metrics.thread_count})，建议优化线程使用")
            
            # CPU优化建议
            if current_metrics.cpu_usage > 80.0:
                recommendations.append(f"CPU使用率较高 ({current_metrics.cpu_usage:.1f}%)，建议优化处理逻辑")
            
            # 缓存优化建议
            cache_stats = self.get_cache_stats()
            for cache_name, stats in cache_stats.items():
                if stats['hits'] + stats['misses'] > 0:
                    hit_rate = stats['hits'] / (stats['hits'] + stats['misses'])
                    if hit_rate < 0.5:
                        recommendations.append(f"缓存 {cache_name} 命中率较低 ({hit_rate:.1%})，建议优化缓存策略")
            
        except Exception as e:
            self.logger.error(f"生成优化建议失败: {str(e)}")
        
        return recommendations
    
    def _optimization_loop(self):
        """优化循环"""
        try:
            self.logger.info("性能优化循环开始")
            
            while self.running and not self.shutdown_event.is_set():
                try:
                    # 收集性能指标
                    self.collect_metrics()
                    
                    # 执行优化
                    self.optimize_memory()
                    self.optimize_threads()
                    
                    # 等待下次优化
                    self.shutdown_event.wait(self.config.optimization_check_interval)
                    
                except Exception as e:
                    self.logger.error(f"优化循环异常: {str(e)}")
                    self.shutdown_event.wait(self.config.optimization_check_interval * 2)
            
            self.logger.info("性能优化循环结束")
            
        except Exception as e:
            self.logger.error(f"优化循环失败: {str(e)}")
    
    def _should_trigger_gc(self) -> bool:
        """判断是否应该触发垃圾回收"""
        try:
            # 获取垃圾回收统计
            gc_stats = gc.get_stats()
            
            for i, stat in enumerate(gc_stats):
                threshold = gc.get_threshold()[i] if i < len(gc.get_threshold()) else 700
                if stat['count'] > threshold * self.config.gc_threshold_ratio:
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查垃圾回收条件失败: {str(e)}")
            return False
    
    def _cleanup_caches(self):
        """清理缓存"""
        try:
            for cache_name, cache_dict in self.cache_registry.items():
                if len(cache_dict) > self.config.cache_size_limit:
                    # 这里可以实现更复杂的缓存清理策略
                    # 例如LRU清理、TTL清理等
                    cache_dict.clear()
                    self.cache_stats[cache_name]['cleanups'] += 1
                    self.logger.info(f"清理缓存: {cache_name}")
                    
        except Exception as e:
            self.logger.error(f"清理缓存失败: {str(e)}")


class MemoryProfiler:
    """内存分析器"""
    
    @staticmethod
    def get_memory_usage() -> Dict[str, float]:
        """获取内存使用情况"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss_mb': memory_info.rss / 1024 / 1024,
                'vms_mb': memory_info.vms / 1024 / 1024,
                'percent': process.memory_percent(),
                'available_mb': psutil.virtual_memory().available / 1024 / 1024
            }
            
        except Exception as e:
            logging.error(f"获取内存使用情况失败: {str(e)}")
            return {}
    
    @staticmethod
    def get_gc_stats() -> Dict[str, Any]:
        """获取垃圾回收统计"""
        try:
            stats = gc.get_stats()
            return {
                'generation_0': stats[0] if len(stats) > 0 else {},
                'generation_1': stats[1] if len(stats) > 1 else {},
                'generation_2': stats[2] if len(stats) > 2 else {},
                'total_collections': sum(stat['collections'] for stat in stats),
                'total_collected': sum(stat['collected'] for stat in stats),
                'total_uncollectable': sum(stat['uncollectable'] for stat in stats)
            }
            
        except Exception as e:
            logging.error(f"获取垃圾回收统计失败: {str(e)}")
            return {}


class ThreadProfiler:
    """线程分析器"""
    
    @staticmethod
    def get_thread_info() -> Dict[str, Any]:
        """获取线程信息"""
        try:
            process = psutil.Process()
            
            return {
                'thread_count': process.num_threads(),
                'active_threads': threading.active_count(),
                'main_thread': threading.main_thread().name,
                'current_thread': threading.current_thread().name
            }
            
        except Exception as e:
            logging.error(f"获取线程信息失败: {str(e)}")
            return {}
    
    @staticmethod
    def get_thread_list() -> List[Dict[str, Any]]:
        """获取线程列表"""
        try:
            threads = []
            for thread in threading.enumerate():
                threads.append({
                    'name': thread.name,
                    'ident': thread.ident,
                    'daemon': thread.daemon,
                    'alive': thread.is_alive()
                })
            
            return threads
            
        except Exception as e:
            logging.error(f"获取线程列表失败: {str(e)}")
            return []
