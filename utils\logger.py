#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日誌管理器
負責設定和管理日誌系統
"""

import logging
import os
from datetime import datetime
from typing import Optional

class LoggerManager:
    """日誌管理器"""
    
    def __init__(self):
        self.logger = None
        self.log_file_path = None
        
    def setup_logging(self, log_file_path: str = None, log_level: str = 'INFO',
                     console_log_level: str = None, file_log_level: str = None) -> logging.Logger:
        """設定日誌系統"""
        try:
            # 處理日誌檔案路徑
            if log_file_path:
                self.log_file_path = self._process_log_path(log_file_path)
            else:
                # 預設日誌路徑
                current_date = datetime.now().strftime("%Y%m%d")
                self.log_file_path = f"./logs/{current_date}/dde_monitor_01.log"

            # 創建日誌目錄
            log_dir = os.path.dirname(self.log_file_path)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)

            # 設定日誌級別
            main_level = getattr(logging, log_level.upper(), logging.INFO)
            console_level = getattr(logging, (console_log_level or log_level).upper(), main_level)
            file_level = getattr(logging, (file_log_level or log_level).upper(), main_level)

            # 創建 logger，設定為最低級別以允許所有處理器接收消息
            self.logger = logging.getLogger('DDEMonitor')
            self.logger.setLevel(min(main_level, console_level, file_level))

            # 清除現有的處理器
            for handler in self.logger.handlers[:]:
                self.logger.removeHandler(handler)

            # 創建格式器
            formatter = logging.Formatter(
                '[%(asctime)s] [%(levelname)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )

            # 檔案處理器
            if self.log_file_path:
                file_handler = logging.FileHandler(self.log_file_path, encoding='utf-8')
                file_handler.setLevel(file_level)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)

            # 控制台處理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(console_level)
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

            # 只有在控制台级别允许的情况下才显示初始化消息
            if console_level <= logging.INFO:
                self.logger.info(f"日誌檔案初始化完成: {self.log_file_path}")
            return self.logger
            
        except Exception as e:
            # 如果日誌設定失敗，使用基本設定
            logging.basicConfig(
                level=logging.INFO,
                format='[%(asctime)s] [%(levelname)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            self.logger = logging.getLogger('DDEMonitor')
            self.logger.error(f"日誌設定失敗: {str(e)}")
            return self.logger
    
    def _process_log_path(self, log_path: str) -> str:
        """處理日誌路徑中的變數"""
        try:
            current_date = datetime.now().strftime("%Y%m%d")
            instance_id = "01"  # 可以根據需要動態生成
            
            processed_path = log_path.replace('{date}', current_date)
            processed_path = processed_path.replace('{instance}', instance_id)
            
            return processed_path
            
        except Exception as e:
            logging.error(f"處理日誌路徑失敗: {str(e)}")
            return log_path
    
    def get_logger(self) -> Optional[logging.Logger]:
        """獲取日誌器"""
        return self.logger
    
    def set_log_level(self, level: str):
        """設定日誌級別"""
        try:
            if self.logger:
                log_level = getattr(logging, level.upper(), logging.INFO)
                self.logger.setLevel(log_level)
                
                # 更新所有處理器的級別
                for handler in self.logger.handlers:
                    handler.setLevel(log_level)
                
                self.logger.info(f"日誌級別已設定為: {level.upper()}")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"設定日誌級別失敗: {str(e)}")
    
    def add_file_handler(self, file_path: str, level: str = 'INFO'):
        """添加額外的檔案處理器"""
        try:
            if not self.logger:
                return False
            
            # 創建目錄
            log_dir = os.path.dirname(file_path)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir, exist_ok=True)
            
            # 創建檔案處理器
            file_handler = logging.FileHandler(file_path, encoding='utf-8')
            file_handler.setLevel(getattr(logging, level.upper(), logging.INFO))
            
            # 設定格式器
            formatter = logging.Formatter(
                '[%(asctime)s] [%(levelname)s] %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            
            # 添加到 logger
            self.logger.addHandler(file_handler)
            self.logger.info(f"已添加額外日誌檔案: {file_path}")
            
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"添加檔案處理器失敗: {str(e)}")
            return False
    
    def remove_console_handler(self):
        """移除控制台處理器"""
        try:
            if not self.logger:
                return
            
            # 移除所有 StreamHandler
            for handler in self.logger.handlers[:]:
                if isinstance(handler, logging.StreamHandler) and not isinstance(handler, logging.FileHandler):
                    self.logger.removeHandler(handler)
            
            self.logger.info("控制台日誌處理器已移除")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"移除控制台處理器失敗: {str(e)}")
    
    def close_handlers(self):
        """關閉所有日誌處理器"""
        try:
            if self.logger:
                for handler in self.logger.handlers[:]:
                    handler.close()
                    self.logger.removeHandler(handler)
                
        except Exception as e:
            logging.error(f"關閉日誌處理器失敗: {str(e)}")

# 全域日誌管理器實例
logger_manager = LoggerManager()

def setup_logging(log_file_path: str = None, log_level: str = 'INFO',
                 console_log_level: str = None, file_log_level: str = None) -> logging.Logger:
    """設定日誌系統的便捷函數"""
    return logger_manager.setup_logging(log_file_path, log_level, console_log_level, file_log_level)

def get_logger() -> Optional[logging.Logger]:
    """獲取日誌器的便捷函數"""
    return logger_manager.get_logger()
