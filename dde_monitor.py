import sys
import os
import logging
import configparser
import time
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from collections import deque
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QTableWidget, QTableWidgetItem, QTextEdit, QSplitter,
                             QMessageBox, QComboBox, QSystemTrayIcon, QMenu, QDialog,
                             QFormLayout, QCheckBox, QSpinBox, QDoubleSpinBox, QTimeEdit,
                             QGroupBox, QGridLayout, QTabWidget, QScrollArea)
from PySide6.QtCore import Qt, QTimer, QThread, Signal, QObject, QTime
from PySide6.QtGui import QIcon
from dydde.dydde import DDEClient
from queue import Queue
import threading
import re

@dataclass
class ItemData:
    """項目資料結構
    用於儲存每個DDE項目的相關資訊
    
    屬性:
        name (str): 項目名稱
        code (str): DDE項目代碼
        value (Optional[str]): 當前值
        update_time (Optional[datetime]): 最後更新時間
        status (str): 訂閱狀態
    """
    name: str
    code: str
    value: Optional[str] = None
    update_time: Optional[datetime] = None
    status: str = "未訂閱"

@dataclass
class RawDataRow:
    """原始資料行結構
    用於儲存每一筆原始資料的完整資訊
    
    屬性:
        receive_date (str): 接收日期 (YYYY-MM-DD)
        receive_time (str): 接收時間 (HH:MM:SS.ffffff)
        values (Dict[str, str]): 項目值字典，key為項目名稱，value為項目值
        is_complete (bool): 是否為完整資料行
    """
    receive_date: str
    receive_time: str
    values: Dict[str, str]
    is_complete: bool = False

class DataFileHandler:
    """檔案處理類別
    負責處理所有檔案相關操作，包括初始化檔案、確保目錄存在、儲存資料等
    
    屬性:
        data_file (str): 原始資料檔案路徑
        complete_data_file (str): 完整資料檔案路徑
        logger (logging.Logger): 日誌記錄器
    """
    def __init__(self):
        self.data_file = None
        self.complete_data_file = None
        self.logger = logging.getLogger("DataFileHandler")
        self.items_data = None  # 新增 items_data 屬性
        self.enable_data_file = True
        self.enable_complete_data_file = True
        self.enable_log_file = True
        self.config = None  # 新增 config 屬性
        
    def init_files(self, config: configparser.ConfigParser, items_data: Dict[str, ItemData]):
        """初始化檔案
        根據設定檔初始化資料檔案和完整資料檔案
        
        參數:
            config (configparser.ConfigParser): 設定檔物件
            items_data (Dict[str, ItemData]): 項目資料字典
        """
        try:
            # 保存 config 和 items_data
            self.config = config
            self.items_data = items_data
            
            # 讀取檔案輸出控制設定
            self.enable_data_file = config.getboolean('FileOutput', 'enable_data_file', fallback=True)
            self.enable_complete_data_file = config.getboolean('FileOutput', 'enable_complete_data_file', fallback=True)
            self.enable_log_file = config.getboolean('FileOutput', 'enable_log_file', fallback=True)
            
            # 獲取當前日期
            now = datetime.now()
            date_str = now.strftime('%Y%m%d')
            
            # 格式化檔案路徑
            data_file_path = config.get('OutputPath', 'data_file', fallback='dde_data_{date}.csv')
            complete_data_file_path = config.get('OutputPath', 'complete_data_file', fallback='complete_data_{date}.csv')
            
            self.data_file = data_file_path.format(date=date_str)
            self.complete_data_file = complete_data_file_path.format(date=date_str)
            
            # 確保目錄存在
            if self.enable_data_file:
                self.ensure_directory(self.data_file)
            if self.enable_complete_data_file:
                self.ensure_directory(self.complete_data_file)
            
            # 寫入標題行
            self.write_headers()
            
            self.logger.info(f"資料檔案初始化完成: {self.data_file}")
            self.logger.info(f"完整資料檔案初始化完成: {self.complete_data_file}")
            
        except Exception as e:
            self.logger.error(f"初始化檔案失敗: {str(e)}")
            raise
            
    def write_headers(self):
        """寫入標題行到兩個檔案"""
        try:
            if not self.items_data:
                self.logger.error("items_data 未初始化，無法寫入標題行")
                return
                
            # 準備標題行
            headers = ["接收日期", "接收時間"]
            # 按照設定檔中的順序添加項目名稱
            for i in range(1, 100):  # 假設最多100個項目
                name_key = f'item{i}_name'
                code_key = f'item{i}_code'
                
                if name_key in self.config['Items'] and code_key in self.config['Items']:
                    name = self.config['Items'][name_key]
                    code = self.config['Items'][code_key]
                    if code in self.items_data:
                        headers.append(name)
            
            # 寫入標題行到兩個檔案
            header_line = ','.join(headers) + '\n'
            
            # 寫入原始資料檔案
            if self.enable_data_file:
                with open(self.data_file, 'w', encoding='utf-8') as f:
                    f.write(header_line)
                
            # 寫入完整資料檔案
            if self.enable_complete_data_file:
                with open(self.complete_data_file, 'w', encoding='utf-8') as f:
                    f.write(header_line)
                
            self.logger.debug("已寫入標題行到兩個檔案")
            
        except Exception as e:
            self.logger.error(f"寫入標題行失敗: {str(e)}")
            raise
            
    def ensure_directory(self, file_path: str):
        """確保目錄存在
        檢查並建立必要的目錄結構
        
        參數:
            file_path (str): 檔案路徑
        """
        try:
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
        except Exception as e:
            self.logger.error(f"建立目錄失敗: {directory}, 錯誤: {str(e)}")
            raise
            
    def save_row(self, row: RawDataRow, is_complete: bool = False):
        """儲存資料行
        將資料行寫入對應的檔案
        
        參數:
            row (RawDataRow): 要儲存的資料行
            is_complete (bool): 是否為完整資料行
        """
        try:
            if not self.items_data:
                self.logger.error("items_data 未初始化，無法儲存資料行")
                return
                
            # 準備資料行
            row_data = [row.receive_date, row.receive_time]
            # 按照設定檔中的順序添加項目值
            for i in range(1, 100):  # 假設最多100個項目
                name_key = f'item{i}_name'
                code_key = f'item{i}_code'
                
                if name_key in self.config['Items'] and code_key in self.config['Items']:
                    code = self.config['Items'][code_key]
                    if code in self.items_data:
                        row_data.append(row.values.get(code, ""))
            
            # 寫入檔案
            if is_complete and self.enable_complete_data_file:
                with open(self.complete_data_file, 'a', encoding='utf-8') as f:
                    f.write(','.join(row_data) + '\n')
            elif not is_complete and self.enable_data_file:
                with open(self.data_file, 'a', encoding='utf-8') as f:
                    f.write(','.join(row_data) + '\n')
                
            self.logger.debug(f"已儲存資料行到 {self.complete_data_file if is_complete else self.data_file}")
            
        except Exception as e:
            self.logger.error(f"儲存資料行失敗: {str(e)}")
            raise 

class DataProcessor(QObject):
    """資料處理類別，使用 QThread 處理資料"""
    data_processed = Signal(str, str)  # 定義信號，用於通知主線程資料已處理
    
    def __init__(self):
        super().__init__()
        self.data_queue = Queue()  # 資料佇列
        self.running = True
        
    def add_data(self, item: str, value: str):
        """將資料加入佇列"""
        self.data_queue.put((item, value))
        
    def process_data(self):
        """處理資料的主循環"""
        batch = []
        while self.running:
            try:
                # 收集一批資料
                while len(batch) < 100 and not self.data_queue.empty():
                    batch.append(self.data_queue.get())
                    
                if batch:
                    # 批次處理
                    for item, value in batch:
                        self.data_processed.emit(item, value)
                    batch = []
                    
                time.sleep(0.01)  # 避免過度消耗 CPU
            except Exception as e:
                logging.error(f"批次處理失敗: {str(e)}")
                
    def stop(self):
        """停止處理"""
        self.running = False

class AutoConnectManager(QObject):
    """自動連線管理器
    負責管理自動連線、斷線和程式結束功能
    """
    # 信號定義
    auto_connect_requested = Signal()
    auto_disconnect_requested = Signal()
    auto_shutdown_requested = Signal()
    status_changed = Signal(str)  # 狀態變化信號

    def __init__(self, config: configparser.ConfigParser, logger: logging.Logger):
        super().__init__()
        self.config = config
        self.logger = logger
        self.is_running = False

        # 定時器
        self.auto_connect_timer = QTimer()
        self.schedule_timer = QTimer()
        self.shutdown_timer = QTimer()

        # 連接信號
        self.auto_connect_timer.timeout.connect(self._handle_auto_connect)
        self.schedule_timer.timeout.connect(self._check_schedule)
        self.shutdown_timer.timeout.connect(self._check_auto_shutdown)

        # 狀態追蹤
        self.current_schedule_state = None  # 'connected' or 'disconnected'
        self.last_schedule_check = None
        self.shutdown_warning_shown = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 0

        # 載入設定
        self._load_settings()

    def _load_settings(self):
        """載入自動連線設定"""
        try:
            # 自動連線設定
            self.enable_auto_connect = self.config.getboolean('AutoConnect', 'enable_auto_connect', fallback=False)
            self.auto_connect_mode = self.config.get('AutoConnect', 'auto_connect_mode', fallback='delay')
            self.auto_connect_delay = self.config.getfloat('AutoConnect', 'auto_connect_delay', fallback=3.0)
            self.schedule_connect_times = self.config.get('AutoConnect', 'schedule_connect_times', fallback='')
            self.enable_cross_day_schedule = self.config.getboolean('AutoConnect', 'enable_cross_day_schedule', fallback=True)
            self.prevent_weekend_startup = self.config.getboolean('AutoConnect', 'prevent_weekend_startup', fallback=False)


            # 自動結束設定
            self.enable_auto_shutdown = self.config.getboolean('AutoShutdown', 'enable_auto_shutdown', fallback=False)
            self.shutdown_time_str = self.config.get('AutoShutdown', 'shutdown_time', fallback='17:30:00')
            self.shutdown_buffer_seconds = self.config.getint('AutoShutdown', 'shutdown_buffer_seconds', fallback=300)
            self.shutdown_warning_seconds = self.config.getint('AutoShutdown', 'shutdown_warning_seconds', fallback=60)
            self.force_shutdown = self.config.getboolean('AutoShutdown', 'force_shutdown', fallback=True)
            self.save_data_before_shutdown = self.config.getboolean('AutoShutdown', 'save_data_before_shutdown', fallback=True)
            self.disconnect_before_shutdown = self.config.getboolean('AutoShutdown', 'disconnect_before_shutdown', fallback=True)
            self.cleanup_temp_files = self.config.getboolean('AutoShutdown', 'cleanup_temp_files', fallback=True)

            # 通知設定
            self.enable_notifications = self.config.getboolean('Notifications', 'enable_system_notifications', fallback=True)
            self.notify_auto_connect = self.config.getboolean('Notifications', 'notify_auto_connect', fallback=True)
            self.notify_auto_disconnect = self.config.getboolean('Notifications', 'notify_auto_disconnect', fallback=True)
            self.notify_auto_shutdown = self.config.getboolean('Notifications', 'notify_auto_shutdown', fallback=True)

            # 解析時間表
            self.schedule_times = self._parse_schedule_times()

            # 解析結束時間
            self.shutdown_time = self._parse_time(self.shutdown_time_str)

            self.logger.info(f"自動連線設定載入完成: 啟用={self.enable_auto_connect}, 模式={self.auto_connect_mode}")
            self.logger.info(f"自動結束設定載入完成: 啟用={self.enable_auto_shutdown}, 時間={self.shutdown_time_str}")

        except Exception as e:
            self.logger.error(f"載入自動連線設定失敗: {str(e)}")
            # 設定預設值
            self.enable_auto_connect = False
            self.enable_auto_shutdown = False

    def load_settings(self, config: configparser.ConfigParser):
        """重新載入設定 (不停止運行)"""
        try:
            old_enable_auto_connect = self.enable_auto_connect
            old_enable_auto_shutdown = self.enable_auto_shutdown
            old_auto_connect_mode = self.auto_connect_mode

            # 更新設定
            self.config = config

            # 重新載入設定
            self.enable_auto_connect = self.config.getboolean('AutoConnect', 'enable_auto_connect', fallback=False)
            self.auto_connect_mode = self.config.get('AutoConnect', 'auto_connect_mode', fallback='delay')
            self.auto_connect_delay = self.config.getfloat('AutoConnect', 'auto_connect_delay', fallback=3.0)
            self.schedule_connect_times = self.config.get('AutoConnect', 'schedule_connect_times', fallback='')
            self.enable_cross_day_schedule = self.config.getboolean('AutoConnect', 'enable_cross_day_schedule', fallback=True)
            self.prevent_weekend_startup = self.config.getboolean('AutoConnect', 'prevent_weekend_startup', fallback=False)

            # 自動結束設定
            self.enable_auto_shutdown = self.config.getboolean('AutoShutdown', 'enable_auto_shutdown', fallback=False)
            self.shutdown_time_str = self.config.get('AutoShutdown', 'shutdown_time', fallback='17:30:00')
            self.shutdown_buffer_seconds = self.config.getint('AutoShutdown', 'shutdown_buffer_seconds', fallback=300)
            self.shutdown_warning_seconds = self.config.getint('AutoShutdown', 'shutdown_warning_seconds', fallback=60)
            self.force_shutdown = self.config.getboolean('AutoShutdown', 'force_shutdown', fallback=True)
            self.save_data_before_shutdown = self.config.getboolean('AutoShutdown', 'save_data_before_shutdown', fallback=True)
            self.disconnect_before_shutdown = self.config.getboolean('AutoShutdown', 'disconnect_before_shutdown', fallback=True)
            self.cleanup_temp_files = self.config.getboolean('AutoShutdown', 'cleanup_temp_files', fallback=True)

            # 通知設定
            self.enable_notifications = self.config.getboolean('Notifications', 'enable_system_notifications', fallback=True)
            self.notify_auto_connect = self.config.getboolean('Notifications', 'notify_auto_connect', fallback=True)
            self.notify_auto_disconnect = self.config.getboolean('Notifications', 'notify_auto_disconnect', fallback=True)
            self.notify_auto_shutdown = self.config.getboolean('Notifications', 'notify_auto_shutdown', fallback=True)

            # 重新解析時間表和結束時間
            self.schedule_times = self._parse_schedule_times()
            self.shutdown_time = self._parse_time(self.shutdown_time_str)

            # 根據設定變更調整定時器
            self._adjust_timers(old_enable_auto_connect, old_enable_auto_shutdown, old_auto_connect_mode)

            self.logger.info("自動化設定已重新載入")
            self.status_changed.emit("設定已更新")

        except Exception as e:
            self.logger.error(f"重新載入設定失敗: {str(e)}")

    def _adjust_timers(self, old_enable_auto_connect, old_enable_auto_shutdown, old_auto_connect_mode):
        """根據設定變更調整定時器"""
        try:
            # 處理自動連線設定變更
            if old_enable_auto_connect != self.enable_auto_connect:
                if self.enable_auto_connect:
                    self.logger.info("自動連線已啟用")
                    if self.auto_connect_mode == 'delay':
                        # 啟動延遲連線
                        delay_ms = int(self.auto_connect_delay * 1000)
                        self.auto_connect_timer.start(delay_ms)
                        self.logger.info(f"延遲 {self.auto_connect_delay} 秒後自動連線")
                    elif self.auto_connect_mode == 'immediate':
                        # 立即連線
                        self.auto_connect_requested.emit()
                        self.logger.info("立即執行自動連線")
                else:
                    self.logger.info("自動連線已停用")
                    self.auto_connect_timer.stop()

            # 處理連線模式變更
            elif self.enable_auto_connect and old_auto_connect_mode != self.auto_connect_mode:
                self.auto_connect_timer.stop()
                if self.auto_connect_mode == 'delay':
                    delay_ms = int(self.auto_connect_delay * 1000)
                    self.auto_connect_timer.start(delay_ms)
                    self.logger.info(f"切換到延遲模式，{self.auto_connect_delay} 秒後連線")
                elif self.auto_connect_mode == 'immediate':
                    self.auto_connect_requested.emit()
                    self.logger.info("切換到立即模式，執行連線")

            # 處理時間表設定
            if self.auto_connect_mode == 'schedule' and self.schedule_times:
                if not self.schedule_timer.isActive():
                    self.schedule_timer.start(1000)
                    self.logger.info("時間表檢查已啟動")
            else:
                self.schedule_timer.stop()

            # 處理自動結束設定變更
            if old_enable_auto_shutdown != self.enable_auto_shutdown:
                if self.enable_auto_shutdown:
                    self.logger.info(f"自動結束已啟用，結束時間: {self.shutdown_time_str}")
                    self.shutdown_timer.start(1000)
                else:
                    self.logger.info("自動結束已停用")
                    self.shutdown_timer.stop()
                    self.shutdown_warning_shown = False

        except Exception as e:
            self.logger.error(f"調整定時器失敗: {str(e)}")

    def _parse_schedule_times(self) -> List[Tuple[datetime.time, datetime.time]]:
        """解析時間表設定"""
        schedule_times = []
        if not self.schedule_connect_times:
            return schedule_times

        try:
            # 格式: 09:00:00-12:00:00;13:30:00-15:00:00
            time_pairs = self.schedule_connect_times.split(';')
            for time_pair in time_pairs:
                if '-' in time_pair:
                    start_str, end_str = time_pair.strip().split('-')
                    start_time = self._parse_time(start_str.strip())
                    end_time = self._parse_time(end_str.strip())
                    if start_time and end_time:
                        schedule_times.append((start_time, end_time))
                        self.logger.debug(f"解析時間表: {start_time} - {end_time}")

        except Exception as e:
            self.logger.error(f"解析時間表失敗: {str(e)}")

        return schedule_times

    def _parse_time(self, time_str: str) -> Optional[datetime.time]:
        """解析時間字串"""
        try:
            # 支援 HH:MM:SS 和 HH:MM 格式
            if time_str.count(':') == 2:
                hour, minute, second = map(int, time_str.split(':'))
            elif time_str.count(':') == 1:
                hour, minute = map(int, time_str.split(':'))
                second = 0
            else:
                raise ValueError(f"無效的時間格式: {time_str}")

            # 修復：直接使用 time 類別
            from datetime import time
            return time(hour, minute, second)
        except Exception as e:
            self.logger.error(f"解析時間失敗 '{time_str}': {str(e)}")
            return None

    def start(self):
        """啟動自動連線管理器"""
        if self.is_running:
            return

        self.is_running = True
        self.logger.info("自動連線管理器已啟動")

        # 啟動自動連線
        if self.enable_auto_connect:
            self._start_auto_connect()

        # 啟動時間表檢查
        if self.auto_connect_mode == 'schedule' and self.schedule_times:
            self.schedule_timer.start(1000)  # 每秒檢查一次
            self.logger.info("時間表檢查已啟動")

        # 啟動自動結束檢查
        if self.enable_auto_shutdown:
            self.shutdown_timer.start(1000)  # 每秒檢查一次
            self.logger.info(f"自動結束檢查已啟動，結束時間: {self.shutdown_time_str}")

        self.status_changed.emit("自動管理器已啟動")

    def stop(self):
        """停止自動連線管理器"""
        if not self.is_running:
            return

        self.is_running = False

        # 停止所有定時器
        self.auto_connect_timer.stop()
        self.schedule_timer.stop()
        self.shutdown_timer.stop()

        self.logger.info("自動連線管理器已停止")
        self.status_changed.emit("自動管理器已停止")

    def _start_auto_connect(self):
        """啟動自動連線"""
        if self.auto_connect_mode == 'immediate':
            self.logger.info("立即執行自動連線")
            self.auto_connect_requested.emit()
            self._notify("自動連線", "立即連線已執行")

        elif self.auto_connect_mode == 'delay':
            delay_ms = int(self.auto_connect_delay * 1000)
            self.auto_connect_timer.start(delay_ms)
            self.logger.info(f"延遲 {self.auto_connect_delay} 秒後自動連線")
            self._notify("自動連線", f"將在 {self.auto_connect_delay} 秒後自動連線")

        elif self.auto_connect_mode == 'schedule':
            self.logger.info("時間表模式已啟用，等待排程時間")
            self._notify("自動連線", "時間表模式已啟用")

    def _handle_auto_connect(self):
        """處理自動連線"""
        self.auto_connect_timer.stop()
        self.logger.info("執行延遲自動連線")
        self.auto_connect_requested.emit()
        self._notify("自動連線", "延遲連線已執行")

    def _check_schedule(self):
        """檢查時間表"""
        if not self.schedule_times:
            return

        current_time = datetime.now().time()
        current_weekday = datetime.now().weekday()  # 0=Monday, 6=Sunday

        # 檢查每個時間段
        should_be_connected = False
        active_period = None

        for start_time, end_time in self.schedule_times:
            if self._is_time_in_range(current_time, start_time, end_time):
                should_be_connected = True
                active_period = f"{start_time} - {end_time}"
                break

        # 狀態變化處理
        if should_be_connected and self.current_schedule_state != 'connected':
            # 檢查是否防止週末起始連線
            if self.prevent_weekend_startup and current_weekday >= 5:  # Saturday=5, Sunday=6
                self.logger.info(f"週末時間，防止自動連線起始: {active_period}")
                return

            self.logger.info(f"時間表自動連線: {active_period}")
            self.auto_connect_requested.emit()
            self.current_schedule_state = 'connected'
            self._notify("自動連線", f"時間表連線: {active_period}")

        elif not should_be_connected and self.current_schedule_state == 'connected':
            self.logger.info("時間表自動斷線")
            self.auto_disconnect_requested.emit()
            self.current_schedule_state = 'disconnected'
            self._notify("自動斷線", "時間表斷線")

    def _is_time_in_range(self, current_time, start_time, end_time) -> bool:
        """檢查當前時間是否在指定範圍內"""
        if start_time <= end_time:
            # 同一天內的時間範圍
            return start_time <= current_time <= end_time
        else:
            # 跨日的時間範圍 (永遠支援)
            return current_time >= start_time or current_time <= end_time

    def _check_auto_shutdown(self):
        """檢查自動結束"""
        if not self.shutdown_time:
            return

        current_datetime = datetime.now()

        # 計算目標結束時間
        target_datetime = datetime.combine(current_datetime.date(), self.shutdown_time)

        # 如果目標時間已過，檢查是否在緩衝時間內
        if current_datetime >= target_datetime:
            time_diff = (current_datetime - target_datetime).total_seconds()

            if time_diff <= self.shutdown_buffer_seconds:
                # 在緩衝時間內，顯示警告並執行結束
                if not self.shutdown_warning_shown:
                    self._show_shutdown_warning()
                else:
                    # 警告已顯示，直接執行結束
                    self._execute_auto_shutdown()
            else:
                # 超過緩衝時間，重置狀態等待明天
                self.shutdown_warning_shown = False

    def _show_shutdown_warning(self):
        """顯示結束警告"""
        if self.shutdown_warning_shown:
            return

        self.shutdown_warning_shown = True
        warning_msg = f"程式將在 {self.shutdown_warning_seconds} 秒後自動結束"
        self.logger.warning(warning_msg)
        self._notify("自動結束警告", warning_msg)

    def _execute_auto_shutdown(self):
        """執行自動結束"""
        self.logger.info("執行自動結束程式")
        self._notify("自動結束", "程式即將自動結束")
        self.auto_shutdown_requested.emit()

    def _notify(self, title: str, message: str):
        """發送通知"""
        if not self.enable_notifications:
            return

        try:
            # 這裡可以實現系統通知
            # 目前只記錄到日誌
            self.logger.info(f"[通知] {title}: {message}")
            self.status_changed.emit(f"{title}: {message}")
        except Exception as e:
            self.logger.error(f"發送通知失敗: {str(e)}")



class AutoSettingsDialog(QDialog):
    """自動化設定對話框 - 非模態視窗"""

    # 信號定義
    settings_changed = Signal(dict)  # 設定變更信號

    def __init__(self, config: configparser.ConfigParser, parent=None):
        super().__init__(parent)
        self.config = config
        self.setWindowTitle("自動化功能設定")
        self.setModal(False)  # 設為非模態
        self.resize(500, 600)

        # 設定視窗屬性，確保不阻塞主程式
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)

        self.init_ui()
        self.load_current_settings()

    def init_ui(self):
        """初始化使用者介面"""
        layout = QVBoxLayout(self)

        # 建立分頁控制項
        tab_widget = QTabWidget()

        # 自動連線分頁
        auto_connect_tab = self.create_auto_connect_tab()
        tab_widget.addTab(auto_connect_tab, "自動連線")

        # 自動結束分頁
        auto_shutdown_tab = self.create_auto_shutdown_tab()
        tab_widget.addTab(auto_shutdown_tab, "自動結束")

        # 通知設定分頁
        notifications_tab = self.create_notifications_tab()
        tab_widget.addTab(notifications_tab, "通知設定")

        layout.addWidget(tab_widget)

        # 按鈕區域
        button_layout = QHBoxLayout()

        self.apply_btn = QPushButton("套用")
        self.apply_btn.clicked.connect(self.apply_settings)

        self.save_btn = QPushButton("儲存")
        self.save_btn.clicked.connect(self.save_settings)

        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_settings)

        self.close_btn = QPushButton("關閉")
        self.close_btn.clicked.connect(self.close)

        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)

        layout.addLayout(button_layout)

    def create_auto_connect_tab(self):
        """建立自動連線設定分頁"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基本設定群組
        basic_group = QGroupBox("基本設定")
        basic_layout = QFormLayout(basic_group)

        self.enable_auto_connect = QCheckBox("啟用自動連線")
        basic_layout.addRow(self.enable_auto_connect)

        self.auto_connect_mode = QComboBox()
        self.auto_connect_mode.addItems(["immediate", "delay", "schedule"])
        self.auto_connect_mode.setCurrentText("delay")
        basic_layout.addRow("連線模式:", self.auto_connect_mode)

        self.auto_connect_delay = QDoubleSpinBox()
        self.auto_connect_delay.setRange(0.1, 300.0)
        self.auto_connect_delay.setSuffix(" 秒")
        self.auto_connect_delay.setValue(30.0)
        basic_layout.addRow("延遲時間:", self.auto_connect_delay)

        layout.addWidget(basic_group)

        # 時間表設定群組
        schedule_group = QGroupBox("時間表設定")
        schedule_layout = QFormLayout(schedule_group)

        self.schedule_connect_times = QLineEdit()
        self.schedule_connect_times.setPlaceholderText("例如: 09:00:00-12:00:00;13:30:00-15:00:00")
        schedule_layout.addRow("連線時間表:", self.schedule_connect_times)

        # 跨日時間表已永遠啟用，移除設定選項

        self.prevent_weekend_startup = QCheckBox("防止週末起始連線")
        schedule_layout.addRow(self.prevent_weekend_startup)

        layout.addWidget(schedule_group)



        # 連接信號
        self.auto_connect_mode.currentTextChanged.connect(self.on_mode_changed)

        return widget

    def create_auto_shutdown_tab(self):
        """建立自動結束設定分頁"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 基本設定群組
        basic_group = QGroupBox("基本設定")
        basic_layout = QFormLayout(basic_group)

        self.enable_auto_shutdown = QCheckBox("啟用自動結束")
        basic_layout.addRow(self.enable_auto_shutdown)

        self.shutdown_time = QTimeEdit()
        self.shutdown_time.setDisplayFormat("HH:mm:ss")
        self.shutdown_time.setTime(QTime(17, 30, 0))
        basic_layout.addRow("結束時間:", self.shutdown_time)

        self.shutdown_buffer_seconds = QSpinBox()
        self.shutdown_buffer_seconds.setRange(0, 3600)
        self.shutdown_buffer_seconds.setSuffix(" 秒")
        self.shutdown_buffer_seconds.setValue(300)
        basic_layout.addRow("緩衝時間:", self.shutdown_buffer_seconds)

        self.shutdown_warning_seconds = QSpinBox()
        self.shutdown_warning_seconds.setRange(0, 600)
        self.shutdown_warning_seconds.setSuffix(" 秒")
        self.shutdown_warning_seconds.setValue(60)
        basic_layout.addRow("警告時間:", self.shutdown_warning_seconds)

        layout.addWidget(basic_group)

        # 結束行為群組
        behavior_group = QGroupBox("結束行為")
        behavior_layout = QFormLayout(behavior_group)

        self.force_shutdown = QCheckBox("強制結束 (不顯示確認對話框)")
        behavior_layout.addRow(self.force_shutdown)

        self.save_data_before_shutdown = QCheckBox("結束前保存資料")
        behavior_layout.addRow(self.save_data_before_shutdown)

        self.disconnect_before_shutdown = QCheckBox("結束前斷開連線")
        behavior_layout.addRow(self.disconnect_before_shutdown)

        self.cleanup_temp_files = QCheckBox("結束前清理暫存檔案")
        behavior_layout.addRow(self.cleanup_temp_files)

        layout.addWidget(behavior_group)

        return widget

    def create_notifications_tab(self):
        """建立通知設定分頁"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 通知設定群組
        notify_group = QGroupBox("通知設定")
        notify_layout = QFormLayout(notify_group)

        self.enable_system_notifications = QCheckBox("啟用系統通知")
        notify_layout.addRow(self.enable_system_notifications)

        self.enable_sound_notifications = QCheckBox("啟用聲音通知")
        notify_layout.addRow(self.enable_sound_notifications)

        layout.addWidget(notify_group)

        # 自動操作通知群組
        auto_notify_group = QGroupBox("自動操作通知")
        auto_notify_layout = QFormLayout(auto_notify_group)

        self.notify_auto_connect = QCheckBox("自動連線通知")
        auto_notify_layout.addRow(self.notify_auto_connect)

        self.notify_auto_disconnect = QCheckBox("自動斷線通知")
        auto_notify_layout.addRow(self.notify_auto_disconnect)

        self.notify_auto_shutdown = QCheckBox("自動結束通知")
        auto_notify_layout.addRow(self.notify_auto_shutdown)

        layout.addWidget(auto_notify_group)

        return widget

    def on_mode_changed(self, mode):
        """連線模式變更時的處理"""
        # 根據模式啟用/禁用相關控制項
        self.auto_connect_delay.setEnabled(mode == "delay")
        self.schedule_connect_times.setEnabled(mode == "schedule")
        # 跨日時間表已永遠啟用，移除設定控制
        self.prevent_weekend_startup.setEnabled(mode == "schedule")

    def load_current_settings(self):
        """載入當前設定"""
        try:
            # 自動連線設定
            if 'AutoConnect' in self.config:
                section = self.config['AutoConnect']
                self.enable_auto_connect.setChecked(section.getboolean('enable_auto_connect', False))
                self.auto_connect_mode.setCurrentText(section.get('auto_connect_mode', 'delay'))
                self.auto_connect_delay.setValue(section.getfloat('auto_connect_delay', 30.0))
                self.schedule_connect_times.setText(section.get('schedule_connect_times', ''))
                # 跨日時間表已永遠啟用，移除設定載入
                self.prevent_weekend_startup.setChecked(section.getboolean('prevent_weekend_startup', False))

            # 自動結束設定
            if 'AutoShutdown' in self.config:
                section = self.config['AutoShutdown']
                self.enable_auto_shutdown.setChecked(section.getboolean('enable_auto_shutdown', False))

                # 解析時間
                shutdown_time_str = section.get('shutdown_time', '17:30:00')
                try:
                    time_parts = shutdown_time_str.split(':')
                    if len(time_parts) >= 2:
                        hour = int(time_parts[0])
                        minute = int(time_parts[1])
                        second = int(time_parts[2]) if len(time_parts) > 2 else 0
                        self.shutdown_time.setTime(QTime(hour, minute, second))
                except:
                    self.shutdown_time.setTime(QTime(17, 30, 0))

                self.shutdown_buffer_seconds.setValue(section.getint('shutdown_buffer_seconds', 300))
                self.shutdown_warning_seconds.setValue(section.getint('shutdown_warning_seconds', 60))
                self.force_shutdown.setChecked(section.getboolean('force_shutdown', True))
                self.save_data_before_shutdown.setChecked(section.getboolean('save_data_before_shutdown', True))
                self.disconnect_before_shutdown.setChecked(section.getboolean('disconnect_before_shutdown', True))
                self.cleanup_temp_files.setChecked(section.getboolean('cleanup_temp_files', True))

            # 通知設定
            if 'Notifications' in self.config:
                section = self.config['Notifications']
                self.enable_system_notifications.setChecked(section.getboolean('enable_system_notifications', True))
                self.enable_sound_notifications.setChecked(section.getboolean('enable_sound_notifications', False))
                self.notify_auto_connect.setChecked(section.getboolean('notify_auto_connect', True))
                self.notify_auto_disconnect.setChecked(section.getboolean('notify_auto_disconnect', True))
                self.notify_auto_shutdown.setChecked(section.getboolean('notify_auto_shutdown', True))

            # 觸發模式變更處理
            self.on_mode_changed(self.auto_connect_mode.currentText())

        except Exception as e:
            QMessageBox.warning(self, "載入設定失敗", f"載入當前設定時發生錯誤:\n{str(e)}")

    def get_current_settings(self):
        """獲取當前設定值"""
        settings = {
            'AutoConnect': {
                'enable_auto_connect': self.enable_auto_connect.isChecked(),
                'auto_connect_mode': self.auto_connect_mode.currentText(),
                'auto_connect_delay': self.auto_connect_delay.value(),
                'schedule_connect_times': self.schedule_connect_times.text().strip(),
                # 跨日時間表已永遠啟用，移除設定儲存
                'prevent_weekend_startup': self.prevent_weekend_startup.isChecked()
            },
            'AutoShutdown': {
                'enable_auto_shutdown': self.enable_auto_shutdown.isChecked(),
                'shutdown_time': self.shutdown_time.time().toString("HH:mm:ss"),
                'shutdown_buffer_seconds': self.shutdown_buffer_seconds.value(),
                'shutdown_warning_seconds': self.shutdown_warning_seconds.value(),
                'force_shutdown': self.force_shutdown.isChecked(),
                'save_data_before_shutdown': self.save_data_before_shutdown.isChecked(),
                'disconnect_before_shutdown': self.disconnect_before_shutdown.isChecked(),
                'cleanup_temp_files': self.cleanup_temp_files.isChecked()
            },
            'Notifications': {
                'enable_system_notifications': self.enable_system_notifications.isChecked(),
                'enable_sound_notifications': self.enable_sound_notifications.isChecked(),
                'notify_auto_connect': self.notify_auto_connect.isChecked(),
                'notify_auto_disconnect': self.notify_auto_disconnect.isChecked(),
                'notify_auto_shutdown': self.notify_auto_shutdown.isChecked()
            }
        }
        return settings

    def apply_settings(self):
        """套用設定 (不儲存到檔案)"""
        try:
            settings = self.get_current_settings()
            self.settings_changed.emit(settings)
            QMessageBox.information(self, "套用成功", "設定已套用，但尚未儲存到檔案。")
        except Exception as e:
            QMessageBox.critical(self, "套用失敗", f"套用設定時發生錯誤:\n{str(e)}")

    def save_settings(self):
        """儲存設定到檔案"""
        try:
            settings = self.get_current_settings()

            # 更新設定檔
            for section_name, section_data in settings.items():
                if section_name not in self.config:
                    self.config.add_section(section_name)

                for key, value in section_data.items():
                    self.config.set(section_name, key, str(value))

            # 寫入檔案
            with open('config.ini', 'w', encoding='utf-8') as f:
                self.config.write(f)

            # 發送設定變更信號
            self.settings_changed.emit(settings)

            QMessageBox.information(self, "儲存成功", "設定已儲存到 config.ini 檔案。")

        except Exception as e:
            QMessageBox.critical(self, "儲存失敗", f"儲存設定時發生錯誤:\n{str(e)}")

    def reset_settings(self):
        """重置設定為預設值"""
        reply = QMessageBox.question(
            self,
            "重置確認",
            "確定要重置所有設定為預設值嗎？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 重置為預設值
            self.enable_auto_connect.setChecked(False)
            self.auto_connect_mode.setCurrentText("delay")
            self.auto_connect_delay.setValue(30.0)
            self.schedule_connect_times.setText("")
            # 跨日時間表已永遠啟用，移除重置設定
            self.prevent_weekend_startup.setChecked(False)

            self.enable_auto_shutdown.setChecked(False)
            self.shutdown_time.setTime(QTime(17, 30, 0))
            self.shutdown_buffer_seconds.setValue(300)
            self.shutdown_warning_seconds.setValue(60)
            self.force_shutdown.setChecked(True)
            self.save_data_before_shutdown.setChecked(True)
            self.disconnect_before_shutdown.setChecked(True)
            self.cleanup_temp_files.setChecked(True)

            self.enable_system_notifications.setChecked(True)
            self.enable_sound_notifications.setChecked(False)
            self.notify_auto_connect.setChecked(True)
            self.notify_auto_disconnect.setChecked(True)
            self.notify_auto_shutdown.setChecked(True)

            self.on_mode_changed(self.auto_connect_mode.currentText())

class DDEMonitor(QMainWindow):
    """DDE監控主類別
    負責管理整個DDE監控程式的運作，包括UI、DDE連接、資料處理等

    屬性:
        config (configparser.ConfigParser): 設定檔物件
        dde_client (DDEClient): DDE客戶端物件
        file_handler (DataFileHandler): 檔案處理物件
        items_data (Dict[str, ItemData]): 項目資料容器
        raw_data (deque): 原始資料佇列
        last_advise_time (float): 最後接收資料的時間戳
        time_newline_interval (float): 時間間隔換行設定
        enable_time_newline (bool): 是否啟用時間間隔換行
        enable_value_change_check (bool): 是否啟用值變化檢查
        value_change_check_item (str): 值變化檢查的項目名稱
        data_processor (DataProcessor): 資料處理器
        process_thread (QThread): 資料處理器線程
        auto_connect_manager (AutoConnectManager): 自動連線管理器
    """
    def __init__(self):
        super().__init__()
        self.setWindowTitle("DDE 監控程式 v6 (自動化版)")
        self.setGeometry(100, 100, 1200, 800)
        
        # 初始化設定
        self.config = None
        self.dde_client = None
        self.file_handler = DataFileHandler()
        
        # 資料容器
        self.items_data: Dict[str, ItemData] = {}  # 項目資料容器
        self.raw_data: deque = deque(maxlen=1000)  # 原始資料佇列
        
        # 計時器相關
        self.last_advise_time = None
        self.time_newline_interval = None
        self.enable_time_newline = None
        self.enable_value_change_check = None
        self.value_change_check_item = None
        
        # 初始化資料處理器
        self.data_processor = DataProcessor()
        self.process_thread = QThread()
        self.data_processor.moveToThread(self.process_thread)
        self.data_processor.data_processed.connect(self._process_advise_data)
        self.process_thread.started.connect(self.data_processor.process_data)

        # 初始化
        self.load_config()  # 先載入設定
        self.setup_logging()
        self.init_data_containers()  # 先初始化資料容器
        self.init_ui()  # 再初始化 UI
        self.file_handler.init_files(self.config, self.items_data)  # 最後初始化檔案

        # 初始化自動連線管理器
        self.init_auto_connect_manager()

        # 設置定時器
        self.timer = QTimer()
        self.timer.timeout.connect(self.check_time_interval)
        self.timer.start(100)  # 每100ms檢查一次

        # 啟動資料處理器線程
        self.process_thread.start()



        # 初始化快速開關和狀態顯示
        self.update_quick_switches()
        self.update_auto_status_display()
        
    def load_config(self):
        """載入設定檔
        從config.ini讀取所有必要的設定
        """
        try:
            self.config = configparser.ConfigParser()
            self.config.read('config.ini', encoding='utf-8')
            
            # 讀取時間換行設定
            self.enable_time_newline = self.config.getboolean('Table', 'enable_time_newline', fallback=True)
            self.time_newline_interval = self.config.getfloat('Table', 'time_newline_interval', fallback=0.800)
            
            # 讀取值變化檢查設定
            self.enable_value_change_check = self.config.getboolean('Table', 'enable_value_change_check', fallback=True)
            self.value_change_check_item = self.config.get('Table', 'value_change_check_items', fallback='')
            
        except Exception as e:
            logging.error(f"載入設定檔失敗: {str(e)}")
            raise
            
    def setup_logging(self):
        """設置日誌系統"""
        try:
            # 獲取當前日期
            now = datetime.now()
            date_str = now.strftime('%Y%m%d')
            
            # 從設定檔讀取日誌路徑
            log_file_path = self.config.get('OutputPath', 'log_file', fallback='./logs/{date}/dde_monitor.log')
            log_file = log_file_path.format(date=date_str)
            
            # 確保日誌目錄存在
            self.file_handler.ensure_directory(log_file)
            
            # 設置日誌格式
            formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] %(message)s',
                                       datefmt='%Y-%m-%d %H:%M:%S')
            
            # 設置檔案處理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setFormatter(formatter)
            file_handler.setLevel(logging.INFO)  # 改為 INFO 級別
            
            # 設置控制台處理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            console_handler.setLevel(logging.INFO)  # 改為 INFO 級別
            
            # 設置根日誌記錄器
            root_logger = logging.getLogger()
            root_logger.setLevel(logging.INFO)  # 改為 INFO 級別
            
            # 移除現有的處理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
                
            # 添加新的處理器
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            
            # 設置程式本身的日誌記錄器
            self.logger = logging.getLogger('Monitor')
            self.logger.setLevel(logging.INFO)  # 改為 INFO 級別
            
            self.logger.info(f"日誌檔案初始化完成: {log_file}")
            
        except Exception as e:
            print(f"設置日誌失敗: {str(e)}")
            raise
            
    def init_ui(self):
        """初始化使用者介面"""
        # 主視窗佈局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        
        # DDE連接區域
        dde_conn_layout = QHBoxLayout()
        
        # 服務和主題輸入框
        self.service_edit = QLineEdit(self.config.get('DDE', 'service', fallback=''))
        self.topic_edit = QLineEdit(self.config.get('DDE', 'topic', fallback=''))
        
        # 狀態標籤
        self.status_label = QLabel("尚未連線")

        # 自動狀態標籤
        self.auto_status_label = QLabel("自動管理器: 啟動中...")
        self.auto_status_label.setStyleSheet("color: blue; font-weight: bold;")

        # 一鍵連線/斷線按鈕（保留原有功能）
        self.connect_btn = QPushButton("連線")
        self.connect_btn.clicked.connect(self.toggle_connection)
        
        # 新增：服務主題連線/斷線按鈕
        self.service_connect_btn = QPushButton("服務連線")
        self.service_connect_btn.clicked.connect(self.toggle_service_connection)
        
        # 新增：測試項目按鈕
        self.test_items_btn = QPushButton("測試項目")
        self.test_items_btn.clicked.connect(self.test_items)
        self.test_items_btn.setEnabled(False)  # 初始狀態禁用
        
        # 新增：訂閱/取消訂閱按鈕
        self.subscribe_btn = QPushButton("訂閱項目")
        self.subscribe_btn.clicked.connect(self.toggle_subscription)
        self.subscribe_btn.setEnabled(False)  # 初始狀態禁用

        # 新增：自動化設定按鈕
        self.auto_settings_btn = QPushButton("自動化設定")
        self.auto_settings_btn.clicked.connect(self.show_auto_settings)
        self.auto_settings_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        # 添加元件到佈局
        dde_conn_layout.addWidget(QLabel("DDE服務:"))
        dde_conn_layout.addWidget(self.service_edit)
        dde_conn_layout.addWidget(QLabel("DDE主題:"))
        dde_conn_layout.addWidget(self.topic_edit)
        dde_conn_layout.addWidget(self.status_label)
        dde_conn_layout.addWidget(self.service_connect_btn)
        dde_conn_layout.addWidget(self.test_items_btn)
        dde_conn_layout.addWidget(self.subscribe_btn)
        dde_conn_layout.addWidget(self.connect_btn)
        dde_conn_layout.addWidget(self.auto_settings_btn)

        # 自動狀態顯示區域
        auto_status_layout = QHBoxLayout()
        auto_status_layout.addWidget(QLabel("自動狀態:"))
        auto_status_layout.addWidget(self.auto_status_label)

        # 快速開關區域
        self.quick_auto_connect_cb = QCheckBox("自動連線")
        self.quick_auto_connect_cb.stateChanged.connect(self.on_quick_auto_connect_changed)

        self.quick_auto_shutdown_cb = QCheckBox("自動結束")
        self.quick_auto_shutdown_cb.stateChanged.connect(self.on_quick_auto_shutdown_changed)

        auto_status_layout.addWidget(self.quick_auto_connect_cb)
        auto_status_layout.addWidget(self.quick_auto_shutdown_cb)
        auto_status_layout.addStretch()  # 添加彈性空間
        
        main_layout.addLayout(dde_conn_layout)
        main_layout.addLayout(auto_status_layout)

        # 項目表格
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["項目名稱", "DDE項目", "值", "更新時間", "狀態"])
        main_layout.addWidget(self.items_table)
        
        # 日誌區域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        main_layout.addWidget(self.log_text)
        
        # 設置定時器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(100)  # 每100ms更新一次
        
    def init_data_containers(self):
        """初始化資料容器
        從設定檔讀取項目設定並初始化資料容器
        """
        try:
            # 讀取項目設定
            for i in range(1, 100):  # 假設最多100個項目
                name_key = f'item{i}_name'
                code_key = f'item{i}_code'
                
                if name_key in self.config['Items'] and code_key in self.config['Items']:
                    name = self.config['Items'][name_key]
                    code = self.config['Items'][code_key]
                    self.items_data[code] = ItemData(name=name, code=code)
            
            # 更新項目表格
            if hasattr(self, 'items_table'):  # 確保 items_table 已初始化
                self.update_items_table()
            
        except Exception as e:
            logging.error(f"初始化資料容器失敗: {str(e)}")
            raise

    def init_auto_connect_manager(self):
        """初始化自動連線管理器"""
        try:
            self.auto_connect_manager = AutoConnectManager(self.config, self.logger)
            self.auto_connect_manager.auto_connect_requested.connect(self.auto_connect)
            self.auto_connect_manager.auto_disconnect_requested.connect(self.auto_disconnect)
            self.auto_connect_manager.auto_shutdown_requested.connect(self.auto_shutdown)
            self.auto_connect_manager.status_changed.connect(self.update_auto_status)
            self.auto_connect_manager.start()

        except Exception as e:
            self.logger.error(f"初始化自動連線管理器失敗: {str(e)}")
            raise

    def auto_connect(self):
        """自動連線方法"""
        try:
            self.logger.info("執行自動連線")

            # 如果已經連線，先斷開
            if self.dde_client and self.dde_client.is_connected():
                self.logger.info("檢測到已有連線，先斷開舊連線")
                self.disconnect_service()
                time.sleep(1)  # 等待斷開完成

            # 執行連線
            self.toggle_connection()

            # 以下內容註解掉，因為已經在 self.toggle_connection()中處理過
            """
            # ---------
            # 如果連線成功，執行測試和訂閱
            if self.dde_client and self.dde_client.is_connected():
                self.logger.info("自動連線成功，開始測試項目")

                # 確保在主線程中執行測試
                QTimer.singleShot(100, self.safe_test_items)


            else:
                self.logger.error("自動連線失敗")
            """

        except Exception as e:
            self.logger.error(f"自動連線過程發生錯誤: {str(e)}")

    def safe_test_items(self):
        """安全的項目測試方法"""
        try:
            if self.dde_client and self.dde_client.is_connected():
                self.test_items()
                # 等待測試完成後自動訂閱
                QTimer.singleShot(2000, self.auto_subscribe)
            else:
                self.logger.warning("連線已斷開，無法執行項目測試")
        except Exception as e:
            self.logger.error(f"安全項目測試失敗: {str(e)}")

    def auto_subscribe(self):
        """自動訂閱方法"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                self.logger.warning("自動訂閱失敗：未連線")
                return

            # 確保 items_data 已初始化
            if not hasattr(self, 'items_data') or not self.items_data:
                self.logger.warning("自動訂閱失敗：items_data 未初始化")
                return

            # 檢查是否有已測試的項目
            tested_items = [item for item in self.items_data.values() if item.status == "已測試"]
            if tested_items:
                self.logger.info(f"開始自動訂閱 {len(tested_items)} 個項目")
                # 使用 QTimer 確保在主線程中執行
                QTimer.singleShot(100, self.safe_toggle_subscription)
            else:
                self.logger.warning("沒有已測試的項目可供訂閱")

        except Exception as e:
            self.logger.error(f"自動訂閱過程發生錯誤: {str(e)}")

    def safe_toggle_subscription(self):
        """安全的訂閱切換方法"""
        try:
            if self.dde_client and self.dde_client.is_connected():
                self.toggle_subscription()
            else:
                self.logger.warning("連線已斷開，無法執行訂閱操作")
        except Exception as e:
            self.logger.error(f"安全訂閱切換失敗: {str(e)}")

    def auto_disconnect(self):
        """自動斷線方法"""
        try:
            self.logger.info("執行自動斷線")

            if self.dde_client and self.dde_client.is_connected():
                # 先取消所有訂閱
                subscribed_items = [item for item in self.items_data.values() if item.status == "已訂閱"]
                if subscribed_items:
                    self.logger.info(f"取消 {len(subscribed_items)} 個項目的訂閱")
                    self.toggle_subscription()
                    time.sleep(1)  # 等待取消訂閱完成

                # 斷開連線
                self.disconnect_service()
                self.logger.info("自動斷線完成")
            else:
                self.logger.info("未連線，無需斷線")

        except Exception as e:
            self.logger.error(f"自動斷線過程發生錯誤: {str(e)}")

    def auto_shutdown(self):
        """自動結束程式方法"""
        try:
            self.logger.info("開始執行自動結束程式")

            # 保存資料
            if self.auto_connect_manager.save_data_before_shutdown:
                self.logger.info("結束前保存資料")
                self.save_current_data()

            # 斷開連線
            if self.auto_connect_manager.disconnect_before_shutdown:
                self.logger.info("結束前斷開連線")
                self.auto_disconnect()
                time.sleep(2)  # 等待斷開完成

            # 清理暫存檔案
            if self.auto_connect_manager.cleanup_temp_files:
                self.logger.info("結束前清理暫存檔案")
                self.cleanup_temp_files()

            # 停止自動管理器
            self.auto_connect_manager.stop()

            self.logger.info("自動結束程式執行完成，程式即將關閉")

            # 強制結束或顯示確認對話框
            if self.auto_connect_manager.force_shutdown:
                QApplication.quit()
            else:
                reply = QMessageBox.question(
                    self,
                    "自動結束確認",
                    "程式已到達預設結束時間，是否要結束程式？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                if reply == QMessageBox.Yes:
                    QApplication.quit()

        except Exception as e:
            self.logger.error(f"自動結束程式過程發生錯誤: {str(e)}")
            # 即使發生錯誤也要嘗試結束
            if self.auto_connect_manager.force_shutdown:
                QApplication.quit()

    def save_current_data(self):
        """保存當前資料"""
        try:
            # 如果有未完成的資料行，保存它
            if self.raw_data:
                current_row = self.raw_data[0]
                if current_row.values:
                    # 補齊缺失資料
                    self.fill_missing_data(current_row)
                    # 保存完整資料行
                    self.file_handler.save_row(current_row, is_complete=True)
                    self.logger.info("已保存當前資料行")
        except Exception as e:
            self.logger.error(f"保存當前資料失敗: {str(e)}")

    def cleanup_temp_files(self):
        """清理暫存檔案"""
        try:
            import glob

            # 清理暫存檔案
            temp_patterns = [
                "*.tmp",
                "*.temp",
                "*.lock",
                "*.pid"
            ]

            for pattern in temp_patterns:
                for file_path in glob.glob(pattern):
                    try:
                        os.remove(file_path)
                        self.logger.debug(f"已刪除暫存檔案: {file_path}")
                    except Exception as e:
                        self.logger.warning(f"刪除暫存檔案失敗 {file_path}: {str(e)}")

        except Exception as e:
            self.logger.error(f"清理暫存檔案失敗: {str(e)}")

    def update_auto_status(self, status: str):
        """更新自動狀態顯示"""
        try:
            self.auto_status_label.setText(status)

            # 根據狀態設置不同顏色
            if "錯誤" in status or "失敗" in status:
                self.auto_status_label.setStyleSheet("color: red; font-weight: bold;")
            elif "警告" in status:
                self.auto_status_label.setStyleSheet("color: orange; font-weight: bold;")
            elif "成功" in status or "完成" in status:
                self.auto_status_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.auto_status_label.setStyleSheet("color: blue; font-weight: bold;")

        except Exception as e:
            self.logger.error(f"更新自動狀態顯示失敗: {str(e)}")

    def show_auto_settings(self):
        """顯示自動化設定對話框"""
        try:
            # 檢查是否已經有對話框開啟
            if hasattr(self, 'auto_settings_dialog') and self.auto_settings_dialog.isVisible():
                # 如果已經開啟，就將其帶到前面
                self.auto_settings_dialog.raise_()
                self.auto_settings_dialog.activateWindow()
                return

            # 創建新的對話框
            self.auto_settings_dialog = AutoSettingsDialog(self.config, self)

            # 連接設定變更信號
            self.auto_settings_dialog.settings_changed.connect(self.on_auto_settings_changed)

            # 顯示對話框 (非模態)
            self.auto_settings_dialog.show()

        except Exception as e:
            self.logger.error(f"顯示自動化設定對話框失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"無法開啟設定對話框: {str(e)}")

    def on_auto_settings_changed(self, settings):
        """處理自動化設定變更"""
        try:
            self.logger.info("自動化設定已變更，重新載入設定")

            # 重新載入設定
            self.load_config()

            # 重新載入自動管理器設定 (不停止運行)
            if hasattr(self, 'auto_connect_manager'):
                self.auto_connect_manager.load_settings(self.config)

            # 更新快速開關狀態
            self.update_quick_switches()

            # 更新自動狀態顯示
            self.update_auto_status_display()

        except Exception as e:
            self.logger.error(f"處理自動化設定變更失敗: {str(e)}")

    def on_quick_auto_connect_changed(self, state):
        """快速自動連線開關變更處理"""
        try:
            # 阻塞信號避免循環觸發
            self.quick_auto_connect_cb.blockSignals(True)

            enabled = state == Qt.Checked

            # 更新設定檔
            if 'AutoConnect' not in self.config:
                self.config.add_section('AutoConnect')

            self.config.set('AutoConnect', 'enable_auto_connect', str(enabled))

            # 儲存設定檔
            with open('config.ini', 'w', encoding='utf-8') as f:
                self.config.write(f)

            # 重新載入自動管理器設定
            if hasattr(self, 'auto_connect_manager'):
                self.auto_connect_manager.load_settings(self.config)

            # 更新狀態顯示
            self.update_auto_status_display()

            self.logger.info(f"自動連線功能已{'啟用' if enabled else '停用'}")

        except Exception as e:
            self.logger.error(f"快速自動連線開關變更失敗: {str(e)}")
        finally:
            # 恢復信號
            self.quick_auto_connect_cb.blockSignals(False)

    def on_quick_auto_shutdown_changed(self, state):
        """快速自動結束開關變更處理"""
        try:
            # 阻塞信號避免循環觸發
            self.quick_auto_shutdown_cb.blockSignals(True)

            enabled = state == Qt.Checked

            # 更新設定檔
            if 'AutoShutdown' not in self.config:
                self.config.add_section('AutoShutdown')

            self.config.set('AutoShutdown', 'enable_auto_shutdown', str(enabled))

            # 儲存設定檔
            with open('config.ini', 'w', encoding='utf-8') as f:
                self.config.write(f)

            # 重新載入自動管理器設定
            if hasattr(self, 'auto_connect_manager'):
                self.auto_connect_manager.load_settings(self.config)

            # 更新狀態顯示
            self.update_auto_status_display()

            self.logger.info(f"自動結束功能已{'啟用' if enabled else '停用'}")

        except Exception as e:
            self.logger.error(f"快速自動結束開關變更失敗: {str(e)}")
        finally:
            # 恢復信號
            self.quick_auto_shutdown_cb.blockSignals(False)

    def update_quick_switches(self):
        """更新快速開關狀態"""
        try:
            # 阻塞信號避免觸發變更事件
            self.quick_auto_connect_cb.blockSignals(True)
            self.quick_auto_shutdown_cb.blockSignals(True)

            # 更新自動連線開關
            auto_connect_enabled = False
            if 'AutoConnect' in self.config:
                auto_connect_enabled = self.config.getboolean('AutoConnect', 'enable_auto_connect', fallback=False)
            self.quick_auto_connect_cb.setChecked(auto_connect_enabled)

            # 更新自動結束開關
            auto_shutdown_enabled = False
            if 'AutoShutdown' in self.config:
                auto_shutdown_enabled = self.config.getboolean('AutoShutdown', 'enable_auto_shutdown', fallback=False)
            self.quick_auto_shutdown_cb.setChecked(auto_shutdown_enabled)

        except Exception as e:
            self.logger.error(f"更新快速開關狀態失敗: {str(e)}")
        finally:
            # 恢復信號
            self.quick_auto_connect_cb.blockSignals(False)
            self.quick_auto_shutdown_cb.blockSignals(False)

    def update_auto_status_display(self):
        """更新自動狀態顯示"""
        try:
            status_parts = []

            # 檢查自動連線狀態
            if 'AutoConnect' in self.config and self.config.getboolean('AutoConnect', 'enable_auto_connect', fallback=False):
                mode = self.config.get('AutoConnect', 'auto_connect_mode', fallback='delay')
                if mode == 'immediate':
                    status_parts.append("🟢 立即連線")
                elif mode == 'delay':
                    delay = self.config.getfloat('AutoConnect', 'auto_connect_delay', fallback=30.0)
                    status_parts.append(f"🟠 延遲連線({delay}秒)")
                elif mode == 'schedule':
                    status_parts.append("🔵 時間表連線")
            else:
                status_parts.append("⚫ 手動連線")

            # 檢查自動結束狀態
            if 'AutoShutdown' in self.config and self.config.getboolean('AutoShutdown', 'enable_auto_shutdown', fallback=False):
                shutdown_time = self.config.get('AutoShutdown', 'shutdown_time', fallback='17:30:00')
                status_parts.append(f"🟢 自動結束({shutdown_time})")
            else:
                status_parts.append("⚫ 手動結束")



            status_text = " | ".join(status_parts)
            self.update_auto_status(status_text)

        except Exception as e:
            self.logger.error(f"更新自動狀態顯示失敗: {str(e)}")
            self.update_auto_status("❌ 狀態更新失敗")

    def toggle_service_connection(self):
        """切換服務主題連接狀態"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                self.connect_service()
            else:
                self.disconnect_service()
        except Exception as e:
            self.logger.error(f"切換服務連接狀態失敗: {str(e)}")
            
    def connect_service(self):
        """連接 DDE 服務"""
        try:
            service = self.service_edit.text()
            topic = self.topic_edit.text()
            
            if not service or not topic:
                QMessageBox.warning(self, "警告", "請輸入服務名稱和主題")
                return
                
            # 從設定檔讀取 disconnect_on_exit 設定
            disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
            
            # 建立 DDE 客戶端，傳入 disconnect_on_exit 參數
            self.dde_client = DDEClient(service, topic, disconnect_on_exit=disconnect_on_exit)
            self.dde_client.connect()
            
            self.status_label.setText("已連線")
            self.service_connect_btn.setText("斷線")
            self.test_items_btn.setEnabled(True)
            self.logger.info(f"已連接到 DDE 服務: {service}.{topic}")


            
        except Exception as e:
            self.logger.error(f"連接 DDE 服務失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"連接 DDE 服務失敗: {str(e)}")
            
    def disconnect_service(self):
        """斷開DDE服務連接"""
        try:
            if self.dde_client:
                # 根據設定決定是否終止 DDE
                disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
                self.dde_client.disconnect(terminate_dde=disconnect_on_exit)
                self.dde_client = None
                
            self.status_label.setText("服務已斷線")
            self.service_connect_btn.setText("服務連線")
            self.test_items_btn.setEnabled(False)  # 禁用測試按鈕
            self.subscribe_btn.setEnabled(False)  # 禁用訂閱按鈕
            self.subscribe_btn.setText("訂閱項目")  # 重置訂閱按鈕文字
            self.logger.info("已斷開DDE服務連接")


            
        except Exception as e:
            self.logger.error(f"斷開DDE服務連接失敗: {str(e)}")
            
    def test_items(self):
        """測試所有項目"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                QMessageBox.warning(self, "警告", "請先連接DDE服務")
                return
                
            # 測試所有項目
            for code, item_data in self.items_data.items():
                value = self.dde_client.request(code)
                if value is not None:
                    item_data.value = value
                    item_data.update_time = datetime.now()
                    item_data.status = "已測試"
                else:
                    item_data.status = "測試失敗"
                    
            self.update_items_table()
            self.subscribe_btn.setEnabled(True)  # 啟用訂閱按鈕
            self.logger.info("項目測試完成")
            
        except Exception as e:
            self.logger.error(f"測試項目失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"測試項目失敗: {str(e)}")
            
    def toggle_subscription(self):
        """切換項目訂閱狀態"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                QMessageBox.warning(self, "警告", "請先連接DDE服務")
                return
                
            # 檢查當前訂閱狀態
            is_subscribed = any(item_data.status == "已訂閱" for item_data in self.items_data.values())
            
            if is_subscribed:
                # 取消訂閱所有項目
                for code, item_data in self.items_data.items():
                    if item_data.status == "已訂閱":
                        self.dde_client.unadvise(code)
                        item_data.status = "已測試"
                        
                self.subscribe_btn.setText("訂閱項目")
                self.logger.info("已取消所有項目訂閱")
            else:
                # 訂閱所有項目
                for code, item_data in self.items_data.items():
                    if item_data.status == "已測試":
                        if self.dde_client.advise(code, self.on_advise_data):
                            item_data.status = "已訂閱"
                        else:
                            item_data.status = "訂閱失敗"
                            
                self.subscribe_btn.setText("取消訂閱")
                self.logger.info("已訂閱所有項目")
                
            self.update_items_table()
            
        except Exception as e:
            self.logger.error(f"切換訂閱狀態失敗: {str(e)}")
            QMessageBox.critical(self, "錯誤", f"切換訂閱狀態失敗: {str(e)}")
            
    def toggle_connection(self):
        """一鍵連線/斷線（保留原有功能）"""
        try:
            if not self.dde_client or not self.dde_client.is_connected():
                self.connect_dde()
            else:
                self.disconnect_dde()
        except Exception as e:
            self.logger.error(f"切換連接狀態失敗: {str(e)}")
            
    def connect_dde(self):
        """一鍵連線（保留原有功能）"""
        try:
            service = self.service_edit.text()
            topic = self.topic_edit.text()
            
            if not service or not topic:
                QMessageBox.warning(self, "警告", "請輸入服務和主題")
                return
                
            self.dde_client = DDEClient(service, topic)
            self.dde_client.connect()
            
            # 測試並訂閱所有項目
            self.test_and_subscribe_items()
            
            self.status_label.setText("已連線")
            self.connect_btn.setText("斷線")
            self.logger.info(f"成功連接到DDE服務: {service}, 主題: {topic}")
            
        except Exception as e:
            self.logger.error(f"連接DDE失敗: {str(e)}")
            self.status_label.setText("連線失敗")
            QMessageBox.critical(self, "錯誤", f"連接DDE失敗: {str(e)}")
            
    def disconnect_dde(self):
        """一鍵斷線（保留原有功能）"""
        try:
            if self.dde_client:
                # 根據設定決定是否終止 DDE
                disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
                self.dde_client.disconnect(terminate_dde=disconnect_on_exit)
                self.dde_client = None
                
            self.status_label.setText("尚未連線")
            self.connect_btn.setText("連線")
            self.logger.info("已斷開DDE連接")
            
        except Exception as e:
            self.logger.error(f"斷開DDE連接失敗: {str(e)}")
            
    def test_and_subscribe_items(self):
        """測試並訂閱所有項目
        先測試所有項目的可訪問性，再進行訂閱
        """
        try:
            # 1. 先測試所有項目
            for code, item_data in self.items_data.items():
                # 測試項目
                value = self.dde_client.request(code)
                if value is not None:
                    item_data.value = value
                    item_data.update_time = datetime.now()
                    item_data.status = "已測試"
                else:
                    item_data.status = "測試失敗"
                    
            # 更新表格顯示測試結果
            self.update_items_table()
            
            # 2. 再訂閱所有項目
            for code, item_data in self.items_data.items():
                if item_data.status == "已測試":
                    # 訂閱項目
                    if self.dde_client.advise(code, self.on_advise_data):
                        item_data.status = "已訂閱"
                    else:
                        item_data.status = "訂閱失敗"
                        
            # 更新表格顯示訂閱結果
            self.update_items_table()
            
        except Exception as e:
            logging.error(f"測試並訂閱項目失敗: {str(e)}")
            raise
            
    def on_advise_data(self, item: str, value: str):
        """處理DDE資料更新"""
        try:
            self.logger.info(f"[回調] 收到DDE數據更新: {item} = {value}")
            # 將資料加入處理佇列
            self.data_processor.add_data(item, value)
        except Exception as e:
            self.logger.error(f"加入資料到佇列失敗: {str(e)}")
            
    def _process_advise_data(self, item: str, value: str):
        """實際處理資料的方法"""
        try:
            self.logger.info(f"[處理] 開始處理數據: {item} = {value}")
            # 1. 檢查是否需要項目重複換行
            if self.check_item_repeat_newline(item, value):
                return

            # 2. 更新原始資料
            self.update_raw_data(item, value)

            # 3. 更新項目資料
            self.update_items_data(item, value)

            # 4. 更新最後接收時間
            self.last_advise_time = time.time()

        except Exception as e:
            self.logger.error(f"處理advise數據失敗: {str(e)}")
            
    def check_time_interval(self):
        """檢查時間間隔
        定期檢查是否需要換行
        """
        if not self.enable_time_newline:
            return
            
        now = time.time()
        
        if (self.last_advise_time is not None and 
            now - self.last_advise_time >= self.time_newline_interval):
            
            self.logger.debug("check_time_interval() - 時間間隔已到，開始換行")
            self.check_time_interval_newline()
            self.last_advise_time = now
            
    def check_time_interval_newline(self):
        """檢查是否需要時間間隔換行
        根據時間間隔檢查是否需要換行
        """
        if not self.enable_time_newline:
            self.logger.debug("check_time_interval_newline() - 未啟用時間間隔換行")
            return
            
        if not self.raw_data:
            self.logger.debug("check_time_interval_newline() - 原始資料佇列為空")
            return
            
        current_row = self.raw_data[0]
        
        # 檢查行是否有資料
        if not current_row.values:
            self.logger.debug("check_time_interval_newline() - 當前行沒有資料")
            return
            
        # 檢查並補齊缺失資料
        if self.has_missing_data(current_row):
            self.logger.debug("check_time_interval_newline() - 當前行缺少資料，開始補齊")
            self.fill_missing_data(current_row)
            
        # 如果啟用了值變化檢查，則檢查值是否有變化
        if self.enable_value_change_check:
            self.logger.debug(f"check_time_interval_newline() - raw_data 長度: {len(self.raw_data)}")
            has_changed = self.check_value_change(current_row)
            self.logger.debug(f"check_time_interval_newline() - 值變化檢查結果: {has_changed}")
            
            if has_changed:
                self.logger.debug("check_time_interval_newline() - 值有變化，儲存完整資料行")
                # 儲存完整資料行
                self.file_handler.save_row(current_row, is_complete=True)
                # 建立新行
                self.create_new_row()
            else:
                self.logger.debug("check_time_interval_newline() - 值未變化，不儲存資料行")
                # 值未變化，不儲存資料行，但建立新行
                self.create_new_row()
        else:
            # 未啟用值變化檢查，直接儲存資料行
            self.logger.debug("check_time_interval_newline() - 未啟用值變化檢查，直接儲存資料行")
            self.file_handler.save_row(current_row, is_complete=True)
            self.create_new_row()
            
    def check_item_repeat_newline(self, item: str, value: str) -> bool:
        """檢查是否需要項目重複換行
        當收到重複項目的資料時，檢查是否需要換行
        
        參數:
            item (str): 項目代碼
            value (str): 項目值
            
        回傳:
            bool: 是否需要換行
        """
        if not self.raw_data:
            self.logger.debug("check_item_repeat_newline() - 原始資料佇列為空")
            return False
            
        current_row = self.raw_data[0]
        
        # 檢查項目是否已存在於當前行
        if item in current_row.values:
            self.logger.debug(f"check_item_repeat_newline() - 項目 {item} 已存在於當前行")
            
            # 補齊缺失資料
            if self.has_missing_data(current_row):
                self.logger.debug("check_item_repeat_newline() - 當前行缺少資料，開始補齊")
                self.fill_missing_data(current_row)
                
            # 如果啟用了值變化檢查，則檢查值是否有變化
            if self.enable_value_change_check:
                self.logger.debug(f"check_item_repeat_newline() - raw_data 長度: {len(self.raw_data)}")
                has_changed = self.check_value_change(current_row)
                self.logger.debug(f"check_item_repeat_newline() - 值變化檢查結果: {has_changed}")
                
                if has_changed:
                    self.logger.debug("check_item_repeat_newline() - 值有變化，儲存完整資料行")
                    # 儲存完整資料行
                    self.file_handler.save_row(current_row, is_complete=True)
                    # 建立新行
                    self.create_new_row()
                    # 將收到的項目值填入新行
                    new_row = self.raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                    return True
                else:
                    self.logger.debug("check_item_repeat_newline() - 值未變化，不儲存資料行")
                    # 值未變化，不儲存資料行，但建立新行
                    self.create_new_row()
                    # 將收到的項目值填入新行
                    new_row = self.raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                    return True
            else:
                # 未啟用值變化檢查，直接儲存資料行
                self.logger.debug("check_item_repeat_newline() - 未啟用值變化檢查，直接儲存資料行")
                self.file_handler.save_row(current_row, is_complete=True)
                self.create_new_row()
                new_row = self.raw_data[0]
                new_row.values[item] = value
                new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                self.logger.debug(f"check_item_repeat_newline() - 已建立新行，並將項目 {item} 填入新行")
                return True
                
        self.logger.debug(f"check_item_repeat_newline() - 項目 {item} 不存在於當前行")
        return False
        
    def update_raw_data(self, item: str, value: str):
        """更新原始資料
        更新原始資料佇列中的資料
        
        參數:
            item (str): 項目代碼
            value (str): 項目值
        """
        try:
            self.logger.debug(f"update_raw_data() - 更新原始資料: {item} = {value}")
            now = datetime.now()
            current_row = None
            
            # 檢查是否需要建立新行
            if not self.raw_data:
                self.logger.debug("update_raw_data() - 建立新行")
                current_row = RawDataRow(
                    receive_date=now.strftime("%Y-%m-%d"),
                    receive_time=now.strftime("%H:%M:%S.%f"),
                    values={}
                )
                self.raw_data.appendleft(current_row)
            else:
                current_row = self.raw_data[0]
                
            # 更新值
            current_row.values[item] = value
            current_row.receive_date = now.strftime("%Y-%m-%d")
            current_row.receive_time = now.strftime("%H:%M:%S.%f")
            
            # 儲存原始資料行
            self.file_handler.save_row(current_row, is_complete=False)
            
            self.logger.debug(f"update_raw_data() - 更新後的值: {current_row.values}")
            
        except Exception as e:
            self.logger.error(f"更新原始資料失敗: {str(e)}")
            raise
            
    def update_items_data(self, item: str, value: str):
        """更新項目資料
        更新項目資料容器中的資料
        
        參數:
            item (str): 項目代碼
            value (str): 項目值
        """
        if item in self.items_data:
            self.logger.debug(f"update_items_data() - 更新項目資料: {item} = {value}")
            self.items_data[item].value = value
            self.items_data[item].update_time = datetime.now()
            self.items_data[item].status = "已訂閱"
            self.update_items_table()
            
    def has_missing_data(self, row: RawDataRow) -> bool:
        """檢查是否有缺失資料
        檢查資料行是否缺少任何項目的資料
        
        參數:
            row (RawDataRow): 要檢查的資料行
            
        回傳:
            bool: 是否有缺失資料
        """
        has_missing = len(row.values) < len(self.items_data)
        self.logger.debug(f"has_missing_data() - 檢查缺失資料: {has_missing}")
        return has_missing
        
    def fill_missing_data(self, row: RawDataRow):
        """補齊缺失資料
        使用項目資料容器中的最新值補齊缺失資料
        
        參數:
            row (RawDataRow): 要補齊的資料行
        """
        try:
            for code, item_data in self.items_data.items():
                if code not in row.values and item_data.value is not None:
                    row.values[code] = item_data.value
                    self.logger.debug(f"fill_missing_data() - 補齊缺失資料: {code} = {item_data.value}")
                    
        except Exception as e:
            logging.error(f"補齊缺失資料失敗: {str(e)}")
            raise
            
    def check_value_change(self, current_row: RawDataRow) -> bool:
        """檢查值變化
        根據設定檢查指定項目的值是否發生變化
        
        參數:
            current_row (RawDataRow): 當前的資料行
            
        回傳:
            bool: 值是否發生變化
        """
        # 如果未啟用值變化檢查，直接返回 True
        if not self.enable_value_change_check:
            self.logger.debug("check_value_change() - 未啟用值變化檢查，返回 True")
            return True
            
        # 獲取檢查模式
        check_mode = self.config.get('Table', 'value_change_check_mode', fallback='single')
        self.logger.debug(f"check_value_change() - 檢查模式: {check_mode}")
        
        # 如果沒有前一行，直接返回 True
        if len(self.raw_data) <= 1:
            self.logger.debug("check_value_change() - 沒有前一行資料，這是第一筆資料，返回 True")
            return True
            
        previous_row = self.raw_data[1]
        
        # 根據不同模式進行檢查
        if check_mode == 'single':
            # 單一項目檢查
            check_item_name = self.config.get('Table', 'value_change_check_items', fallback='')
            if not check_item_name:
                self.logger.debug("check_value_change() - 未指定檢查項目，返回 True")
                return True
                
            # 獲取檢查項目的代碼
            check_item_code = None
            for code, item_data in self.items_data.items():
                if item_data.name == check_item_name:
                    check_item_code = code
                    break
                    
            if not check_item_code:
                self.logger.debug(f"check_value_change() - 找不到檢查項目 {check_item_name} 的代碼")
                return True
                
            # 檢查值是否變化
            current_value = current_row.values.get(check_item_code, "")
            previous_value = previous_row.values.get(check_item_code, "")
            has_changed = current_value != previous_value
            
            self.logger.debug(f"check_value_change() - 單一項目檢查:")
            self.logger.debug(f"  項目名稱: {check_item_name}")
            self.logger.debug(f"  項目代碼: {check_item_code}")
            self.logger.debug(f"  當前值: {current_value}")
            self.logger.debug(f"  前一個值: {previous_value}")
            self.logger.debug(f"  是否有變化: {has_changed}")
            
            return has_changed
            
        elif check_mode == 'multiple':
            # 多個項目檢查
            check_items_str = self.config.get('Table', 'value_change_check_items', fallback='')
            if not check_items_str:
                self.logger.debug("check_value_change() - 未指定檢查項目，返回 True")
                return True
                
            check_item_names = [item.strip() for item in check_items_str.split(',')]
            self.logger.debug(f"check_value_change() - 多個項目檢查: {check_item_names}")
            
            # 檢查每個項目
            for check_item_name in check_item_names:
                # 獲取檢查項目的代碼
                check_item_code = None
                for code, item_data in self.items_data.items():
                    if item_data.name == check_item_name:
                        check_item_code = code
                        break
                        
                if not check_item_code:
                    self.logger.debug(f"check_value_change() - 找不到檢查項目 {check_item_name} 的代碼")
                    continue
                    
                # 檢查值是否變化
                current_value = current_row.values.get(check_item_code, "")
                previous_value = previous_row.values.get(check_item_code, "")
                
                self.logger.debug(f"check_value_change() - 檢查項目 {check_item_name}:")
                self.logger.debug(f"  項目代碼: {check_item_code}")
                self.logger.debug(f"  當前值: {current_value}")
                self.logger.debug(f"  前一個值: {previous_value}")
                
                # 只要有一個項目值變化就返回 True
                if current_value != previous_value:
                    self.logger.debug(f"check_value_change() - 項目 {check_item_name} 值有變化")
                    return True
                    
            self.logger.debug("check_value_change() - 所有檢查項目值都沒有變化")
            return False
            
        elif check_mode == 'all':
            # 檢查所有項目
            self.logger.debug("check_value_change() - 檢查所有項目")
            
            # 檢查每個項目
            for code, item_data in self.items_data.items():
                # 跳過 receive_date 和 receive_time
                if code in ['receive_date', 'receive_time']:
                    continue
                    
                current_value = current_row.values.get(code, "")
                previous_value = previous_row.values.get(code, "")
                
                self.logger.debug(f"check_value_change() - 檢查項目 {item_data.name}:")
                self.logger.debug(f"  項目代碼: {code}")
                self.logger.debug(f"  當前值: {current_value}")
                self.logger.debug(f"  前一個值: {previous_value}")
                
                # 只要有一個項目值變化就返回 True
                if current_value != previous_value:
                    self.logger.debug(f"check_value_change() - 項目 {item_data.name} 值有變化")
                    return True
                    
            self.logger.debug("check_value_change() - 所有項目值都沒有變化")
            return False
            
        else:
            self.logger.warning(f"check_value_change() - 未知的檢查模式: {check_mode}")
            return True
        
    def create_new_row(self):
        """建立新的資料行
        在原始資料佇列中建立新的資料行
        """
        now = datetime.now()
        new_row = RawDataRow(
            receive_date=now.strftime("%Y-%m-%d"),
            receive_time=now.strftime("%H:%M:%S.%f"),
            values={}
        )
        self.raw_data.appendleft(new_row)
        self.logger.debug("create_new_row() - 已建立新行")
        
    def update_items_table(self):
        """更新項目表格
        更新UI中的項目表格顯示
        """
        try:
            self.items_table.setRowCount(len(self.items_data))
            for row, (code, item_data) in enumerate(self.items_data.items()):
                self.items_table.setItem(row, 0, QTableWidgetItem(item_data.name))
                self.items_table.setItem(row, 1, QTableWidgetItem(code))
                self.items_table.setItem(row, 2, QTableWidgetItem(str(item_data.value or "")))
                self.items_table.setItem(row, 3, QTableWidgetItem(
                    item_data.update_time.strftime("%H:%M:%S.%f") if item_data.update_time else ""))
                self.items_table.setItem(row, 4, QTableWidgetItem(item_data.status))
                
        except Exception as e:
            logging.error(f"更新項目表格失敗: {str(e)}")
            raise
            
    def update_ui(self):
        """更新使用者介面
        定期更新UI元件
        """
        try:
            # 更新項目表格
            self.update_items_table()
            
            # 更新日誌顯示
            self.update_log_display()
            
        except Exception as e:
            logging.error(f"更新使用者介面失敗: {str(e)}")
            raise
            
    def update_log_display(self):
        """更新日誌顯示
        更新UI中的日誌顯示區域
        """
        try:
            # 獲取最新的日誌記錄
            log_records = []
            for handler in logging.getLogger().handlers:
                if isinstance(handler, logging.FileHandler):
                    with open(handler.baseFilename, 'r', encoding='utf-8') as f:
                        log_records = f.readlines()[-100:]  # 只顯示最後100行
                        
            # 更新日誌顯示
            self.log_text.clear()
            self.log_text.append(''.join(log_records))
            
            # 滾動到最底部
            scrollbar = self.log_text.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())
            
        except Exception as e:
            logging.error(f"更新日誌顯示失敗: {str(e)}")
            raise

    def closeEvent(self, event):
        """關閉視窗事件處理"""
        try:
            # 停止自動管理器
            if hasattr(self, 'auto_connect_manager'):
                self.auto_connect_manager.stop()
                self.logger.info("自動管理器已停止")

            if self.dde_client and self.dde_client.is_connected():
                # 檢查是否需要斷線
                disconnect_on_exit = self.config.getboolean('DDE', 'disconnect_on_exit', fallback=True)
                
                if disconnect_on_exit:
                    # 顯示確認對話框
                    reply = QMessageBox.question(
                        self,
                        "確認關閉",
                        "程式仍在連線中，確定要結束程式嗎？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    
                    if reply == QMessageBox.Yes:
                        # 停止資料處理器
                        self.data_processor.stop()
                        self.process_thread.quit()
                        self.process_thread.wait()
                        
                        # 先取消所有訂閱
                        for code, item_data in self.items_data.items():
                            if item_data.status == "已訂閱":
                                self.dde_client.unadvise(code)
                                
                        # 再斷開連接
                        self.dde_client.disconnect(terminate_dde=disconnect_on_exit)
                        
                        # 手動調用清理方法
                        if hasattr(self.dde_client, '_cleanup_resources'):
                            print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] [DEBUG] [M] 手動調用 _cleanup_resources')
                            self.dde_client._cleanup_resources()
                            
                        self.dde_client = None
                        event.accept()
                    else:
                        event.ignore()
                else:
                    # 只取消訂閱，不斷開連接
                    for code, item_data in self.items_data.items():
                        if item_data.status == "已訂閱":
                            self.dde_client.unadvise(code)
                            
                    # 停止資料處理器
                    self.data_processor.stop()
                    self.process_thread.quit()
                    self.process_thread.wait()
                    
                    # 手動調用清理方法
                    if hasattr(self.dde_client, '_cleanup_resources'):
                        print(f'[{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}] [DEBUG] [M] 手動調用 _cleanup_resources')
                        self.dde_client._cleanup_resources()
                        
                    # 不設定 self.dde_client = None，保持連接
                    event.accept()
            else:
                # 停止資料處理器
                self.data_processor.stop()
                self.process_thread.quit()
                self.process_thread.wait()
                event.accept()
                
        except Exception as e:
            self.logger.error(f"關閉視窗時發生錯誤: {str(e)}")
            event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DDEMonitor()
    window.show()
    sys.exit(app.exec()) 