# DDE Actor System 性能調優指南

## 性能目標

### 基準指標
- **吞吐量**：每秒處理10萬+筆DDE數據更新
- **延遲**：端到端處理延遲 < 1ms (P99)
- **內存效率**：24小時運行內存增長 < 100MB
- **CPU使用率**：多核心環境下 < 80%
- **GUI響應性**：界面更新延遲 < 16ms (60FPS)

### 測試環境
- **CPU**：Intel i7-8700K (6核12線程) 或同等性能
- **內存**：16GB DDR4-3200
- **存儲**：NVMe SSD
- **網絡**：千兆以太網

## 核心優化策略

### 1. 數據接收優化

#### DDE回調函數優化
```python
# 優化前：在回調中進行複雜處理
def on_dde_data_slow(self, item: str, value: str):
    # 數據驗證 - 耗時操作
    if not self.validate_item(item):
        return
    # 數據轉換 - 耗時操作  
    processed_value = self.transform_value(value)
    # 直接更新GUI - 阻塞操作
    self.update_gui(item, processed_value)

# 優化後：最小化回調處理時間
def on_dde_data_fast(self, item: str, value: str):
    # 只做最基本的數據寫入，無鎖操作
    timestamp = time.time_ns()  # 納秒精度時間戳
    self.ring_buffer.write_atomic(item, value, timestamp)
```

#### 環形緩衝區設計
```python
class LockFreeRingBuffer:
    def __init__(self, size: int = 1024 * 1024):
        self.size = size
        self.buffer = (ctypes.c_char * size)()
        self.write_pos = ctypes.c_uint64(0)
        self.read_pos = ctypes.c_uint64(0)
        
    def write_atomic(self, item: str, value: str, timestamp: int):
        """原子寫入操作，無鎖設計"""
        data = f"{timestamp}|{item}|{value}\n".encode('utf-8')
        data_len = len(data)
        
        # 原子操作獲取寫入位置
        pos = self.write_pos.value
        if pos + data_len < self.size:
            # 直接寫入
            ctypes.memmove(self.buffer[pos:], data, data_len)
            self.write_pos.value = pos + data_len
        else:
            # 環形回繞
            self.write_pos.value = 0
```

### 2. 數據處理優化

#### 批量處理策略
```python
class BatchProcessor:
    def __init__(self, batch_size: int = 1000, timeout_ms: int = 10):
        self.batch_size = batch_size
        self.timeout_ms = timeout_ms
        self.batch = []
        self.last_process_time = time.time()
        
    async def add_data(self, data):
        self.batch.append(data)
        
        # 批量大小觸發或超時觸發
        if (len(self.batch) >= self.batch_size or 
            time.time() - self.last_process_time > self.timeout_ms / 1000):
            await self.process_batch()
            
    async def process_batch(self):
        if not self.batch:
            return
            
        # 批量處理，提高效率
        processed_batch = []
        for data in self.batch:
            processed = self.process_single(data)
            if processed:
                processed_batch.append(processed)
                
        # 批量發送結果
        if processed_batch:
            await self.send_batch_result(processed_batch)
            
        self.batch.clear()
        self.last_process_time = time.time()
```

#### 內存池管理
```python
class MemoryPool:
    def __init__(self, object_type, initial_size: int = 1000):
        self.object_type = object_type
        self.available = collections.deque()
        self.in_use = set()
        
        # 預分配對象
        for _ in range(initial_size):
            obj = object_type()
            self.available.append(obj)
            
    def acquire(self):
        """獲取對象"""
        if self.available:
            obj = self.available.popleft()
        else:
            obj = self.object_type()
            
        self.in_use.add(obj)
        return obj
        
    def release(self, obj):
        """釋放對象"""
        if obj in self.in_use:
            self.in_use.remove(obj)
            # 重置對象狀態
            obj.reset()
            self.available.append(obj)
```

### 3. GUI更新優化

#### 虛擬化表格
```python
class VirtualTableWidget(QTableWidget):
    def __init__(self, data_model):
        super().__init__()
        self.data_model = data_model
        self.visible_rows = 50  # 只渲染可見行
        self.row_height = 25
        
    def paintEvent(self, event):
        """只繪製可見區域"""
        viewport_rect = self.viewport().rect()
        first_visible_row = viewport_rect.top() // self.row_height
        last_visible_row = min(
            first_visible_row + self.visible_rows,
            self.data_model.row_count()
        )
        
        # 只更新可見行
        for row in range(first_visible_row, last_visible_row):
            self.update_row(row)
```

#### 批量GUI更新
```python
class BatchGUIUpdater:
    def __init__(self, update_interval_ms: int = 16):  # 60FPS
        self.update_interval_ms = update_interval_ms
        self.pending_updates = {}
        self.timer = QTimer()
        self.timer.timeout.connect(self.flush_updates)
        self.timer.start(update_interval_ms)
        
    def add_update(self, item: str, value: str):
        """添加待更新項目"""
        self.pending_updates[item] = value
        
    def flush_updates(self):
        """批量刷新GUI"""
        if not self.pending_updates:
            return
            
        # 批量更新，減少重繪次數
        self.table_widget.setUpdatesEnabled(False)
        
        for item, value in self.pending_updates.items():
            self.update_single_item(item, value)
            
        self.table_widget.setUpdatesEnabled(True)
        self.pending_updates.clear()
```

### 4. I/O優化

#### 異步文件寫入
```python
class AsyncFileWriter:
    def __init__(self, filename: str, buffer_size: int = 64*1024):
        self.filename = filename
        self.buffer_size = buffer_size
        self.write_queue = asyncio.Queue()
        self.buffer = io.StringIO()
        
    async def write_async(self, data: str):
        """異步寫入數據"""
        await self.write_queue.put(data)
        
    async def flush_worker(self):
        """後台刷新工作線程"""
        while True:
            try:
                # 批量收集數據
                batch_data = []
                batch_size = 0
                
                while batch_size < self.buffer_size:
                    data = await asyncio.wait_for(
                        self.write_queue.get(), timeout=0.1)
                    batch_data.append(data)
                    batch_size += len(data)
                    
            except asyncio.TimeoutError:
                pass
                
            if batch_data:
                # 批量寫入文件
                async with aiofiles.open(self.filename, 'a') as f:
                    await f.writelines(batch_data)
```

## 性能監控

### 1. 實時指標收集
```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'dde_received_per_sec': 0,
            'data_processed_per_sec': 0,
            'gui_updates_per_sec': 0,
            'memory_usage_mb': 0,
            'cpu_usage_percent': 0,
            'avg_latency_ms': 0,
            'p99_latency_ms': 0
        }
        self.latency_samples = collections.deque(maxlen=10000)
        
    def record_latency(self, start_time: float):
        """記錄延遲"""
        latency = (time.time() - start_time) * 1000  # 轉換為毫秒
        self.latency_samples.append(latency)
        
    def calculate_metrics(self):
        """計算性能指標"""
        if self.latency_samples:
            self.metrics['avg_latency_ms'] = statistics.mean(self.latency_samples)
            self.metrics['p99_latency_ms'] = statistics.quantiles(
                self.latency_samples, n=100)[98]
                
        # CPU和內存使用率
        process = psutil.Process()
        self.metrics['memory_usage_mb'] = process.memory_info().rss / 1024 / 1024
        self.metrics['cpu_usage_percent'] = process.cpu_percent()
```

### 2. 性能分析工具
```python
class Profiler:
    def __init__(self):
        self.profiles = {}
        
    @contextmanager
    def profile(self, name: str):
        """性能分析上下文管理器"""
        start_time = time.perf_counter()
        try:
            yield
        finally:
            elapsed = time.perf_counter() - start_time
            if name not in self.profiles:
                self.profiles[name] = []
            self.profiles[name].append(elapsed)
            
    def get_report(self):
        """獲取性能報告"""
        report = {}
        for name, times in self.profiles.items():
            report[name] = {
                'count': len(times),
                'total_time': sum(times),
                'avg_time': statistics.mean(times),
                'max_time': max(times),
                'min_time': min(times)
            }
        return report
```

## 配置調優

### 1. 關鍵配置參數
```json
{
    "performance": {
        "ring_buffer_size": 1048576,
        "batch_size": 1000,
        "batch_timeout_ms": 10,
        "gui_update_interval_ms": 16,
        "memory_pool_initial_size": 10000,
        "max_queue_size": 100000,
        "worker_thread_count": 4
    },
    "backpressure": {
        "strategy": "drop_oldest",
        "high_watermark": 80000,
        "low_watermark": 60000
    }
}
```

### 2. 動態調優
```python
class DynamicTuner:
    def __init__(self, performance_monitor):
        self.monitor = performance_monitor
        self.config = {}
        
    def auto_tune(self):
        """自動調優"""
        metrics = self.monitor.get_metrics()
        
        # 根據CPU使用率調整批量大小
        if metrics['cpu_usage_percent'] > 80:
            self.config['batch_size'] = min(
                self.config['batch_size'] * 1.2, 2000)
        elif metrics['cpu_usage_percent'] < 50:
            self.config['batch_size'] = max(
                self.config['batch_size'] * 0.8, 100)
                
        # 根據延遲調整更新頻率
        if metrics['p99_latency_ms'] > 10:
            self.config['gui_update_interval_ms'] = min(
                self.config['gui_update_interval_ms'] * 1.5, 100)
```

## 測試和基準

### 1. 性能測試腳本
```python
async def performance_test():
    """性能基準測試"""
    system = DDEActorSystem()
    await system.start()
    
    # 模擬高頻數據
    start_time = time.time()
    for i in range(100000):
        await system.send_dde_data(f"ITEM_{i%1000}", f"VALUE_{i}")
        
    end_time = time.time()
    
    # 計算性能指標
    throughput = 100000 / (end_time - start_time)
    print(f"吞吐量: {throughput:.2f} 筆/秒")
    
    # 等待處理完成
    await system.wait_for_completion()
    
    # 獲取性能報告
    report = system.get_performance_report()
    print(f"平均延遲: {report['avg_latency_ms']:.2f} ms")
    print(f"P99延遲: {report['p99_latency_ms']:.2f} ms")
```

### 2. 壓力測試
```python
async def stress_test():
    """壓力測試"""
    system = DDEActorSystem()
    
    # 創建多個數據生產者
    producers = []
    for i in range(10):
        producer = DataProducer(rate=10000)  # 每秒10000筆
        producers.append(producer)
        
    # 運行24小時壓力測試
    await asyncio.gather(*[p.run(duration=24*3600) for p in producers])
    
    # 檢查內存洩漏
    memory_report = system.get_memory_report()
    assert memory_report['memory_growth_mb'] < 100
```

## 故障排除

### 1. 常見性能問題
- **高延遲**：檢查批量大小和處理邏輯
- **內存洩漏**：檢查對象池和引用循環
- **CPU過高**：檢查無限循環和阻塞操作
- **GUI卡頓**：檢查主線程阻塞和更新頻率

### 2. 調試工具
- **性能分析器**：使用內置的Profiler類
- **內存分析**：使用memory_profiler和tracemalloc
- **CPU分析**：使用cProfile和py-spy
- **網絡分析**：使用Wireshark監控DDE通信
