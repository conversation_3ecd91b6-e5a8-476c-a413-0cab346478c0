#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件分析工具

分析 system_config.json 與原始 multi_config.ini 的差異
"""

import json
import configparser
from pathlib import Path


def analyze_config_usage():
    """分析配置使用情況"""
    print("🔍 DDE Actor System 配置分析")
    print("=" * 60)
    
    # 讀取 JSON 配置
    json_config = None
    try:
        with open("config/system_config.json", 'r', encoding='utf-8') as f:
            json_config = json.load(f)
        print("✅ JSON 配置文件加載成功")
    except Exception as e:
        print(f"❌ JSON 配置文件加載失敗: {str(e)}")
        return
    
    # 讀取 INI 配置
    ini_config = None
    ini_path = Path("../dde_multi_engine_hybrid/multi_config.ini")
    if ini_path.exists():
        try:
            ini_config = configparser.ConfigParser()
            ini_config.read(ini_path, encoding='utf-8')
            print("✅ INI 配置文件加載成功")
        except Exception as e:
            print(f"❌ INI 配置文件加載失敗: {str(e)}")
    else:
        print("⚠️  INI 配置文件不存在")
    
    print("\n" + "=" * 60)
    print("📋 配置項目使用情況分析")
    print("=" * 60)
    
    # 分析 JSON 配置各部分
    analyze_system_config(json_config.get('system', {}))
    analyze_performance_config(json_config.get('performance', {}))
    analyze_products_config(json_config.get('products', []))
    
    if ini_config:
        print("\n" + "=" * 60)
        print("🔄 與原始 INI 配置對比")
        print("=" * 60)
        compare_with_ini(json_config, ini_config)


def analyze_system_config(system_config):
    """分析系統配置"""
    print("\n🖥️  系統配置 (system)")
    print("-" * 30)
    
    used_configs = {
        "system_name": "✅ 用於系統標識",
        "version": "✅ 用於版本顯示",
        "debug_mode": "❌ 未實際使用",
        "log_level": "❌ 未實際使用 (硬編碼為 INFO)",
        "log_file": "❌ 未實際使用",
        "log_max_size_mb": "❌ 未實際使用",
        "log_backup_count": "❌ 未實際使用",
        "enable_monitoring": "❌ 未實際使用",
        "monitoring_interval": "❌ 未實際使用",
        "health_check_interval": "❌ 未實際使用",
        "enable_remote_api": "❌ 未實際使用",
        "api_host": "❌ 未實際使用",
        "api_port": "❌ 未實際使用"
    }
    
    for key, status in used_configs.items():
        value = system_config.get(key, "未設置")
        print(f"   {key}: {value} - {status}")


def analyze_performance_config(perf_config):
    """分析性能配置"""
    print("\n⚡ 性能配置 (performance)")
    print("-" * 30)
    
    used_configs = {
        "dde_buffer_size": "❌ 未實際使用",
        "dde_batch_size": "✅ 用於 DDE Receiver 批次大小",
        "dde_batch_timeout": "✅ 用於 DDE Receiver 批次超時",
        "dde_enable_polling": "✅ 用於啟用輪詢模式",
        "dde_polling_interval": "✅ 用於輪詢間隔",
        "processing_batch_size": "❌ 未實際使用",
        "processing_queue_size": "❌ 未實際使用",
        "processing_workers": "❌ 未實際使用",
        "gui_update_interval_ms": "❌ 未實際使用",
        "gui_max_batch_size": "❌ 未實際使用",
        "gui_virtual_rows": "❌ 未實際使用",
        "file_batch_size": "✅ 用於文件寫入批次大小",
        "file_flush_interval": "✅ 用於文件刷新間隔",
        "file_buffer_size": "❌ 未實際使用",
        "backpressure_high_watermark": "❌ 未實際使用",
        "backpressure_low_watermark": "❌ 未實際使用",
        "backpressure_strategy": "❌ 未實際使用",
        "memory_pool_initial_size": "❌ 未實際使用",
        "gc_threshold": "❌ 未實際使用"
    }
    
    for key, status in used_configs.items():
        value = perf_config.get(key, "未設置")
        print(f"   {key}: {value} - {status}")


def analyze_products_config(products_config):
    """分析產品配置"""
    print("\n📦 產品配置 (products)")
    print("-" * 30)
    
    used_configs = {
        "symbol": "✅ 用於產品識別",
        "data_types": "❌ 未實際使用 (僅作為標記)",
        "service": "✅ 用於 DDE 服務名稱",
        "topic": "✅ 用於 DDE 主題",
        "items": "✅ 用於 DDE 項目列表",
        "enabled": "✅ 用於啟用/禁用產品",
        "priority": "❌ 未實際使用",
        "custom_settings": "❌ 未實際使用"
    }
    
    print(f"   總產品數: {len(products_config)}")
    enabled_count = sum(1 for p in products_config if p.get('enabled', False))
    print(f"   啟用產品數: {enabled_count}")
    
    print(f"\n   配置項目使用情況:")
    for key, status in used_configs.items():
        print(f"     {key} - {status}")
    
    print(f"\n   產品詳情:")
    for product in products_config:
        symbol = product.get('symbol', '未知')
        enabled = product.get('enabled', False)
        data_types = product.get('data_types', [])
        items_count = len(product.get('items', []))
        
        status = "✅ 啟用" if enabled else "❌ 禁用"
        print(f"     {symbol}: {status}, 數據類型: {data_types}, 項目數: {items_count}")


def compare_with_ini(json_config, ini_config):
    """與 INI 配置對比"""
    print("🔄 關鍵差異分析:")
    print("-" * 30)
    
    print("1. 數據類型處理:")
    print("   INI 版本: 使用模板系統 (Template_Tick_Items, Template_Order_Items 等)")
    print("   JSON 版本: 直接列出項目，忽略 data_types 設置")
    print("   影響: JSON 版本無法根據數據類型動態生成項目")
    
    print("\n2. 輸出格式:")
    print("   INI 版本: 複雜的分類輸出 (tick, order, level2, daily)")
    print("   JSON 版本: 簡單的統一格式 (timestamp, item, original_value, processed_value)")
    print("   影響: 輸出格式完全不同")
    
    print("\n3. 自動連線:")
    print("   INI 版本: 複雜的時間排程和自動連線模板")
    print("   JSON 版本: 簡單的手動啟動")
    print("   影響: 無自動時間控制功能")
    
    print("\n4. 數據處理:")
    print("   INI 版本: 支援數值變化檢查、時間間隔控制")
    print("   JSON 版本: 簡單的數據轉發")
    print("   影響: 缺少智能數據過濾")
    
    print("\n5. 文件輸出:")
    print("   INI 版本: 分類文件輸出到不同目錄")
    print("   JSON 版本: 統一輸出到 outputs/ 目錄")
    print("   影響: 文件組織方式不同")


def suggest_improvements():
    """建議改進"""
    print("\n" + "=" * 60)
    print("💡 改進建議")
    print("=" * 60)
    
    print("1. 實現 data_types 功能:")
    print("   - 根據 data_types 動態生成 DDE 項目")
    print("   - 支援 tick, order, level2, daily 四種類型")
    print("   - 使用模板系統生成項目列表")
    
    print("\n2. 改進輸出格式:")
    print("   - 支援原始 INI 版本的輸出格式")
    print("   - 按數據類型分類輸出")
    print("   - 支援數值變化檢查")
    
    print("\n3. 添加自動連線功能:")
    print("   - 實現時間排程")
    print("   - 支援自動啟動/停止")
    print("   - 週末防護功能")
    
    print("\n4. 優化配置使用:")
    print("   - 移除未使用的配置項目")
    print("   - 添加實際需要的配置")
    print("   - 改進配置驗證")


def main():
    """主函數"""
    analyze_config_usage()
    suggest_improvements()
    
    print(f"\n🎯 總結:")
    print("system_config.json 中約 70% 的配置項目未被實際使用")
    print("data_types 設置存在但未實現對應功能")
    print("輸出格式與原始 INI 版本完全不同")
    print("建議根據實際需求簡化配置或實現完整功能")


if __name__ == "__main__":
    main()
