#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理和備份工具
"""

import shutil
import os
from pathlib import Path
from datetime import datetime


def backup_and_cleanup():
    """備份並清理舊數據"""
    print("🧹 清理和備份舊數據")
    print("=" * 40)
    
    # 創建備份目錄
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = Path(f"backups/{timestamp}")
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"📁 備份目錄: {backup_dir}")
    
    # 備份 outputs 目錄
    outputs_dir = Path("outputs")
    if outputs_dir.exists():
        files_backed_up = 0
        for file_path in outputs_dir.glob("*"):
            if file_path.is_file() and file_path.stat().st_size > 0:
                backup_file = backup_dir / file_path.name
                shutil.copy2(file_path, backup_file)
                print(f"   ✅ 備份: {file_path.name} ({file_path.stat().st_size} bytes)")
                files_backed_up += 1
        
        if files_backed_up == 0:
            print("   ⚪ outputs/ 目錄沒有需要備份的文件")
    else:
        print("   ⚪ outputs/ 目錄不存在")
    
    # 備份 logs 目錄
    logs_dir = Path("logs")
    if logs_dir.exists():
        logs_backed_up = 0
        for file_path in logs_dir.glob("*"):
            if file_path.is_file() and file_path.stat().st_size > 0:
                backup_file = backup_dir / file_path.name
                shutil.copy2(file_path, backup_file)
                print(f"   ✅ 備份: {file_path.name} ({file_path.stat().st_size} bytes)")
                logs_backed_up += 1
        
        if logs_backed_up == 0:
            print("   ⚪ logs/ 目錄沒有需要備份的文件")
    else:
        print("   ⚪ logs/ 目錄不存在")
    
    print(f"\n🗑️  清理舊文件...")
    
    # 清理 outputs 目錄
    if outputs_dir.exists():
        files_removed = 0
        for file_path in outputs_dir.glob("*"):
            if file_path.is_file():
                try:
                    file_path.unlink()
                    print(f"   🗑️  刪除: {file_path.name}")
                    files_removed += 1
                except Exception as e:
                    print(f"   ❌ 刪除失敗: {file_path.name}: {str(e)}")
        
        if files_removed == 0:
            print("   ⚪ outputs/ 目錄沒有文件需要清理")
    
    # 清理 logs 目錄
    if logs_dir.exists():
        logs_removed = 0
        for file_path in logs_dir.glob("*"):
            if file_path.is_file():
                try:
                    file_path.unlink()
                    print(f"   🗑️  刪除: {file_path.name}")
                    logs_removed += 1
                except Exception as e:
                    print(f"   ❌ 刪除失敗: {file_path.name}: {str(e)}")
        
        if logs_removed == 0:
            print("   ⚪ logs/ 目錄沒有文件需要清理")
    
    print(f"\n✅ 清理完成!")
    print(f"備份位置: {backup_dir}")


if __name__ == "__main__":
    backup_and_cleanup()
