# DDE 多商品版資料處理流程文件

## 概述

本文件詳細說明 DDE 多商品版 (`dde_monitor_multi.py` 及相關模組) 從接收資料到完整輸出的完整流程。多商品版基於模組化版本改造，支援多個商品同時監控，每個商品可以有不同的資料類型配置。

## 核心架構

### 主要模組
- **dde_monitor_multi.py**: 多商品主程式入口
- **gui/multi_product_window.py**: 多商品主視窗GUI模組
- **core/data_handler.py**: 包含 `MultiProductDataProcessor` 多商品資料處理器
- **utils/config_manager.py**: 包含 `MultiProductConfigManager` 多商品配置管理器
- **utils/logger.py**: 日誌管理模組

### 多商品資料結構

```python
# 多商品配置結構
symbols = ['FITXN07', 'FITXN08', 'TXFN07']  # 商品列表
data_types = ['tick', 'order', 'level2', 'daily']  # 資料類型

# 每個商品的資料容器
symbol_items_data = {}      # symbol -> data_type -> item_code -> ItemData
symbol_raw_data = {}        # symbol -> data_type -> deque
symbol_file_handlers = {}   # symbol -> data_type -> DataFileHandler
symbol_current_rows = {}    # symbol -> data_type -> RawDataRow
symbol_last_advise_time = {} # symbol -> data_type -> last_time
symbol_time_intervals = {}   # symbol -> data_type -> interval_seconds
```

### 模板化配置系統

```ini
# multi_config.ini 範例
[System]
symbols = FITXN07,FITXN08,TXFN07
data_types = tick,order,level2,daily
dde_service = SKCOM
dde_topic = SKCOM

[FITXN07]
output_path = ./output/FITXN07

[DataType_tick]
table_time_newline_interval = 0.800
table_enable_value_change_check = true
table_value_change_check_mode = single
table_value_change_check_items = 總量

[Items_tick]
{symbol}.TF-Price = 價格
{symbol}.TF-Volume = 總量
{symbol}.TF-Time = 時間
```

**模板化特點**：
- 使用 `{symbol}` 佔位符
- 可重用的資料類型配置
- 靈活的商品特定設定

## 完整資料處理流程

### 1. 多商品應用初始化 (`MultiProductDDEApp.initialize`)

```python
def initialize(self):
    """初始化多商品應用程序"""
    # 創建 QApplication
    self.app = QApplication(sys.argv)
    
    # 初始化多商品配置管理器
    self.multi_config = MultiProductConfigManager(self.config_file)
    
    # 載入配置文件
    if not self.multi_config.load_config():
        raise Exception(f"載入多商品配置文件失败: {self.config_file}")
    
    # 驗證配置
    errors = self.multi_config.validate_multi_config()
    if errors:
        # 處理配置錯誤
        pass
    
    # 初始化日誌系統
    system_config = self.multi_config.get_system_config()
    log_base_path = system_config.get('log_base_path', './logs/')
    log_file_path = os.path.join(log_base_path, 'multi_product_monitor.log')
    self.logger = setup_logging(log_file_path, 'INFO', 'INFO', 'DEBUG')
    
    # 記錄配置信息
    self.logger.info(f"載入商品數量: {len(self.multi_config.symbols)}")
    self.logger.info(f"商品列表: {', '.join(self.multi_config.symbols)}")
    self.logger.info(f"數據類型: {', '.join(self.multi_config.data_types)}")
    
    # 初始化主窗口
    self.main_window = MultiProductMainWindow(self.multi_config, self.logger)
```

**多商品初始化特點**：
- 專用的多商品配置管理器
- 配置驗證機制
- 詳細的配置資訊記錄

### 2. 多商品資料處理器初始化 (`MultiProductDataProcessor`)

```python
class MultiProductDataProcessor(QObject):
    """多商品数据处理器"""
    
    # 信号定义
    data_processed = Signal(str, str, str, str)  # symbol, data_type, item, value
    
    def __init__(self, multi_config_manager):
        super().__init__()
        self.multi_config = multi_config_manager
        self.data_queue = Queue()
        self.running = False
        
        # 每个商品的数据容器
        self.symbol_items_data = {}     # symbol -> data_type -> item_code -> ItemData
        self.symbol_raw_data = {}       # symbol -> data_type -> deque
        self.symbol_file_handlers = {}  # symbol -> data_type -> DataFileHandler
        
        # 每个商品数据类型的当前数据行
        self.symbol_current_rows = {}   # symbol -> data_type -> RawDataRow
        
        # 时间间隔检查相关
        self.symbol_last_advise_time = {}  # symbol -> data_type -> last_time
        self.symbol_time_intervals = {}    # symbol -> data_type -> interval_seconds
        
        # 初始化数据容器
        self._init_symbol_data_containers()

    def _init_symbol_data_containers(self):
        """初始化各商品的数据容器"""
        all_items = self.multi_config.get_all_symbol_items()
        
        for symbol, symbol_data in all_items.items():
            self.symbol_items_data[symbol] = {}
            self.symbol_raw_data[symbol] = {}
            self.symbol_file_handlers[symbol] = {}
            self.symbol_current_rows[symbol] = {}
            self.symbol_last_advise_time[symbol] = {}
            self.symbol_time_intervals[symbol] = {}
            
            for data_type, items in symbol_data.items():
                # 初始化項目数据容器
                self.symbol_items_data[symbol][data_type] = {}
                for item_code, item_name in items.items():
                    self.symbol_items_data[symbol][data_type][item_code] = ItemData(
                        name=item_name,
                        code=item_code
                    )
                
                # 初始化原始数据队列
                self.symbol_raw_data[symbol][data_type] = deque(maxlen=1000)
                
                # 初始化时间间隔设定
                datatype_config = self.multi_config.get_datatype_config(data_type)
                if datatype_config:
                    time_interval = float(datatype_config.get('table_time_newline_interval', '0.800'))
                    self.symbol_time_intervals[symbol][data_type] = time_interval
                    self.symbol_last_advise_time[symbol][data_type] = None
                
                # 初始化文件处理器
                output_path = self.multi_config.get_symbol_output_path(symbol)
                file_handler = DataFileHandler()
                file_handler.init_file_paths(
                    log_file=f"{output_path}/logs/{symbol}_{data_type}.log",
                    data_file=f"{output_path}/data_{data_type}.csv",
                    complete_data_file=f"{output_path}/complete_data_{data_type}.csv"
                )
                self.symbol_file_handlers[symbol][data_type] = file_handler
```

**多商品處理器特點**：
- 多層次的資料容器結構
- 每個商品-資料類型組合獨立處理
- 模板化配置應用

### 3. 資料接收入口 (`gui/multi_product_window.py: on_advise_data`)

```python
def on_advise_data(self, item: str, value: str):
    """处理DDE数据更新回调"""
    try:
        # 记录最后接收数据的时间
        self.last_advise_time = time.time()
        
        # 将数据加入多商品处理器
        self.multi_data_processor.add_data(item, value)
        
    except Exception as e:
        self.logger.error(f"处理DDE数据回调失敗: {str(e)}")
```

**多商品接收特點**：
- 統一的資料接收入口
- 自動路由到多商品處理器
- 全域時間記錄

### 4. 多商品資料路由和處理

```python
def add_data(self, item: str, value: str):
    """添加数据到处理队列"""
    try:
        self.data_queue.put((item, value))
    except Exception as e:
        logging.error(f"添加数据到队列失败: {str(e)}")

def process_data(self):
    """处理数据的主循环"""
    self.running = True
    
    while self.running:
        try:
            # 从队列中获取数据
            item, value = self.data_queue.get(timeout=1)
            
            # 解析项目代码，确定商品和数据类型
            symbol, data_type = self._parse_item_code(item)
            
            if symbol and data_type:
                # 处理特定商品的数据
                self._process_symbol_data(symbol, data_type, item, value)
                
                # 发送处理完成信号
                self.data_processed.emit(symbol, data_type, item, value)
            
            # 标记任务完成
            self.data_queue.task_done()
            
        except:
            # 超时或其他异常，继续循环
            continue

def _parse_item_code(self, item: str) -> Tuple[str, str]:
    """解析项目代码，确定商品和数据类型"""
    # 根据项目代码格式解析商品和数据类型
    # 例如: FITXN07.TF-Price -> symbol=FITXN07, data_type=tick
    for symbol in self.multi_config.symbols:
        for data_type in self.multi_config.data_types:
            if symbol in item:
                # 檢查項目是否屬於此資料類型
                items = self.multi_config.get_symbol_items(symbol, data_type)
                if item in items:
                    return symbol, data_type
    
    return None, None

def _process_symbol_data(self, symbol: str, data_type: str, item: str, value: str):
    """处理特定商品的数据 - 实现与模块化版本相同的处理逻辑"""
    try:
        # 获取该商品数据类型的相关容器
        items_data = self.symbol_items_data[symbol][data_type]
        raw_data = self.symbol_raw_data[symbol][data_type]
        file_handler = self.symbol_file_handlers[symbol][data_type]
        
        # 1. 检查是否需要项目重复换行
        if self._check_item_repeat_newline(symbol, data_type, item, value):
            return
        
        # 2. 更新原始数据
        self._update_raw_data(symbol, data_type, item, value)
        
        # 3. 更新项目数据
        if item in items_data:
            items_data[item].value = value
            items_data[item].update_time = datetime.now()
        
        # 4. 更新最后接收时间
        self.symbol_last_advise_time[symbol][data_type] = time.time()
        
    except Exception as e:
        logging.error(f"处理商品数据失败 {symbol}-{data_type}: {str(e)}")
```

**多商品路由特點**：
- 自動解析項目代碼確定商品和資料類型
- 每個商品-資料類型組合獨立處理
- 與模組化版本相同的處理邏輯

### 5. 多商品項目重複換行檢查

```python
def _check_item_repeat_newline(self, symbol: str, data_type: str, item: str, value: str) -> bool:
    """检查是否需要项目重复换行 - 针对特定商品数据类型"""
    try:
        raw_data = self.symbol_raw_data[symbol][data_type]
        
        if not raw_data:
            return False
        
        current_row = raw_data[0]
        
        # 检查项目是否已存在于当前行
        if item in current_row.values:
            # 补齐缺失数据
            if self._has_missing_data(symbol, data_type, current_row):
                self._fill_missing_data(symbol, data_type, current_row)
            
            # 获取该数据类型的配置
            datatype_config = self.multi_config.get_datatype_config(data_type)
            enable_value_change_check = datatype_config.getboolean('table_enable_value_change_check', True)
            
            if enable_value_change_check:
                has_changed = self._check_value_change(symbol, data_type, current_row)
                
                if has_changed:
                    # 储存完整数据行
                    file_handler = self.symbol_file_handlers[symbol][data_type]
                    file_handler.save_row(current_row, is_complete=True)
                    # 建立新行
                    self._create_new_row(symbol, data_type)
                    # 将收到的项目值填入新行
                    new_row = raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    return True
                else:
                    # 值未变化，不储存数据行，但建立新行
                    self._create_new_row(symbol, data_type)
                    # 将收到的项目值填入新行
                    new_row = raw_data[0]
                    new_row.values[item] = value
                    new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                    new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                    return True
            else:
                # 未启用值变化检查，直接储存数据行
                file_handler = self.symbol_file_handlers[symbol][data_type]
                file_handler.save_row(current_row, is_complete=True)
                self._create_new_row(symbol, data_type)
                new_row = raw_data[0]
                new_row.values[item] = value
                new_row.receive_date = datetime.now().strftime("%Y-%m-%d")
                new_row.receive_time = datetime.now().strftime("%H:%M:%S.%f")
                return True
        
        return False
        
    except Exception as e:
        logging.error(f"项目重复检查失败 {symbol}-{data_type}: {str(e)}")
        return False
```

**多商品重複檢查特點**：
- 針對特定商品-資料類型組合
- 使用對應的配置設定
- 獨立的檔案處理器

### 6. 多商品時間間隔檢查

```python
def check_time_intervals(self):
    """检查所有商品数据类型的时间间隔"""
    try:
        current_time = time.time()
        
        for symbol in self.symbol_last_advise_time:
            for data_type in self.symbol_last_advise_time[symbol]:
                last_time = self.symbol_last_advise_time[symbol][data_type]
                interval = self.symbol_time_intervals[symbol][data_type]
                
                if last_time and (current_time - last_time >= interval):
                    self._check_time_interval_newline(symbol, data_type)
                    self.symbol_last_advise_time[symbol][data_type] = current_time
                    
    except Exception as e:
        logging.error(f"检查时间间隔失败: {str(e)}")

def _check_time_interval_newline(self, symbol: str, data_type: str):
    """检查是否需要时间间隔换行 - 针对特定商品数据类型"""
    try:
        # 获取数据类型配置
        datatype_config = self.multi_config.get_datatype_config(data_type)
        enable_time_newline = datatype_config.getboolean('table_enable_time_newline', True)
        
        if not enable_time_newline:
            return
        
        raw_data = self.symbol_raw_data[symbol][data_type]
        
        if not raw_data:
            return
        
        current_row = raw_data[0]
        
        # 检查行是否有数据
        if not current_row.values:
            return
        
        # 检查并补齐缺失数据
        if self._has_missing_data(symbol, data_type, current_row):
            self._fill_missing_data(symbol, data_type, current_row)
        
        # 检查值变化
        enable_value_change_check = datatype_config.getboolean('table_enable_value_change_check', True)
        
        if enable_value_change_check:
            has_changed = self._check_value_change(symbol, data_type, current_row)
            
            if has_changed:
                # 储存完整数据行
                file_handler = self.symbol_file_handlers[symbol][data_type]
                file_handler.save_row(current_row, is_complete=True)
                # 建立新行
                self._create_new_row(symbol, data_type)
            else:
                # 值未变化，不储存数据行，但建立新行
                self._create_new_row(symbol, data_type)
        else:
            # 未启用值变化检查，直接储存数据行
            file_handler = self.symbol_file_handlers[symbol][data_type]
            file_handler.save_row(current_row, is_complete=True)
            self._create_new_row(symbol, data_type)
            
    except Exception as e:
        logging.error(f"时间间隔换行检查失败 {symbol}-{data_type}: {str(e)}")
```

**多商品時間檢查特點**：
- 每個商品-資料類型組合獨立的時間間隔
- 使用對應的資料類型配置
- 批量檢查所有組合

### 7. 多商品值變化檢查

```python
def _check_value_change(self, symbol: str, data_type: str, current_row: RawDataRow) -> bool:
    """检查值变化 - 针对特定商品数据类型"""
    try:
        # 获取数据类型配置
        datatype_config = self.multi_config.get_datatype_config(data_type)
        enable_value_change_check = datatype_config.getboolean('table_enable_value_change_check', True)
        
        if not enable_value_change_check:
            return True
        
        # 获取检查模式
        check_mode = datatype_config.get('table_value_change_check_mode', 'single')
        
        raw_data = self.symbol_raw_data[symbol][data_type]
        
        # 如果没有前一行，直接返回 True
        if len(raw_data) <= 1:
            return True
        
        previous_row = raw_data[1]
        
        # 根据不同模式进行检查
        if check_mode == 'single':
            # 单一项目检查
            check_items = datatype_config.get('table_value_change_check_items', '')
            if not check_items:
                return True
            
            # 查找检查项目的代码
            items_data = self.symbol_items_data[symbol][data_type]
            check_item_code = None
            for item_code, item_data in items_data.items():
                if item_data.name == check_items:
                    check_item_code = item_code
                    break
            
            if check_item_code:
                current_value = current_row.values.get(check_item_code, "")
                previous_value = previous_row.values.get(check_item_code, "")
                return current_value != previous_value
            else:
                return True
                
        elif check_mode == 'multiple':
            # 多个项目检查
            check_items_str = datatype_config.get('table_value_change_check_items', '')
            if not check_items_str:
                return True
            
            check_item_names = [item.strip() for item in check_items_str.split(',')]
            items_data = self.symbol_items_data[symbol][data_type]
            
            for check_item_name in check_item_names:
                # 查找检查项目的代码
                check_item_code = None
                for item_code, item_data in items_data.items():
                    if item_data.name == check_item_name:
                        check_item_code = item_code
                        break
                
                if check_item_code:
                    current_value = current_row.values.get(check_item_code, "")
                    previous_value = previous_row.values.get(check_item_code, "")
                    if current_value != previous_value:
                        return True
            
            return False
            
        elif check_mode == 'all':
            # 全部项目检查
            for item_code in current_row.values.keys():
                current_value = current_row.values.get(item_code, "")
                previous_value = previous_row.values.get(item_code, "")
                if current_value != previous_value:
                    return True
            
            return False
        
        return True
        
    except Exception as e:
        logging.error(f"值变化检查失败 {symbol}-{data_type}: {str(e)}")
        return True
```

**多商品值變化檢查特點**：
- 使用資料類型特定的配置
- 支援所有三種檢查模式
- 針對特定商品-資料類型組合的項目

## 多商品配置管理

### MultiProductConfigManager

```python
class MultiProductConfigManager:
    """多商品配置管理器"""
    
    def __init__(self, config_file):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.symbols = []
        self.data_types = []
    
    def load_config(self):
        """載入多商品配置"""
        if not os.path.exists(self.config_file):
            return False
        
        self.config.read(self.config_file, encoding='utf-8')
        
        # 解析系統配置
        system_config = self.config['System']
        self.symbols = [s.strip() for s in system_config.get('symbols', '').split(',')]
        self.data_types = [d.strip() for d in system_config.get('data_types', '').split(',')]
        
        return True
    
    def get_symbol_items(self, symbol: str, data_type: str) -> Dict[str, str]:
        """獲取特定商品和資料類型的項目配置"""
        section_name = f"Items_{data_type}"
        if section_name not in self.config:
            return {}
        
        items = {}
        for item_code, item_name in self.config[section_name].items():
            # 替換模板變數
            actual_item_code = item_code.replace('{symbol}', symbol)
            items[actual_item_code] = item_name
        
        return items
    
    def get_datatype_config(self, data_type: str):
        """獲取資料類型配置"""
        section_name = f"DataType_{data_type}"
        if section_name in self.config:
            return self.config[section_name]
        return None
```

**配置管理特點**：
- 模板變數替換機制
- 分層配置結構
- 動態項目代碼生成

## 關鍵特性

### 1. 多商品支援
- 同時監控多個商品
- 每個商品獨立的資料處理
- 統一的管理介面

### 2. 多資料類型
- 每個商品支援多種資料類型
- 獨立的配置和處理邏輯
- 靈活的資料類型擴展

### 3. 模板化配置
- 使用佔位符的可重用配置
- 自動項目代碼生成
- 簡化配置管理

### 4. 獨立檔案輸出
- 每個商品-資料類型組合獨立的檔案
- 分層目錄結構
- 可配置的輸出路徑

### 5. 資源監控
- CPU和記憶體使用監控
- 效能統計和分析
- 可調整的GUI更新頻率

## 資料流程圖

```
多商品應用啟動 → 配置載入 → 多商品處理器初始化
     ↓
DDE資料接收 → 統一入口 → 商品-資料類型路由 → 特定處理器
     ↓
項目重複檢查 → 補齊資料 → 值變化檢查 → 保存/跳過決策
     ↓
獨立檔案輸出 → GUI更新 → 統計記錄
     ↓
批量時間檢查 → 多組合處理 → 資源監控
```

---

*本文件基於 DDE 多商品版的實際程式碼內容編寫，反映了多商品版的完整資料處理流程和架構設計。*
