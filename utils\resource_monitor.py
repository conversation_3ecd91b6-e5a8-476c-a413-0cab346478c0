#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
資源監控器
監控程式自身的CPU、記憶體等資源使用情況
"""

import os
import time
import psutil
import threading
from datetime import datetime
from collections import deque
from typing import Dict, List, Optional
import logging

class ResourceStats:
    """資源統計數據"""
    
    def __init__(self):
        self.values: List[float] = []
        self.timestamps: List[datetime] = []
        
    def add_value(self, value: float):
        """添加數值"""
        self.values.append(value)
        self.timestamps.append(datetime.now())
        
    def get_min(self) -> float:
        """獲取最小值"""
        return min(self.values) if self.values else 0.0
        
    def get_max(self) -> float:
        """獲取最大值"""
        return max(self.values) if self.values else 0.0
        
    def get_avg(self) -> float:
        """獲取平均值"""
        return sum(self.values) / len(self.values) if self.values else 0.0
        
    def get_current(self) -> float:
        """獲取當前值"""
        return self.values[-1] if self.values else 0.0
        
    def clear(self):
        """清空數據"""
        self.values.clear()
        self.timestamps.clear()

class ResourceMonitor:
    """資源監控器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.process = psutil.Process(os.getpid())
        
        # 監控開關
        self.monitoring = False
        self.monitor_thread = None
        self.monitor_interval = 1.0  # 默認1秒監控一次
        
        # 統計數據
        self.cpu_stats = ResourceStats()
        self.memory_stats = ResourceStats()  # MB
        self.memory_percent_stats = ResourceStats()  # %
        self.thread_count_stats = ResourceStats()
        self.handle_count_stats = ResourceStats()
        
        # 歷史數據（保留最近1000個數據點）
        self.history_size = 1000
        
        # 監控開始時間
        self.start_time = None
        
    def start_monitoring(self, interval: float = 1.0):
        """開始監控"""
        if self.monitoring:
            self.logger.warning("資源監控已在運行中")
            return
            
        self.monitor_interval = interval
        self.monitoring = True
        self.start_time = datetime.now()
        
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info(f"資源監控已啟動，監控間隔: {interval}秒")
        
    def stop_monitoring(self):
        """停止監控"""
        if not self.monitoring:
            return
            
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
            
        self.logger.info("資源監控已停止")
        
    def _monitor_loop(self):
        """監控循環"""
        while self.monitoring:
            try:
                # 獲取CPU使用率
                cpu_percent = self.process.cpu_percent()
                
                # 獲取記憶體使用情況
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024  # 轉換為MB
                memory_percent = self.process.memory_percent()
                
                # 獲取線程數
                thread_count = self.process.num_threads()
                
                # 獲取句柄數（Windows）
                handle_count = 0
                try:
                    if hasattr(self.process, 'num_handles'):
                        handle_count = self.process.num_handles()
                except:
                    pass
                
                # 記錄數據
                self.cpu_stats.add_value(cpu_percent)
                self.memory_stats.add_value(memory_mb)
                self.memory_percent_stats.add_value(memory_percent)
                self.thread_count_stats.add_value(thread_count)
                self.handle_count_stats.add_value(handle_count)
                
                # 限制歷史數據大小
                self._limit_history_size()
                
                # 記錄詳細日誌（可選）
                self.logger.debug(f"資源監控 - CPU: {cpu_percent:.1f}%, "
                                f"記憶體: {memory_mb:.1f}MB ({memory_percent:.1f}%), "
                                f"線程: {thread_count}, 句柄: {handle_count}")
                
            except Exception as e:
                self.logger.error(f"資源監控錯誤: {str(e)}")
                
            time.sleep(self.monitor_interval)
            
    def _limit_history_size(self):
        """限制歷史數據大小"""
        for stats in [self.cpu_stats, self.memory_stats, self.memory_percent_stats,
                     self.thread_count_stats, self.handle_count_stats]:
            if len(stats.values) > self.history_size:
                # 保留最新的數據
                keep_count = self.history_size
                stats.values = stats.values[-keep_count:]
                stats.timestamps = stats.timestamps[-keep_count:]
                
    def get_current_stats(self) -> Dict:
        """獲取當前統計數據"""
        if not self.cpu_stats.values:
            return {}
            
        return {
            'cpu': {
                'current': self.cpu_stats.get_current(),
                'min': self.cpu_stats.get_min(),
                'max': self.cpu_stats.get_max(),
                'avg': self.cpu_stats.get_avg()
            },
            'memory_mb': {
                'current': self.memory_stats.get_current(),
                'min': self.memory_stats.get_min(),
                'max': self.memory_stats.get_max(),
                'avg': self.memory_stats.get_avg()
            },
            'memory_percent': {
                'current': self.memory_percent_stats.get_current(),
                'min': self.memory_percent_stats.get_min(),
                'max': self.memory_percent_stats.get_max(),
                'avg': self.memory_percent_stats.get_avg()
            },
            'threads': {
                'current': self.thread_count_stats.get_current(),
                'min': self.thread_count_stats.get_min(),
                'max': self.thread_count_stats.get_max(),
                'avg': self.thread_count_stats.get_avg()
            },
            'handles': {
                'current': self.handle_count_stats.get_current(),
                'min': self.handle_count_stats.get_min(),
                'max': self.handle_count_stats.get_max(),
                'avg': self.handle_count_stats.get_avg()
            },
            'monitoring_duration': self._get_monitoring_duration(),
            'data_points': len(self.cpu_stats.values)
        }
        
    def _get_monitoring_duration(self) -> str:
        """獲取監控持續時間"""
        if not self.start_time:
            return "未開始"
            
        duration = datetime.now() - self.start_time
        hours = duration.seconds // 3600
        minutes = (duration.seconds % 3600) // 60
        seconds = duration.seconds % 60
        
        if duration.days > 0:
            return f"{duration.days}天 {hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            
    def get_formatted_stats(self) -> str:
        """獲取格式化的統計信息"""
        stats = self.get_current_stats()
        if not stats:
            return "無監控數據"
            
        lines = [
            f"=== 資源使用統計 (監控時間: {stats['monitoring_duration']}) ===",
            f"數據點數: {stats['data_points']}",
            "",
            f"CPU使用率 (%)",
            f"  當前: {stats['cpu']['current']:.1f}%",
            f"  最小: {stats['cpu']['min']:.1f}%",
            f"  最大: {stats['cpu']['max']:.1f}%",
            f"  平均: {stats['cpu']['avg']:.1f}%",
            "",
            f"記憶體使用 (MB)",
            f"  當前: {stats['memory_mb']['current']:.1f} MB",
            f"  最小: {stats['memory_mb']['min']:.1f} MB",
            f"  最大: {stats['memory_mb']['max']:.1f} MB",
            f"  平均: {stats['memory_mb']['avg']:.1f} MB",
            "",
            f"記憶體使用率 (%)",
            f"  當前: {stats['memory_percent']['current']:.2f}%",
            f"  最小: {stats['memory_percent']['min']:.2f}%",
            f"  最大: {stats['memory_percent']['max']:.2f}%",
            f"  平均: {stats['memory_percent']['avg']:.2f}%",
            "",
            f"線程數",
            f"  當前: {stats['threads']['current']:.0f}",
            f"  最小: {stats['threads']['min']:.0f}",
            f"  最大: {stats['threads']['max']:.0f}",
            f"  平均: {stats['threads']['avg']:.1f}",
        ]
        
        if stats['handles']['current'] > 0:
            lines.extend([
                "",
                f"句柄數 (Windows)",
                f"  當前: {stats['handles']['current']:.0f}",
                f"  最小: {stats['handles']['min']:.0f}",
                f"  最大: {stats['handles']['max']:.0f}",
                f"  平均: {stats['handles']['avg']:.1f}",
            ])
            
        return "\n".join(lines)
        
    def reset_stats(self):
        """重置統計數據"""
        self.cpu_stats.clear()
        self.memory_stats.clear()
        self.memory_percent_stats.clear()
        self.thread_count_stats.clear()
        self.handle_count_stats.clear()
        self.start_time = datetime.now()
        self.logger.info("資源監控統計數據已重置")
        
    def save_stats_to_file(self, file_path: str):
        """保存統計數據到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.get_formatted_stats())
                f.write("\n\n")
                
                # 寫入詳細數據
                f.write("=== 詳細數據 ===\n")
                f.write("時間,CPU(%),記憶體(MB),記憶體(%),線程數,句柄數\n")
                
                for i in range(len(self.cpu_stats.values)):
                    timestamp = self.cpu_stats.timestamps[i].strftime("%H:%M:%S")
                    cpu = self.cpu_stats.values[i]
                    memory_mb = self.memory_stats.values[i]
                    memory_pct = self.memory_percent_stats.values[i]
                    threads = self.thread_count_stats.values[i]
                    handles = self.handle_count_stats.values[i] if i < len(self.handle_count_stats.values) else 0
                    
                    f.write(f"{timestamp},{cpu:.1f},{memory_mb:.1f},{memory_pct:.2f},{threads:.0f},{handles:.0f}\n")
                    
            self.logger.info(f"資源監控統計已保存到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"保存資源監控統計失敗: {str(e)}")
