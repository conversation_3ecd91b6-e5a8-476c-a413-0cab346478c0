#!/bin/bash

echo "DDE 監控程式啟動腳本"
echo "========================"

echo ""
echo "可用的配置選項："
echo "1. 使用默認配置 (config.ini)"
echo "2. 使用 FITX 配置 (config_example_FITX.ini)"
echo "3. 使用 TXF 配置 (config_example_TXF.ini)"
echo "4. 自定義配置文件"
echo "5. 查看幫助信息"
echo "6. 查看版本信息"
echo "0. 退出"

echo ""
read -p "請選擇 (0-6): " choice

case $choice in
    1)
        echo "啟動默認配置..."
        python dde_monitor_new.py
        ;;
    2)
        echo "啟動 FITX 配置..."
        python dde_monitor_new.py -c config_example_FITX.ini
        ;;
    3)
        echo "啟動 TXF 配置..."
        python dde_monitor_new.py -c config_example_TXF.ini
        ;;
    4)
        read -p "請輸入配置文件名: " config_file
        echo "啟動自定義配置: $config_file"
        python dde_monitor_new.py -c "$config_file"
        ;;
    5)
        python dde_monitor_new.py --help
        read -p "按 Enter 繼續..."
        ;;
    6)
        python dde_monitor_new.py --version
        read -p "按 Enter 繼續..."
        ;;
    0)
        echo "退出..."
        exit 0
        ;;
    *)
        echo "無效選擇，請重新運行腳本"
        read -p "按 Enter 繼續..."
        ;;
esac

echo ""
echo "程式已結束"
read -p "按 Enter 繼續..."
