# DDE引擎包裝器部署指南

## 📋 概述

本指南提供DDE引擎包裝器在不同環境下的部署方法和最佳實踐。

## 🔧 系統需求

### 硬體需求

#### 最低需求
- **CPU**: Intel i5 或同等級處理器
- **記憶體**: 4GB RAM
- **硬碟**: 10GB 可用空間
- **網路**: 穩定的網路連線

#### 建議需求
- **CPU**: Intel i7 或同等級處理器（多核心）
- **記憶體**: 8GB+ RAM
- **硬碟**: SSD 20GB+ 可用空間
- **網路**: 低延遲網路連線

#### 生產環境需求
- **CPU**: Intel i9 或同等級處理器（8核心+）
- **記憶體**: 16GB+ RAM
- **硬碟**: NVMe SSD 50GB+ 可用空間
- **網路**: 專線或高速網路

### 軟體需求

#### 作業系統
- Windows 10/11 (64位元)
- Windows Server 2019/2022
- 支援 Python 3.8+ 的環境

#### 依賴軟體
- Python 3.8 或更高版本
- PySide6 (GUI框架)
- DDE服務 (如 XQ全球贏家)

## 🚀 安裝步驟

### 1. 環境準備

```bash
# 檢查Python版本
python --version

# 建立虛擬環境
python -m venv dde_wrapper_env

# 啟動虛擬環境
dde_wrapper_env\Scripts\activate

# 升級pip
python -m pip install --upgrade pip
```

### 2. 安裝依賴

```bash
# 安裝必要套件
pip install -r requirements.txt

# 驗證安裝
python -c "import PySide6; print('PySide6 安裝成功')"
```

### 3. 配置設定

#### 3.1 複製配置模板
```bash
# 複製主配置檔案
copy config\templates\multi_wrapper_config_template.ini config\multi_wrapper_config.ini

# 複製商品配置模板
copy config\templates\product_template.ini config\products\
```

#### 3.2 修改配置檔案
編輯 `config/multi_wrapper_config.ini`：

```ini
[General]
max_engines = 80
thread_pool_size = 20
log_level = INFO

[DDE]
service = XQTISC
topic = Quote

[Products]
symbols = FITXN07,FITXN08,FITMN07,FITMN08
data_types = tick,order,level2,daily

[Paths]
log_output = logs/
data_output = outputs/
```

### 4. 測試安裝

```bash
# 執行簡單測試
python examples\simple_test.py

# 執行配置測試
python tests\test_config.py

# 執行整合測試
python tests\test_integration.py
```

## 🏗️ 部署架構

### 開發環境

```
開發機器
├── Python 開發環境
├── DDE引擎包裝器
├── 測試用DDE服務
└── 開發工具 (IDE, Git等)
```

### 測試環境

```
測試伺服器
├── 模擬生產環境
├── 完整的DDE服務
├── 自動化測試套件
└── 效能監控工具
```

### 生產環境

```
生產伺服器
├── 高可用性配置
├── 實時DDE服務
├── 監控和告警系統
├── 備份和恢復機制
└── 日誌收集系統
```

## 📊 效能調優

### 記憶體優化

```ini
[Performance]
# 限制引擎數量
max_engines = 80

# 調整執行緒池大小
thread_pool_size = 20

# 設定記憶體限制
max_memory_usage = 200MB
```

### CPU優化

```ini
[Performance]
# 啟用多核心處理
enable_multiprocessing = true

# 設定CPU親和性
cpu_affinity = 0,1,2,3

# 調整處理優先級
process_priority = high
```

### I/O優化

```ini
[Performance]
# 使用SSD路徑
data_output = D:\SSD\dde_data\
log_output = D:\SSD\dde_logs\

# 啟用批次寫入
enable_batch_write = true
batch_size = 100
```

## 🔍 監控和維護

### 系統監控

#### 1. 效能指標監控
```python
# 監控腳本範例
import psutil
import time

def monitor_system():
    while True:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 記憶體使用率
        memory = psutil.virtual_memory()
        
        # 磁碟使用率
        disk = psutil.disk_usage('/')
        
        print(f"CPU: {cpu_percent}%, Memory: {memory.percent}%, Disk: {disk.percent}%")
        time.sleep(60)
```

#### 2. 應用程式監控
```python
# 包裝器狀態監控
def monitor_wrapper(wrapper):
    status = wrapper.get_status()
    
    # 檢查引擎狀態
    if status['active_engines'] < status['total_engines']:
        print("警告: 部分引擎未運行")
    
    # 檢查記憶體使用
    if status['memory_usage_mb'] > 200:
        print("警告: 記憶體使用過高")
```

### 日誌管理

#### 日誌配置
```ini
[Logging]
log_level = INFO
log_file = logs/wrapper_{date}.log
max_log_size = 100MB
backup_count = 7
```

#### 日誌輪轉
```python
import logging
from logging.handlers import RotatingFileHandler

# 設定日誌輪轉
handler = RotatingFileHandler(
    'logs/wrapper.log',
    maxBytes=100*1024*1024,  # 100MB
    backupCount=7
)
```

### 備份策略

#### 1. 配置檔案備份
```bash
# 每日備份配置檔案
robocopy config backup\config_%date% /MIR
```

#### 2. 資料檔案備份
```bash
# 每小時備份資料檔案
robocopy outputs backup\data_%date%_%time% /MIR
```

#### 3. 日誌檔案歸檔
```bash
# 每週歸檔日誌檔案
7z a backup\logs_%date%.7z logs\*.log
```

## 🚨 故障排除

### 常見問題

#### 1. 引擎啟動失敗
**症狀**: 引擎無法創建或啟動
**原因**: 
- 記憶體不足
- DDE服務未啟動
- 配置檔案錯誤

**解決方案**:
```bash
# 檢查記憶體使用
tasklist /fi "imagename eq python.exe"

# 檢查DDE服務
sc query "DDE服務名稱"

# 驗證配置檔案
python tests\test_config.py
```

#### 2. 資料遺失
**症狀**: 部分DDE資料未記錄
**原因**:
- 磁碟空間不足
- 檔案權限問題
- 引擎處理延遲

**解決方案**:
```bash
# 檢查磁碟空間
dir outputs

# 檢查檔案權限
icacls outputs

# 調整處理參數
# 在配置檔案中增加緩衝區大小
```

#### 3. 效能問題
**症狀**: 系統響應緩慢
**原因**:
- CPU使用率過高
- 記憶體洩漏
- I/O瓶頸

**解決方案**:
```bash
# 效能分析
python examples\performance_test.py

# 調整配置參數
# 減少引擎數量或調整執行緒池大小
```

## 🔒 安全考量

### 檔案權限
```bash
# 設定適當的檔案權限
icacls config /grant Users:R
icacls outputs /grant Users:F
icacls logs /grant Users:F
```

### 網路安全
- 使用防火牆限制網路存取
- 定期更新系統和依賴套件
- 監控異常網路活動

### 資料保護
- 加密敏感配置資訊
- 定期備份重要資料
- 實施存取控制機制

## 📈 擴展指南

### 水平擴展
- 部署多個包裝器實例
- 使用負載均衡器分散流量
- 實施資料同步機制

### 垂直擴展
- 增加伺服器硬體資源
- 調整系統配置參數
- 優化應用程式效能

### 高可用性
- 實施主備切換機制
- 設定健康檢查和自動恢復
- 建立災難恢復計劃
