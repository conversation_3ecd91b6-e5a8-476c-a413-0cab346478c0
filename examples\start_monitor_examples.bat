@echo off
chcp 65001 >nul
echo DDE 監控程式啟動腳本
echo ========================

echo.
echo 可用的配置選項：
echo 1. 使用默認配置 (config.ini)
echo 2. 使用 FITX 配置 (config_example_FITX.ini)
echo 3. 使用 TXF 配置 (config_example_TXF.ini)
echo 4. 自定義配置文件
echo 5. 查看幫助信息
echo 6. 查看版本信息
echo 0. 退出

echo.
set /p choice=請選擇 (0-6): 

if "%choice%"=="1" (
    echo 啟動默認配置...
    python dde_monitor_new.py
) else if "%choice%"=="2" (
    echo 啟動 FITX 配置...
    python dde_monitor_new.py -c config_example_FITX.ini
) else if "%choice%"=="3" (
    echo 啟動 TXF 配置...
    python dde_monitor_new.py -c config_example_TXF.ini
) else if "%choice%"=="4" (
    set /p config_file=請輸入配置文件名: 
    echo 啟動自定義配置: !config_file!
    python dde_monitor_new.py -c "!config_file!"
) else if "%choice%"=="5" (
    python dde_monitor_new.py --help
    pause
) else if "%choice%"=="6" (
    python dde_monitor_new.py --version
    pause
) else if "%choice%"=="0" (
    echo 退出...
    exit /b 0
) else (
    echo 無效選擇，請重新運行腳本
    pause
)

echo.
echo 程式已結束
pause
