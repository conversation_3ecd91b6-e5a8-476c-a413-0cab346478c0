#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件生成工具

根據需求生成不同類型的配置文件
"""

import json
from pathlib import Path


def generate_minimal_config():
    """生成最小配置"""
    config = {
        "products": [
            {
                "symbol": "FITXN07",
                "service": "XQTISC",
                "topic": "Quote",
                "items": [
                    "FITXN07.TF-Price",
                    "FITXN07.TF-Volume"
                ],
                "enabled": True
            }
        ]
    }
    
    with open("config/minimal_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ 生成最小配置: config/minimal_config.json")


def generate_recommended_config():
    """生成推薦配置"""
    config = {
        "system": {
            "system_name": "DDE Actor System",
            "version": "1.0.0"
        },
        "dde": {
            "polling_interval": 1.0,
            "batch_size": 1000,
            "batch_timeout": 0.01
        },
        "file_output": {
            "batch_size": 1000,
            "flush_interval": 5.0
        },
        "products": [
            {
                "symbol": "FITXN07",
                "service": "XQTISC",
                "topic": "Quote",
                "items": [
                    "FITXN07.TF-Time",
                    "FITXN07.TF-TradingDate",
                    "FITXN07.TF-Open",
                    "FITXN07.TF-High",
                    "FITXN07.TF-Low",
                    "FITXN07.TF-Price",
                    "FITXN07.TF-TotalVolume",
                    "FITXN07.TF-Volume"
                ],
                "enabled": True
            },
            {
                "symbol": "FITXN08",
                "service": "XQTISC",
                "topic": "Quote",
                "items": [
                    "FITXN08.TF-Time",
                    "FITXN08.TF-TradingDate",
                    "FITXN08.TF-Open",
                    "FITXN08.TF-High",
                    "FITXN08.TF-Low",
                    "FITXN08.TF-Price",
                    "FITXN08.TF-TotalVolume",
                    "FITXN08.TF-Volume"
                ],
                "enabled": True
            }
        ]
    }
    
    with open("config/recommended_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ 生成推薦配置: config/recommended_config.json")


def generate_high_frequency_config():
    """生成高頻交易配置"""
    config = {
        "system": {
            "system_name": "DDE High Frequency Monitor",
            "version": "1.0.0"
        },
        "dde": {
            "polling_interval": 0.1,  # 100ms 高頻輪詢
            "batch_size": 100,        # 小批次快速處理
            "batch_timeout": 0.001    # 1ms 超時
        },
        "file_output": {
            "batch_size": 50,         # 小批次快速寫入
            "flush_interval": 1.0     # 1秒刷新
        },
        "products": [
            {
                "symbol": "FITXN07",
                "service": "XQTISC",
                "topic": "Quote",
                "items": [
                    "FITXN07.TF-Price",
                    "FITXN07.TF-Volume"
                ],
                "enabled": True
            }
        ]
    }
    
    with open("config/high_frequency_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ 生成高頻配置: config/high_frequency_config.json")


def generate_multi_product_config():
    """生成多產品配置"""
    products = []
    
    # 期貨產品
    futures = ["FITXN07", "FITXN08", "FITXN09"]
    for symbol in futures:
        products.append({
            "symbol": symbol,
            "service": "XQTISC",
            "topic": "Quote",
            "items": [
                f"{symbol}.TF-Time",
                f"{symbol}.TF-TradingDate",
                f"{symbol}.TF-Open",
                f"{symbol}.TF-High",
                f"{symbol}.TF-Low",
                f"{symbol}.TF-Price",
                f"{symbol}.TF-TotalVolume",
                f"{symbol}.TF-Volume"
            ],
            "enabled": True
        })
    
    # 股票產品（範例）
    stocks = ["2330", "2317", "2454"]
    for symbol in stocks:
        products.append({
            "symbol": symbol,
            "service": "XQTISC",
            "topic": "Quote",
            "items": [
                f"{symbol}.TW-Time",
                f"{symbol}.TW-TradingDate",
                f"{symbol}.TW-Open",
                f"{symbol}.TW-High",
                f"{symbol}.TW-Low",
                f"{symbol}.TW-Price",
                f"{symbol}.TW-TotalVolume",
                f"{symbol}.TW-Volume"
            ],
            "enabled": False  # 預設禁用，需要時手動啟用
        })
    
    config = {
        "system": {
            "system_name": "DDE Multi-Product Monitor",
            "version": "1.0.0"
        },
        "dde": {
            "polling_interval": 2.0,  # 多產品時使用較慢的輪詢
            "batch_size": 2000,       # 大批次處理
            "batch_timeout": 0.05     # 較長的超時
        },
        "file_output": {
            "batch_size": 2000,
            "flush_interval": 10.0    # 較長的刷新間隔
        },
        "products": products
    }
    
    with open("config/multi_product_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ 生成多產品配置: config/multi_product_config.json")
    print(f"   包含 {len(futures)} 個期貨產品（啟用）")
    print(f"   包含 {len(stocks)} 個股票產品（禁用）")


def create_config_readme():
    """創建配置說明文件"""
    readme_content = """# 配置文件使用指南

## 📁 可用的配置文件

| 配置文件 | 用途 | 特點 |
|---------|------|------|
| `minimal_config.json` | 最小測試 | 僅1個產品，2個項目 |
| `recommended_config.json` | 推薦使用 | 2個產品，完整項目 |
| `high_frequency_config.json` | 高頻交易 | 100ms輪詢，快速處理 |
| `multi_product_config.json` | 多產品監控 | 6個產品，適合大規模監控 |
| `simple_config.json` | 精簡版本 | 手動創建的精簡配置 |

## 🚀 使用方法

```bash
# 最小測試
python clean_dde_monitor.py --config config/minimal_config.json

# 推薦配置
python clean_dde_monitor.py --config config/recommended_config.json

# 高頻交易
python clean_dde_monitor.py --config config/high_frequency_config.json

# 多產品監控
python clean_dde_monitor.py --config config/multi_product_config.json
```

## ⚙️ 配置調整建議

### 輪詢間隔 (polling_interval)
- **0.1-0.5秒**: 高頻交易，高CPU使用
- **1.0-2.0秒**: 一般監控，平衡性能
- **5.0秒以上**: 低頻監控，省資源

### 批次大小 (batch_size)
- **50-100**: 低延遲，高頻處理
- **500-1000**: 平衡性能
- **2000以上**: 高吞吐量，可能有延遲

### 刷新間隔 (flush_interval)
- **1.0秒**: 即時寫入，高IO
- **5.0秒**: 平衡性能
- **10.0秒以上**: 批量寫入，省IO

## 🔧 自定義配置

1. 複製推薦配置
2. 修改產品列表
3. 調整性能參數
4. 測試運行

## ⚠️ 注意事項

1. `data_types` 配置不起作用
2. 只修改實際使用的配置項目
3. 測試新配置前先備份數據
"""
    
    with open("config/README.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 生成配置說明: config/README.md")


def main():
    """主函數"""
    print("🔧 DDE Actor System 配置生成工具")
    print("=" * 50)
    
    # 確保配置目錄存在
    Path("config").mkdir(exist_ok=True)
    
    # 生成各種配置文件
    generate_minimal_config()
    generate_recommended_config()
    generate_high_frequency_config()
    generate_multi_product_config()
    create_config_readme()
    
    print(f"\n🎉 配置文件生成完成!")
    print("=" * 50)
    print("📋 生成的文件:")
    print("   config/minimal_config.json      - 最小測試配置")
    print("   config/recommended_config.json  - 推薦使用配置")
    print("   config/high_frequency_config.json - 高頻交易配置")
    print("   config/multi_product_config.json - 多產品配置")
    print("   config/README.md                - 使用說明")
    print("\n💡 建議:")
    print("   新用戶使用: recommended_config.json")
    print("   高頻交易使用: high_frequency_config.json")
    print("   多產品監控使用: multi_product_config.json")


if __name__ == "__main__":
    main()
