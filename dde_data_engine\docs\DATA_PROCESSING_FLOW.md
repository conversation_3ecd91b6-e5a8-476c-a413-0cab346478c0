# DDE 引擎版資料處理流程文件

## 概述

本文件詳細說明 DDE 引擎版 (`dde_data_engine`) 從接收資料到完整輸出的完整流程。引擎版採用模組化設計，將資料處理邏輯抽象為獨立的處理器，提供更好的可重用性和可測試性。

## 核心架構

### 主要模組
- **DDEDataProcessor**: 核心資料處理器
- **DataFileHandler**: 檔案輸出處理器  
- **DataProcessorConfig**: 配置管理器
- **data_structures**: 資料結構定義

### 資料結構
- **ItemData**: DDE項目資料結構
- **RawDataRow**: 原始資料行結構
- **ProcessorStats**: 處理器統計資訊

## 完整資料處理流程

### 1. 資料接收入口 (`process_dde_data` 方法)

```python
def process_dde_data(self, item: str, value: str) -> bool:
    """處理DDE資料更新 - 主要入口點"""
    try:
        with self._lock:
            self.stats.total_received += 1
            self.stats.last_update_time = datetime.now()
            
            # 觸發資料接收回調
            if self.on_data_received:
                self.on_data_received(item, value)
            
            # 1. 檢查是否需要項目重複換行
            repeat_check_result = self._check_item_repeat_newline(item, value)
            if repeat_check_result:
                return True
            
            # 2. 更新原始資料
            self._update_raw_data(item, value)
            
            # 3. 更新項目資料
            self._update_items_data(item, value)
            
            # 4. 更新最後接收時間
            self.last_advise_time = time.time()
            
            self.stats.total_processed += 1
            return True
            
    except Exception as e:
        self.logger.error(f"處理DDE資料失敗: {str(e)}")
        return False
```

### 2. 項目重複換行檢查 (`_check_item_repeat_newline` 方法)

```python
def _check_item_repeat_newline(self, item: str, value: str) -> bool:
    """檢查是否需要項目重複換行"""
    try:
        if not self.raw_data:
            # 第一次接收資料，沒有當前行
            return False
        
        current_row = self.raw_data[0]
        
        # 檢查項目是否已存在於當前行
        if item in current_row.values:
            # 項目重複，觸發檢查邏輯
            
            # 補齊缺失資料
            self._fill_missing_data(current_row)
            
            # 檢查值變化
            enable_check = self.config.get_bool('enable_value_change_check', True)
            
            if enable_check:
                has_changed = self._check_value_change(current_row)
                
                if has_changed:
                    # 值有變化，儲存完整資料行
                    self._save_complete_row(current_row)
                    self._create_new_row()
                    self._add_item_to_current_row(item, value)
                    return True
                else:
                    # 值未變化，跳過資料行
                    self._skip_row(current_row)
                    self._create_new_row()
                    self._add_item_to_current_row(item, value)
                    return True
            else:
                # 未啟用值變化檢查，直接儲存資料行
                self._save_complete_row(current_row)
                self._create_new_row()
                self._add_item_to_current_row(item, value)
                return True
                
        return False
        
    except Exception as e:
        self.logger.error(f"項目重複檢查失敗: {str(e)}")
        return False
```

### 3. 時間間隔檢查 (`check_time_interval` 方法)

```python
def check_time_interval(self) -> bool:
    """檢查時間間隔換行"""
    try:
        if not self.config.get_bool('enable_time_newline', True):
            return False
        
        if self.last_advise_time is None:
            return False
        
        current_time = time.time()
        elapsed = current_time - self.last_advise_time
        
        if elapsed >= self.time_newline_interval:
            if not self.raw_data:
                return False
            
            current_row = self.raw_data[0]
            
            # 檢查行是否有資料
            if not current_row.values:
                return False
            
            # 檢查並補齊缺失資料
            if self._has_missing_data(current_row):
                self._fill_missing_data(current_row)
            
            # 檢查值變化
            if self.config.get_bool('enable_value_change_check', True):
                has_changed = self._check_value_change(current_row)
                
                if has_changed:
                    # 儲存完整資料行
                    self._save_complete_row(current_row)
                    self._create_new_row()
                else:
                    # 值未變化，不儲存資料行但建立新行
                    self._create_new_row()
            else:
                # 未啟用值變化檢查，直接儲存資料行
                self._save_complete_row(current_row)
                self._create_new_row()
            
            # 更新時間戳記
            self.last_advise_time = current_time
            return True
        
        return False
        
    except Exception as e:
        self.logger.error(f"檢查時間間隔失敗: {str(e)}")
        return False
```

### 4. 值變化檢查 (`_check_value_change` 方法)

引擎版支援三種值變化檢查模式：

#### 4.1 單項檢查模式 (single)
```python
if check_mode == 'single':
    # 單項檢查模式
    if not check_items:
        return True
    
    # 查找檢查項目的代碼
    check_item_code = None
    for item_code, item_data in self.items_data.items():
        if item_data.name == check_items:
            check_item_code = item_code
            break
    
    if check_item_code:
        current_value = current_row.values.get(check_item_code, "")
        previous_value = last_saved_row.values.get(check_item_code, "")
        has_changed = (current_value != previous_value)
        return has_changed
    else:
        return True
```

#### 4.2 多項檢查模式 (multiple)
```python
elif check_mode == 'multiple':
    # 多項檢查模式
    if not check_items:
        return True
    
    check_items_list = [item.strip() for item in check_items.split(',') if item.strip()]
    
    for check_item_name in check_items_list:
        # 查找檢查項目的代碼
        check_item_code = None
        for item_code, item_data in self.items_data.items():
            if item_data.name == check_item_name:
                check_item_code = item_code
                break
        
        if check_item_code:
            current_value = current_row.values.get(check_item_code, "")
            previous_value = last_saved_row.values.get(check_item_code, "")
            if current_value != previous_value:
                return True
    
    return False
```

#### 4.3 全部檢查模式 (all)
```python
elif check_mode == 'all':
    # 全部檢查模式 - 檢查所有DDE項目
    for item_code in current_row.values.keys():
        # 跳過時間戳記相關項目
        if item_code in ['receive_date', 'receive_time']:
            continue
        
        current_value = current_row.values.get(item_code, "")
        previous_value = last_saved_row.values.get(item_code, "")
        if current_value != previous_value:
            return True
    
    return False
```

### 5. 資料補齊 (`_fill_missing_data` 方法)

```python
def _fill_missing_data(self, row: RawDataRow):
    """補齊缺失的資料項目"""
    try:
        for item_code, item_data in self.items_data.items():
            if item_code not in row.values:
                row.values[item_code] = item_data.value or ""
        
        row.is_complete = True
        
    except Exception as e:
        self.logger.error(f"補齊資料失敗: {str(e)}")
```

### 6. 資料儲存 (`_save_complete_row` 方法)

```python
def _save_complete_row(self, row: RawDataRow):
    """保存完整資料行"""
    try:
        # 確保資料行已補齊
        if not row.is_complete:
            self._fill_missing_data(row)
        
        # 使用檔案處理器保存
        if self.file_handler:
            self.file_handler.save_row(row, is_complete=True)
        
        # 觸發回調
        if self.on_row_saved:
            self.on_row_saved(row)
        
        self.stats.total_saved += 1
        
    except Exception as e:
        self.logger.error(f"保存完整資料行失敗: {str(e)}")
```

## 檔案輸出處理

### DataFileHandler 檔案處理器

引擎版使用獨立的檔案處理器處理所有檔案輸出：

```python
def save_row(self, row: RawDataRow, is_complete: bool = False):
    """保存資料行"""
    try:
        # 確保標題行已初始化
        if not self._headers_initialized:
            self.init_file_headers()
        
        # 保存到完整資料檔案
        if is_complete and self.enable_complete_data_file and self.complete_data_file:
            self._save_to_complete_data_file(row)
        
        # 保存到資料檔案
        if self.enable_data_file and self.data_file:
            self._save_to_data_file(row)
        
        # 記錄到日誌檔案
        if self.enable_log_file and self.log_file:
            self._log_to_file(row, is_complete)
            
    except Exception as e:
        self.logger.error(f"保存資料行失敗: {str(e)}")
```

### 完整資料檔案輸出格式

```python
def _save_to_complete_data_file(self, row: RawDataRow):
    """保存到完整資料檔案"""
    try:
        with open(self.complete_data_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 構建資料行
            data_row = [row.receive_date, row.receive_time]
            
            # 按照項目順序添加值
            for item_code, item_data in self.items_data.items():
                value = row.values.get(item_code, "")
                data_row.append(value)
            
            writer.writerow(data_row)
            
    except Exception as e:
        self.logger.error(f"保存到完整資料檔案失敗: {str(e)}")
```

## 配置管理

### DataProcessorConfig 配置選項

引擎版提供完整的配置管理：

```python
defaults = {
    # 時間間隔設定
    'enable_time_newline': True,
    'time_newline_interval': 0.800,
    
    # 值變化檢查設定
    'enable_value_change_check': True,
    'value_change_check_mode': 'single',  # single, multiple, all
    'value_change_check_items': '',
    
    # 檔案輸出設定
    'enable_data_file': False,
    'enable_complete_data_file': True,
    'enable_log_file': True,
    
    # 資料處理設定
    'max_history_rows': 10,
    'max_raw_data_queue': 1000,
    
    # 日誌設定
    'log_level': 'INFO',
    'debug_mode': False
}
```

## 統計資訊

### ProcessorStats 統計追蹤

```python
@dataclass
class ProcessorStats:
    total_received: int = 0      # 總接收筆數
    total_processed: int = 0     # 總處理筆數
    total_saved: int = 0         # 總保存筆數
    total_skipped: int = 0       # 總跳過筆數
    last_update_time: Optional[datetime] = None  # 最後更新時間
```

## 回調機制

引擎版提供完整的回調機制：

```python
# 資料接收回調
self.on_data_received: Optional[Callable[[str, str], None]] = None

# 資料行保存回調
self.on_row_saved: Optional[Callable[[RawDataRow], None]] = None

# 資料行跳過回調
self.on_row_skipped: Optional[Callable[[RawDataRow], None]] = None
```

## 執行緒安全

引擎版使用 `threading.RLock()` 確保執行緒安全：

```python
# 執行緒安全
self._lock = threading.RLock()

# 在關鍵操作中使用鎖
with self._lock:
    # 執行資料處理操作
    pass
```

## 關鍵特性

### 1. 換行觸發條件
- **項目重複**: 當收到已存在於當前行中的項目資料時
- **時間間隔**: 當距離上次更新超過設定的時間間隔時

### 2. 資料完整性保證
- 每次換行前檢查並補齊缺失資料
- 使用項目資料容器中的最新值補齊缺失資料
- 確保每行資料都包含所有項目的值

### 3. 值變化檢測
- **單項模式**: 檢查指定單一項目的值變化
- **多項模式**: 檢查指定多個項目的值變化（任一變化即觸發）
- **全部模式**: 檢查所有項目的值變化（任一變化即觸發）

### 4. 檔案輸出
- **完整資料檔案**: 記錄經過處理的完整資料行（CSV格式）
- **資料檔案**: 記錄所有接收到的資料（逐項記錄）
- **日誌檔案**: 記錄處理過程的詳細資訊

### 5. 錯誤處理
- 每個步驟都有完整的錯誤處理機制
- 詳細的日誌記錄
- 異常情況下的資料保護

## 與其他版本的差異

### 相較於模組化版本
- **更好的模組化**: 將資料處理邏輯完全抽象為獨立模組
- **更強的可重用性**: 可以輕鬆整合到其他應用程式中
- **更完整的配置管理**: 提供統一的配置介面
- **更豐富的回調機制**: 支援多種事件回調
- **更好的執行緒安全**: 內建執行緒安全機制

### 相較於v6版本
- **現代化的程式架構**: 使用dataclass和類型提示
- **更靈活的配置**: 支援動態配置更新
- **更詳細的統計資訊**: 提供完整的處理統計
- **更好的錯誤處理**: 更完善的異常處理機制

## 使用範例

詳細的使用範例請參考 `example.py` 檔案，其中包含：
- 單項檢查模式範例
- 多項檢查模式範例  
- 全部檢查模式範例
- 完整的配置和回調設定

## 技術實現細節

### 資料結構設計

#### RawDataRow 原始資料行
```python
@dataclass
class RawDataRow:
    receive_date: str           # 接收日期 (YYYY-MM-DD)
    receive_time: str           # 接收時間 (HH:MM:SS.fff)
    values: Dict[str, str]      # 項目值字典 {項目代碼: 值}
    is_complete: bool = False   # 是否為完整資料行
```

#### ItemData 項目資料
```python
@dataclass
class ItemData:
    name: str                           # 項目顯示名稱
    code: str                           # DDE項目代碼
    value: Optional[str] = None         # 當前值
    update_time: Optional[datetime] = None  # 最後更新時間
    status: str = "未訂閱"              # 訂閱狀態
```

### 資料流轉機制

#### 1. 資料接收流程
```
DDE資料 → process_dde_data() → 統計更新 → 回調觸發 → 重複檢查 → 資料更新 → 項目更新 → 時間記錄
```

#### 2. 換行決策流程
```
項目重複檢查 → 補齊缺失資料 → 值變化檢查 → 決定保存/跳過 → 建立新行 → 添加新項目
```

#### 3. 時間間隔檢查流程
```
時間檢查 → 間隔判斷 → 資料補齊 → 值變化檢查 → 保存決策 → 新行建立 → 時間更新
```

### 記憶體管理

#### deque 資料佇列
```python
self.raw_data: deque = deque(maxlen=config.get_int('max_raw_data_queue', 1000))
```
- 使用 `collections.deque` 實現固定大小的資料佇列
- 自動移除舊資料，防止記憶體洩漏
- 支援高效的前端插入和後端移除操作

#### 資料行管理
- 新資料行總是插入到佇列前端 (`appendleft`)
- 歷史資料行用於值變化比較
- 完整資料行標記 (`is_complete`) 用於識別已處理的資料

### 檔案處理機制

#### 標題行初始化
```python
def init_file_headers(self):
    # 只在第一次寫入時創建標題行
    if self._headers_initialized:
        return

    # 完整資料檔案標題: 接收日期,接收時間,項目1,項目2,...
    headers = ['接收日期', '接收時間']
    for item_code, item_data in self.items_data.items():
        headers.append(item_data.name)
```

#### CSV 寫入格式
- **完整資料檔案**: 每行包含時間戳記和所有項目值
- **資料檔案**: 每個項目值單獨一行記錄
- **日誌檔案**: 純文字格式記錄處理狀態

### 配置系統設計

#### 類型安全的配置存取
```python
def get_bool(self, key: str, default: bool = False) -> bool:
    value = self.get(key, default)
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        return value.lower() in ('true', '1', 'yes', 'on')
    return bool(value)
```

#### 配置驗證機制
```python
def validate(self) -> List[str]:
    errors = []

    # 檢查時間間隔
    interval = self.get_float('time_newline_interval')
    if interval <= 0:
        errors.append("time_newline_interval 必須大於 0")

    # 檢查值變化檢查模式
    mode = self.get('value_change_check_mode', '').lower()
    if mode not in ['single', 'multiple', 'all']:
        errors.append("value_change_check_mode 必須是 single, multiple 或 all")

    return errors
```

### 執行緒安全實現

#### RLock 重入鎖
```python
self._lock = threading.RLock()

# 在所有關鍵操作中使用
with self._lock:
    # 資料處理操作
    self.stats.total_received += 1
    # 資料結構修改
    self.raw_data.appendleft(new_row)
```

#### 原子操作保證
- 所有統計更新都在鎖保護下進行
- 資料結構修改操作具有原子性
- 回調函數在鎖外執行，避免死鎖

### 效能最佳化

#### 1. 資料查找最佳化
```python
# 使用字典快速查找項目代碼
check_item_code = None
for item_code, item_data in self.items_data.items():
    if item_data.name == check_items:
        check_item_code = item_code
        break
```

#### 2. 記憶體使用最佳化
- 使用 `deque` 限制歷史資料數量
- 及時清理不需要的資料行
- 避免深度複製大型資料結構

#### 3. I/O 最佳化
- 使用 `csv.writer` 進行高效的CSV寫入
- 檔案操作使用適當的緩衝區大小
- 避免頻繁的檔案開關操作

### 錯誤處理策略

#### 分層錯誤處理
```python
try:
    # 主要處理邏輯
    result = self._process_data()
    return result
except SpecificException as e:
    # 特定異常處理
    self.logger.warning(f"特定錯誤: {str(e)}")
    return default_value
except Exception as e:
    # 通用異常處理
    self.logger.error(f"未預期錯誤: {str(e)}")
    return False
```

#### 資料完整性保護
- 在異常情況下保護已處理的資料
- 提供資料恢復機制
- 記錄詳細的錯誤資訊用於除錯

### 擴展性設計

#### 回調機制擴展
```python
# 支援多種事件回調
self.on_data_received: Optional[Callable[[str, str], None]] = None
self.on_row_saved: Optional[Callable[[RawDataRow], None]] = None
self.on_row_skipped: Optional[Callable[[RawDataRow], None]] = None

# 未來可以輕鬆添加更多回調
# self.on_error: Optional[Callable[[Exception], None]] = None
# self.on_stats_updated: Optional[Callable[[ProcessorStats], None]] = None
```

#### 配置系統擴展
- 支援動態配置更新
- 配置驗證機制可擴展
- 支援不同類型的配置值轉換

## 整合指南

### 與現有系統整合

#### 1. 作為獨立模組使用
```python
from dde_data_engine import DDEDataProcessor, DataProcessorConfig, ItemData

# 創建配置
config = DataProcessorConfig({
    'enable_value_change_check': True,
    'value_change_check_mode': 'single',
    'value_change_check_items': '總量'
})

# 創建處理器
processor = DDEDataProcessor(config=config, items_data=items_data)

# 處理資料
processor.process_dde_data("VOLUME", "1000")
```

#### 2. 與GUI應用程式整合
```python
# 設定回調函數更新GUI
def update_gui(item, value):
    # 更新GUI顯示
    self.update_item_display(item, value)

processor.on_data_received = update_gui
```

#### 3. 與多產品系統整合
```python
# 為每個產品創建獨立的處理器
processors = {}
for product_code in product_list:
    config = load_product_config(product_code)
    items_data = load_product_items(product_code)
    processors[product_code] = DDEDataProcessor(config, items_data)
```

### 測試策略

#### 單元測試
- 測試各個方法的獨立功能
- 模擬不同的資料輸入情況
- 驗證錯誤處理機制

#### 整合測試
- 測試完整的資料處理流程
- 驗證檔案輸出正確性
- 測試多執行緒環境下的穩定性

#### 效能測試
- 測試高頻資料處理能力
- 記憶體使用情況監控
- I/O 效能評估

---

*本文件基於 DDE 引擎版 (dde_data_engine) 的實際程式碼內容編寫，反映了引擎版的完整資料處理流程和架構設計。*
