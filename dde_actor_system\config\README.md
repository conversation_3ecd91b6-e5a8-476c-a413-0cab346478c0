# 配置文件使用指南

## 📁 可用的配置文件

| 配置文件 | 用途 | 特點 |
|---------|------|------|
| `minimal_config.json` | 最小測試 | 僅1個產品，2個項目 |
| `recommended_config.json` | 推薦使用 | 2個產品，完整項目 |
| `high_frequency_config.json` | 高頻交易 | 100ms輪詢，快速處理 |
| `multi_product_config.json` | 多產品監控 | 6個產品，適合大規模監控 |
| `simple_config.json` | 精簡版本 | 手動創建的精簡配置 |

## 🚀 使用方法

```bash
# 最小測試
python clean_dde_monitor.py --config config/minimal_config.json

# 推薦配置
python clean_dde_monitor.py --config config/recommended_config.json

# 高頻交易
python clean_dde_monitor.py --config config/high_frequency_config.json

# 多產品監控
python clean_dde_monitor.py --config config/multi_product_config.json
```

## ⚙️ 配置調整建議

### 輪詢間隔 (polling_interval)
- **0.1-0.5秒**: 高頻交易，高CPU使用
- **1.0-2.0秒**: 一般監控，平衡性能
- **5.0秒以上**: 低頻監控，省資源

### 批次大小 (batch_size)
- **50-100**: 低延遲，高頻處理
- **500-1000**: 平衡性能
- **2000以上**: 高吞吐量，可能有延遲

### 刷新間隔 (flush_interval)
- **1.0秒**: 即時寫入，高IO
- **5.0秒**: 平衡性能
- **10.0秒以上**: 批量寫入，省IO

## 🔧 自定義配置

1. 複製推薦配置
2. 修改產品列表
3. 調整性能參數
4. 測試運行

## ⚠️ 注意事項

1. `data_types` 配置不起作用
2. 只修改實際使用的配置項目
3. 測試新配置前先備份數據
