#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 資料結構定義
定義DDE資料處理中使用的核心資料結構
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Optional


@dataclass
class ItemData:
    """DDE項目資料結構
    用於儲存每個DDE項目的相關資訊
    
    屬性:
        name (str): 項目顯示名稱
        code (str): DDE項目代碼
        value (Optional[str]): 當前值
        update_time (Optional[datetime]): 最後更新時間
        status (str): 訂閱狀態
    """
    name: str
    code: str
    value: Optional[str] = None
    update_time: Optional[datetime] = None
    status: str = "未訂閱"  # 未訂閱, 已測試, 已訂閱, 訂閱失敗, 測試失敗


@dataclass
class RawDataRow:
    """原始資料行結構
    用於儲存每一筆原始資料的完整資訊
    
    屬性:
        receive_date (str): 接收日期 (YYYY-MM-DD)
        receive_time (str): 接收時間 (HH:MM:SS.ffffff)
        values (Dict[str, str]): 項目值字典，key為項目代碼，value為項目值
        is_complete (bool): 是否為完整資料行
    """
    receive_date: str
    receive_time: str
    values: Dict[str, str]
    is_complete: bool = False


@dataclass
class DataRow:
    """處理後的資料行結構
    用於儲存處理後的資料行資訊
    
    屬性:
        timestamp (datetime): 時間戳記
        values (Dict[str, str]): 項目值字典
        is_complete (bool): 是否為完整資料行
    """
    timestamp: datetime
    values: Dict[str, str]
    is_complete: bool = False


@dataclass
class ProcessorStats:
    """處理器統計資訊
    
    屬性:
        total_received (int): 總接收筆數
        total_processed (int): 總處理筆數
        total_saved (int): 總保存筆數
        total_skipped (int): 總跳過筆數
        last_update_time (Optional[datetime]): 最後更新時間
    """
    total_received: int = 0
    total_processed: int = 0
    total_saved: int = 0
    total_skipped: int = 0
    last_update_time: Optional[datetime] = None
    
    def reset(self):
        """重置統計資訊"""
        self.total_received = 0
        self.total_processed = 0
        self.total_saved = 0
        self.total_skipped = 0
        self.last_update_time = None
