#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 資料處理引擎
獨立的DDE資料處理模組，提供完整的資料接收、處理、去重和輸出功能

主要功能:
- DDE 資料接收和初始值建立
- 資料補全和完整行建立
- 時間間隔和重複項目檢查
- 值變化檢測和有效行識別
- 資料輸出和記錄

版本: 1.0.0
作者: DDE Monitor Team
"""

from .data_structures import ItemData, RawDataRow, DataRow
from .data_processor import DDEDataProcessor
from .file_handler import DataFileHandler
from .config_handler import DataProcessorConfig

__version__ = "1.0.0"
__all__ = [
    "ItemData",
    "RawDataRow", 
    "DataRow",
    "DDEDataProcessor",
    "DataFileHandler",
    "DataProcessorConfig"
]
