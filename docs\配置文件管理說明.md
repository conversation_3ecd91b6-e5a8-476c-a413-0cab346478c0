# DDE 監控程式 - 配置文件管理說明

## 概述

DDE 監控程式現在支持通過命令行參數指定不同的配置文件，這樣您就可以：
- 為不同的交易品種創建專用配置
- 避免複製多個程式代碼資料夾
- 更方便地管理多個監控任務

## 使用方法

### 基本用法

```bash
# 使用默認配置文件 config.ini
python dde_monitor_new.py

# 使用指定配置文件
python dde_monitor_new.py -c config_FITX.ini
python dde_monitor_new.py --config config_TXF.ini

# 使用完整路徑的配置文件
python dde_monitor_new.py -c "D:\Trading\configs\my_config.ini"
```

### 查看幫助信息

```bash
python dde_monitor_new.py --help
python dde_monitor_new.py --version
```

## 配置文件管理建議

### 1. 命名規範

建議使用以下命名規範：
- `config.ini` - 默認配置文件
- `config_品種名.ini` - 特定品種配置
- `config_時段名.ini` - 特定時段配置

例如：
- `config_FITX.ini` - FITX期貨專用
- `config_TXF.ini` - TXF期貨專用
- `config_夜盤.ini` - 夜盤交易專用
- `config_日盤.ini` - 日盤交易專用

### 2. 目錄結構

推薦的目錄結構：
```
dde_monitor/
├── dde_monitor_new.py          # 主程式
├── config.ini                  # 默認配置
├── config_FITX.ini            # FITX配置
├── config_TXF.ini             # TXF配置
├── configs/                    # 配置文件目錄（可選）
│   ├── trading_session_1.ini
│   └── trading_session_2.ini
├── logs/                       # 日誌目錄
├── outputs/                    # 輸出目錄
└── ...
```

### 3. 配置文件差異化

不同配置文件可以設定不同的：

#### 監控項目
```ini
[Items]
# FITX期貨
item1_code = FITXN06.TF-Price

# TXF期貨  
item1_code = TXFN06.TF-Price
```

#### 輸出路徑
```ini
[OutputPath]
# FITX專用路徑
complete_data_file = ./outputs/FITX/complete_data.csv

# TXF專用路徑
complete_data_file = ./outputs/TXF/complete_data.csv
```

#### 自動化設定
```ini
[AutoConnect]
# 日盤設定
schedule_connect_times = 08:45:00

# 夜盤設定
schedule_connect_times = 15:00:00,21:00:00
```

#### 日誌級別
```ini
[Logging]
# 調試用配置
log_level = DEBUG

# 生產用配置
log_level = WARNING
```

## 實際使用範例

### 範例1：監控多個品種

```bash
# 終端1 - 監控FITX
python dde_monitor_new.py -c config_FITX.ini

# 終端2 - 監控TXF  
python dde_monitor_new.py -c config_TXF.ini
```

### 範例2：不同時段使用不同配置

```bash
# 日盤時段
python dde_monitor_new.py -c config_日盤.ini

# 夜盤時段
python dde_monitor_new.py -c config_夜盤.ini
```

### 範例3：測試和生產環境

```bash
# 測試環境（詳細日誌）
python dde_monitor_new.py -c config_test.ini

# 生產環境（簡潔日誌）
python dde_monitor_new.py -c config_prod.ini
```

## 配置文件創建

### 方法1：複製現有配置
```bash
cp config.ini config_FITX.ini
# 然後編輯 config_FITX.ini
```

### 方法2：使用示例配置
程式提供了示例配置文件：
- `config_example_FITX.ini`
- `config_example_TXF.ini`

可以複製並修改這些示例文件。

## 注意事項

1. **配置文件路徑**：如果指定的配置文件不存在，程式會報錯並退出
2. **相對路徑**：配置文件中的路徑是相對於程式執行目錄的
3. **編碼格式**：配置文件必須使用 UTF-8 編碼
4. **備份重要配置**：建議定期備份重要的配置文件

## 故障排除

### 配置文件不存在
```
錯誤：指定的配置文件不存在: config_xxx.ini
解決：檢查文件路徑和文件名是否正確
```

### 配置文件格式錯誤
```
錯誤：載入設定檔失敗
解決：檢查配置文件的INI格式是否正確
```

### 權限問題
```
錯誤：無法讀取配置文件
解決：檢查文件權限，確保程式有讀取權限
```
