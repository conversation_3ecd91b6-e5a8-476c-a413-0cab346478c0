#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自動化設定對話框
非模態設定視窗，用於調整自動化功能
"""

import configparser
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                             QCheckBox, QSpinBox, QDoubleSpinBox, QTimeEdit,
                             QGroupBox, QTabWidget, QLineEdit, QComboBox,
                             QPushButton, QMessageBox, QWidget, QLabel)
from PySide6.QtCore import Qt, QTime, Signal

class AutoSettingsDialog(QDialog):
    """自動化設定對話框 - 非模態視窗"""
    
    # 信號定義
    settings_changed = Signal(dict)  # 設定變更信號
    
    def __init__(self, config: configparser.ConfigParser, parent=None):
        super().__init__(parent)
        self.config = config
        self.setWindowTitle("自動化功能設定")
        self.setModal(False)  # 設為非模態
        self.resize(500, 600)
        
        # 設定視窗屬性，確保不阻塞主程式
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)
        
        self.init_ui()
        self.load_current_settings()
        
    def init_ui(self):
        """初始化使用者介面"""
        layout = QVBoxLayout(self)
        
        # 建立分頁控制項
        tab_widget = QTabWidget()
        
        # 自動連線分頁
        auto_connect_tab = self.create_auto_connect_tab()
        tab_widget.addTab(auto_connect_tab, "自動連線")
        
        # 自動結束分頁
        auto_shutdown_tab = self.create_auto_shutdown_tab()
        tab_widget.addTab(auto_shutdown_tab, "自動結束")
        
        # 通知設定分頁
        notifications_tab = self.create_notifications_tab()
        tab_widget.addTab(notifications_tab, "通知設定")
        
        layout.addWidget(tab_widget)
        
        # 按鈕區域
        button_layout = QHBoxLayout()
        
        self.apply_btn = QPushButton("套用")
        self.apply_btn.clicked.connect(self.apply_settings)
        
        self.save_btn = QPushButton("儲存")
        self.save_btn.clicked.connect(self.save_settings)
        
        self.reset_btn = QPushButton("重置")
        self.reset_btn.clicked.connect(self.reset_settings)
        
        self.close_btn = QPushButton("關閉")
        self.close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.reset_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_auto_connect_tab(self):
        """建立自動連線設定分頁"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本設定群組
        basic_group = QGroupBox("基本設定")
        basic_layout = QFormLayout(basic_group)
        
        self.enable_auto_connect = QCheckBox("啟用自動連線")
        basic_layout.addRow(self.enable_auto_connect)
        
        self.auto_connect_mode = QComboBox()
        self.auto_connect_mode.addItems(["immediate", "delay", "schedule"])
        self.auto_connect_mode.setCurrentText("delay")
        basic_layout.addRow("連線模式:", self.auto_connect_mode)
        
        self.auto_connect_delay = QDoubleSpinBox()
        self.auto_connect_delay.setRange(0.1, 300.0)
        self.auto_connect_delay.setSuffix(" 秒")
        self.auto_connect_delay.setValue(30.0)
        basic_layout.addRow("延遲時間:", self.auto_connect_delay)
        
        layout.addWidget(basic_group)
        
        # 時間表設定群組
        schedule_group = QGroupBox("時間表設定")
        schedule_layout = QFormLayout(schedule_group)
        
        self.schedule_connect_times = QLineEdit()
        self.schedule_connect_times.setPlaceholderText("例如: 09:00:00-12:00:00;13:30:00-15:00:00")
        schedule_layout.addRow("連線時間表:", self.schedule_connect_times)
        
        # 跨日時間表已永遠啟用，移除設定選項
        
        self.prevent_weekend_startup = QCheckBox("防止週末起始連線")
        schedule_layout.addRow(self.prevent_weekend_startup)

        self.schedule_end_action = QComboBox()
        self.schedule_end_action.addItems(["unadvise_only", "disconnect"])
        self.schedule_end_action.setCurrentText("unadvise_only")
        schedule_layout.addRow("結束時間行為:", self.schedule_end_action)

        # 添加說明標籤
        end_action_help = QLabel("• unadvise_only: 僅取消訂閱，保持連線\n• disconnect: 完整斷線+取消訂閱")
        end_action_help.setStyleSheet("color: gray; font-size: 10px;")
        schedule_layout.addRow("", end_action_help)

        layout.addWidget(schedule_group)
        
        # 連接信號
        self.auto_connect_mode.currentTextChanged.connect(self.on_mode_changed)
        
        return widget
    
    def create_auto_shutdown_tab(self):
        """建立自動結束設定分頁"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本設定群組
        basic_group = QGroupBox("基本設定")
        basic_layout = QFormLayout(basic_group)
        
        self.enable_auto_shutdown = QCheckBox("啟用自動結束")
        basic_layout.addRow(self.enable_auto_shutdown)
        
        self.shutdown_time = QTimeEdit()
        self.shutdown_time.setDisplayFormat("HH:mm:ss")
        self.shutdown_time.setTime(QTime(17, 30, 0))
        basic_layout.addRow("結束時間:", self.shutdown_time)
        
        self.shutdown_buffer_seconds = QSpinBox()
        self.shutdown_buffer_seconds.setRange(0, 3600)
        self.shutdown_buffer_seconds.setSuffix(" 秒")
        self.shutdown_buffer_seconds.setValue(300)
        basic_layout.addRow("緩衝時間:", self.shutdown_buffer_seconds)
        
        self.shutdown_warning_seconds = QSpinBox()
        self.shutdown_warning_seconds.setRange(0, 600)
        self.shutdown_warning_seconds.setSuffix(" 秒")
        self.shutdown_warning_seconds.setValue(60)
        basic_layout.addRow("警告時間:", self.shutdown_warning_seconds)
        
        layout.addWidget(basic_group)
        
        # 結束行為群組
        behavior_group = QGroupBox("結束行為")
        behavior_layout = QFormLayout(behavior_group)
        
        self.force_shutdown = QCheckBox("強制結束 (不顯示確認對話框)")
        behavior_layout.addRow(self.force_shutdown)
        
        self.save_data_before_shutdown = QCheckBox("結束前保存資料")
        behavior_layout.addRow(self.save_data_before_shutdown)
        
        self.disconnect_before_shutdown = QCheckBox("結束前斷開連線")
        behavior_layout.addRow(self.disconnect_before_shutdown)
        
        self.cleanup_temp_files = QCheckBox("結束前清理暫存檔案")
        behavior_layout.addRow(self.cleanup_temp_files)
        
        layout.addWidget(behavior_group)
        
        return widget
    
    def create_notifications_tab(self):
        """建立通知設定分頁"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 通知設定群組
        notify_group = QGroupBox("通知設定")
        notify_layout = QFormLayout(notify_group)
        
        self.enable_system_notifications = QCheckBox("啟用系統通知")
        notify_layout.addRow(self.enable_system_notifications)
        
        self.enable_sound_notifications = QCheckBox("啟用聲音通知")
        notify_layout.addRow(self.enable_sound_notifications)
        
        layout.addWidget(notify_group)
        
        # 自動操作通知群組
        auto_notify_group = QGroupBox("自動操作通知")
        auto_notify_layout = QFormLayout(auto_notify_group)
        
        self.notify_auto_connect = QCheckBox("自動連線通知")
        auto_notify_layout.addRow(self.notify_auto_connect)
        
        self.notify_auto_disconnect = QCheckBox("自動斷線通知")
        auto_notify_layout.addRow(self.notify_auto_disconnect)
        
        self.notify_auto_shutdown = QCheckBox("自動結束通知")
        auto_notify_layout.addRow(self.notify_auto_shutdown)
        
        layout.addWidget(auto_notify_group)
        
        return widget
    
    def on_mode_changed(self, mode):
        """連線模式變更時的處理"""
        # 根據模式啟用/禁用相關控制項
        self.auto_connect_delay.setEnabled(mode == "delay")
        self.schedule_connect_times.setEnabled(mode == "schedule")
        # 跨日時間表已永遠啟用，移除設定控制
        self.prevent_weekend_startup.setEnabled(mode == "schedule")
        self.schedule_end_action.setEnabled(mode == "schedule")

    def load_current_settings(self):
        """載入當前設定"""
        try:
            # 自動連線設定
            if 'AutoConnect' in self.config:
                section = self.config['AutoConnect']
                self.enable_auto_connect.setChecked(section.getboolean('enable_auto_connect', False))
                self.auto_connect_mode.setCurrentText(section.get('auto_connect_mode', 'delay'))
                self.auto_connect_delay.setValue(section.getfloat('auto_connect_delay', 30.0))
                self.schedule_connect_times.setText(section.get('schedule_connect_times', ''))
                # 跨日時間表已永遠啟用，移除設定載入
                self.prevent_weekend_startup.setChecked(section.getboolean('prevent_weekend_startup', False))

                # 載入 schedule_end_action 設定
                schedule_end_action = section.get('schedule_end_action', 'unadvise_only')
                # 驗證設定值
                if schedule_end_action.lower() in ['disconnect', 'unadvise_only']:
                    self.schedule_end_action.setCurrentText(schedule_end_action.lower())
                else:
                    self.schedule_end_action.setCurrentText('unadvise_only')

            # 自動結束設定
            if 'AutoShutdown' in self.config:
                section = self.config['AutoShutdown']
                self.enable_auto_shutdown.setChecked(section.getboolean('enable_auto_shutdown', False))

                # 解析時間
                shutdown_time_str = section.get('shutdown_time', '17:30:00')
                try:
                    time_parts = shutdown_time_str.split(':')
                    if len(time_parts) >= 2:
                        hour = int(time_parts[0])
                        minute = int(time_parts[1])
                        second = int(time_parts[2]) if len(time_parts) > 2 else 0
                        self.shutdown_time.setTime(QTime(hour, minute, second))
                except:
                    self.shutdown_time.setTime(QTime(17, 30, 0))

                self.shutdown_buffer_seconds.setValue(section.getint('shutdown_buffer_seconds', 300))
                self.shutdown_warning_seconds.setValue(section.getint('shutdown_warning_seconds', 60))
                self.force_shutdown.setChecked(section.getboolean('force_shutdown', True))
                self.save_data_before_shutdown.setChecked(section.getboolean('save_data_before_shutdown', True))
                self.disconnect_before_shutdown.setChecked(section.getboolean('disconnect_before_shutdown', True))
                self.cleanup_temp_files.setChecked(section.getboolean('cleanup_temp_files', True))

            # 通知設定
            if 'Notifications' in self.config:
                section = self.config['Notifications']
                self.enable_system_notifications.setChecked(section.getboolean('enable_system_notifications', True))
                self.enable_sound_notifications.setChecked(section.getboolean('enable_sound_notifications', False))
                self.notify_auto_connect.setChecked(section.getboolean('notify_auto_connect', True))
                self.notify_auto_disconnect.setChecked(section.getboolean('notify_auto_disconnect', True))
                self.notify_auto_shutdown.setChecked(section.getboolean('notify_auto_shutdown', True))

            # 觸發模式變更處理
            self.on_mode_changed(self.auto_connect_mode.currentText())

        except Exception as e:
            QMessageBox.warning(self, "載入設定失敗", f"載入當前設定時發生錯誤:\n{str(e)}")

    def get_current_settings(self):
        """獲取當前設定值"""
        settings = {
            'AutoConnect': {
                'enable_auto_connect': self.enable_auto_connect.isChecked(),
                'auto_connect_mode': self.auto_connect_mode.currentText(),
                'auto_connect_delay': self.auto_connect_delay.value(),
                'schedule_connect_times': self.schedule_connect_times.text().strip(),
                # 跨日時間表已永遠啟用，移除設定儲存
                'prevent_weekend_startup': self.prevent_weekend_startup.isChecked(),
                'schedule_end_action': self.schedule_end_action.currentText()
            },
            'AutoShutdown': {
                'enable_auto_shutdown': self.enable_auto_shutdown.isChecked(),
                'shutdown_time': self.shutdown_time.time().toString("HH:mm:ss"),
                'shutdown_buffer_seconds': self.shutdown_buffer_seconds.value(),
                'shutdown_warning_seconds': self.shutdown_warning_seconds.value(),
                'force_shutdown': self.force_shutdown.isChecked(),
                'save_data_before_shutdown': self.save_data_before_shutdown.isChecked(),
                'disconnect_before_shutdown': self.disconnect_before_shutdown.isChecked(),
                'cleanup_temp_files': self.cleanup_temp_files.isChecked()
            },
            'Notifications': {
                'enable_system_notifications': self.enable_system_notifications.isChecked(),
                'enable_sound_notifications': self.enable_sound_notifications.isChecked(),
                'notify_auto_connect': self.notify_auto_connect.isChecked(),
                'notify_auto_disconnect': self.notify_auto_disconnect.isChecked(),
                'notify_auto_shutdown': self.notify_auto_shutdown.isChecked()
            }
        }
        return settings

    def apply_settings(self):
        """套用設定 (不儲存到檔案)"""
        try:
            settings = self.get_current_settings()
            self.settings_changed.emit(settings)
            QMessageBox.information(self, "套用成功", "設定已套用，但尚未儲存到檔案。")
        except Exception as e:
            QMessageBox.critical(self, "套用失敗", f"套用設定時發生錯誤:\n{str(e)}")

    def save_settings(self):
        """儲存設定到檔案"""
        try:
            settings = self.get_current_settings()

            # 更新設定檔
            for section_name, section_data in settings.items():
                if section_name not in self.config:
                    self.config.add_section(section_name)

                for key, value in section_data.items():
                    self.config.set(section_name, key, str(value))

            # 寫入檔案
            with open('config.ini', 'w', encoding='utf-8') as f:
                self.config.write(f)

            # 發送設定變更信號
            self.settings_changed.emit(settings)

            QMessageBox.information(self, "儲存成功", "設定已儲存到 config.ini 檔案。")

        except Exception as e:
            QMessageBox.critical(self, "儲存失敗", f"儲存設定時發生錯誤:\n{str(e)}")

    def reset_settings(self):
        """重置設定為預設值"""
        reply = QMessageBox.question(
            self,
            "重置確認",
            "確定要重置所有設定為預設值嗎？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 重置為預設值
            self.enable_auto_connect.setChecked(False)
            self.auto_connect_mode.setCurrentText("delay")
            self.auto_connect_delay.setValue(30.0)
            self.schedule_connect_times.setText("")
            # 跨日時間表已永遠啟用，移除重置設定
            self.prevent_weekend_startup.setChecked(False)
            self.schedule_end_action.setCurrentText("unadvise_only")

            self.enable_auto_shutdown.setChecked(False)
            self.shutdown_time.setTime(QTime(17, 30, 0))
            self.shutdown_buffer_seconds.setValue(300)
            self.shutdown_warning_seconds.setValue(60)
            self.force_shutdown.setChecked(True)
            self.save_data_before_shutdown.setChecked(True)
            self.disconnect_before_shutdown.setChecked(True)
            self.cleanup_temp_files.setChecked(True)

            self.enable_system_notifications.setChecked(True)
            self.enable_sound_notifications.setChecked(False)
            self.notify_auto_connect.setChecked(True)
            self.notify_auto_disconnect.setChecked(True)
            self.notify_auto_shutdown.setChecked(True)

            self.on_mode_changed(self.auto_connect_mode.currentText())
