#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理系統

提供動態配置管理功能，包括：
- 多產品配置支持
- 動態配置更新
- 配置驗證和校驗
- 性能參數調優
"""

import asyncio
import json
import logging
import os
import time
import threading
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from configparser import ConfigParser
import yaml


@dataclass
class ProductConfig:
    """產品配置"""
    symbol: str
    data_types: List[str]
    service: str
    topic: str
    items: List[str]
    enabled: bool = True
    priority: int = 0
    custom_settings: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_settings is None:
            self.custom_settings = {}


@dataclass
class PerformanceConfig:
    """性能配置"""
    # DDE接收配置
    dde_buffer_size: int = 1024 * 1024
    dde_batch_size: int = 1000
    dde_batch_timeout: float = 0.01
    
    # 數據處理配置
    processing_batch_size: int = 1000
    processing_queue_size: int = 10000
    processing_workers: int = 4
    
    # GUI更新配置
    gui_update_interval_ms: int = 16
    gui_max_batch_size: int = 1000
    gui_virtual_rows: int = 100
    
    # 文件寫入配置
    file_batch_size: int = 1000
    file_flush_interval: float = 5.0
    file_buffer_size: int = 8192
    
    # 背壓控制配置
    backpressure_high_watermark: int = 8000
    backpressure_low_watermark: int = 6000
    backpressure_strategy: str = "drop_oldest"
    
    # 內存管理配置
    memory_pool_initial_size: int = 1000
    gc_threshold: int = 10000


@dataclass
class SystemConfig:
    """系統配置"""
    # 基本設置
    system_name: str = "DDE Actor System"
    version: str = "1.0.0"
    debug_mode: bool = False
    
    # 日誌配置
    log_level: str = "INFO"
    log_file: str = "logs/system.log"
    log_max_size_mb: int = 100
    log_backup_count: int = 5
    
    # 監控配置
    enable_monitoring: bool = True
    monitoring_interval: float = 60.0
    health_check_interval: float = 300.0
    
    # 網絡配置
    enable_remote_api: bool = False
    api_host: str = "localhost"
    api_port: int = 8080


class ConfigValidator:
    """配置驗證器"""
    
    @staticmethod
    def validate_product_config(config: ProductConfig) -> List[str]:
        """驗證產品配置
        
        Args:
            config: 產品配置
            
        Returns:
            List[str]: 錯誤信息列表
        """
        errors = []
        
        if not config.symbol:
            errors.append("產品代碼不能為空")
        
        if not config.data_types:
            errors.append("數據類型不能為空")
        
        if not config.service:
            errors.append("DDE服務名不能為空")
        
        if not config.topic:
            errors.append("DDE主題不能為空")
        
        if not config.items:
            errors.append("DDE項目列表不能為空")
        
        # 驗證數據類型
        valid_types = ['tick', 'order', 'level2', 'daily']
        for data_type in config.data_types:
            if data_type not in valid_types:
                errors.append(f"無效的數據類型: {data_type}")
        
        return errors
    
    @staticmethod
    def validate_performance_config(config: PerformanceConfig) -> List[str]:
        """驗證性能配置
        
        Args:
            config: 性能配置
            
        Returns:
            List[str]: 錯誤信息列表
        """
        errors = []
        
        # 驗證緩衝區大小
        if config.dde_buffer_size <= 0:
            errors.append("DDE緩衝區大小必須大於0")
        
        if config.dde_batch_size <= 0:
            errors.append("DDE批量大小必須大於0")
        
        if config.dde_batch_timeout <= 0:
            errors.append("DDE批量超時必須大於0")
        
        # 驗證處理配置
        if config.processing_workers <= 0:
            errors.append("處理工作線程數必須大於0")
        
        if config.processing_queue_size <= 0:
            errors.append("處理隊列大小必須大於0")
        
        # 驗證GUI配置
        if config.gui_update_interval_ms <= 0:
            errors.append("GUI更新間隔必須大於0")
        
        # 驗證背壓控制
        if config.backpressure_high_watermark <= config.backpressure_low_watermark:
            errors.append("背壓高水位必須大於低水位")
        
        valid_strategies = ['drop_oldest', 'drop_newest', 'block']
        if config.backpressure_strategy not in valid_strategies:
            errors.append(f"無效的背壓策略: {config.backpressure_strategy}")
        
        return errors


class ConfigManager:
    """配置管理器
    
    負責加載、驗證和管理系統配置
    """
    
    def __init__(self, config_file: str):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = logging.getLogger("ConfigManager")
        
        # 配置對象
        self.system_config = SystemConfig()
        self.performance_config = PerformanceConfig()
        self.product_configs: Dict[str, ProductConfig] = {}
        
        # 配置變更回調
        self.change_callbacks: List[Callable[[str, Any], None]] = []
        
        # 配置鎖
        self.config_lock = threading.RLock()
    
    def load_config(self) -> bool:
        """加載配置文件
        
        Returns:
            bool: 加載是否成功
        """
        try:
            with self.config_lock:
                if not os.path.exists(self.config_file):
                    self.logger.error(f"配置文件不存在: {self.config_file}")
                    return False
                
                # 根據文件擴展名選擇解析器
                file_ext = Path(self.config_file).suffix.lower()
                
                if file_ext == '.ini':
                    return self._load_ini_config()
                elif file_ext in ['.json']:
                    return self._load_json_config()
                elif file_ext in ['.yaml', '.yml']:
                    return self._load_yaml_config()
                else:
                    self.logger.error(f"不支持的配置文件格式: {file_ext}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"加載配置文件失敗: {str(e)}")
            return False
    
    def save_config(self) -> bool:
        """保存配置文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with self.config_lock:
                # 根據文件擴展名選擇保存格式
                file_ext = Path(self.config_file).suffix.lower()
                
                if file_ext == '.ini':
                    return self._save_ini_config()
                elif file_ext == '.json':
                    return self._save_json_config()
                elif file_ext in ['.yaml', '.yml']:
                    return self._save_yaml_config()
                else:
                    self.logger.error(f"不支持的配置文件格式: {file_ext}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"保存配置文件失敗: {str(e)}")
            return False
    
    def validate_config(self) -> List[str]:
        """驗證配置
        
        Returns:
            List[str]: 錯誤信息列表
        """
        errors = []
        
        # 驗證性能配置
        perf_errors = ConfigValidator.validate_performance_config(self.performance_config)
        errors.extend(perf_errors)
        
        # 驗證產品配置
        for symbol, config in self.product_configs.items():
            product_errors = ConfigValidator.validate_product_config(config)
            for error in product_errors:
                errors.append(f"產品 {symbol}: {error}")
        
        return errors
    
    def get_product_config(self, symbol: str) -> Optional[ProductConfig]:
        """獲取產品配置
        
        Args:
            symbol: 產品代碼
            
        Returns:
            ProductConfig: 產品配置，如果不存在則返回None
        """
        with self.config_lock:
            return self.product_configs.get(symbol)
    
    def add_product_config(self, config: ProductConfig) -> bool:
        """添加產品配置
        
        Args:
            config: 產品配置
            
        Returns:
            bool: 添加是否成功
        """
        try:
            # 驗證配置
            errors = ConfigValidator.validate_product_config(config)
            if errors:
                self.logger.error(f"產品配置驗證失敗: {errors}")
                return False
            
            with self.config_lock:
                self.product_configs[config.symbol] = config
                self._notify_config_change("product_added", config)
            
            return True
            
        except Exception as e:
            self.logger.error(f"添加產品配置失敗: {str(e)}")
            return False
    
    def update_product_config(self, symbol: str, updates: Dict[str, Any]) -> bool:
        """更新產品配置
        
        Args:
            symbol: 產品代碼
            updates: 更新字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self.config_lock:
                if symbol not in self.product_configs:
                    self.logger.error(f"產品配置不存在: {symbol}")
                    return False
                
                config = self.product_configs[symbol]
                
                # 應用更新
                for key, value in updates.items():
                    if hasattr(config, key):
                        setattr(config, key, value)
                
                # 驗證更新後的配置
                errors = ConfigValidator.validate_product_config(config)
                if errors:
                    self.logger.error(f"更新後配置驗證失敗: {errors}")
                    return False
                
                self._notify_config_change("product_updated", config)
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新產品配置失敗: {str(e)}")
            return False
    
    def remove_product_config(self, symbol: str) -> bool:
        """移除產品配置
        
        Args:
            symbol: 產品代碼
            
        Returns:
            bool: 移除是否成功
        """
        try:
            with self.config_lock:
                if symbol in self.product_configs:
                    config = self.product_configs.pop(symbol)
                    self._notify_config_change("product_removed", config)
                    return True
                else:
                    self.logger.warning(f"產品配置不存在: {symbol}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"移除產品配置失敗: {str(e)}")
            return False
    
    def update_performance_config(self, updates: Dict[str, Any]) -> bool:
        """更新性能配置
        
        Args:
            updates: 更新字典
            
        Returns:
            bool: 更新是否成功
        """
        try:
            with self.config_lock:
                # 應用更新
                for key, value in updates.items():
                    if hasattr(self.performance_config, key):
                        setattr(self.performance_config, key, value)
                
                # 驗證更新後的配置
                errors = ConfigValidator.validate_performance_config(self.performance_config)
                if errors:
                    self.logger.error(f"性能配置驗證失敗: {errors}")
                    return False
                
                self._notify_config_change("performance_updated", self.performance_config)
            
            return True
            
        except Exception as e:
            self.logger.error(f"更新性能配置失敗: {str(e)}")
            return False
    
    def add_change_callback(self, callback: Callable[[str, Any], None]):
        """添加配置變更回調
        
        Args:
            callback: 回調函數
        """
        self.change_callbacks.append(callback)
    
    def _notify_config_change(self, change_type: str, config: Any):
        """通知配置變更
        
        Args:
            change_type: 變更類型
            config: 配置對象
        """
        for callback in self.change_callbacks:
            try:
                callback(change_type, config)
            except Exception as e:
                self.logger.error(f"配置變更回調失敗: {str(e)}")
    
    def _load_ini_config(self) -> bool:
        """加載INI配置文件"""
        try:
            parser = ConfigParser()
            parser.read(self.config_file, encoding='utf-8')
            
            # 加載系統配置
            if parser.has_section('System'):
                system_section = parser['System']
                self.system_config.system_name = system_section.get('name', self.system_config.system_name)
                self.system_config.debug_mode = system_section.getboolean('debug_mode', self.system_config.debug_mode)
                self.system_config.log_level = system_section.get('log_level', self.system_config.log_level)
            
            # 加載性能配置
            if parser.has_section('Performance'):
                perf_section = parser['Performance']
                self.performance_config.dde_buffer_size = perf_section.getint('dde_buffer_size', self.performance_config.dde_buffer_size)
                self.performance_config.dde_batch_size = perf_section.getint('dde_batch_size', self.performance_config.dde_batch_size)
                self.performance_config.processing_workers = perf_section.getint('processing_workers', self.performance_config.processing_workers)
            
            # 加載產品配置
            for section_name in parser.sections():
                if section_name.startswith('Product_'):
                    symbol = section_name[8:]  # 移除 'Product_' 前綴
                    section = parser[section_name]
                    
                    config = ProductConfig(
                        symbol=symbol,
                        data_types=section.get('data_types', '').split(','),
                        service=section.get('service', ''),
                        topic=section.get('topic', ''),
                        items=section.get('items', '').split(','),
                        enabled=section.getboolean('enabled', True),
                        priority=section.getint('priority', 0)
                    )
                    
                    self.product_configs[symbol] = config
            
            return True
            
        except Exception as e:
            self.logger.error(f"加載INI配置失敗: {str(e)}")
            return False
    
    def _load_json_config(self) -> bool:
        """加載JSON配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加載系統配置
            if 'system' in data:
                system_data = data['system']
                for key, value in system_data.items():
                    if hasattr(self.system_config, key):
                        setattr(self.system_config, key, value)
            
            # 加載性能配置
            if 'performance' in data:
                perf_data = data['performance']
                for key, value in perf_data.items():
                    if hasattr(self.performance_config, key):
                        setattr(self.performance_config, key, value)
            
            # 加載產品配置
            if 'products' in data:
                for product_data in data['products']:
                    config = ProductConfig(**product_data)
                    self.product_configs[config.symbol] = config
            
            return True
            
        except Exception as e:
            self.logger.error(f"加載JSON配置失敗: {str(e)}")
            return False
    
    def _load_yaml_config(self) -> bool:
        """加載YAML配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            # 與JSON加載邏輯相同
            return self._load_from_dict(data)
            
        except Exception as e:
            self.logger.error(f"加載YAML配置失敗: {str(e)}")
            return False
    
    def _save_json_config(self) -> bool:
        """保存JSON配置文件"""
        try:
            data = {
                'system': asdict(self.system_config),
                'performance': asdict(self.performance_config),
                'products': [asdict(config) for config in self.product_configs.values()]
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存JSON配置失敗: {str(e)}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """獲取配置摘要
        
        Returns:
            Dict: 配置摘要
        """
        with self.config_lock:
            return {
                'system': asdict(self.system_config),
                'performance': asdict(self.performance_config),
                'products_count': len(self.product_configs),
                'enabled_products': sum(1 for config in self.product_configs.values() if config.enabled),
                'config_file': self.config_file,
                'last_modified': os.path.getmtime(self.config_file) if os.path.exists(self.config_file) else 0
            }


class DynamicConfigManager(ConfigManager):
    """動態配置管理器

    支持運行時配置更新和文件監控
    """

    def __init__(self, config_file: str, watch_interval: float = 5.0):
        """初始化動態配置管理器

        Args:
            config_file: 配置文件路径
            watch_interval: 文件監控間隔（秒）
        """
        super().__init__(config_file)

        self.watch_interval = watch_interval
        self.last_modified = 0
        self.watching = False
        self.watch_task: Optional[asyncio.Task] = None

        # 性能調優器
        self.auto_tuner = PerformanceTuner(self)

    async def start_watching(self):
        """開始監控配置文件變更"""
        if self.watching:
            return

        self.watching = True
        self.last_modified = os.path.getmtime(self.config_file) if os.path.exists(self.config_file) else 0
        self.watch_task = asyncio.create_task(self._watch_loop())

        self.logger.info("開始監控配置文件變更")

    async def stop_watching(self):
        """停止監控配置文件變更"""
        if not self.watching:
            return

        self.watching = False

        if self.watch_task:
            self.watch_task.cancel()
            try:
                await self.watch_task
            except asyncio.CancelledError:
                pass

        self.logger.info("停止監控配置文件變更")

    async def _watch_loop(self):
        """文件監控循環"""
        while self.watching:
            try:
                if os.path.exists(self.config_file):
                    current_modified = os.path.getmtime(self.config_file)

                    if current_modified > self.last_modified:
                        self.logger.info("檢測到配置文件變更，重新加載")

                        if self.load_config():
                            self.last_modified = current_modified
                            self._notify_config_change("file_reloaded", None)
                        else:
                            self.logger.error("重新加載配置文件失敗")

                await asyncio.sleep(self.watch_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"文件監控錯誤: {str(e)}")
                await asyncio.sleep(self.watch_interval)

    async def update_config_async(self, updates: Dict[str, Any]) -> bool:
        """異步更新配置

        Args:
            updates: 更新字典

        Returns:
            bool: 更新是否成功
        """
        try:
            success = False

            if 'performance' in updates:
                success = self.update_performance_config(updates['performance'])

            if 'products' in updates:
                for symbol, product_updates in updates['products'].items():
                    if symbol in self.product_configs:
                        success = self.update_product_config(symbol, product_updates)
                    else:
                        # 創建新產品配置
                        if 'symbol' not in product_updates:
                            product_updates['symbol'] = symbol
                        config = ProductConfig(**product_updates)
                        success = self.add_product_config(config)

            if success:
                # 保存配置文件
                await asyncio.to_thread(self.save_config)

            return success

        except Exception as e:
            self.logger.error(f"異步更新配置失敗: {str(e)}")
            return False

    def enable_auto_tuning(self, performance_monitor):
        """啟用自動性能調優

        Args:
            performance_monitor: 性能監控器
        """
        self.auto_tuner.set_performance_monitor(performance_monitor)
        self.auto_tuner.enable()

    def disable_auto_tuning(self):
        """禁用自動性能調優"""
        self.auto_tuner.disable()


class PerformanceTuner:
    """性能調優器

    根據系統性能指標自動調整配置參數
    """

    def __init__(self, config_manager: DynamicConfigManager):
        """初始化性能調優器

        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.performance_monitor = None
        self.enabled = False
        self.tuning_interval = 60.0  # 調優間隔（秒）
        self.tuning_task: Optional[asyncio.Task] = None

        self.logger = logging.getLogger("PerformanceTuner")

        # 調優規則
        self.tuning_rules = {
            'cpu_high': self._tune_for_high_cpu,
            'memory_high': self._tune_for_high_memory,
            'latency_high': self._tune_for_high_latency,
            'throughput_low': self._tune_for_low_throughput
        }

    def set_performance_monitor(self, monitor):
        """設置性能監控器

        Args:
            monitor: 性能監控器
        """
        self.performance_monitor = monitor

    def enable(self):
        """啟用自動調優"""
        if self.enabled or not self.performance_monitor:
            return

        self.enabled = True
        self.tuning_task = asyncio.create_task(self._tuning_loop())
        self.logger.info("啟用自動性能調優")

    def disable(self):
        """禁用自動調優"""
        if not self.enabled:
            return

        self.enabled = False

        if self.tuning_task:
            self.tuning_task.cancel()

        self.logger.info("禁用自動性能調優")

    async def _tuning_loop(self):
        """調優循環"""
        while self.enabled:
            try:
                await asyncio.sleep(self.tuning_interval)

                if self.performance_monitor:
                    await self._analyze_and_tune()

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"性能調優錯誤: {str(e)}")

    async def _analyze_and_tune(self):
        """分析性能並調優"""
        try:
            metrics = self.performance_monitor.get_current_metrics()

            # 檢查各項指標並應用調優規則
            if metrics.cpu_usage_percent > 80:
                await self._tune_for_high_cpu(metrics)

            if metrics.memory_usage_percent > 85:
                await self._tune_for_high_memory(metrics)

            if metrics.avg_latency_ms > 100:
                await self._tune_for_high_latency(metrics)

            if metrics.messages_per_second < 1000:  # 假設期望吞吐量
                await self._tune_for_low_throughput(metrics)

        except Exception as e:
            self.logger.error(f"性能分析失敗: {str(e)}")

    async def _tune_for_high_cpu(self, metrics):
        """針對高CPU使用率調優"""
        try:
            config = self.config_manager.performance_config

            # 減少批量大小以降低CPU負載
            new_batch_size = max(100, int(config.dde_batch_size * 0.8))

            # 增加批量超時以減少處理頻率
            new_timeout = min(0.1, config.dde_batch_timeout * 1.2)

            updates = {
                'dde_batch_size': new_batch_size,
                'dde_batch_timeout': new_timeout,
                'processing_batch_size': max(100, int(config.processing_batch_size * 0.8))
            }

            success = self.config_manager.update_performance_config(updates)
            if success:
                self.logger.info(f"CPU調優: 批量大小 {config.dde_batch_size} -> {new_batch_size}")

        except Exception as e:
            self.logger.error(f"CPU調優失敗: {str(e)}")

    async def _tune_for_high_memory(self, metrics):
        """針對高內存使用率調優"""
        try:
            config = self.config_manager.performance_config

            # 減少緩衝區大小
            new_buffer_size = max(512*1024, int(config.dde_buffer_size * 0.8))

            # 減少隊列大小
            new_queue_size = max(1000, int(config.processing_queue_size * 0.8))

            updates = {
                'dde_buffer_size': new_buffer_size,
                'processing_queue_size': new_queue_size,
                'memory_pool_initial_size': max(100, int(config.memory_pool_initial_size * 0.8))
            }

            success = self.config_manager.update_performance_config(updates)
            if success:
                self.logger.info(f"內存調優: 緩衝區 {config.dde_buffer_size} -> {new_buffer_size}")

        except Exception as e:
            self.logger.error(f"內存調優失敗: {str(e)}")

    async def _tune_for_high_latency(self, metrics):
        """針對高延遲調優"""
        try:
            config = self.config_manager.performance_config

            # 減少批量大小以降低延遲
            new_batch_size = max(50, int(config.dde_batch_size * 0.7))

            # 減少批量超時
            new_timeout = max(0.001, config.dde_batch_timeout * 0.5)

            # 增加處理工作線程
            new_workers = min(8, config.processing_workers + 1)

            updates = {
                'dde_batch_size': new_batch_size,
                'dde_batch_timeout': new_timeout,
                'processing_workers': new_workers
            }

            success = self.config_manager.update_performance_config(updates)
            if success:
                self.logger.info(f"延遲調優: 批量大小 {config.dde_batch_size} -> {new_batch_size}")

        except Exception as e:
            self.logger.error(f"延遲調優失敗: {str(e)}")

    async def _tune_for_low_throughput(self, metrics):
        """針對低吞吐量調優"""
        try:
            config = self.config_manager.performance_config

            # 增加批量大小以提高吞吐量
            new_batch_size = min(2000, int(config.dde_batch_size * 1.2))

            # 增加緩衝區大小
            new_buffer_size = min(2*1024*1024, int(config.dde_buffer_size * 1.2))

            updates = {
                'dde_batch_size': new_batch_size,
                'dde_buffer_size': new_buffer_size,
                'processing_batch_size': min(2000, int(config.processing_batch_size * 1.2))
            }

            success = self.config_manager.update_performance_config(updates)
            if success:
                self.logger.info(f"吞吐量調優: 批量大小 {config.dde_batch_size} -> {new_batch_size}")

        except Exception as e:
            self.logger.error(f"吞吐量調優失敗: {str(e)}")
