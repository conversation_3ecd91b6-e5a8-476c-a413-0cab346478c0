{"system": {"system_name": "DDE Multi-Product Monitor", "version": "1.0.0"}, "dde": {"polling_interval": 2.0, "batch_size": 2000, "batch_timeout": 0.05}, "file_output": {"batch_size": 2000, "flush_interval": 10.0}, "products": [{"symbol": "FITXN07", "service": "XQTISC", "topic": "Quote", "items": ["FITXN07.TF-Time", "FITXN07.TF-TradingDate", "FITXN07.TF-Open", "FITXN07.TF-High", "FITXN07.TF-Low", "FITXN07.TF-Price", "FITXN07.TF-TotalVolume", "FITXN07.TF-Volume", "FITXN07.TF-NWTotalBidContract", "FITXN07.TF-NWTotalAskContract", "FITXN07.TF-NWTotalBidSize", "FITXN07.TF-NWTotalAskSize", "FITXN07.TF-InSize", "FITXN07.TF-OutSize", "FITXN07.TF-TotalBidMatchTx", "FITXN07.TF-TotalAskMatchTx", "FITXN07.TF-BestBid1", "FITXN07.TF-BestBid2", "FITXN07.TF-BestBid3", "FITXN07.TF-BestBid4", "FITXN07.TF-BestBid5", "FITXN07.TF-BestAsk1", "FITXN07.TF-BestAsk2", "FITXN07.TF-BestAsk3", "FITXN07.TF-BestAsk4", "FITXN07.TF-BestAsk5", "FITXN07.TF-BestBidSize1", "FITXN07.TF-BestBidSize2", "FITXN07.TF-BestBidSize3", "FITXN07.TF-BestBidSize4", "FITXN07.TF-BestBidSize5", "FITXN07.TF-BestAskSize1", "FITXN07.TF-BestAskSize2", "FITXN07.TF-BestAskSize3", "FITXN07.TF-BestAskSize4", "FITXN07.TF-BestAskSize5", "FITXN07.TF-Name", "FITXN07.TF-WContractDate", "FITXN07.TF-SettlePrice", "FITXN07.TF-UpLimit", "FITXN07.TF-DownLimit", "FITXN07.TF-OI", "FITXN07.TF-TradingDate", "FITXN07.TF-WRemainDate", "FITXN07.TF-PreClose", "FITXN07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FIMTXN07", "service": "XQTISC", "topic": "Quote", "items": ["FIMTXN07.TF-Time", "FIMTXN07.TF-TradingDate", "FIMTXN07.TF-Open", "FIMTXN07.TF-High", "FIMTXN07.TF-Low", "FIMTXN07.TF-Price", "FIMTXN07.TF-TotalVolume", "FIMTXN07.TF-Volume", "FIMTXN07.TF-NWTotalBidContract", "FIMTXN07.TF-NWTotalAskContract", "FIMTXN07.TF-NWTotalBidSize", "FIMTXN07.TF-NWTotalAskSize", "FIMTXN07.TF-InSize", "FIMTXN07.TF-OutSize", "FIMTXN07.TF-TotalBidMatchTx", "FIMTXN07.TF-TotalAskMatchTx", "FIMTXN07.TF-BestBid1", "FIMTXN07.TF-BestBid2", "FIMTXN07.TF-BestBid3", "FIMTXN07.TF-BestBid4", "FIMTXN07.TF-BestBid5", "FIMTXN07.TF-BestAsk1", "FIMTXN07.TF-BestAsk2", "FIMTXN07.TF-BestAsk3", "FIMTXN07.TF-BestAsk4", "FIMTXN07.TF-BestAsk5", "FIMTXN07.TF-BestBidSize1", "FIMTXN07.TF-BestBidSize2", "FIMTXN07.TF-BestBidSize3", "FIMTXN07.TF-BestBidSize4", "FIMTXN07.TF-BestBidSize5", "FIMTXN07.TF-BestAskSize1", "FIMTXN07.TF-BestAskSize2", "FIMTXN07.TF-BestAskSize3", "FIMTXN07.TF-BestAskSize4", "FIMTXN07.TF-BestAskSize5", "FIMTXN07.TF-Name", "FIMTXN07.TF-WContractDate", "FIMTXN07.TF-SettlePrice", "FIMTXN07.TF-UpLimit", "FIMTXN07.TF-DownLimit", "FIMTXN07.TF-OI", "FIMTXN07.TF-TradingDate", "FIMTXN07.TF-WRemainDate", "FIMTXN07.TF-PreClose", "FIMTXN07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITMN07", "service": "XQTISC", "topic": "Quote", "items": ["FITMN07.TF-Time", "FITMN07.TF-TradingDate", "FITMN07.TF-Open", "FITMN07.TF-High", "FITMN07.TF-Low", "FITMN07.TF-Price", "FITMN07.TF-TotalVolume", "FITMN07.TF-Volume", "FITMN07.TF-NWTotalBidContract", "FITMN07.TF-NWTotalAskContract", "FITMN07.TF-NWTotalBidSize", "FITMN07.TF-NWTotalAskSize", "FITMN07.TF-InSize", "FITMN07.TF-OutSize", "FITMN07.TF-TotalBidMatchTx", "FITMN07.TF-TotalAskMatchTx", "FITMN07.TF-BestBid1", "FITMN07.TF-BestBid2", "FITMN07.TF-BestBid3", "FITMN07.TF-BestBid4", "FITMN07.TF-BestBid5", "FITMN07.TF-BestAsk1", "FITMN07.TF-BestAsk2", "FITMN07.TF-BestAsk3", "FITMN07.TF-BestAsk4", "FITMN07.TF-BestAsk5", "FITMN07.TF-BestBidSize1", "FITMN07.TF-BestBidSize2", "FITMN07.TF-BestBidSize3", "FITMN07.TF-BestBidSize4", "FITMN07.TF-BestBidSize5", "FITMN07.TF-BestAskSize1", "FITMN07.TF-BestAskSize2", "FITMN07.TF-BestAskSize3", "FITMN07.TF-BestAskSize4", "FITMN07.TF-BestAskSize5", "FITMN07.TF-Name", "FITMN07.TF-WContractDate", "FITMN07.TF-SettlePrice", "FITMN07.TF-UpLimit", "FITMN07.TF-DownLimit", "FITMN07.TF-OI", "FITMN07.TF-TradingDate", "FITMN07.TF-WRemainDate", "FITMN07.TF-PreClose", "FITMN07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITXN08", "service": "XQTISC", "topic": "Quote", "items": ["FITXN08.TF-Time", "FITXN08.TF-TradingDate", "FITXN08.TF-Open", "FITXN08.TF-High", "FITXN08.TF-Low", "FITXN08.TF-Price", "FITXN08.TF-TotalVolume", "FITXN08.TF-Volume", "FITXN08.TF-NWTotalBidContract", "FITXN08.TF-NWTotalAskContract", "FITXN08.TF-NWTotalBidSize", "FITXN08.TF-NWTotalAskSize", "FITXN08.TF-InSize", "FITXN08.TF-OutSize", "FITXN08.TF-TotalBidMatchTx", "FITXN08.TF-TotalAskMatchTx", "FITXN08.TF-BestBid1", "FITXN08.TF-BestBid2", "FITXN08.TF-BestBid3", "FITXN08.TF-BestBid4", "FITXN08.TF-BestBid5", "FITXN08.TF-BestAsk1", "FITXN08.TF-BestAsk2", "FITXN08.TF-BestAsk3", "FITXN08.TF-BestAsk4", "FITXN08.TF-BestAsk5", "FITXN08.TF-BestBidSize1", "FITXN08.TF-BestBidSize2", "FITXN08.TF-BestBidSize3", "FITXN08.TF-BestBidSize4", "FITXN08.TF-BestBidSize5", "FITXN08.TF-BestAskSize1", "FITXN08.TF-BestAskSize2", "FITXN08.TF-BestAskSize3", "FITXN08.TF-BestAskSize4", "FITXN08.TF-BestAskSize5", "FITXN08.TF-Name", "FITXN08.TF-WContractDate", "FITXN08.TF-SettlePrice", "FITXN08.TF-UpLimit", "FITXN08.TF-DownLimit", "FITXN08.TF-OI", "FITXN08.TF-TradingDate", "FITXN08.TF-WRemainDate", "FITXN08.TF-PreClose", "FITXN08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FIMTXN08", "service": "XQTISC", "topic": "Quote", "items": ["FIMTXN08.TF-Time", "FIMTXN08.TF-TradingDate", "FIMTXN08.TF-Open", "FIMTXN08.TF-High", "FIMTXN08.TF-Low", "FIMTXN08.TF-Price", "FIMTXN08.TF-TotalVolume", "FIMTXN08.TF-Volume", "FIMTXN08.TF-NWTotalBidContract", "FIMTXN08.TF-NWTotalAskContract", "FIMTXN08.TF-NWTotalBidSize", "FIMTXN08.TF-NWTotalAskSize", "FIMTXN08.TF-InSize", "FIMTXN08.TF-OutSize", "FIMTXN08.TF-TotalBidMatchTx", "FIMTXN08.TF-TotalAskMatchTx", "FIMTXN08.TF-BestBid1", "FIMTXN08.TF-BestBid2", "FIMTXN08.TF-BestBid3", "FIMTXN08.TF-BestBid4", "FIMTXN08.TF-BestBid5", "FIMTXN08.TF-BestAsk1", "FIMTXN08.TF-BestAsk2", "FIMTXN08.TF-BestAsk3", "FIMTXN08.TF-BestAsk4", "FIMTXN08.TF-BestAsk5", "FIMTXN08.TF-BestBidSize1", "FIMTXN08.TF-BestBidSize2", "FIMTXN08.TF-BestBidSize3", "FIMTXN08.TF-BestBidSize4", "FIMTXN08.TF-BestBidSize5", "FIMTXN08.TF-BestAskSize1", "FIMTXN08.TF-BestAskSize2", "FIMTXN08.TF-BestAskSize3", "FIMTXN08.TF-BestAskSize4", "FIMTXN08.TF-BestAskSize5", "FIMTXN08.TF-Name", "FIMTXN08.TF-WContractDate", "FIMTXN08.TF-SettlePrice", "FIMTXN08.TF-UpLimit", "FIMTXN08.TF-DownLimit", "FIMTXN08.TF-OI", "FIMTXN08.TF-TradingDate", "FIMTXN08.TF-WRemainDate", "FIMTXN08.TF-PreClose", "FIMTXN08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITMN08", "service": "XQTISC", "topic": "Quote", "items": ["FITMN08.TF-Time", "FITMN08.TF-TradingDate", "FITMN08.TF-Open", "FITMN08.TF-High", "FITMN08.TF-Low", "FITMN08.TF-Price", "FITMN08.TF-TotalVolume", "FITMN08.TF-Volume", "FITMN08.TF-NWTotalBidContract", "FITMN08.TF-NWTotalAskContract", "FITMN08.TF-NWTotalBidSize", "FITMN08.TF-NWTotalAskSize", "FITMN08.TF-InSize", "FITMN08.TF-OutSize", "FITMN08.TF-TotalBidMatchTx", "FITMN08.TF-TotalAskMatchTx", "FITMN08.TF-BestBid1", "FITMN08.TF-BestBid2", "FITMN08.TF-BestBid3", "FITMN08.TF-BestBid4", "FITMN08.TF-BestBid5", "FITMN08.TF-BestAsk1", "FITMN08.TF-BestAsk2", "FITMN08.TF-BestAsk3", "FITMN08.TF-BestAsk4", "FITMN08.TF-BestAsk5", "FITMN08.TF-BestBidSize1", "FITMN08.TF-BestBidSize2", "FITMN08.TF-BestBidSize3", "FITMN08.TF-BestBidSize4", "FITMN08.TF-BestBidSize5", "FITMN08.TF-BestAskSize1", "FITMN08.TF-BestAskSize2", "FITMN08.TF-BestAskSize3", "FITMN08.TF-BestAskSize4", "FITMN08.TF-BestAskSize5", "FITMN08.TF-Name", "FITMN08.TF-WContractDate", "FITMN08.TF-SettlePrice", "FITMN08.TF-UpLimit", "FITMN08.TF-DownLimit", "FITMN08.TF-OI", "FITMN08.TF-TradingDate", "FITMN08.TF-WRemainDate", "FITMN08.TF-PreClose", "FITMN08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITX07", "service": "XQTISC", "topic": "Quote", "items": ["FITX07.TF-Time", "FITX07.TF-TradingDate", "FITX07.TF-Open", "FITX07.TF-High", "FITX07.TF-Low", "FITX07.TF-Price", "FITX07.TF-TotalVolume", "FITX07.TF-Volume", "FITX07.TF-NWTotalBidContract", "FITX07.TF-NWTotalAskContract", "FITX07.TF-NWTotalBidSize", "FITX07.TF-NWTotalAskSize", "FITX07.TF-InSize", "FITX07.TF-OutSize", "FITX07.TF-TotalBidMatchTx", "FITX07.TF-TotalAskMatchTx", "FITX07.TF-BestBid1", "FITX07.TF-BestBid2", "FITX07.TF-BestBid3", "FITX07.TF-BestBid4", "FITX07.TF-BestBid5", "FITX07.TF-BestAsk1", "FITX07.TF-BestAsk2", "FITX07.TF-BestAsk3", "FITX07.TF-BestAsk4", "FITX07.TF-BestAsk5", "FITX07.TF-BestBidSize1", "FITX07.TF-BestBidSize2", "FITX07.TF-BestBidSize3", "FITX07.TF-BestBidSize4", "FITX07.TF-BestBidSize5", "FITX07.TF-BestAskSize1", "FITX07.TF-BestAskSize2", "FITX07.TF-BestAskSize3", "FITX07.TF-BestAskSize4", "FITX07.TF-BestAskSize5", "FITX07.TF-Name", "FITX07.TF-WContractDate", "FITX07.TF-SettlePrice", "FITX07.TF-UpLimit", "FITX07.TF-DownLimit", "FITX07.TF-OI", "FITX07.TF-TradingDate", "FITX07.TF-WRemainDate", "FITX07.TF-PreClose", "FITX07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FIMTX07", "service": "XQTISC", "topic": "Quote", "items": ["FIMTX07.TF-Time", "FIMTX07.TF-TradingDate", "FIMTX07.TF-Open", "FIMTX07.TF-High", "FIMTX07.TF-Low", "FIMTX07.TF-Price", "FIMTX07.TF-TotalVolume", "FIMTX07.TF-Volume", "FIMTX07.TF-NWTotalBidContract", "FIMTX07.TF-NWTotalAskContract", "FIMTX07.TF-NWTotalBidSize", "FIMTX07.TF-NWTotalAskSize", "FIMTX07.TF-InSize", "FIMTX07.TF-OutSize", "FIMTX07.TF-TotalBidMatchTx", "FIMTX07.TF-TotalAskMatchTx", "FIMTX07.TF-BestBid1", "FIMTX07.TF-BestBid2", "FIMTX07.TF-BestBid3", "FIMTX07.TF-BestBid4", "FIMTX07.TF-BestBid5", "FIMTX07.TF-BestAsk1", "FIMTX07.TF-BestAsk2", "FIMTX07.TF-BestAsk3", "FIMTX07.TF-BestAsk4", "FIMTX07.TF-BestAsk5", "FIMTX07.TF-BestBidSize1", "FIMTX07.TF-BestBidSize2", "FIMTX07.TF-BestBidSize3", "FIMTX07.TF-BestBidSize4", "FIMTX07.TF-BestBidSize5", "FIMTX07.TF-BestAskSize1", "FIMTX07.TF-BestAskSize2", "FIMTX07.TF-BestAskSize3", "FIMTX07.TF-BestAskSize4", "FIMTX07.TF-BestAskSize5", "FIMTX07.TF-Name", "FIMTX07.TF-WContractDate", "FIMTX07.TF-SettlePrice", "FIMTX07.TF-UpLimit", "FIMTX07.TF-DownLimit", "FIMTX07.TF-OI", "FIMTX07.TF-TradingDate", "FIMTX07.TF-WRemainDate", "FIMTX07.TF-PreClose", "FIMTX07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITM07", "service": "XQTISC", "topic": "Quote", "items": ["FITM07.TF-Time", "FITM07.TF-TradingDate", "FITM07.TF-Open", "FITM07.TF-High", "FITM07.TF-Low", "FITM07.TF-Price", "FITM07.TF-TotalVolume", "FITM07.TF-Volume", "FITM07.TF-NWTotalBidContract", "FITM07.TF-NWTotalAskContract", "FITM07.TF-NWTotalBidSize", "FITM07.TF-NWTotalAskSize", "FITM07.TF-InSize", "FITM07.TF-OutSize", "FITM07.TF-TotalBidMatchTx", "FITM07.TF-TotalAskMatchTx", "FITM07.TF-BestBid1", "FITM07.TF-BestBid2", "FITM07.TF-BestBid3", "FITM07.TF-BestBid4", "FITM07.TF-BestBid5", "FITM07.TF-BestAsk1", "FITM07.TF-BestAsk2", "FITM07.TF-BestAsk3", "FITM07.TF-BestAsk4", "FITM07.TF-BestAsk5", "FITM07.TF-BestBidSize1", "FITM07.TF-BestBidSize2", "FITM07.TF-BestBidSize3", "FITM07.TF-BestBidSize4", "FITM07.TF-BestBidSize5", "FITM07.TF-BestAskSize1", "FITM07.TF-BestAskSize2", "FITM07.TF-BestAskSize3", "FITM07.TF-BestAskSize4", "FITM07.TF-BestAskSize5", "FITM07.TF-Name", "FITM07.TF-WContractDate", "FITM07.TF-SettlePrice", "FITM07.TF-UpLimit", "FITM07.TF-DownLimit", "FITM07.TF-OI", "FITM07.TF-TradingDate", "FITM07.TF-WRemainDate", "FITM07.TF-PreClose", "FITM07.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITX08", "service": "XQTISC", "topic": "Quote", "items": ["FITX08.TF-Time", "FITX08.TF-TradingDate", "FITX08.TF-Open", "FITX08.TF-High", "FITX08.TF-Low", "FITX08.TF-Price", "FITX08.TF-TotalVolume", "FITX08.TF-Volume", "FITX08.TF-NWTotalBidContract", "FITX08.TF-NWTotalAskContract", "FITX08.TF-NWTotalBidSize", "FITX08.TF-NWTotalAskSize", "FITX08.TF-InSize", "FITX08.TF-OutSize", "FITX08.TF-TotalBidMatchTx", "FITX08.TF-TotalAskMatchTx", "FITX08.TF-BestBid1", "FITX08.TF-BestBid2", "FITX08.TF-BestBid3", "FITX08.TF-BestBid4", "FITX08.TF-BestBid5", "FITX08.TF-BestAsk1", "FITX08.TF-BestAsk2", "FITX08.TF-BestAsk3", "FITX08.TF-BestAsk4", "FITX08.TF-BestAsk5", "FITX08.TF-BestBidSize1", "FITX08.TF-BestBidSize2", "FITX08.TF-BestBidSize3", "FITX08.TF-BestBidSize4", "FITX08.TF-BestBidSize5", "FITX08.TF-BestAskSize1", "FITX08.TF-BestAskSize2", "FITX08.TF-BestAskSize3", "FITX08.TF-BestAskSize4", "FITX08.TF-BestAskSize5", "FITX08.TF-Name", "FITX08.TF-WContractDate", "FITX08.TF-SettlePrice", "FITX08.TF-UpLimit", "FITX08.TF-DownLimit", "FITX08.TF-OI", "FITX08.TF-TradingDate", "FITX08.TF-WRemainDate", "FITX08.TF-PreClose", "FITX08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FIMTX08", "service": "XQTISC", "topic": "Quote", "items": ["FIMTX08.TF-Time", "FIMTX08.TF-TradingDate", "FIMTX08.TF-Open", "FIMTX08.TF-High", "FIMTX08.TF-Low", "FIMTX08.TF-Price", "FIMTX08.TF-TotalVolume", "FIMTX08.TF-Volume", "FIMTX08.TF-NWTotalBidContract", "FIMTX08.TF-NWTotalAskContract", "FIMTX08.TF-NWTotalBidSize", "FIMTX08.TF-NWTotalAskSize", "FIMTX08.TF-InSize", "FIMTX08.TF-OutSize", "FIMTX08.TF-TotalBidMatchTx", "FIMTX08.TF-TotalAskMatchTx", "FIMTX08.TF-BestBid1", "FIMTX08.TF-BestBid2", "FIMTX08.TF-BestBid3", "FIMTX08.TF-BestBid4", "FIMTX08.TF-BestBid5", "FIMTX08.TF-BestAsk1", "FIMTX08.TF-BestAsk2", "FIMTX08.TF-BestAsk3", "FIMTX08.TF-BestAsk4", "FIMTX08.TF-BestAsk5", "FIMTX08.TF-BestBidSize1", "FIMTX08.TF-BestBidSize2", "FIMTX08.TF-BestBidSize3", "FIMTX08.TF-BestBidSize4", "FIMTX08.TF-BestBidSize5", "FIMTX08.TF-BestAskSize1", "FIMTX08.TF-BestAskSize2", "FIMTX08.TF-BestAskSize3", "FIMTX08.TF-BestAskSize4", "FIMTX08.TF-BestAskSize5", "FIMTX08.TF-Name", "FIMTX08.TF-WContractDate", "FIMTX08.TF-SettlePrice", "FIMTX08.TF-UpLimit", "FIMTX08.TF-DownLimit", "FIMTX08.TF-OI", "FIMTX08.TF-TradingDate", "FIMTX08.TF-WRemainDate", "FIMTX08.TF-PreClose", "FIMTX08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "FITM08", "service": "XQTISC", "topic": "Quote", "items": ["FITM08.TF-Time", "FITM08.TF-TradingDate", "FITM08.TF-Open", "FITM08.TF-High", "FITM08.TF-Low", "FITM08.TF-Price", "FITM08.TF-TotalVolume", "FITM08.TF-Volume", "FITM08.TF-NWTotalBidContract", "FITM08.TF-NWTotalAskContract", "FITM08.TF-NWTotalBidSize", "FITM08.TF-NWTotalAskSize", "FITM08.TF-InSize", "FITM08.TF-OutSize", "FITM08.TF-TotalBidMatchTx", "FITM08.TF-TotalAskMatchTx", "FITM08.TF-BestBid1", "FITM08.TF-BestBid2", "FITM08.TF-BestBid3", "FITM08.TF-BestBid4", "FITM08.TF-BestBid5", "FITM08.TF-BestAsk1", "FITM08.TF-BestAsk2", "FITM08.TF-BestAsk3", "FITM08.TF-BestAsk4", "FITM08.TF-BestAsk5", "FITM08.TF-BestBidSize1", "FITM08.TF-BestBidSize2", "FITM08.TF-BestBidSize3", "FITM08.TF-BestBidSize4", "FITM08.TF-BestBidSize5", "FITM08.TF-BestAskSize1", "FITM08.TF-BestAskSize2", "FITM08.TF-BestAskSize3", "FITM08.TF-BestAskSize4", "FITM08.TF-BestAskSize5", "FITM08.TF-Name", "FITM08.TF-WContractDate", "FITM08.TF-SettlePrice", "FITM08.TF-UpLimit", "FITM08.TF-DownLimit", "FITM08.TF-OI", "FITM08.TF-TradingDate", "FITM08.TF-WRemainDate", "FITM08.TF-PreClose", "FITM08.TF-PreTotalVolume"], "enabled": true}, {"symbol": "2330", "service": "XQTISC", "topic": "Quote", "items": ["2330.TW-Time", "2330.TW-TradingDate", "2330.TW-Open", "2330.TW-High", "2330.TW-Low", "2330.TW-Price", "2330.TW-TotalVolume", "2330.TW-Volume", "2330.TW-NWTotalBidContract", "2330.TW-NWTotalAskContract", "2330.TW-NWTotalBidSize", "2330.TW-NWTotalAskSize", "2330.TW-InSize", "2330.TW-OutSize", "2330.TW-TotalBidMatchTx", "2330.TW-TotalAskMatchTx", "2330.TW-BestBid1", "2330.TW-BestBid2", "2330.TW-BestBid3", "2330.TW-BestBid4", "2330.TW-BestBid5", "2330.TW-BestAsk1", "2330.TW-BestAsk2", "2330.TW-BestAsk3", "2330.TW-BestAsk4", "2330.TW-BestAsk5", "2330.TW-BestBidSize1", "2330.TW-BestBidSize2", "2330.TW-BestBidSize3", "2330.TW-BestBidSize4", "2330.TW-BestBidSize5", "2330.TW-BestAskSize1", "2330.TW-BestAskSize2", "2330.TW-BestAskSize3", "2330.TW-BestAskSize4", "2330.TW-BestAskSize5", "2330.TW-Name", "2330.TW-WContractDate", "2330.TW-SettlePrice", "2330.TW-UpLimit", "2330.TW-DownLimit", "2330.TW-OI", "2330.TW-TradingDate", "2330.TW-WRemainDate", "2330.TW-PreClose", "2330.TW-PreTotalVolume"], "enabled": true}, {"symbol": "2317", "service": "XQTISC", "topic": "Quote", "items": ["2317.TW-Time", "2317.TW-TradingDate", "2317.TW-Open", "2317.TW-High", "2317.TW-Low", "2317.TW-Price", "2317.TW-TotalVolume", "2317.TW-Volume", "2317.TW-NWTotalBidContract", "2317.TW-NWTotalAskContract", "2317.TW-NWTotalBidSize", "2317.TW-NWTotalAskSize", "2317.TW-InSize", "2317.TW-OutSize", "2317.TW-TotalBidMatchTx", "2317.TW-TotalAskMatchTx", "2317.TW-BestBid1", "2317.TW-BestBid2", "2317.TW-BestBid3", "2317.TW-BestBid4", "2317.TW-BestBid5", "2317.TW-BestAsk1", "2317.TW-BestAsk2", "2317.TW-BestAsk3", "2317.TW-BestAsk4", "2317.TW-BestAsk5", "2317.TW-BestBidSize1", "2317.TW-BestBidSize2", "2317.TW-BestBidSize3", "2317.TW-BestBidSize4", "2317.TW-BestBidSize5", "2317.TW-BestAskSize1", "2317.TW-BestAskSize2", "2317.TW-BestAskSize3", "2317.TW-BestAskSize4", "2317.TW-BestAskSize5", "2317.TW-Name", "2317.TW-WContractDate", "2317.TW-SettlePrice", "2317.TW-UpLimit", "2317.TW-DownLimit", "2317.TW-OI", "2317.TW-TradingDate", "2317.TW-WRemainDate", "2317.TW-PreClose", "2317.TW-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C19500", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C19500.TF-Time", "TX1N07C19500.TF-TradingDate", "TX1N07C19500.TF-Open", "TX1N07C19500.TF-High", "TX1N07C19500.TF-Low", "TX1N07C19500.TF-Price", "TX1N07C19500.TF-TotalVolume", "TX1N07C19500.TF-Volume", "TX1N07C19500.TF-NWTotalBidContract", "TX1N07C19500.TF-NWTotalAskContract", "TX1N07C19500.TF-NWTotalBidSize", "TX1N07C19500.TF-NWTotalAskSize", "TX1N07C19500.TF-InSize", "TX1N07C19500.TF-OutSize", "TX1N07C19500.TF-TotalBidMatchTx", "TX1N07C19500.TF-TotalAskMatchTx", "TX1N07C19500.TF-BestBid1", "TX1N07C19500.TF-BestBid2", "TX1N07C19500.TF-BestBid3", "TX1N07C19500.TF-BestBid4", "TX1N07C19500.TF-BestBid5", "TX1N07C19500.TF-BestAsk1", "TX1N07C19500.TF-BestAsk2", "TX1N07C19500.TF-BestAsk3", "TX1N07C19500.TF-BestAsk4", "TX1N07C19500.TF-BestAsk5", "TX1N07C19500.TF-BestBidSize1", "TX1N07C19500.TF-BestBidSize2", "TX1N07C19500.TF-BestBidSize3", "TX1N07C19500.TF-BestBidSize4", "TX1N07C19500.TF-BestBidSize5", "TX1N07C19500.TF-BestAskSize1", "TX1N07C19500.TF-BestAskSize2", "TX1N07C19500.TF-BestAskSize3", "TX1N07C19500.TF-BestAskSize4", "TX1N07C19500.TF-BestAskSize5", "TX1N07C19500.TF-Name", "TX1N07C19500.TF-WContractDate", "TX1N07C19500.TF-SettlePrice", "TX1N07C19500.TF-UpLimit", "TX1N07C19500.TF-DownLimit", "TX1N07C19500.TF-OI", "TX1N07C19500.TF-TradingDate", "TX1N07C19500.TF-WRemainDate", "TX1N07C19500.TF-PreClose", "TX1N07C19500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P19500", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P19500.TF-Time", "TX1N07P19500.TF-TradingDate", "TX1N07P19500.TF-Open", "TX1N07P19500.TF-High", "TX1N07P19500.TF-Low", "TX1N07P19500.TF-Price", "TX1N07P19500.TF-TotalVolume", "TX1N07P19500.TF-Volume", "TX1N07P19500.TF-NWTotalBidContract", "TX1N07P19500.TF-NWTotalAskContract", "TX1N07P19500.TF-NWTotalBidSize", "TX1N07P19500.TF-NWTotalAskSize", "TX1N07P19500.TF-InSize", "TX1N07P19500.TF-OutSize", "TX1N07P19500.TF-TotalBidMatchTx", "TX1N07P19500.TF-TotalAskMatchTx", "TX1N07P19500.TF-BestBid1", "TX1N07P19500.TF-BestBid2", "TX1N07P19500.TF-BestBid3", "TX1N07P19500.TF-BestBid4", "TX1N07P19500.TF-BestBid5", "TX1N07P19500.TF-BestAsk1", "TX1N07P19500.TF-BestAsk2", "TX1N07P19500.TF-BestAsk3", "TX1N07P19500.TF-BestAsk4", "TX1N07P19500.TF-BestAsk5", "TX1N07P19500.TF-BestBidSize1", "TX1N07P19500.TF-BestBidSize2", "TX1N07P19500.TF-BestBidSize3", "TX1N07P19500.TF-BestBidSize4", "TX1N07P19500.TF-BestBidSize5", "TX1N07P19500.TF-BestAskSize1", "TX1N07P19500.TF-BestAskSize2", "TX1N07P19500.TF-BestAskSize3", "TX1N07P19500.TF-BestAskSize4", "TX1N07P19500.TF-BestAskSize5", "TX1N07P19500.TF-Name", "TX1N07P19500.TF-WContractDate", "TX1N07P19500.TF-SettlePrice", "TX1N07P19500.TF-UpLimit", "TX1N07P19500.TF-DownLimit", "TX1N07P19500.TF-OI", "TX1N07P19500.TF-TradingDate", "TX1N07P19500.TF-WRemainDate", "TX1N07P19500.TF-PreClose", "TX1N07P19500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C19600", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C19600.TF-Time", "TX1N07C19600.TF-TradingDate", "TX1N07C19600.TF-Open", "TX1N07C19600.TF-High", "TX1N07C19600.TF-Low", "TX1N07C19600.TF-Price", "TX1N07C19600.TF-TotalVolume", "TX1N07C19600.TF-Volume", "TX1N07C19600.TF-NWTotalBidContract", "TX1N07C19600.TF-NWTotalAskContract", "TX1N07C19600.TF-NWTotalBidSize", "TX1N07C19600.TF-NWTotalAskSize", "TX1N07C19600.TF-InSize", "TX1N07C19600.TF-OutSize", "TX1N07C19600.TF-TotalBidMatchTx", "TX1N07C19600.TF-TotalAskMatchTx", "TX1N07C19600.TF-BestBid1", "TX1N07C19600.TF-BestBid2", "TX1N07C19600.TF-BestBid3", "TX1N07C19600.TF-BestBid4", "TX1N07C19600.TF-BestBid5", "TX1N07C19600.TF-BestAsk1", "TX1N07C19600.TF-BestAsk2", "TX1N07C19600.TF-BestAsk3", "TX1N07C19600.TF-BestAsk4", "TX1N07C19600.TF-BestAsk5", "TX1N07C19600.TF-BestBidSize1", "TX1N07C19600.TF-BestBidSize2", "TX1N07C19600.TF-BestBidSize3", "TX1N07C19600.TF-BestBidSize4", "TX1N07C19600.TF-BestBidSize5", "TX1N07C19600.TF-BestAskSize1", "TX1N07C19600.TF-BestAskSize2", "TX1N07C19600.TF-BestAskSize3", "TX1N07C19600.TF-BestAskSize4", "TX1N07C19600.TF-BestAskSize5", "TX1N07C19600.TF-Name", "TX1N07C19600.TF-WContractDate", "TX1N07C19600.TF-SettlePrice", "TX1N07C19600.TF-UpLimit", "TX1N07C19600.TF-DownLimit", "TX1N07C19600.TF-OI", "TX1N07C19600.TF-TradingDate", "TX1N07C19600.TF-WRemainDate", "TX1N07C19600.TF-PreClose", "TX1N07C19600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P19600", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P19600.TF-Time", "TX1N07P19600.TF-TradingDate", "TX1N07P19600.TF-Open", "TX1N07P19600.TF-High", "TX1N07P19600.TF-Low", "TX1N07P19600.TF-Price", "TX1N07P19600.TF-TotalVolume", "TX1N07P19600.TF-Volume", "TX1N07P19600.TF-NWTotalBidContract", "TX1N07P19600.TF-NWTotalAskContract", "TX1N07P19600.TF-NWTotalBidSize", "TX1N07P19600.TF-NWTotalAskSize", "TX1N07P19600.TF-InSize", "TX1N07P19600.TF-OutSize", "TX1N07P19600.TF-TotalBidMatchTx", "TX1N07P19600.TF-TotalAskMatchTx", "TX1N07P19600.TF-BestBid1", "TX1N07P19600.TF-BestBid2", "TX1N07P19600.TF-BestBid3", "TX1N07P19600.TF-BestBid4", "TX1N07P19600.TF-BestBid5", "TX1N07P19600.TF-BestAsk1", "TX1N07P19600.TF-BestAsk2", "TX1N07P19600.TF-BestAsk3", "TX1N07P19600.TF-BestAsk4", "TX1N07P19600.TF-BestAsk5", "TX1N07P19600.TF-BestBidSize1", "TX1N07P19600.TF-BestBidSize2", "TX1N07P19600.TF-BestBidSize3", "TX1N07P19600.TF-BestBidSize4", "TX1N07P19600.TF-BestBidSize5", "TX1N07P19600.TF-BestAskSize1", "TX1N07P19600.TF-BestAskSize2", "TX1N07P19600.TF-BestAskSize3", "TX1N07P19600.TF-BestAskSize4", "TX1N07P19600.TF-BestAskSize5", "TX1N07P19600.TF-Name", "TX1N07P19600.TF-WContractDate", "TX1N07P19600.TF-SettlePrice", "TX1N07P19600.TF-UpLimit", "TX1N07P19600.TF-DownLimit", "TX1N07P19600.TF-OI", "TX1N07P19600.TF-TradingDate", "TX1N07P19600.TF-WRemainDate", "TX1N07P19600.TF-PreClose", "TX1N07P19600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C19700", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C19700.TF-Time", "TX1N07C19700.TF-TradingDate", "TX1N07C19700.TF-Open", "TX1N07C19700.TF-High", "TX1N07C19700.TF-Low", "TX1N07C19700.TF-Price", "TX1N07C19700.TF-TotalVolume", "TX1N07C19700.TF-Volume", "TX1N07C19700.TF-NWTotalBidContract", "TX1N07C19700.TF-NWTotalAskContract", "TX1N07C19700.TF-NWTotalBidSize", "TX1N07C19700.TF-NWTotalAskSize", "TX1N07C19700.TF-InSize", "TX1N07C19700.TF-OutSize", "TX1N07C19700.TF-TotalBidMatchTx", "TX1N07C19700.TF-TotalAskMatchTx", "TX1N07C19700.TF-BestBid1", "TX1N07C19700.TF-BestBid2", "TX1N07C19700.TF-BestBid3", "TX1N07C19700.TF-BestBid4", "TX1N07C19700.TF-BestBid5", "TX1N07C19700.TF-BestAsk1", "TX1N07C19700.TF-BestAsk2", "TX1N07C19700.TF-BestAsk3", "TX1N07C19700.TF-BestAsk4", "TX1N07C19700.TF-BestAsk5", "TX1N07C19700.TF-BestBidSize1", "TX1N07C19700.TF-BestBidSize2", "TX1N07C19700.TF-BestBidSize3", "TX1N07C19700.TF-BestBidSize4", "TX1N07C19700.TF-BestBidSize5", "TX1N07C19700.TF-BestAskSize1", "TX1N07C19700.TF-BestAskSize2", "TX1N07C19700.TF-BestAskSize3", "TX1N07C19700.TF-BestAskSize4", "TX1N07C19700.TF-BestAskSize5", "TX1N07C19700.TF-Name", "TX1N07C19700.TF-WContractDate", "TX1N07C19700.TF-SettlePrice", "TX1N07C19700.TF-UpLimit", "TX1N07C19700.TF-DownLimit", "TX1N07C19700.TF-OI", "TX1N07C19700.TF-TradingDate", "TX1N07C19700.TF-WRemainDate", "TX1N07C19700.TF-PreClose", "TX1N07C19700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P19700", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P19700.TF-Time", "TX1N07P19700.TF-TradingDate", "TX1N07P19700.TF-Open", "TX1N07P19700.TF-High", "TX1N07P19700.TF-Low", "TX1N07P19700.TF-Price", "TX1N07P19700.TF-TotalVolume", "TX1N07P19700.TF-Volume", "TX1N07P19700.TF-NWTotalBidContract", "TX1N07P19700.TF-NWTotalAskContract", "TX1N07P19700.TF-NWTotalBidSize", "TX1N07P19700.TF-NWTotalAskSize", "TX1N07P19700.TF-InSize", "TX1N07P19700.TF-OutSize", "TX1N07P19700.TF-TotalBidMatchTx", "TX1N07P19700.TF-TotalAskMatchTx", "TX1N07P19700.TF-BestBid1", "TX1N07P19700.TF-BestBid2", "TX1N07P19700.TF-BestBid3", "TX1N07P19700.TF-BestBid4", "TX1N07P19700.TF-BestBid5", "TX1N07P19700.TF-BestAsk1", "TX1N07P19700.TF-BestAsk2", "TX1N07P19700.TF-BestAsk3", "TX1N07P19700.TF-BestAsk4", "TX1N07P19700.TF-BestAsk5", "TX1N07P19700.TF-BestBidSize1", "TX1N07P19700.TF-BestBidSize2", "TX1N07P19700.TF-BestBidSize3", "TX1N07P19700.TF-BestBidSize4", "TX1N07P19700.TF-BestBidSize5", "TX1N07P19700.TF-BestAskSize1", "TX1N07P19700.TF-BestAskSize2", "TX1N07P19700.TF-BestAskSize3", "TX1N07P19700.TF-BestAskSize4", "TX1N07P19700.TF-BestAskSize5", "TX1N07P19700.TF-Name", "TX1N07P19700.TF-WContractDate", "TX1N07P19700.TF-SettlePrice", "TX1N07P19700.TF-UpLimit", "TX1N07P19700.TF-DownLimit", "TX1N07P19700.TF-OI", "TX1N07P19700.TF-TradingDate", "TX1N07P19700.TF-WRemainDate", "TX1N07P19700.TF-PreClose", "TX1N07P19700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C19800", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C19800.TF-Time", "TX1N07C19800.TF-TradingDate", "TX1N07C19800.TF-Open", "TX1N07C19800.TF-High", "TX1N07C19800.TF-Low", "TX1N07C19800.TF-Price", "TX1N07C19800.TF-TotalVolume", "TX1N07C19800.TF-Volume", "TX1N07C19800.TF-NWTotalBidContract", "TX1N07C19800.TF-NWTotalAskContract", "TX1N07C19800.TF-NWTotalBidSize", "TX1N07C19800.TF-NWTotalAskSize", "TX1N07C19800.TF-InSize", "TX1N07C19800.TF-OutSize", "TX1N07C19800.TF-TotalBidMatchTx", "TX1N07C19800.TF-TotalAskMatchTx", "TX1N07C19800.TF-BestBid1", "TX1N07C19800.TF-BestBid2", "TX1N07C19800.TF-BestBid3", "TX1N07C19800.TF-BestBid4", "TX1N07C19800.TF-BestBid5", "TX1N07C19800.TF-BestAsk1", "TX1N07C19800.TF-BestAsk2", "TX1N07C19800.TF-BestAsk3", "TX1N07C19800.TF-BestAsk4", "TX1N07C19800.TF-BestAsk5", "TX1N07C19800.TF-BestBidSize1", "TX1N07C19800.TF-BestBidSize2", "TX1N07C19800.TF-BestBidSize3", "TX1N07C19800.TF-BestBidSize4", "TX1N07C19800.TF-BestBidSize5", "TX1N07C19800.TF-BestAskSize1", "TX1N07C19800.TF-BestAskSize2", "TX1N07C19800.TF-BestAskSize3", "TX1N07C19800.TF-BestAskSize4", "TX1N07C19800.TF-BestAskSize5", "TX1N07C19800.TF-Name", "TX1N07C19800.TF-WContractDate", "TX1N07C19800.TF-SettlePrice", "TX1N07C19800.TF-UpLimit", "TX1N07C19800.TF-DownLimit", "TX1N07C19800.TF-OI", "TX1N07C19800.TF-TradingDate", "TX1N07C19800.TF-WRemainDate", "TX1N07C19800.TF-PreClose", "TX1N07C19800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P19800", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P19800.TF-Time", "TX1N07P19800.TF-TradingDate", "TX1N07P19800.TF-Open", "TX1N07P19800.TF-High", "TX1N07P19800.TF-Low", "TX1N07P19800.TF-Price", "TX1N07P19800.TF-TotalVolume", "TX1N07P19800.TF-Volume", "TX1N07P19800.TF-NWTotalBidContract", "TX1N07P19800.TF-NWTotalAskContract", "TX1N07P19800.TF-NWTotalBidSize", "TX1N07P19800.TF-NWTotalAskSize", "TX1N07P19800.TF-InSize", "TX1N07P19800.TF-OutSize", "TX1N07P19800.TF-TotalBidMatchTx", "TX1N07P19800.TF-TotalAskMatchTx", "TX1N07P19800.TF-BestBid1", "TX1N07P19800.TF-BestBid2", "TX1N07P19800.TF-BestBid3", "TX1N07P19800.TF-BestBid4", "TX1N07P19800.TF-BestBid5", "TX1N07P19800.TF-BestAsk1", "TX1N07P19800.TF-BestAsk2", "TX1N07P19800.TF-BestAsk3", "TX1N07P19800.TF-BestAsk4", "TX1N07P19800.TF-BestAsk5", "TX1N07P19800.TF-BestBidSize1", "TX1N07P19800.TF-BestBidSize2", "TX1N07P19800.TF-BestBidSize3", "TX1N07P19800.TF-BestBidSize4", "TX1N07P19800.TF-BestBidSize5", "TX1N07P19800.TF-BestAskSize1", "TX1N07P19800.TF-BestAskSize2", "TX1N07P19800.TF-BestAskSize3", "TX1N07P19800.TF-BestAskSize4", "TX1N07P19800.TF-BestAskSize5", "TX1N07P19800.TF-Name", "TX1N07P19800.TF-WContractDate", "TX1N07P19800.TF-SettlePrice", "TX1N07P19800.TF-UpLimit", "TX1N07P19800.TF-DownLimit", "TX1N07P19800.TF-OI", "TX1N07P19800.TF-TradingDate", "TX1N07P19800.TF-WRemainDate", "TX1N07P19800.TF-PreClose", "TX1N07P19800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C19900", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C19900.TF-Time", "TX1N07C19900.TF-TradingDate", "TX1N07C19900.TF-Open", "TX1N07C19900.TF-High", "TX1N07C19900.TF-Low", "TX1N07C19900.TF-Price", "TX1N07C19900.TF-TotalVolume", "TX1N07C19900.TF-Volume", "TX1N07C19900.TF-NWTotalBidContract", "TX1N07C19900.TF-NWTotalAskContract", "TX1N07C19900.TF-NWTotalBidSize", "TX1N07C19900.TF-NWTotalAskSize", "TX1N07C19900.TF-InSize", "TX1N07C19900.TF-OutSize", "TX1N07C19900.TF-TotalBidMatchTx", "TX1N07C19900.TF-TotalAskMatchTx", "TX1N07C19900.TF-BestBid1", "TX1N07C19900.TF-BestBid2", "TX1N07C19900.TF-BestBid3", "TX1N07C19900.TF-BestBid4", "TX1N07C19900.TF-BestBid5", "TX1N07C19900.TF-BestAsk1", "TX1N07C19900.TF-BestAsk2", "TX1N07C19900.TF-BestAsk3", "TX1N07C19900.TF-BestAsk4", "TX1N07C19900.TF-BestAsk5", "TX1N07C19900.TF-BestBidSize1", "TX1N07C19900.TF-BestBidSize2", "TX1N07C19900.TF-BestBidSize3", "TX1N07C19900.TF-BestBidSize4", "TX1N07C19900.TF-BestBidSize5", "TX1N07C19900.TF-BestAskSize1", "TX1N07C19900.TF-BestAskSize2", "TX1N07C19900.TF-BestAskSize3", "TX1N07C19900.TF-BestAskSize4", "TX1N07C19900.TF-BestAskSize5", "TX1N07C19900.TF-Name", "TX1N07C19900.TF-WContractDate", "TX1N07C19900.TF-SettlePrice", "TX1N07C19900.TF-UpLimit", "TX1N07C19900.TF-DownLimit", "TX1N07C19900.TF-OI", "TX1N07C19900.TF-TradingDate", "TX1N07C19900.TF-WRemainDate", "TX1N07C19900.TF-PreClose", "TX1N07C19900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P19900", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P19900.TF-Time", "TX1N07P19900.TF-TradingDate", "TX1N07P19900.TF-Open", "TX1N07P19900.TF-High", "TX1N07P19900.TF-Low", "TX1N07P19900.TF-Price", "TX1N07P19900.TF-TotalVolume", "TX1N07P19900.TF-Volume", "TX1N07P19900.TF-NWTotalBidContract", "TX1N07P19900.TF-NWTotalAskContract", "TX1N07P19900.TF-NWTotalBidSize", "TX1N07P19900.TF-NWTotalAskSize", "TX1N07P19900.TF-InSize", "TX1N07P19900.TF-OutSize", "TX1N07P19900.TF-TotalBidMatchTx", "TX1N07P19900.TF-TotalAskMatchTx", "TX1N07P19900.TF-BestBid1", "TX1N07P19900.TF-BestBid2", "TX1N07P19900.TF-BestBid3", "TX1N07P19900.TF-BestBid4", "TX1N07P19900.TF-BestBid5", "TX1N07P19900.TF-BestAsk1", "TX1N07P19900.TF-BestAsk2", "TX1N07P19900.TF-BestAsk3", "TX1N07P19900.TF-BestAsk4", "TX1N07P19900.TF-BestAsk5", "TX1N07P19900.TF-BestBidSize1", "TX1N07P19900.TF-BestBidSize2", "TX1N07P19900.TF-BestBidSize3", "TX1N07P19900.TF-BestBidSize4", "TX1N07P19900.TF-BestBidSize5", "TX1N07P19900.TF-BestAskSize1", "TX1N07P19900.TF-BestAskSize2", "TX1N07P19900.TF-BestAskSize3", "TX1N07P19900.TF-BestAskSize4", "TX1N07P19900.TF-BestAskSize5", "TX1N07P19900.TF-Name", "TX1N07P19900.TF-WContractDate", "TX1N07P19900.TF-SettlePrice", "TX1N07P19900.TF-UpLimit", "TX1N07P19900.TF-DownLimit", "TX1N07P19900.TF-OI", "TX1N07P19900.TF-TradingDate", "TX1N07P19900.TF-WRemainDate", "TX1N07P19900.TF-PreClose", "TX1N07P19900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20000", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20000.TF-Time", "TX1N07C20000.TF-TradingDate", "TX1N07C20000.TF-Open", "TX1N07C20000.TF-High", "TX1N07C20000.TF-Low", "TX1N07C20000.TF-Price", "TX1N07C20000.TF-TotalVolume", "TX1N07C20000.TF-Volume", "TX1N07C20000.TF-NWTotalBidContract", "TX1N07C20000.TF-NWTotalAskContract", "TX1N07C20000.TF-NWTotalBidSize", "TX1N07C20000.TF-NWTotalAskSize", "TX1N07C20000.TF-InSize", "TX1N07C20000.TF-OutSize", "TX1N07C20000.TF-TotalBidMatchTx", "TX1N07C20000.TF-TotalAskMatchTx", "TX1N07C20000.TF-BestBid1", "TX1N07C20000.TF-BestBid2", "TX1N07C20000.TF-BestBid3", "TX1N07C20000.TF-BestBid4", "TX1N07C20000.TF-BestBid5", "TX1N07C20000.TF-BestAsk1", "TX1N07C20000.TF-BestAsk2", "TX1N07C20000.TF-BestAsk3", "TX1N07C20000.TF-BestAsk4", "TX1N07C20000.TF-BestAsk5", "TX1N07C20000.TF-BestBidSize1", "TX1N07C20000.TF-BestBidSize2", "TX1N07C20000.TF-BestBidSize3", "TX1N07C20000.TF-BestBidSize4", "TX1N07C20000.TF-BestBidSize5", "TX1N07C20000.TF-BestAskSize1", "TX1N07C20000.TF-BestAskSize2", "TX1N07C20000.TF-BestAskSize3", "TX1N07C20000.TF-BestAskSize4", "TX1N07C20000.TF-BestAskSize5", "TX1N07C20000.TF-Name", "TX1N07C20000.TF-WContractDate", "TX1N07C20000.TF-SettlePrice", "TX1N07C20000.TF-UpLimit", "TX1N07C20000.TF-DownLimit", "TX1N07C20000.TF-OI", "TX1N07C20000.TF-TradingDate", "TX1N07C20000.TF-WRemainDate", "TX1N07C20000.TF-PreClose", "TX1N07C20000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20000", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20000.TF-Time", "TX1N07P20000.TF-TradingDate", "TX1N07P20000.TF-Open", "TX1N07P20000.TF-High", "TX1N07P20000.TF-Low", "TX1N07P20000.TF-Price", "TX1N07P20000.TF-TotalVolume", "TX1N07P20000.TF-Volume", "TX1N07P20000.TF-NWTotalBidContract", "TX1N07P20000.TF-NWTotalAskContract", "TX1N07P20000.TF-NWTotalBidSize", "TX1N07P20000.TF-NWTotalAskSize", "TX1N07P20000.TF-InSize", "TX1N07P20000.TF-OutSize", "TX1N07P20000.TF-TotalBidMatchTx", "TX1N07P20000.TF-TotalAskMatchTx", "TX1N07P20000.TF-BestBid1", "TX1N07P20000.TF-BestBid2", "TX1N07P20000.TF-BestBid3", "TX1N07P20000.TF-BestBid4", "TX1N07P20000.TF-BestBid5", "TX1N07P20000.TF-BestAsk1", "TX1N07P20000.TF-BestAsk2", "TX1N07P20000.TF-BestAsk3", "TX1N07P20000.TF-BestAsk4", "TX1N07P20000.TF-BestAsk5", "TX1N07P20000.TF-BestBidSize1", "TX1N07P20000.TF-BestBidSize2", "TX1N07P20000.TF-BestBidSize3", "TX1N07P20000.TF-BestBidSize4", "TX1N07P20000.TF-BestBidSize5", "TX1N07P20000.TF-BestAskSize1", "TX1N07P20000.TF-BestAskSize2", "TX1N07P20000.TF-BestAskSize3", "TX1N07P20000.TF-BestAskSize4", "TX1N07P20000.TF-BestAskSize5", "TX1N07P20000.TF-Name", "TX1N07P20000.TF-WContractDate", "TX1N07P20000.TF-SettlePrice", "TX1N07P20000.TF-UpLimit", "TX1N07P20000.TF-DownLimit", "TX1N07P20000.TF-OI", "TX1N07P20000.TF-TradingDate", "TX1N07P20000.TF-WRemainDate", "TX1N07P20000.TF-PreClose", "TX1N07P20000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20100", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20100.TF-Time", "TX1N07C20100.TF-TradingDate", "TX1N07C20100.TF-Open", "TX1N07C20100.TF-High", "TX1N07C20100.TF-Low", "TX1N07C20100.TF-Price", "TX1N07C20100.TF-TotalVolume", "TX1N07C20100.TF-Volume", "TX1N07C20100.TF-NWTotalBidContract", "TX1N07C20100.TF-NWTotalAskContract", "TX1N07C20100.TF-NWTotalBidSize", "TX1N07C20100.TF-NWTotalAskSize", "TX1N07C20100.TF-InSize", "TX1N07C20100.TF-OutSize", "TX1N07C20100.TF-TotalBidMatchTx", "TX1N07C20100.TF-TotalAskMatchTx", "TX1N07C20100.TF-BestBid1", "TX1N07C20100.TF-BestBid2", "TX1N07C20100.TF-BestBid3", "TX1N07C20100.TF-BestBid4", "TX1N07C20100.TF-BestBid5", "TX1N07C20100.TF-BestAsk1", "TX1N07C20100.TF-BestAsk2", "TX1N07C20100.TF-BestAsk3", "TX1N07C20100.TF-BestAsk4", "TX1N07C20100.TF-BestAsk5", "TX1N07C20100.TF-BestBidSize1", "TX1N07C20100.TF-BestBidSize2", "TX1N07C20100.TF-BestBidSize3", "TX1N07C20100.TF-BestBidSize4", "TX1N07C20100.TF-BestBidSize5", "TX1N07C20100.TF-BestAskSize1", "TX1N07C20100.TF-BestAskSize2", "TX1N07C20100.TF-BestAskSize3", "TX1N07C20100.TF-BestAskSize4", "TX1N07C20100.TF-BestAskSize5", "TX1N07C20100.TF-Name", "TX1N07C20100.TF-WContractDate", "TX1N07C20100.TF-SettlePrice", "TX1N07C20100.TF-UpLimit", "TX1N07C20100.TF-DownLimit", "TX1N07C20100.TF-OI", "TX1N07C20100.TF-TradingDate", "TX1N07C20100.TF-WRemainDate", "TX1N07C20100.TF-PreClose", "TX1N07C20100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20100", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20100.TF-Time", "TX1N07P20100.TF-TradingDate", "TX1N07P20100.TF-Open", "TX1N07P20100.TF-High", "TX1N07P20100.TF-Low", "TX1N07P20100.TF-Price", "TX1N07P20100.TF-TotalVolume", "TX1N07P20100.TF-Volume", "TX1N07P20100.TF-NWTotalBidContract", "TX1N07P20100.TF-NWTotalAskContract", "TX1N07P20100.TF-NWTotalBidSize", "TX1N07P20100.TF-NWTotalAskSize", "TX1N07P20100.TF-InSize", "TX1N07P20100.TF-OutSize", "TX1N07P20100.TF-TotalBidMatchTx", "TX1N07P20100.TF-TotalAskMatchTx", "TX1N07P20100.TF-BestBid1", "TX1N07P20100.TF-BestBid2", "TX1N07P20100.TF-BestBid3", "TX1N07P20100.TF-BestBid4", "TX1N07P20100.TF-BestBid5", "TX1N07P20100.TF-BestAsk1", "TX1N07P20100.TF-BestAsk2", "TX1N07P20100.TF-BestAsk3", "TX1N07P20100.TF-BestAsk4", "TX1N07P20100.TF-BestAsk5", "TX1N07P20100.TF-BestBidSize1", "TX1N07P20100.TF-BestBidSize2", "TX1N07P20100.TF-BestBidSize3", "TX1N07P20100.TF-BestBidSize4", "TX1N07P20100.TF-BestBidSize5", "TX1N07P20100.TF-BestAskSize1", "TX1N07P20100.TF-BestAskSize2", "TX1N07P20100.TF-BestAskSize3", "TX1N07P20100.TF-BestAskSize4", "TX1N07P20100.TF-BestAskSize5", "TX1N07P20100.TF-Name", "TX1N07P20100.TF-WContractDate", "TX1N07P20100.TF-SettlePrice", "TX1N07P20100.TF-UpLimit", "TX1N07P20100.TF-DownLimit", "TX1N07P20100.TF-OI", "TX1N07P20100.TF-TradingDate", "TX1N07P20100.TF-WRemainDate", "TX1N07P20100.TF-PreClose", "TX1N07P20100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20200", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20200.TF-Time", "TX1N07C20200.TF-TradingDate", "TX1N07C20200.TF-Open", "TX1N07C20200.TF-High", "TX1N07C20200.TF-Low", "TX1N07C20200.TF-Price", "TX1N07C20200.TF-TotalVolume", "TX1N07C20200.TF-Volume", "TX1N07C20200.TF-NWTotalBidContract", "TX1N07C20200.TF-NWTotalAskContract", "TX1N07C20200.TF-NWTotalBidSize", "TX1N07C20200.TF-NWTotalAskSize", "TX1N07C20200.TF-InSize", "TX1N07C20200.TF-OutSize", "TX1N07C20200.TF-TotalBidMatchTx", "TX1N07C20200.TF-TotalAskMatchTx", "TX1N07C20200.TF-BestBid1", "TX1N07C20200.TF-BestBid2", "TX1N07C20200.TF-BestBid3", "TX1N07C20200.TF-BestBid4", "TX1N07C20200.TF-BestBid5", "TX1N07C20200.TF-BestAsk1", "TX1N07C20200.TF-BestAsk2", "TX1N07C20200.TF-BestAsk3", "TX1N07C20200.TF-BestAsk4", "TX1N07C20200.TF-BestAsk5", "TX1N07C20200.TF-BestBidSize1", "TX1N07C20200.TF-BestBidSize2", "TX1N07C20200.TF-BestBidSize3", "TX1N07C20200.TF-BestBidSize4", "TX1N07C20200.TF-BestBidSize5", "TX1N07C20200.TF-BestAskSize1", "TX1N07C20200.TF-BestAskSize2", "TX1N07C20200.TF-BestAskSize3", "TX1N07C20200.TF-BestAskSize4", "TX1N07C20200.TF-BestAskSize5", "TX1N07C20200.TF-Name", "TX1N07C20200.TF-WContractDate", "TX1N07C20200.TF-SettlePrice", "TX1N07C20200.TF-UpLimit", "TX1N07C20200.TF-DownLimit", "TX1N07C20200.TF-OI", "TX1N07C20200.TF-TradingDate", "TX1N07C20200.TF-WRemainDate", "TX1N07C20200.TF-PreClose", "TX1N07C20200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20200", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20200.TF-Time", "TX1N07P20200.TF-TradingDate", "TX1N07P20200.TF-Open", "TX1N07P20200.TF-High", "TX1N07P20200.TF-Low", "TX1N07P20200.TF-Price", "TX1N07P20200.TF-TotalVolume", "TX1N07P20200.TF-Volume", "TX1N07P20200.TF-NWTotalBidContract", "TX1N07P20200.TF-NWTotalAskContract", "TX1N07P20200.TF-NWTotalBidSize", "TX1N07P20200.TF-NWTotalAskSize", "TX1N07P20200.TF-InSize", "TX1N07P20200.TF-OutSize", "TX1N07P20200.TF-TotalBidMatchTx", "TX1N07P20200.TF-TotalAskMatchTx", "TX1N07P20200.TF-BestBid1", "TX1N07P20200.TF-BestBid2", "TX1N07P20200.TF-BestBid3", "TX1N07P20200.TF-BestBid4", "TX1N07P20200.TF-BestBid5", "TX1N07P20200.TF-BestAsk1", "TX1N07P20200.TF-BestAsk2", "TX1N07P20200.TF-BestAsk3", "TX1N07P20200.TF-BestAsk4", "TX1N07P20200.TF-BestAsk5", "TX1N07P20200.TF-BestBidSize1", "TX1N07P20200.TF-BestBidSize2", "TX1N07P20200.TF-BestBidSize3", "TX1N07P20200.TF-BestBidSize4", "TX1N07P20200.TF-BestBidSize5", "TX1N07P20200.TF-BestAskSize1", "TX1N07P20200.TF-BestAskSize2", "TX1N07P20200.TF-BestAskSize3", "TX1N07P20200.TF-BestAskSize4", "TX1N07P20200.TF-BestAskSize5", "TX1N07P20200.TF-Name", "TX1N07P20200.TF-WContractDate", "TX1N07P20200.TF-SettlePrice", "TX1N07P20200.TF-UpLimit", "TX1N07P20200.TF-DownLimit", "TX1N07P20200.TF-OI", "TX1N07P20200.TF-TradingDate", "TX1N07P20200.TF-WRemainDate", "TX1N07P20200.TF-PreClose", "TX1N07P20200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20300", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20300.TF-Time", "TX1N07C20300.TF-TradingDate", "TX1N07C20300.TF-Open", "TX1N07C20300.TF-High", "TX1N07C20300.TF-Low", "TX1N07C20300.TF-Price", "TX1N07C20300.TF-TotalVolume", "TX1N07C20300.TF-Volume", "TX1N07C20300.TF-NWTotalBidContract", "TX1N07C20300.TF-NWTotalAskContract", "TX1N07C20300.TF-NWTotalBidSize", "TX1N07C20300.TF-NWTotalAskSize", "TX1N07C20300.TF-InSize", "TX1N07C20300.TF-OutSize", "TX1N07C20300.TF-TotalBidMatchTx", "TX1N07C20300.TF-TotalAskMatchTx", "TX1N07C20300.TF-BestBid1", "TX1N07C20300.TF-BestBid2", "TX1N07C20300.TF-BestBid3", "TX1N07C20300.TF-BestBid4", "TX1N07C20300.TF-BestBid5", "TX1N07C20300.TF-BestAsk1", "TX1N07C20300.TF-BestAsk2", "TX1N07C20300.TF-BestAsk3", "TX1N07C20300.TF-BestAsk4", "TX1N07C20300.TF-BestAsk5", "TX1N07C20300.TF-BestBidSize1", "TX1N07C20300.TF-BestBidSize2", "TX1N07C20300.TF-BestBidSize3", "TX1N07C20300.TF-BestBidSize4", "TX1N07C20300.TF-BestBidSize5", "TX1N07C20300.TF-BestAskSize1", "TX1N07C20300.TF-BestAskSize2", "TX1N07C20300.TF-BestAskSize3", "TX1N07C20300.TF-BestAskSize4", "TX1N07C20300.TF-BestAskSize5", "TX1N07C20300.TF-Name", "TX1N07C20300.TF-WContractDate", "TX1N07C20300.TF-SettlePrice", "TX1N07C20300.TF-UpLimit", "TX1N07C20300.TF-DownLimit", "TX1N07C20300.TF-OI", "TX1N07C20300.TF-TradingDate", "TX1N07C20300.TF-WRemainDate", "TX1N07C20300.TF-PreClose", "TX1N07C20300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20300", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20300.TF-Time", "TX1N07P20300.TF-TradingDate", "TX1N07P20300.TF-Open", "TX1N07P20300.TF-High", "TX1N07P20300.TF-Low", "TX1N07P20300.TF-Price", "TX1N07P20300.TF-TotalVolume", "TX1N07P20300.TF-Volume", "TX1N07P20300.TF-NWTotalBidContract", "TX1N07P20300.TF-NWTotalAskContract", "TX1N07P20300.TF-NWTotalBidSize", "TX1N07P20300.TF-NWTotalAskSize", "TX1N07P20300.TF-InSize", "TX1N07P20300.TF-OutSize", "TX1N07P20300.TF-TotalBidMatchTx", "TX1N07P20300.TF-TotalAskMatchTx", "TX1N07P20300.TF-BestBid1", "TX1N07P20300.TF-BestBid2", "TX1N07P20300.TF-BestBid3", "TX1N07P20300.TF-BestBid4", "TX1N07P20300.TF-BestBid5", "TX1N07P20300.TF-BestAsk1", "TX1N07P20300.TF-BestAsk2", "TX1N07P20300.TF-BestAsk3", "TX1N07P20300.TF-BestAsk4", "TX1N07P20300.TF-BestAsk5", "TX1N07P20300.TF-BestBidSize1", "TX1N07P20300.TF-BestBidSize2", "TX1N07P20300.TF-BestBidSize3", "TX1N07P20300.TF-BestBidSize4", "TX1N07P20300.TF-BestBidSize5", "TX1N07P20300.TF-BestAskSize1", "TX1N07P20300.TF-BestAskSize2", "TX1N07P20300.TF-BestAskSize3", "TX1N07P20300.TF-BestAskSize4", "TX1N07P20300.TF-BestAskSize5", "TX1N07P20300.TF-Name", "TX1N07P20300.TF-WContractDate", "TX1N07P20300.TF-SettlePrice", "TX1N07P20300.TF-UpLimit", "TX1N07P20300.TF-DownLimit", "TX1N07P20300.TF-OI", "TX1N07P20300.TF-TradingDate", "TX1N07P20300.TF-WRemainDate", "TX1N07P20300.TF-PreClose", "TX1N07P20300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20400", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20400.TF-Time", "TX1N07C20400.TF-TradingDate", "TX1N07C20400.TF-Open", "TX1N07C20400.TF-High", "TX1N07C20400.TF-Low", "TX1N07C20400.TF-Price", "TX1N07C20400.TF-TotalVolume", "TX1N07C20400.TF-Volume", "TX1N07C20400.TF-NWTotalBidContract", "TX1N07C20400.TF-NWTotalAskContract", "TX1N07C20400.TF-NWTotalBidSize", "TX1N07C20400.TF-NWTotalAskSize", "TX1N07C20400.TF-InSize", "TX1N07C20400.TF-OutSize", "TX1N07C20400.TF-TotalBidMatchTx", "TX1N07C20400.TF-TotalAskMatchTx", "TX1N07C20400.TF-BestBid1", "TX1N07C20400.TF-BestBid2", "TX1N07C20400.TF-BestBid3", "TX1N07C20400.TF-BestBid4", "TX1N07C20400.TF-BestBid5", "TX1N07C20400.TF-BestAsk1", "TX1N07C20400.TF-BestAsk2", "TX1N07C20400.TF-BestAsk3", "TX1N07C20400.TF-BestAsk4", "TX1N07C20400.TF-BestAsk5", "TX1N07C20400.TF-BestBidSize1", "TX1N07C20400.TF-BestBidSize2", "TX1N07C20400.TF-BestBidSize3", "TX1N07C20400.TF-BestBidSize4", "TX1N07C20400.TF-BestBidSize5", "TX1N07C20400.TF-BestAskSize1", "TX1N07C20400.TF-BestAskSize2", "TX1N07C20400.TF-BestAskSize3", "TX1N07C20400.TF-BestAskSize4", "TX1N07C20400.TF-BestAskSize5", "TX1N07C20400.TF-Name", "TX1N07C20400.TF-WContractDate", "TX1N07C20400.TF-SettlePrice", "TX1N07C20400.TF-UpLimit", "TX1N07C20400.TF-DownLimit", "TX1N07C20400.TF-OI", "TX1N07C20400.TF-TradingDate", "TX1N07C20400.TF-WRemainDate", "TX1N07C20400.TF-PreClose", "TX1N07C20400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20400", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20400.TF-Time", "TX1N07P20400.TF-TradingDate", "TX1N07P20400.TF-Open", "TX1N07P20400.TF-High", "TX1N07P20400.TF-Low", "TX1N07P20400.TF-Price", "TX1N07P20400.TF-TotalVolume", "TX1N07P20400.TF-Volume", "TX1N07P20400.TF-NWTotalBidContract", "TX1N07P20400.TF-NWTotalAskContract", "TX1N07P20400.TF-NWTotalBidSize", "TX1N07P20400.TF-NWTotalAskSize", "TX1N07P20400.TF-InSize", "TX1N07P20400.TF-OutSize", "TX1N07P20400.TF-TotalBidMatchTx", "TX1N07P20400.TF-TotalAskMatchTx", "TX1N07P20400.TF-BestBid1", "TX1N07P20400.TF-BestBid2", "TX1N07P20400.TF-BestBid3", "TX1N07P20400.TF-BestBid4", "TX1N07P20400.TF-BestBid5", "TX1N07P20400.TF-BestAsk1", "TX1N07P20400.TF-BestAsk2", "TX1N07P20400.TF-BestAsk3", "TX1N07P20400.TF-BestAsk4", "TX1N07P20400.TF-BestAsk5", "TX1N07P20400.TF-BestBidSize1", "TX1N07P20400.TF-BestBidSize2", "TX1N07P20400.TF-BestBidSize3", "TX1N07P20400.TF-BestBidSize4", "TX1N07P20400.TF-BestBidSize5", "TX1N07P20400.TF-BestAskSize1", "TX1N07P20400.TF-BestAskSize2", "TX1N07P20400.TF-BestAskSize3", "TX1N07P20400.TF-BestAskSize4", "TX1N07P20400.TF-BestAskSize5", "TX1N07P20400.TF-Name", "TX1N07P20400.TF-WContractDate", "TX1N07P20400.TF-SettlePrice", "TX1N07P20400.TF-UpLimit", "TX1N07P20400.TF-DownLimit", "TX1N07P20400.TF-OI", "TX1N07P20400.TF-TradingDate", "TX1N07P20400.TF-WRemainDate", "TX1N07P20400.TF-PreClose", "TX1N07P20400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20500", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20500.TF-Time", "TX1N07C20500.TF-TradingDate", "TX1N07C20500.TF-Open", "TX1N07C20500.TF-High", "TX1N07C20500.TF-Low", "TX1N07C20500.TF-Price", "TX1N07C20500.TF-TotalVolume", "TX1N07C20500.TF-Volume", "TX1N07C20500.TF-NWTotalBidContract", "TX1N07C20500.TF-NWTotalAskContract", "TX1N07C20500.TF-NWTotalBidSize", "TX1N07C20500.TF-NWTotalAskSize", "TX1N07C20500.TF-InSize", "TX1N07C20500.TF-OutSize", "TX1N07C20500.TF-TotalBidMatchTx", "TX1N07C20500.TF-TotalAskMatchTx", "TX1N07C20500.TF-BestBid1", "TX1N07C20500.TF-BestBid2", "TX1N07C20500.TF-BestBid3", "TX1N07C20500.TF-BestBid4", "TX1N07C20500.TF-BestBid5", "TX1N07C20500.TF-BestAsk1", "TX1N07C20500.TF-BestAsk2", "TX1N07C20500.TF-BestAsk3", "TX1N07C20500.TF-BestAsk4", "TX1N07C20500.TF-BestAsk5", "TX1N07C20500.TF-BestBidSize1", "TX1N07C20500.TF-BestBidSize2", "TX1N07C20500.TF-BestBidSize3", "TX1N07C20500.TF-BestBidSize4", "TX1N07C20500.TF-BestBidSize5", "TX1N07C20500.TF-BestAskSize1", "TX1N07C20500.TF-BestAskSize2", "TX1N07C20500.TF-BestAskSize3", "TX1N07C20500.TF-BestAskSize4", "TX1N07C20500.TF-BestAskSize5", "TX1N07C20500.TF-Name", "TX1N07C20500.TF-WContractDate", "TX1N07C20500.TF-SettlePrice", "TX1N07C20500.TF-UpLimit", "TX1N07C20500.TF-DownLimit", "TX1N07C20500.TF-OI", "TX1N07C20500.TF-TradingDate", "TX1N07C20500.TF-WRemainDate", "TX1N07C20500.TF-PreClose", "TX1N07C20500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20500", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20500.TF-Time", "TX1N07P20500.TF-TradingDate", "TX1N07P20500.TF-Open", "TX1N07P20500.TF-High", "TX1N07P20500.TF-Low", "TX1N07P20500.TF-Price", "TX1N07P20500.TF-TotalVolume", "TX1N07P20500.TF-Volume", "TX1N07P20500.TF-NWTotalBidContract", "TX1N07P20500.TF-NWTotalAskContract", "TX1N07P20500.TF-NWTotalBidSize", "TX1N07P20500.TF-NWTotalAskSize", "TX1N07P20500.TF-InSize", "TX1N07P20500.TF-OutSize", "TX1N07P20500.TF-TotalBidMatchTx", "TX1N07P20500.TF-TotalAskMatchTx", "TX1N07P20500.TF-BestBid1", "TX1N07P20500.TF-BestBid2", "TX1N07P20500.TF-BestBid3", "TX1N07P20500.TF-BestBid4", "TX1N07P20500.TF-BestBid5", "TX1N07P20500.TF-BestAsk1", "TX1N07P20500.TF-BestAsk2", "TX1N07P20500.TF-BestAsk3", "TX1N07P20500.TF-BestAsk4", "TX1N07P20500.TF-BestAsk5", "TX1N07P20500.TF-BestBidSize1", "TX1N07P20500.TF-BestBidSize2", "TX1N07P20500.TF-BestBidSize3", "TX1N07P20500.TF-BestBidSize4", "TX1N07P20500.TF-BestBidSize5", "TX1N07P20500.TF-BestAskSize1", "TX1N07P20500.TF-BestAskSize2", "TX1N07P20500.TF-BestAskSize3", "TX1N07P20500.TF-BestAskSize4", "TX1N07P20500.TF-BestAskSize5", "TX1N07P20500.TF-Name", "TX1N07P20500.TF-WContractDate", "TX1N07P20500.TF-SettlePrice", "TX1N07P20500.TF-UpLimit", "TX1N07P20500.TF-DownLimit", "TX1N07P20500.TF-OI", "TX1N07P20500.TF-TradingDate", "TX1N07P20500.TF-WRemainDate", "TX1N07P20500.TF-PreClose", "TX1N07P20500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20600", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20600.TF-Time", "TX1N07C20600.TF-TradingDate", "TX1N07C20600.TF-Open", "TX1N07C20600.TF-High", "TX1N07C20600.TF-Low", "TX1N07C20600.TF-Price", "TX1N07C20600.TF-TotalVolume", "TX1N07C20600.TF-Volume", "TX1N07C20600.TF-NWTotalBidContract", "TX1N07C20600.TF-NWTotalAskContract", "TX1N07C20600.TF-NWTotalBidSize", "TX1N07C20600.TF-NWTotalAskSize", "TX1N07C20600.TF-InSize", "TX1N07C20600.TF-OutSize", "TX1N07C20600.TF-TotalBidMatchTx", "TX1N07C20600.TF-TotalAskMatchTx", "TX1N07C20600.TF-BestBid1", "TX1N07C20600.TF-BestBid2", "TX1N07C20600.TF-BestBid3", "TX1N07C20600.TF-BestBid4", "TX1N07C20600.TF-BestBid5", "TX1N07C20600.TF-BestAsk1", "TX1N07C20600.TF-BestAsk2", "TX1N07C20600.TF-BestAsk3", "TX1N07C20600.TF-BestAsk4", "TX1N07C20600.TF-BestAsk5", "TX1N07C20600.TF-BestBidSize1", "TX1N07C20600.TF-BestBidSize2", "TX1N07C20600.TF-BestBidSize3", "TX1N07C20600.TF-BestBidSize4", "TX1N07C20600.TF-BestBidSize5", "TX1N07C20600.TF-BestAskSize1", "TX1N07C20600.TF-BestAskSize2", "TX1N07C20600.TF-BestAskSize3", "TX1N07C20600.TF-BestAskSize4", "TX1N07C20600.TF-BestAskSize5", "TX1N07C20600.TF-Name", "TX1N07C20600.TF-WContractDate", "TX1N07C20600.TF-SettlePrice", "TX1N07C20600.TF-UpLimit", "TX1N07C20600.TF-DownLimit", "TX1N07C20600.TF-OI", "TX1N07C20600.TF-TradingDate", "TX1N07C20600.TF-WRemainDate", "TX1N07C20600.TF-PreClose", "TX1N07C20600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20600", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20600.TF-Time", "TX1N07P20600.TF-TradingDate", "TX1N07P20600.TF-Open", "TX1N07P20600.TF-High", "TX1N07P20600.TF-Low", "TX1N07P20600.TF-Price", "TX1N07P20600.TF-TotalVolume", "TX1N07P20600.TF-Volume", "TX1N07P20600.TF-NWTotalBidContract", "TX1N07P20600.TF-NWTotalAskContract", "TX1N07P20600.TF-NWTotalBidSize", "TX1N07P20600.TF-NWTotalAskSize", "TX1N07P20600.TF-InSize", "TX1N07P20600.TF-OutSize", "TX1N07P20600.TF-TotalBidMatchTx", "TX1N07P20600.TF-TotalAskMatchTx", "TX1N07P20600.TF-BestBid1", "TX1N07P20600.TF-BestBid2", "TX1N07P20600.TF-BestBid3", "TX1N07P20600.TF-BestBid4", "TX1N07P20600.TF-BestBid5", "TX1N07P20600.TF-BestAsk1", "TX1N07P20600.TF-BestAsk2", "TX1N07P20600.TF-BestAsk3", "TX1N07P20600.TF-BestAsk4", "TX1N07P20600.TF-BestAsk5", "TX1N07P20600.TF-BestBidSize1", "TX1N07P20600.TF-BestBidSize2", "TX1N07P20600.TF-BestBidSize3", "TX1N07P20600.TF-BestBidSize4", "TX1N07P20600.TF-BestBidSize5", "TX1N07P20600.TF-BestAskSize1", "TX1N07P20600.TF-BestAskSize2", "TX1N07P20600.TF-BestAskSize3", "TX1N07P20600.TF-BestAskSize4", "TX1N07P20600.TF-BestAskSize5", "TX1N07P20600.TF-Name", "TX1N07P20600.TF-WContractDate", "TX1N07P20600.TF-SettlePrice", "TX1N07P20600.TF-UpLimit", "TX1N07P20600.TF-DownLimit", "TX1N07P20600.TF-OI", "TX1N07P20600.TF-TradingDate", "TX1N07P20600.TF-WRemainDate", "TX1N07P20600.TF-PreClose", "TX1N07P20600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20700", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20700.TF-Time", "TX1N07C20700.TF-TradingDate", "TX1N07C20700.TF-Open", "TX1N07C20700.TF-High", "TX1N07C20700.TF-Low", "TX1N07C20700.TF-Price", "TX1N07C20700.TF-TotalVolume", "TX1N07C20700.TF-Volume", "TX1N07C20700.TF-NWTotalBidContract", "TX1N07C20700.TF-NWTotalAskContract", "TX1N07C20700.TF-NWTotalBidSize", "TX1N07C20700.TF-NWTotalAskSize", "TX1N07C20700.TF-InSize", "TX1N07C20700.TF-OutSize", "TX1N07C20700.TF-TotalBidMatchTx", "TX1N07C20700.TF-TotalAskMatchTx", "TX1N07C20700.TF-BestBid1", "TX1N07C20700.TF-BestBid2", "TX1N07C20700.TF-BestBid3", "TX1N07C20700.TF-BestBid4", "TX1N07C20700.TF-BestBid5", "TX1N07C20700.TF-BestAsk1", "TX1N07C20700.TF-BestAsk2", "TX1N07C20700.TF-BestAsk3", "TX1N07C20700.TF-BestAsk4", "TX1N07C20700.TF-BestAsk5", "TX1N07C20700.TF-BestBidSize1", "TX1N07C20700.TF-BestBidSize2", "TX1N07C20700.TF-BestBidSize3", "TX1N07C20700.TF-BestBidSize4", "TX1N07C20700.TF-BestBidSize5", "TX1N07C20700.TF-BestAskSize1", "TX1N07C20700.TF-BestAskSize2", "TX1N07C20700.TF-BestAskSize3", "TX1N07C20700.TF-BestAskSize4", "TX1N07C20700.TF-BestAskSize5", "TX1N07C20700.TF-Name", "TX1N07C20700.TF-WContractDate", "TX1N07C20700.TF-SettlePrice", "TX1N07C20700.TF-UpLimit", "TX1N07C20700.TF-DownLimit", "TX1N07C20700.TF-OI", "TX1N07C20700.TF-TradingDate", "TX1N07C20700.TF-WRemainDate", "TX1N07C20700.TF-PreClose", "TX1N07C20700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20700", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20700.TF-Time", "TX1N07P20700.TF-TradingDate", "TX1N07P20700.TF-Open", "TX1N07P20700.TF-High", "TX1N07P20700.TF-Low", "TX1N07P20700.TF-Price", "TX1N07P20700.TF-TotalVolume", "TX1N07P20700.TF-Volume", "TX1N07P20700.TF-NWTotalBidContract", "TX1N07P20700.TF-NWTotalAskContract", "TX1N07P20700.TF-NWTotalBidSize", "TX1N07P20700.TF-NWTotalAskSize", "TX1N07P20700.TF-InSize", "TX1N07P20700.TF-OutSize", "TX1N07P20700.TF-TotalBidMatchTx", "TX1N07P20700.TF-TotalAskMatchTx", "TX1N07P20700.TF-BestBid1", "TX1N07P20700.TF-BestBid2", "TX1N07P20700.TF-BestBid3", "TX1N07P20700.TF-BestBid4", "TX1N07P20700.TF-BestBid5", "TX1N07P20700.TF-BestAsk1", "TX1N07P20700.TF-BestAsk2", "TX1N07P20700.TF-BestAsk3", "TX1N07P20700.TF-BestAsk4", "TX1N07P20700.TF-BestAsk5", "TX1N07P20700.TF-BestBidSize1", "TX1N07P20700.TF-BestBidSize2", "TX1N07P20700.TF-BestBidSize3", "TX1N07P20700.TF-BestBidSize4", "TX1N07P20700.TF-BestBidSize5", "TX1N07P20700.TF-BestAskSize1", "TX1N07P20700.TF-BestAskSize2", "TX1N07P20700.TF-BestAskSize3", "TX1N07P20700.TF-BestAskSize4", "TX1N07P20700.TF-BestAskSize5", "TX1N07P20700.TF-Name", "TX1N07P20700.TF-WContractDate", "TX1N07P20700.TF-SettlePrice", "TX1N07P20700.TF-UpLimit", "TX1N07P20700.TF-DownLimit", "TX1N07P20700.TF-OI", "TX1N07P20700.TF-TradingDate", "TX1N07P20700.TF-WRemainDate", "TX1N07P20700.TF-PreClose", "TX1N07P20700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20800", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20800.TF-Time", "TX1N07C20800.TF-TradingDate", "TX1N07C20800.TF-Open", "TX1N07C20800.TF-High", "TX1N07C20800.TF-Low", "TX1N07C20800.TF-Price", "TX1N07C20800.TF-TotalVolume", "TX1N07C20800.TF-Volume", "TX1N07C20800.TF-NWTotalBidContract", "TX1N07C20800.TF-NWTotalAskContract", "TX1N07C20800.TF-NWTotalBidSize", "TX1N07C20800.TF-NWTotalAskSize", "TX1N07C20800.TF-InSize", "TX1N07C20800.TF-OutSize", "TX1N07C20800.TF-TotalBidMatchTx", "TX1N07C20800.TF-TotalAskMatchTx", "TX1N07C20800.TF-BestBid1", "TX1N07C20800.TF-BestBid2", "TX1N07C20800.TF-BestBid3", "TX1N07C20800.TF-BestBid4", "TX1N07C20800.TF-BestBid5", "TX1N07C20800.TF-BestAsk1", "TX1N07C20800.TF-BestAsk2", "TX1N07C20800.TF-BestAsk3", "TX1N07C20800.TF-BestAsk4", "TX1N07C20800.TF-BestAsk5", "TX1N07C20800.TF-BestBidSize1", "TX1N07C20800.TF-BestBidSize2", "TX1N07C20800.TF-BestBidSize3", "TX1N07C20800.TF-BestBidSize4", "TX1N07C20800.TF-BestBidSize5", "TX1N07C20800.TF-BestAskSize1", "TX1N07C20800.TF-BestAskSize2", "TX1N07C20800.TF-BestAskSize3", "TX1N07C20800.TF-BestAskSize4", "TX1N07C20800.TF-BestAskSize5", "TX1N07C20800.TF-Name", "TX1N07C20800.TF-WContractDate", "TX1N07C20800.TF-SettlePrice", "TX1N07C20800.TF-UpLimit", "TX1N07C20800.TF-DownLimit", "TX1N07C20800.TF-OI", "TX1N07C20800.TF-TradingDate", "TX1N07C20800.TF-WRemainDate", "TX1N07C20800.TF-PreClose", "TX1N07C20800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20800", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20800.TF-Time", "TX1N07P20800.TF-TradingDate", "TX1N07P20800.TF-Open", "TX1N07P20800.TF-High", "TX1N07P20800.TF-Low", "TX1N07P20800.TF-Price", "TX1N07P20800.TF-TotalVolume", "TX1N07P20800.TF-Volume", "TX1N07P20800.TF-NWTotalBidContract", "TX1N07P20800.TF-NWTotalAskContract", "TX1N07P20800.TF-NWTotalBidSize", "TX1N07P20800.TF-NWTotalAskSize", "TX1N07P20800.TF-InSize", "TX1N07P20800.TF-OutSize", "TX1N07P20800.TF-TotalBidMatchTx", "TX1N07P20800.TF-TotalAskMatchTx", "TX1N07P20800.TF-BestBid1", "TX1N07P20800.TF-BestBid2", "TX1N07P20800.TF-BestBid3", "TX1N07P20800.TF-BestBid4", "TX1N07P20800.TF-BestBid5", "TX1N07P20800.TF-BestAsk1", "TX1N07P20800.TF-BestAsk2", "TX1N07P20800.TF-BestAsk3", "TX1N07P20800.TF-BestAsk4", "TX1N07P20800.TF-BestAsk5", "TX1N07P20800.TF-BestBidSize1", "TX1N07P20800.TF-BestBidSize2", "TX1N07P20800.TF-BestBidSize3", "TX1N07P20800.TF-BestBidSize4", "TX1N07P20800.TF-BestBidSize5", "TX1N07P20800.TF-BestAskSize1", "TX1N07P20800.TF-BestAskSize2", "TX1N07P20800.TF-BestAskSize3", "TX1N07P20800.TF-BestAskSize4", "TX1N07P20800.TF-BestAskSize5", "TX1N07P20800.TF-Name", "TX1N07P20800.TF-WContractDate", "TX1N07P20800.TF-SettlePrice", "TX1N07P20800.TF-UpLimit", "TX1N07P20800.TF-DownLimit", "TX1N07P20800.TF-OI", "TX1N07P20800.TF-TradingDate", "TX1N07P20800.TF-WRemainDate", "TX1N07P20800.TF-PreClose", "TX1N07P20800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C20900", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C20900.TF-Time", "TX1N07C20900.TF-TradingDate", "TX1N07C20900.TF-Open", "TX1N07C20900.TF-High", "TX1N07C20900.TF-Low", "TX1N07C20900.TF-Price", "TX1N07C20900.TF-TotalVolume", "TX1N07C20900.TF-Volume", "TX1N07C20900.TF-NWTotalBidContract", "TX1N07C20900.TF-NWTotalAskContract", "TX1N07C20900.TF-NWTotalBidSize", "TX1N07C20900.TF-NWTotalAskSize", "TX1N07C20900.TF-InSize", "TX1N07C20900.TF-OutSize", "TX1N07C20900.TF-TotalBidMatchTx", "TX1N07C20900.TF-TotalAskMatchTx", "TX1N07C20900.TF-BestBid1", "TX1N07C20900.TF-BestBid2", "TX1N07C20900.TF-BestBid3", "TX1N07C20900.TF-BestBid4", "TX1N07C20900.TF-BestBid5", "TX1N07C20900.TF-BestAsk1", "TX1N07C20900.TF-BestAsk2", "TX1N07C20900.TF-BestAsk3", "TX1N07C20900.TF-BestAsk4", "TX1N07C20900.TF-BestAsk5", "TX1N07C20900.TF-BestBidSize1", "TX1N07C20900.TF-BestBidSize2", "TX1N07C20900.TF-BestBidSize3", "TX1N07C20900.TF-BestBidSize4", "TX1N07C20900.TF-BestBidSize5", "TX1N07C20900.TF-BestAskSize1", "TX1N07C20900.TF-BestAskSize2", "TX1N07C20900.TF-BestAskSize3", "TX1N07C20900.TF-BestAskSize4", "TX1N07C20900.TF-BestAskSize5", "TX1N07C20900.TF-Name", "TX1N07C20900.TF-WContractDate", "TX1N07C20900.TF-SettlePrice", "TX1N07C20900.TF-UpLimit", "TX1N07C20900.TF-DownLimit", "TX1N07C20900.TF-OI", "TX1N07C20900.TF-TradingDate", "TX1N07C20900.TF-WRemainDate", "TX1N07C20900.TF-PreClose", "TX1N07C20900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P20900", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P20900.TF-Time", "TX1N07P20900.TF-TradingDate", "TX1N07P20900.TF-Open", "TX1N07P20900.TF-High", "TX1N07P20900.TF-Low", "TX1N07P20900.TF-Price", "TX1N07P20900.TF-TotalVolume", "TX1N07P20900.TF-Volume", "TX1N07P20900.TF-NWTotalBidContract", "TX1N07P20900.TF-NWTotalAskContract", "TX1N07P20900.TF-NWTotalBidSize", "TX1N07P20900.TF-NWTotalAskSize", "TX1N07P20900.TF-InSize", "TX1N07P20900.TF-OutSize", "TX1N07P20900.TF-TotalBidMatchTx", "TX1N07P20900.TF-TotalAskMatchTx", "TX1N07P20900.TF-BestBid1", "TX1N07P20900.TF-BestBid2", "TX1N07P20900.TF-BestBid3", "TX1N07P20900.TF-BestBid4", "TX1N07P20900.TF-BestBid5", "TX1N07P20900.TF-BestAsk1", "TX1N07P20900.TF-BestAsk2", "TX1N07P20900.TF-BestAsk3", "TX1N07P20900.TF-BestAsk4", "TX1N07P20900.TF-BestAsk5", "TX1N07P20900.TF-BestBidSize1", "TX1N07P20900.TF-BestBidSize2", "TX1N07P20900.TF-BestBidSize3", "TX1N07P20900.TF-BestBidSize4", "TX1N07P20900.TF-BestBidSize5", "TX1N07P20900.TF-BestAskSize1", "TX1N07P20900.TF-BestAskSize2", "TX1N07P20900.TF-BestAskSize3", "TX1N07P20900.TF-BestAskSize4", "TX1N07P20900.TF-BestAskSize5", "TX1N07P20900.TF-Name", "TX1N07P20900.TF-WContractDate", "TX1N07P20900.TF-SettlePrice", "TX1N07P20900.TF-UpLimit", "TX1N07P20900.TF-DownLimit", "TX1N07P20900.TF-OI", "TX1N07P20900.TF-TradingDate", "TX1N07P20900.TF-WRemainDate", "TX1N07P20900.TF-PreClose", "TX1N07P20900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21000", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21000.TF-Time", "TX1N07C21000.TF-TradingDate", "TX1N07C21000.TF-Open", "TX1N07C21000.TF-High", "TX1N07C21000.TF-Low", "TX1N07C21000.TF-Price", "TX1N07C21000.TF-TotalVolume", "TX1N07C21000.TF-Volume", "TX1N07C21000.TF-NWTotalBidContract", "TX1N07C21000.TF-NWTotalAskContract", "TX1N07C21000.TF-NWTotalBidSize", "TX1N07C21000.TF-NWTotalAskSize", "TX1N07C21000.TF-InSize", "TX1N07C21000.TF-OutSize", "TX1N07C21000.TF-TotalBidMatchTx", "TX1N07C21000.TF-TotalAskMatchTx", "TX1N07C21000.TF-BestBid1", "TX1N07C21000.TF-BestBid2", "TX1N07C21000.TF-BestBid3", "TX1N07C21000.TF-BestBid4", "TX1N07C21000.TF-BestBid5", "TX1N07C21000.TF-BestAsk1", "TX1N07C21000.TF-BestAsk2", "TX1N07C21000.TF-BestAsk3", "TX1N07C21000.TF-BestAsk4", "TX1N07C21000.TF-BestAsk5", "TX1N07C21000.TF-BestBidSize1", "TX1N07C21000.TF-BestBidSize2", "TX1N07C21000.TF-BestBidSize3", "TX1N07C21000.TF-BestBidSize4", "TX1N07C21000.TF-BestBidSize5", "TX1N07C21000.TF-BestAskSize1", "TX1N07C21000.TF-BestAskSize2", "TX1N07C21000.TF-BestAskSize3", "TX1N07C21000.TF-BestAskSize4", "TX1N07C21000.TF-BestAskSize5", "TX1N07C21000.TF-Name", "TX1N07C21000.TF-WContractDate", "TX1N07C21000.TF-SettlePrice", "TX1N07C21000.TF-UpLimit", "TX1N07C21000.TF-DownLimit", "TX1N07C21000.TF-OI", "TX1N07C21000.TF-TradingDate", "TX1N07C21000.TF-WRemainDate", "TX1N07C21000.TF-PreClose", "TX1N07C21000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21000", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21000.TF-Time", "TX1N07P21000.TF-TradingDate", "TX1N07P21000.TF-Open", "TX1N07P21000.TF-High", "TX1N07P21000.TF-Low", "TX1N07P21000.TF-Price", "TX1N07P21000.TF-TotalVolume", "TX1N07P21000.TF-Volume", "TX1N07P21000.TF-NWTotalBidContract", "TX1N07P21000.TF-NWTotalAskContract", "TX1N07P21000.TF-NWTotalBidSize", "TX1N07P21000.TF-NWTotalAskSize", "TX1N07P21000.TF-InSize", "TX1N07P21000.TF-OutSize", "TX1N07P21000.TF-TotalBidMatchTx", "TX1N07P21000.TF-TotalAskMatchTx", "TX1N07P21000.TF-BestBid1", "TX1N07P21000.TF-BestBid2", "TX1N07P21000.TF-BestBid3", "TX1N07P21000.TF-BestBid4", "TX1N07P21000.TF-BestBid5", "TX1N07P21000.TF-BestAsk1", "TX1N07P21000.TF-BestAsk2", "TX1N07P21000.TF-BestAsk3", "TX1N07P21000.TF-BestAsk4", "TX1N07P21000.TF-BestAsk5", "TX1N07P21000.TF-BestBidSize1", "TX1N07P21000.TF-BestBidSize2", "TX1N07P21000.TF-BestBidSize3", "TX1N07P21000.TF-BestBidSize4", "TX1N07P21000.TF-BestBidSize5", "TX1N07P21000.TF-BestAskSize1", "TX1N07P21000.TF-BestAskSize2", "TX1N07P21000.TF-BestAskSize3", "TX1N07P21000.TF-BestAskSize4", "TX1N07P21000.TF-BestAskSize5", "TX1N07P21000.TF-Name", "TX1N07P21000.TF-WContractDate", "TX1N07P21000.TF-SettlePrice", "TX1N07P21000.TF-UpLimit", "TX1N07P21000.TF-DownLimit", "TX1N07P21000.TF-OI", "TX1N07P21000.TF-TradingDate", "TX1N07P21000.TF-WRemainDate", "TX1N07P21000.TF-PreClose", "TX1N07P21000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21100", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21100.TF-Time", "TX1N07C21100.TF-TradingDate", "TX1N07C21100.TF-Open", "TX1N07C21100.TF-High", "TX1N07C21100.TF-Low", "TX1N07C21100.TF-Price", "TX1N07C21100.TF-TotalVolume", "TX1N07C21100.TF-Volume", "TX1N07C21100.TF-NWTotalBidContract", "TX1N07C21100.TF-NWTotalAskContract", "TX1N07C21100.TF-NWTotalBidSize", "TX1N07C21100.TF-NWTotalAskSize", "TX1N07C21100.TF-InSize", "TX1N07C21100.TF-OutSize", "TX1N07C21100.TF-TotalBidMatchTx", "TX1N07C21100.TF-TotalAskMatchTx", "TX1N07C21100.TF-BestBid1", "TX1N07C21100.TF-BestBid2", "TX1N07C21100.TF-BestBid3", "TX1N07C21100.TF-BestBid4", "TX1N07C21100.TF-BestBid5", "TX1N07C21100.TF-BestAsk1", "TX1N07C21100.TF-BestAsk2", "TX1N07C21100.TF-BestAsk3", "TX1N07C21100.TF-BestAsk4", "TX1N07C21100.TF-BestAsk5", "TX1N07C21100.TF-BestBidSize1", "TX1N07C21100.TF-BestBidSize2", "TX1N07C21100.TF-BestBidSize3", "TX1N07C21100.TF-BestBidSize4", "TX1N07C21100.TF-BestBidSize5", "TX1N07C21100.TF-BestAskSize1", "TX1N07C21100.TF-BestAskSize2", "TX1N07C21100.TF-BestAskSize3", "TX1N07C21100.TF-BestAskSize4", "TX1N07C21100.TF-BestAskSize5", "TX1N07C21100.TF-Name", "TX1N07C21100.TF-WContractDate", "TX1N07C21100.TF-SettlePrice", "TX1N07C21100.TF-UpLimit", "TX1N07C21100.TF-DownLimit", "TX1N07C21100.TF-OI", "TX1N07C21100.TF-TradingDate", "TX1N07C21100.TF-WRemainDate", "TX1N07C21100.TF-PreClose", "TX1N07C21100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21100", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21100.TF-Time", "TX1N07P21100.TF-TradingDate", "TX1N07P21100.TF-Open", "TX1N07P21100.TF-High", "TX1N07P21100.TF-Low", "TX1N07P21100.TF-Price", "TX1N07P21100.TF-TotalVolume", "TX1N07P21100.TF-Volume", "TX1N07P21100.TF-NWTotalBidContract", "TX1N07P21100.TF-NWTotalAskContract", "TX1N07P21100.TF-NWTotalBidSize", "TX1N07P21100.TF-NWTotalAskSize", "TX1N07P21100.TF-InSize", "TX1N07P21100.TF-OutSize", "TX1N07P21100.TF-TotalBidMatchTx", "TX1N07P21100.TF-TotalAskMatchTx", "TX1N07P21100.TF-BestBid1", "TX1N07P21100.TF-BestBid2", "TX1N07P21100.TF-BestBid3", "TX1N07P21100.TF-BestBid4", "TX1N07P21100.TF-BestBid5", "TX1N07P21100.TF-BestAsk1", "TX1N07P21100.TF-BestAsk2", "TX1N07P21100.TF-BestAsk3", "TX1N07P21100.TF-BestAsk4", "TX1N07P21100.TF-BestAsk5", "TX1N07P21100.TF-BestBidSize1", "TX1N07P21100.TF-BestBidSize2", "TX1N07P21100.TF-BestBidSize3", "TX1N07P21100.TF-BestBidSize4", "TX1N07P21100.TF-BestBidSize5", "TX1N07P21100.TF-BestAskSize1", "TX1N07P21100.TF-BestAskSize2", "TX1N07P21100.TF-BestAskSize3", "TX1N07P21100.TF-BestAskSize4", "TX1N07P21100.TF-BestAskSize5", "TX1N07P21100.TF-Name", "TX1N07P21100.TF-WContractDate", "TX1N07P21100.TF-SettlePrice", "TX1N07P21100.TF-UpLimit", "TX1N07P21100.TF-DownLimit", "TX1N07P21100.TF-OI", "TX1N07P21100.TF-TradingDate", "TX1N07P21100.TF-WRemainDate", "TX1N07P21100.TF-PreClose", "TX1N07P21100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21150", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21150.TF-Time", "TX1N07C21150.TF-TradingDate", "TX1N07C21150.TF-Open", "TX1N07C21150.TF-High", "TX1N07C21150.TF-Low", "TX1N07C21150.TF-Price", "TX1N07C21150.TF-TotalVolume", "TX1N07C21150.TF-Volume", "TX1N07C21150.TF-NWTotalBidContract", "TX1N07C21150.TF-NWTotalAskContract", "TX1N07C21150.TF-NWTotalBidSize", "TX1N07C21150.TF-NWTotalAskSize", "TX1N07C21150.TF-InSize", "TX1N07C21150.TF-OutSize", "TX1N07C21150.TF-TotalBidMatchTx", "TX1N07C21150.TF-TotalAskMatchTx", "TX1N07C21150.TF-BestBid1", "TX1N07C21150.TF-BestBid2", "TX1N07C21150.TF-BestBid3", "TX1N07C21150.TF-BestBid4", "TX1N07C21150.TF-BestBid5", "TX1N07C21150.TF-BestAsk1", "TX1N07C21150.TF-BestAsk2", "TX1N07C21150.TF-BestAsk3", "TX1N07C21150.TF-BestAsk4", "TX1N07C21150.TF-BestAsk5", "TX1N07C21150.TF-BestBidSize1", "TX1N07C21150.TF-BestBidSize2", "TX1N07C21150.TF-BestBidSize3", "TX1N07C21150.TF-BestBidSize4", "TX1N07C21150.TF-BestBidSize5", "TX1N07C21150.TF-BestAskSize1", "TX1N07C21150.TF-BestAskSize2", "TX1N07C21150.TF-BestAskSize3", "TX1N07C21150.TF-BestAskSize4", "TX1N07C21150.TF-BestAskSize5", "TX1N07C21150.TF-Name", "TX1N07C21150.TF-WContractDate", "TX1N07C21150.TF-SettlePrice", "TX1N07C21150.TF-UpLimit", "TX1N07C21150.TF-DownLimit", "TX1N07C21150.TF-OI", "TX1N07C21150.TF-TradingDate", "TX1N07C21150.TF-WRemainDate", "TX1N07C21150.TF-PreClose", "TX1N07C21150.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21150", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21150.TF-Time", "TX1N07P21150.TF-TradingDate", "TX1N07P21150.TF-Open", "TX1N07P21150.TF-High", "TX1N07P21150.TF-Low", "TX1N07P21150.TF-Price", "TX1N07P21150.TF-TotalVolume", "TX1N07P21150.TF-Volume", "TX1N07P21150.TF-NWTotalBidContract", "TX1N07P21150.TF-NWTotalAskContract", "TX1N07P21150.TF-NWTotalBidSize", "TX1N07P21150.TF-NWTotalAskSize", "TX1N07P21150.TF-InSize", "TX1N07P21150.TF-OutSize", "TX1N07P21150.TF-TotalBidMatchTx", "TX1N07P21150.TF-TotalAskMatchTx", "TX1N07P21150.TF-BestBid1", "TX1N07P21150.TF-BestBid2", "TX1N07P21150.TF-BestBid3", "TX1N07P21150.TF-BestBid4", "TX1N07P21150.TF-BestBid5", "TX1N07P21150.TF-BestAsk1", "TX1N07P21150.TF-BestAsk2", "TX1N07P21150.TF-BestAsk3", "TX1N07P21150.TF-BestAsk4", "TX1N07P21150.TF-BestAsk5", "TX1N07P21150.TF-BestBidSize1", "TX1N07P21150.TF-BestBidSize2", "TX1N07P21150.TF-BestBidSize3", "TX1N07P21150.TF-BestBidSize4", "TX1N07P21150.TF-BestBidSize5", "TX1N07P21150.TF-BestAskSize1", "TX1N07P21150.TF-BestAskSize2", "TX1N07P21150.TF-BestAskSize3", "TX1N07P21150.TF-BestAskSize4", "TX1N07P21150.TF-BestAskSize5", "TX1N07P21150.TF-Name", "TX1N07P21150.TF-WContractDate", "TX1N07P21150.TF-SettlePrice", "TX1N07P21150.TF-UpLimit", "TX1N07P21150.TF-DownLimit", "TX1N07P21150.TF-OI", "TX1N07P21150.TF-TradingDate", "TX1N07P21150.TF-WRemainDate", "TX1N07P21150.TF-PreClose", "TX1N07P21150.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21200", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21200.TF-Time", "TX1N07C21200.TF-TradingDate", "TX1N07C21200.TF-Open", "TX1N07C21200.TF-High", "TX1N07C21200.TF-Low", "TX1N07C21200.TF-Price", "TX1N07C21200.TF-TotalVolume", "TX1N07C21200.TF-Volume", "TX1N07C21200.TF-NWTotalBidContract", "TX1N07C21200.TF-NWTotalAskContract", "TX1N07C21200.TF-NWTotalBidSize", "TX1N07C21200.TF-NWTotalAskSize", "TX1N07C21200.TF-InSize", "TX1N07C21200.TF-OutSize", "TX1N07C21200.TF-TotalBidMatchTx", "TX1N07C21200.TF-TotalAskMatchTx", "TX1N07C21200.TF-BestBid1", "TX1N07C21200.TF-BestBid2", "TX1N07C21200.TF-BestBid3", "TX1N07C21200.TF-BestBid4", "TX1N07C21200.TF-BestBid5", "TX1N07C21200.TF-BestAsk1", "TX1N07C21200.TF-BestAsk2", "TX1N07C21200.TF-BestAsk3", "TX1N07C21200.TF-BestAsk4", "TX1N07C21200.TF-BestAsk5", "TX1N07C21200.TF-BestBidSize1", "TX1N07C21200.TF-BestBidSize2", "TX1N07C21200.TF-BestBidSize3", "TX1N07C21200.TF-BestBidSize4", "TX1N07C21200.TF-BestBidSize5", "TX1N07C21200.TF-BestAskSize1", "TX1N07C21200.TF-BestAskSize2", "TX1N07C21200.TF-BestAskSize3", "TX1N07C21200.TF-BestAskSize4", "TX1N07C21200.TF-BestAskSize5", "TX1N07C21200.TF-Name", "TX1N07C21200.TF-WContractDate", "TX1N07C21200.TF-SettlePrice", "TX1N07C21200.TF-UpLimit", "TX1N07C21200.TF-DownLimit", "TX1N07C21200.TF-OI", "TX1N07C21200.TF-TradingDate", "TX1N07C21200.TF-WRemainDate", "TX1N07C21200.TF-PreClose", "TX1N07C21200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21200", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21200.TF-Time", "TX1N07P21200.TF-TradingDate", "TX1N07P21200.TF-Open", "TX1N07P21200.TF-High", "TX1N07P21200.TF-Low", "TX1N07P21200.TF-Price", "TX1N07P21200.TF-TotalVolume", "TX1N07P21200.TF-Volume", "TX1N07P21200.TF-NWTotalBidContract", "TX1N07P21200.TF-NWTotalAskContract", "TX1N07P21200.TF-NWTotalBidSize", "TX1N07P21200.TF-NWTotalAskSize", "TX1N07P21200.TF-InSize", "TX1N07P21200.TF-OutSize", "TX1N07P21200.TF-TotalBidMatchTx", "TX1N07P21200.TF-TotalAskMatchTx", "TX1N07P21200.TF-BestBid1", "TX1N07P21200.TF-BestBid2", "TX1N07P21200.TF-BestBid3", "TX1N07P21200.TF-BestBid4", "TX1N07P21200.TF-BestBid5", "TX1N07P21200.TF-BestAsk1", "TX1N07P21200.TF-BestAsk2", "TX1N07P21200.TF-BestAsk3", "TX1N07P21200.TF-BestAsk4", "TX1N07P21200.TF-BestAsk5", "TX1N07P21200.TF-BestBidSize1", "TX1N07P21200.TF-BestBidSize2", "TX1N07P21200.TF-BestBidSize3", "TX1N07P21200.TF-BestBidSize4", "TX1N07P21200.TF-BestBidSize5", "TX1N07P21200.TF-BestAskSize1", "TX1N07P21200.TF-BestAskSize2", "TX1N07P21200.TF-BestAskSize3", "TX1N07P21200.TF-BestAskSize4", "TX1N07P21200.TF-BestAskSize5", "TX1N07P21200.TF-Name", "TX1N07P21200.TF-WContractDate", "TX1N07P21200.TF-SettlePrice", "TX1N07P21200.TF-UpLimit", "TX1N07P21200.TF-DownLimit", "TX1N07P21200.TF-OI", "TX1N07P21200.TF-TradingDate", "TX1N07P21200.TF-WRemainDate", "TX1N07P21200.TF-PreClose", "TX1N07P21200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21250", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21250.TF-Time", "TX1N07C21250.TF-TradingDate", "TX1N07C21250.TF-Open", "TX1N07C21250.TF-High", "TX1N07C21250.TF-Low", "TX1N07C21250.TF-Price", "TX1N07C21250.TF-TotalVolume", "TX1N07C21250.TF-Volume", "TX1N07C21250.TF-NWTotalBidContract", "TX1N07C21250.TF-NWTotalAskContract", "TX1N07C21250.TF-NWTotalBidSize", "TX1N07C21250.TF-NWTotalAskSize", "TX1N07C21250.TF-InSize", "TX1N07C21250.TF-OutSize", "TX1N07C21250.TF-TotalBidMatchTx", "TX1N07C21250.TF-TotalAskMatchTx", "TX1N07C21250.TF-BestBid1", "TX1N07C21250.TF-BestBid2", "TX1N07C21250.TF-BestBid3", "TX1N07C21250.TF-BestBid4", "TX1N07C21250.TF-BestBid5", "TX1N07C21250.TF-BestAsk1", "TX1N07C21250.TF-BestAsk2", "TX1N07C21250.TF-BestAsk3", "TX1N07C21250.TF-BestAsk4", "TX1N07C21250.TF-BestAsk5", "TX1N07C21250.TF-BestBidSize1", "TX1N07C21250.TF-BestBidSize2", "TX1N07C21250.TF-BestBidSize3", "TX1N07C21250.TF-BestBidSize4", "TX1N07C21250.TF-BestBidSize5", "TX1N07C21250.TF-BestAskSize1", "TX1N07C21250.TF-BestAskSize2", "TX1N07C21250.TF-BestAskSize3", "TX1N07C21250.TF-BestAskSize4", "TX1N07C21250.TF-BestAskSize5", "TX1N07C21250.TF-Name", "TX1N07C21250.TF-WContractDate", "TX1N07C21250.TF-SettlePrice", "TX1N07C21250.TF-UpLimit", "TX1N07C21250.TF-DownLimit", "TX1N07C21250.TF-OI", "TX1N07C21250.TF-TradingDate", "TX1N07C21250.TF-WRemainDate", "TX1N07C21250.TF-PreClose", "TX1N07C21250.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21250", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21250.TF-Time", "TX1N07P21250.TF-TradingDate", "TX1N07P21250.TF-Open", "TX1N07P21250.TF-High", "TX1N07P21250.TF-Low", "TX1N07P21250.TF-Price", "TX1N07P21250.TF-TotalVolume", "TX1N07P21250.TF-Volume", "TX1N07P21250.TF-NWTotalBidContract", "TX1N07P21250.TF-NWTotalAskContract", "TX1N07P21250.TF-NWTotalBidSize", "TX1N07P21250.TF-NWTotalAskSize", "TX1N07P21250.TF-InSize", "TX1N07P21250.TF-OutSize", "TX1N07P21250.TF-TotalBidMatchTx", "TX1N07P21250.TF-TotalAskMatchTx", "TX1N07P21250.TF-BestBid1", "TX1N07P21250.TF-BestBid2", "TX1N07P21250.TF-BestBid3", "TX1N07P21250.TF-BestBid4", "TX1N07P21250.TF-BestBid5", "TX1N07P21250.TF-BestAsk1", "TX1N07P21250.TF-BestAsk2", "TX1N07P21250.TF-BestAsk3", "TX1N07P21250.TF-BestAsk4", "TX1N07P21250.TF-BestAsk5", "TX1N07P21250.TF-BestBidSize1", "TX1N07P21250.TF-BestBidSize2", "TX1N07P21250.TF-BestBidSize3", "TX1N07P21250.TF-BestBidSize4", "TX1N07P21250.TF-BestBidSize5", "TX1N07P21250.TF-BestAskSize1", "TX1N07P21250.TF-BestAskSize2", "TX1N07P21250.TF-BestAskSize3", "TX1N07P21250.TF-BestAskSize4", "TX1N07P21250.TF-BestAskSize5", "TX1N07P21250.TF-Name", "TX1N07P21250.TF-WContractDate", "TX1N07P21250.TF-SettlePrice", "TX1N07P21250.TF-UpLimit", "TX1N07P21250.TF-DownLimit", "TX1N07P21250.TF-OI", "TX1N07P21250.TF-TradingDate", "TX1N07P21250.TF-WRemainDate", "TX1N07P21250.TF-PreClose", "TX1N07P21250.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21300", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21300.TF-Time", "TX1N07C21300.TF-TradingDate", "TX1N07C21300.TF-Open", "TX1N07C21300.TF-High", "TX1N07C21300.TF-Low", "TX1N07C21300.TF-Price", "TX1N07C21300.TF-TotalVolume", "TX1N07C21300.TF-Volume", "TX1N07C21300.TF-NWTotalBidContract", "TX1N07C21300.TF-NWTotalAskContract", "TX1N07C21300.TF-NWTotalBidSize", "TX1N07C21300.TF-NWTotalAskSize", "TX1N07C21300.TF-InSize", "TX1N07C21300.TF-OutSize", "TX1N07C21300.TF-TotalBidMatchTx", "TX1N07C21300.TF-TotalAskMatchTx", "TX1N07C21300.TF-BestBid1", "TX1N07C21300.TF-BestBid2", "TX1N07C21300.TF-BestBid3", "TX1N07C21300.TF-BestBid4", "TX1N07C21300.TF-BestBid5", "TX1N07C21300.TF-BestAsk1", "TX1N07C21300.TF-BestAsk2", "TX1N07C21300.TF-BestAsk3", "TX1N07C21300.TF-BestAsk4", "TX1N07C21300.TF-BestAsk5", "TX1N07C21300.TF-BestBidSize1", "TX1N07C21300.TF-BestBidSize2", "TX1N07C21300.TF-BestBidSize3", "TX1N07C21300.TF-BestBidSize4", "TX1N07C21300.TF-BestBidSize5", "TX1N07C21300.TF-BestAskSize1", "TX1N07C21300.TF-BestAskSize2", "TX1N07C21300.TF-BestAskSize3", "TX1N07C21300.TF-BestAskSize4", "TX1N07C21300.TF-BestAskSize5", "TX1N07C21300.TF-Name", "TX1N07C21300.TF-WContractDate", "TX1N07C21300.TF-SettlePrice", "TX1N07C21300.TF-UpLimit", "TX1N07C21300.TF-DownLimit", "TX1N07C21300.TF-OI", "TX1N07C21300.TF-TradingDate", "TX1N07C21300.TF-WRemainDate", "TX1N07C21300.TF-PreClose", "TX1N07C21300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21300", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21300.TF-Time", "TX1N07P21300.TF-TradingDate", "TX1N07P21300.TF-Open", "TX1N07P21300.TF-High", "TX1N07P21300.TF-Low", "TX1N07P21300.TF-Price", "TX1N07P21300.TF-TotalVolume", "TX1N07P21300.TF-Volume", "TX1N07P21300.TF-NWTotalBidContract", "TX1N07P21300.TF-NWTotalAskContract", "TX1N07P21300.TF-NWTotalBidSize", "TX1N07P21300.TF-NWTotalAskSize", "TX1N07P21300.TF-InSize", "TX1N07P21300.TF-OutSize", "TX1N07P21300.TF-TotalBidMatchTx", "TX1N07P21300.TF-TotalAskMatchTx", "TX1N07P21300.TF-BestBid1", "TX1N07P21300.TF-BestBid2", "TX1N07P21300.TF-BestBid3", "TX1N07P21300.TF-BestBid4", "TX1N07P21300.TF-BestBid5", "TX1N07P21300.TF-BestAsk1", "TX1N07P21300.TF-BestAsk2", "TX1N07P21300.TF-BestAsk3", "TX1N07P21300.TF-BestAsk4", "TX1N07P21300.TF-BestAsk5", "TX1N07P21300.TF-BestBidSize1", "TX1N07P21300.TF-BestBidSize2", "TX1N07P21300.TF-BestBidSize3", "TX1N07P21300.TF-BestBidSize4", "TX1N07P21300.TF-BestBidSize5", "TX1N07P21300.TF-BestAskSize1", "TX1N07P21300.TF-BestAskSize2", "TX1N07P21300.TF-BestAskSize3", "TX1N07P21300.TF-BestAskSize4", "TX1N07P21300.TF-BestAskSize5", "TX1N07P21300.TF-Name", "TX1N07P21300.TF-WContractDate", "TX1N07P21300.TF-SettlePrice", "TX1N07P21300.TF-UpLimit", "TX1N07P21300.TF-DownLimit", "TX1N07P21300.TF-OI", "TX1N07P21300.TF-TradingDate", "TX1N07P21300.TF-WRemainDate", "TX1N07P21300.TF-PreClose", "TX1N07P21300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21350", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21350.TF-Time", "TX1N07C21350.TF-TradingDate", "TX1N07C21350.TF-Open", "TX1N07C21350.TF-High", "TX1N07C21350.TF-Low", "TX1N07C21350.TF-Price", "TX1N07C21350.TF-TotalVolume", "TX1N07C21350.TF-Volume", "TX1N07C21350.TF-NWTotalBidContract", "TX1N07C21350.TF-NWTotalAskContract", "TX1N07C21350.TF-NWTotalBidSize", "TX1N07C21350.TF-NWTotalAskSize", "TX1N07C21350.TF-InSize", "TX1N07C21350.TF-OutSize", "TX1N07C21350.TF-TotalBidMatchTx", "TX1N07C21350.TF-TotalAskMatchTx", "TX1N07C21350.TF-BestBid1", "TX1N07C21350.TF-BestBid2", "TX1N07C21350.TF-BestBid3", "TX1N07C21350.TF-BestBid4", "TX1N07C21350.TF-BestBid5", "TX1N07C21350.TF-BestAsk1", "TX1N07C21350.TF-BestAsk2", "TX1N07C21350.TF-BestAsk3", "TX1N07C21350.TF-BestAsk4", "TX1N07C21350.TF-BestAsk5", "TX1N07C21350.TF-BestBidSize1", "TX1N07C21350.TF-BestBidSize2", "TX1N07C21350.TF-BestBidSize3", "TX1N07C21350.TF-BestBidSize4", "TX1N07C21350.TF-BestBidSize5", "TX1N07C21350.TF-BestAskSize1", "TX1N07C21350.TF-BestAskSize2", "TX1N07C21350.TF-BestAskSize3", "TX1N07C21350.TF-BestAskSize4", "TX1N07C21350.TF-BestAskSize5", "TX1N07C21350.TF-Name", "TX1N07C21350.TF-WContractDate", "TX1N07C21350.TF-SettlePrice", "TX1N07C21350.TF-UpLimit", "TX1N07C21350.TF-DownLimit", "TX1N07C21350.TF-OI", "TX1N07C21350.TF-TradingDate", "TX1N07C21350.TF-WRemainDate", "TX1N07C21350.TF-PreClose", "TX1N07C21350.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21350", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21350.TF-Time", "TX1N07P21350.TF-TradingDate", "TX1N07P21350.TF-Open", "TX1N07P21350.TF-High", "TX1N07P21350.TF-Low", "TX1N07P21350.TF-Price", "TX1N07P21350.TF-TotalVolume", "TX1N07P21350.TF-Volume", "TX1N07P21350.TF-NWTotalBidContract", "TX1N07P21350.TF-NWTotalAskContract", "TX1N07P21350.TF-NWTotalBidSize", "TX1N07P21350.TF-NWTotalAskSize", "TX1N07P21350.TF-InSize", "TX1N07P21350.TF-OutSize", "TX1N07P21350.TF-TotalBidMatchTx", "TX1N07P21350.TF-TotalAskMatchTx", "TX1N07P21350.TF-BestBid1", "TX1N07P21350.TF-BestBid2", "TX1N07P21350.TF-BestBid3", "TX1N07P21350.TF-BestBid4", "TX1N07P21350.TF-BestBid5", "TX1N07P21350.TF-BestAsk1", "TX1N07P21350.TF-BestAsk2", "TX1N07P21350.TF-BestAsk3", "TX1N07P21350.TF-BestAsk4", "TX1N07P21350.TF-BestAsk5", "TX1N07P21350.TF-BestBidSize1", "TX1N07P21350.TF-BestBidSize2", "TX1N07P21350.TF-BestBidSize3", "TX1N07P21350.TF-BestBidSize4", "TX1N07P21350.TF-BestBidSize5", "TX1N07P21350.TF-BestAskSize1", "TX1N07P21350.TF-BestAskSize2", "TX1N07P21350.TF-BestAskSize3", "TX1N07P21350.TF-BestAskSize4", "TX1N07P21350.TF-BestAskSize5", "TX1N07P21350.TF-Name", "TX1N07P21350.TF-WContractDate", "TX1N07P21350.TF-SettlePrice", "TX1N07P21350.TF-UpLimit", "TX1N07P21350.TF-DownLimit", "TX1N07P21350.TF-OI", "TX1N07P21350.TF-TradingDate", "TX1N07P21350.TF-WRemainDate", "TX1N07P21350.TF-PreClose", "TX1N07P21350.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21400", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21400.TF-Time", "TX1N07C21400.TF-TradingDate", "TX1N07C21400.TF-Open", "TX1N07C21400.TF-High", "TX1N07C21400.TF-Low", "TX1N07C21400.TF-Price", "TX1N07C21400.TF-TotalVolume", "TX1N07C21400.TF-Volume", "TX1N07C21400.TF-NWTotalBidContract", "TX1N07C21400.TF-NWTotalAskContract", "TX1N07C21400.TF-NWTotalBidSize", "TX1N07C21400.TF-NWTotalAskSize", "TX1N07C21400.TF-InSize", "TX1N07C21400.TF-OutSize", "TX1N07C21400.TF-TotalBidMatchTx", "TX1N07C21400.TF-TotalAskMatchTx", "TX1N07C21400.TF-BestBid1", "TX1N07C21400.TF-BestBid2", "TX1N07C21400.TF-BestBid3", "TX1N07C21400.TF-BestBid4", "TX1N07C21400.TF-BestBid5", "TX1N07C21400.TF-BestAsk1", "TX1N07C21400.TF-BestAsk2", "TX1N07C21400.TF-BestAsk3", "TX1N07C21400.TF-BestAsk4", "TX1N07C21400.TF-BestAsk5", "TX1N07C21400.TF-BestBidSize1", "TX1N07C21400.TF-BestBidSize2", "TX1N07C21400.TF-BestBidSize3", "TX1N07C21400.TF-BestBidSize4", "TX1N07C21400.TF-BestBidSize5", "TX1N07C21400.TF-BestAskSize1", "TX1N07C21400.TF-BestAskSize2", "TX1N07C21400.TF-BestAskSize3", "TX1N07C21400.TF-BestAskSize4", "TX1N07C21400.TF-BestAskSize5", "TX1N07C21400.TF-Name", "TX1N07C21400.TF-WContractDate", "TX1N07C21400.TF-SettlePrice", "TX1N07C21400.TF-UpLimit", "TX1N07C21400.TF-DownLimit", "TX1N07C21400.TF-OI", "TX1N07C21400.TF-TradingDate", "TX1N07C21400.TF-WRemainDate", "TX1N07C21400.TF-PreClose", "TX1N07C21400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21400", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21400.TF-Time", "TX1N07P21400.TF-TradingDate", "TX1N07P21400.TF-Open", "TX1N07P21400.TF-High", "TX1N07P21400.TF-Low", "TX1N07P21400.TF-Price", "TX1N07P21400.TF-TotalVolume", "TX1N07P21400.TF-Volume", "TX1N07P21400.TF-NWTotalBidContract", "TX1N07P21400.TF-NWTotalAskContract", "TX1N07P21400.TF-NWTotalBidSize", "TX1N07P21400.TF-NWTotalAskSize", "TX1N07P21400.TF-InSize", "TX1N07P21400.TF-OutSize", "TX1N07P21400.TF-TotalBidMatchTx", "TX1N07P21400.TF-TotalAskMatchTx", "TX1N07P21400.TF-BestBid1", "TX1N07P21400.TF-BestBid2", "TX1N07P21400.TF-BestBid3", "TX1N07P21400.TF-BestBid4", "TX1N07P21400.TF-BestBid5", "TX1N07P21400.TF-BestAsk1", "TX1N07P21400.TF-BestAsk2", "TX1N07P21400.TF-BestAsk3", "TX1N07P21400.TF-BestAsk4", "TX1N07P21400.TF-BestAsk5", "TX1N07P21400.TF-BestBidSize1", "TX1N07P21400.TF-BestBidSize2", "TX1N07P21400.TF-BestBidSize3", "TX1N07P21400.TF-BestBidSize4", "TX1N07P21400.TF-BestBidSize5", "TX1N07P21400.TF-BestAskSize1", "TX1N07P21400.TF-BestAskSize2", "TX1N07P21400.TF-BestAskSize3", "TX1N07P21400.TF-BestAskSize4", "TX1N07P21400.TF-BestAskSize5", "TX1N07P21400.TF-Name", "TX1N07P21400.TF-WContractDate", "TX1N07P21400.TF-SettlePrice", "TX1N07P21400.TF-UpLimit", "TX1N07P21400.TF-DownLimit", "TX1N07P21400.TF-OI", "TX1N07P21400.TF-TradingDate", "TX1N07P21400.TF-WRemainDate", "TX1N07P21400.TF-PreClose", "TX1N07P21400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21450", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21450.TF-Time", "TX1N07C21450.TF-TradingDate", "TX1N07C21450.TF-Open", "TX1N07C21450.TF-High", "TX1N07C21450.TF-Low", "TX1N07C21450.TF-Price", "TX1N07C21450.TF-TotalVolume", "TX1N07C21450.TF-Volume", "TX1N07C21450.TF-NWTotalBidContract", "TX1N07C21450.TF-NWTotalAskContract", "TX1N07C21450.TF-NWTotalBidSize", "TX1N07C21450.TF-NWTotalAskSize", "TX1N07C21450.TF-InSize", "TX1N07C21450.TF-OutSize", "TX1N07C21450.TF-TotalBidMatchTx", "TX1N07C21450.TF-TotalAskMatchTx", "TX1N07C21450.TF-BestBid1", "TX1N07C21450.TF-BestBid2", "TX1N07C21450.TF-BestBid3", "TX1N07C21450.TF-BestBid4", "TX1N07C21450.TF-BestBid5", "TX1N07C21450.TF-BestAsk1", "TX1N07C21450.TF-BestAsk2", "TX1N07C21450.TF-BestAsk3", "TX1N07C21450.TF-BestAsk4", "TX1N07C21450.TF-BestAsk5", "TX1N07C21450.TF-BestBidSize1", "TX1N07C21450.TF-BestBidSize2", "TX1N07C21450.TF-BestBidSize3", "TX1N07C21450.TF-BestBidSize4", "TX1N07C21450.TF-BestBidSize5", "TX1N07C21450.TF-BestAskSize1", "TX1N07C21450.TF-BestAskSize2", "TX1N07C21450.TF-BestAskSize3", "TX1N07C21450.TF-BestAskSize4", "TX1N07C21450.TF-BestAskSize5", "TX1N07C21450.TF-Name", "TX1N07C21450.TF-WContractDate", "TX1N07C21450.TF-SettlePrice", "TX1N07C21450.TF-UpLimit", "TX1N07C21450.TF-DownLimit", "TX1N07C21450.TF-OI", "TX1N07C21450.TF-TradingDate", "TX1N07C21450.TF-WRemainDate", "TX1N07C21450.TF-PreClose", "TX1N07C21450.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21450", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21450.TF-Time", "TX1N07P21450.TF-TradingDate", "TX1N07P21450.TF-Open", "TX1N07P21450.TF-High", "TX1N07P21450.TF-Low", "TX1N07P21450.TF-Price", "TX1N07P21450.TF-TotalVolume", "TX1N07P21450.TF-Volume", "TX1N07P21450.TF-NWTotalBidContract", "TX1N07P21450.TF-NWTotalAskContract", "TX1N07P21450.TF-NWTotalBidSize", "TX1N07P21450.TF-NWTotalAskSize", "TX1N07P21450.TF-InSize", "TX1N07P21450.TF-OutSize", "TX1N07P21450.TF-TotalBidMatchTx", "TX1N07P21450.TF-TotalAskMatchTx", "TX1N07P21450.TF-BestBid1", "TX1N07P21450.TF-BestBid2", "TX1N07P21450.TF-BestBid3", "TX1N07P21450.TF-BestBid4", "TX1N07P21450.TF-BestBid5", "TX1N07P21450.TF-BestAsk1", "TX1N07P21450.TF-BestAsk2", "TX1N07P21450.TF-BestAsk3", "TX1N07P21450.TF-BestAsk4", "TX1N07P21450.TF-BestAsk5", "TX1N07P21450.TF-BestBidSize1", "TX1N07P21450.TF-BestBidSize2", "TX1N07P21450.TF-BestBidSize3", "TX1N07P21450.TF-BestBidSize4", "TX1N07P21450.TF-BestBidSize5", "TX1N07P21450.TF-BestAskSize1", "TX1N07P21450.TF-BestAskSize2", "TX1N07P21450.TF-BestAskSize3", "TX1N07P21450.TF-BestAskSize4", "TX1N07P21450.TF-BestAskSize5", "TX1N07P21450.TF-Name", "TX1N07P21450.TF-WContractDate", "TX1N07P21450.TF-SettlePrice", "TX1N07P21450.TF-UpLimit", "TX1N07P21450.TF-DownLimit", "TX1N07P21450.TF-OI", "TX1N07P21450.TF-TradingDate", "TX1N07P21450.TF-WRemainDate", "TX1N07P21450.TF-PreClose", "TX1N07P21450.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21500", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21500.TF-Time", "TX1N07C21500.TF-TradingDate", "TX1N07C21500.TF-Open", "TX1N07C21500.TF-High", "TX1N07C21500.TF-Low", "TX1N07C21500.TF-Price", "TX1N07C21500.TF-TotalVolume", "TX1N07C21500.TF-Volume", "TX1N07C21500.TF-NWTotalBidContract", "TX1N07C21500.TF-NWTotalAskContract", "TX1N07C21500.TF-NWTotalBidSize", "TX1N07C21500.TF-NWTotalAskSize", "TX1N07C21500.TF-InSize", "TX1N07C21500.TF-OutSize", "TX1N07C21500.TF-TotalBidMatchTx", "TX1N07C21500.TF-TotalAskMatchTx", "TX1N07C21500.TF-BestBid1", "TX1N07C21500.TF-BestBid2", "TX1N07C21500.TF-BestBid3", "TX1N07C21500.TF-BestBid4", "TX1N07C21500.TF-BestBid5", "TX1N07C21500.TF-BestAsk1", "TX1N07C21500.TF-BestAsk2", "TX1N07C21500.TF-BestAsk3", "TX1N07C21500.TF-BestAsk4", "TX1N07C21500.TF-BestAsk5", "TX1N07C21500.TF-BestBidSize1", "TX1N07C21500.TF-BestBidSize2", "TX1N07C21500.TF-BestBidSize3", "TX1N07C21500.TF-BestBidSize4", "TX1N07C21500.TF-BestBidSize5", "TX1N07C21500.TF-BestAskSize1", "TX1N07C21500.TF-BestAskSize2", "TX1N07C21500.TF-BestAskSize3", "TX1N07C21500.TF-BestAskSize4", "TX1N07C21500.TF-BestAskSize5", "TX1N07C21500.TF-Name", "TX1N07C21500.TF-WContractDate", "TX1N07C21500.TF-SettlePrice", "TX1N07C21500.TF-UpLimit", "TX1N07C21500.TF-DownLimit", "TX1N07C21500.TF-OI", "TX1N07C21500.TF-TradingDate", "TX1N07C21500.TF-WRemainDate", "TX1N07C21500.TF-PreClose", "TX1N07C21500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21500", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21500.TF-Time", "TX1N07P21500.TF-TradingDate", "TX1N07P21500.TF-Open", "TX1N07P21500.TF-High", "TX1N07P21500.TF-Low", "TX1N07P21500.TF-Price", "TX1N07P21500.TF-TotalVolume", "TX1N07P21500.TF-Volume", "TX1N07P21500.TF-NWTotalBidContract", "TX1N07P21500.TF-NWTotalAskContract", "TX1N07P21500.TF-NWTotalBidSize", "TX1N07P21500.TF-NWTotalAskSize", "TX1N07P21500.TF-InSize", "TX1N07P21500.TF-OutSize", "TX1N07P21500.TF-TotalBidMatchTx", "TX1N07P21500.TF-TotalAskMatchTx", "TX1N07P21500.TF-BestBid1", "TX1N07P21500.TF-BestBid2", "TX1N07P21500.TF-BestBid3", "TX1N07P21500.TF-BestBid4", "TX1N07P21500.TF-BestBid5", "TX1N07P21500.TF-BestAsk1", "TX1N07P21500.TF-BestAsk2", "TX1N07P21500.TF-BestAsk3", "TX1N07P21500.TF-BestAsk4", "TX1N07P21500.TF-BestAsk5", "TX1N07P21500.TF-BestBidSize1", "TX1N07P21500.TF-BestBidSize2", "TX1N07P21500.TF-BestBidSize3", "TX1N07P21500.TF-BestBidSize4", "TX1N07P21500.TF-BestBidSize5", "TX1N07P21500.TF-BestAskSize1", "TX1N07P21500.TF-BestAskSize2", "TX1N07P21500.TF-BestAskSize3", "TX1N07P21500.TF-BestAskSize4", "TX1N07P21500.TF-BestAskSize5", "TX1N07P21500.TF-Name", "TX1N07P21500.TF-WContractDate", "TX1N07P21500.TF-SettlePrice", "TX1N07P21500.TF-UpLimit", "TX1N07P21500.TF-DownLimit", "TX1N07P21500.TF-OI", "TX1N07P21500.TF-TradingDate", "TX1N07P21500.TF-WRemainDate", "TX1N07P21500.TF-PreClose", "TX1N07P21500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21550", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21550.TF-Time", "TX1N07C21550.TF-TradingDate", "TX1N07C21550.TF-Open", "TX1N07C21550.TF-High", "TX1N07C21550.TF-Low", "TX1N07C21550.TF-Price", "TX1N07C21550.TF-TotalVolume", "TX1N07C21550.TF-Volume", "TX1N07C21550.TF-NWTotalBidContract", "TX1N07C21550.TF-NWTotalAskContract", "TX1N07C21550.TF-NWTotalBidSize", "TX1N07C21550.TF-NWTotalAskSize", "TX1N07C21550.TF-InSize", "TX1N07C21550.TF-OutSize", "TX1N07C21550.TF-TotalBidMatchTx", "TX1N07C21550.TF-TotalAskMatchTx", "TX1N07C21550.TF-BestBid1", "TX1N07C21550.TF-BestBid2", "TX1N07C21550.TF-BestBid3", "TX1N07C21550.TF-BestBid4", "TX1N07C21550.TF-BestBid5", "TX1N07C21550.TF-BestAsk1", "TX1N07C21550.TF-BestAsk2", "TX1N07C21550.TF-BestAsk3", "TX1N07C21550.TF-BestAsk4", "TX1N07C21550.TF-BestAsk5", "TX1N07C21550.TF-BestBidSize1", "TX1N07C21550.TF-BestBidSize2", "TX1N07C21550.TF-BestBidSize3", "TX1N07C21550.TF-BestBidSize4", "TX1N07C21550.TF-BestBidSize5", "TX1N07C21550.TF-BestAskSize1", "TX1N07C21550.TF-BestAskSize2", "TX1N07C21550.TF-BestAskSize3", "TX1N07C21550.TF-BestAskSize4", "TX1N07C21550.TF-BestAskSize5", "TX1N07C21550.TF-Name", "TX1N07C21550.TF-WContractDate", "TX1N07C21550.TF-SettlePrice", "TX1N07C21550.TF-UpLimit", "TX1N07C21550.TF-DownLimit", "TX1N07C21550.TF-OI", "TX1N07C21550.TF-TradingDate", "TX1N07C21550.TF-WRemainDate", "TX1N07C21550.TF-PreClose", "TX1N07C21550.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21550", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21550.TF-Time", "TX1N07P21550.TF-TradingDate", "TX1N07P21550.TF-Open", "TX1N07P21550.TF-High", "TX1N07P21550.TF-Low", "TX1N07P21550.TF-Price", "TX1N07P21550.TF-TotalVolume", "TX1N07P21550.TF-Volume", "TX1N07P21550.TF-NWTotalBidContract", "TX1N07P21550.TF-NWTotalAskContract", "TX1N07P21550.TF-NWTotalBidSize", "TX1N07P21550.TF-NWTotalAskSize", "TX1N07P21550.TF-InSize", "TX1N07P21550.TF-OutSize", "TX1N07P21550.TF-TotalBidMatchTx", "TX1N07P21550.TF-TotalAskMatchTx", "TX1N07P21550.TF-BestBid1", "TX1N07P21550.TF-BestBid2", "TX1N07P21550.TF-BestBid3", "TX1N07P21550.TF-BestBid4", "TX1N07P21550.TF-BestBid5", "TX1N07P21550.TF-BestAsk1", "TX1N07P21550.TF-BestAsk2", "TX1N07P21550.TF-BestAsk3", "TX1N07P21550.TF-BestAsk4", "TX1N07P21550.TF-BestAsk5", "TX1N07P21550.TF-BestBidSize1", "TX1N07P21550.TF-BestBidSize2", "TX1N07P21550.TF-BestBidSize3", "TX1N07P21550.TF-BestBidSize4", "TX1N07P21550.TF-BestBidSize5", "TX1N07P21550.TF-BestAskSize1", "TX1N07P21550.TF-BestAskSize2", "TX1N07P21550.TF-BestAskSize3", "TX1N07P21550.TF-BestAskSize4", "TX1N07P21550.TF-BestAskSize5", "TX1N07P21550.TF-Name", "TX1N07P21550.TF-WContractDate", "TX1N07P21550.TF-SettlePrice", "TX1N07P21550.TF-UpLimit", "TX1N07P21550.TF-DownLimit", "TX1N07P21550.TF-OI", "TX1N07P21550.TF-TradingDate", "TX1N07P21550.TF-WRemainDate", "TX1N07P21550.TF-PreClose", "TX1N07P21550.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21600", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21600.TF-Time", "TX1N07C21600.TF-TradingDate", "TX1N07C21600.TF-Open", "TX1N07C21600.TF-High", "TX1N07C21600.TF-Low", "TX1N07C21600.TF-Price", "TX1N07C21600.TF-TotalVolume", "TX1N07C21600.TF-Volume", "TX1N07C21600.TF-NWTotalBidContract", "TX1N07C21600.TF-NWTotalAskContract", "TX1N07C21600.TF-NWTotalBidSize", "TX1N07C21600.TF-NWTotalAskSize", "TX1N07C21600.TF-InSize", "TX1N07C21600.TF-OutSize", "TX1N07C21600.TF-TotalBidMatchTx", "TX1N07C21600.TF-TotalAskMatchTx", "TX1N07C21600.TF-BestBid1", "TX1N07C21600.TF-BestBid2", "TX1N07C21600.TF-BestBid3", "TX1N07C21600.TF-BestBid4", "TX1N07C21600.TF-BestBid5", "TX1N07C21600.TF-BestAsk1", "TX1N07C21600.TF-BestAsk2", "TX1N07C21600.TF-BestAsk3", "TX1N07C21600.TF-BestAsk4", "TX1N07C21600.TF-BestAsk5", "TX1N07C21600.TF-BestBidSize1", "TX1N07C21600.TF-BestBidSize2", "TX1N07C21600.TF-BestBidSize3", "TX1N07C21600.TF-BestBidSize4", "TX1N07C21600.TF-BestBidSize5", "TX1N07C21600.TF-BestAskSize1", "TX1N07C21600.TF-BestAskSize2", "TX1N07C21600.TF-BestAskSize3", "TX1N07C21600.TF-BestAskSize4", "TX1N07C21600.TF-BestAskSize5", "TX1N07C21600.TF-Name", "TX1N07C21600.TF-WContractDate", "TX1N07C21600.TF-SettlePrice", "TX1N07C21600.TF-UpLimit", "TX1N07C21600.TF-DownLimit", "TX1N07C21600.TF-OI", "TX1N07C21600.TF-TradingDate", "TX1N07C21600.TF-WRemainDate", "TX1N07C21600.TF-PreClose", "TX1N07C21600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21600", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21600.TF-Time", "TX1N07P21600.TF-TradingDate", "TX1N07P21600.TF-Open", "TX1N07P21600.TF-High", "TX1N07P21600.TF-Low", "TX1N07P21600.TF-Price", "TX1N07P21600.TF-TotalVolume", "TX1N07P21600.TF-Volume", "TX1N07P21600.TF-NWTotalBidContract", "TX1N07P21600.TF-NWTotalAskContract", "TX1N07P21600.TF-NWTotalBidSize", "TX1N07P21600.TF-NWTotalAskSize", "TX1N07P21600.TF-InSize", "TX1N07P21600.TF-OutSize", "TX1N07P21600.TF-TotalBidMatchTx", "TX1N07P21600.TF-TotalAskMatchTx", "TX1N07P21600.TF-BestBid1", "TX1N07P21600.TF-BestBid2", "TX1N07P21600.TF-BestBid3", "TX1N07P21600.TF-BestBid4", "TX1N07P21600.TF-BestBid5", "TX1N07P21600.TF-BestAsk1", "TX1N07P21600.TF-BestAsk2", "TX1N07P21600.TF-BestAsk3", "TX1N07P21600.TF-BestAsk4", "TX1N07P21600.TF-BestAsk5", "TX1N07P21600.TF-BestBidSize1", "TX1N07P21600.TF-BestBidSize2", "TX1N07P21600.TF-BestBidSize3", "TX1N07P21600.TF-BestBidSize4", "TX1N07P21600.TF-BestBidSize5", "TX1N07P21600.TF-BestAskSize1", "TX1N07P21600.TF-BestAskSize2", "TX1N07P21600.TF-BestAskSize3", "TX1N07P21600.TF-BestAskSize4", "TX1N07P21600.TF-BestAskSize5", "TX1N07P21600.TF-Name", "TX1N07P21600.TF-WContractDate", "TX1N07P21600.TF-SettlePrice", "TX1N07P21600.TF-UpLimit", "TX1N07P21600.TF-DownLimit", "TX1N07P21600.TF-OI", "TX1N07P21600.TF-TradingDate", "TX1N07P21600.TF-WRemainDate", "TX1N07P21600.TF-PreClose", "TX1N07P21600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21650", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21650.TF-Time", "TX1N07C21650.TF-TradingDate", "TX1N07C21650.TF-Open", "TX1N07C21650.TF-High", "TX1N07C21650.TF-Low", "TX1N07C21650.TF-Price", "TX1N07C21650.TF-TotalVolume", "TX1N07C21650.TF-Volume", "TX1N07C21650.TF-NWTotalBidContract", "TX1N07C21650.TF-NWTotalAskContract", "TX1N07C21650.TF-NWTotalBidSize", "TX1N07C21650.TF-NWTotalAskSize", "TX1N07C21650.TF-InSize", "TX1N07C21650.TF-OutSize", "TX1N07C21650.TF-TotalBidMatchTx", "TX1N07C21650.TF-TotalAskMatchTx", "TX1N07C21650.TF-BestBid1", "TX1N07C21650.TF-BestBid2", "TX1N07C21650.TF-BestBid3", "TX1N07C21650.TF-BestBid4", "TX1N07C21650.TF-BestBid5", "TX1N07C21650.TF-BestAsk1", "TX1N07C21650.TF-BestAsk2", "TX1N07C21650.TF-BestAsk3", "TX1N07C21650.TF-BestAsk4", "TX1N07C21650.TF-BestAsk5", "TX1N07C21650.TF-BestBidSize1", "TX1N07C21650.TF-BestBidSize2", "TX1N07C21650.TF-BestBidSize3", "TX1N07C21650.TF-BestBidSize4", "TX1N07C21650.TF-BestBidSize5", "TX1N07C21650.TF-BestAskSize1", "TX1N07C21650.TF-BestAskSize2", "TX1N07C21650.TF-BestAskSize3", "TX1N07C21650.TF-BestAskSize4", "TX1N07C21650.TF-BestAskSize5", "TX1N07C21650.TF-Name", "TX1N07C21650.TF-WContractDate", "TX1N07C21650.TF-SettlePrice", "TX1N07C21650.TF-UpLimit", "TX1N07C21650.TF-DownLimit", "TX1N07C21650.TF-OI", "TX1N07C21650.TF-TradingDate", "TX1N07C21650.TF-WRemainDate", "TX1N07C21650.TF-PreClose", "TX1N07C21650.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21650", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21650.TF-Time", "TX1N07P21650.TF-TradingDate", "TX1N07P21650.TF-Open", "TX1N07P21650.TF-High", "TX1N07P21650.TF-Low", "TX1N07P21650.TF-Price", "TX1N07P21650.TF-TotalVolume", "TX1N07P21650.TF-Volume", "TX1N07P21650.TF-NWTotalBidContract", "TX1N07P21650.TF-NWTotalAskContract", "TX1N07P21650.TF-NWTotalBidSize", "TX1N07P21650.TF-NWTotalAskSize", "TX1N07P21650.TF-InSize", "TX1N07P21650.TF-OutSize", "TX1N07P21650.TF-TotalBidMatchTx", "TX1N07P21650.TF-TotalAskMatchTx", "TX1N07P21650.TF-BestBid1", "TX1N07P21650.TF-BestBid2", "TX1N07P21650.TF-BestBid3", "TX1N07P21650.TF-BestBid4", "TX1N07P21650.TF-BestBid5", "TX1N07P21650.TF-BestAsk1", "TX1N07P21650.TF-BestAsk2", "TX1N07P21650.TF-BestAsk3", "TX1N07P21650.TF-BestAsk4", "TX1N07P21650.TF-BestAsk5", "TX1N07P21650.TF-BestBidSize1", "TX1N07P21650.TF-BestBidSize2", "TX1N07P21650.TF-BestBidSize3", "TX1N07P21650.TF-BestBidSize4", "TX1N07P21650.TF-BestBidSize5", "TX1N07P21650.TF-BestAskSize1", "TX1N07P21650.TF-BestAskSize2", "TX1N07P21650.TF-BestAskSize3", "TX1N07P21650.TF-BestAskSize4", "TX1N07P21650.TF-BestAskSize5", "TX1N07P21650.TF-Name", "TX1N07P21650.TF-WContractDate", "TX1N07P21650.TF-SettlePrice", "TX1N07P21650.TF-UpLimit", "TX1N07P21650.TF-DownLimit", "TX1N07P21650.TF-OI", "TX1N07P21650.TF-TradingDate", "TX1N07P21650.TF-WRemainDate", "TX1N07P21650.TF-PreClose", "TX1N07P21650.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21700", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21700.TF-Time", "TX1N07C21700.TF-TradingDate", "TX1N07C21700.TF-Open", "TX1N07C21700.TF-High", "TX1N07C21700.TF-Low", "TX1N07C21700.TF-Price", "TX1N07C21700.TF-TotalVolume", "TX1N07C21700.TF-Volume", "TX1N07C21700.TF-NWTotalBidContract", "TX1N07C21700.TF-NWTotalAskContract", "TX1N07C21700.TF-NWTotalBidSize", "TX1N07C21700.TF-NWTotalAskSize", "TX1N07C21700.TF-InSize", "TX1N07C21700.TF-OutSize", "TX1N07C21700.TF-TotalBidMatchTx", "TX1N07C21700.TF-TotalAskMatchTx", "TX1N07C21700.TF-BestBid1", "TX1N07C21700.TF-BestBid2", "TX1N07C21700.TF-BestBid3", "TX1N07C21700.TF-BestBid4", "TX1N07C21700.TF-BestBid5", "TX1N07C21700.TF-BestAsk1", "TX1N07C21700.TF-BestAsk2", "TX1N07C21700.TF-BestAsk3", "TX1N07C21700.TF-BestAsk4", "TX1N07C21700.TF-BestAsk5", "TX1N07C21700.TF-BestBidSize1", "TX1N07C21700.TF-BestBidSize2", "TX1N07C21700.TF-BestBidSize3", "TX1N07C21700.TF-BestBidSize4", "TX1N07C21700.TF-BestBidSize5", "TX1N07C21700.TF-BestAskSize1", "TX1N07C21700.TF-BestAskSize2", "TX1N07C21700.TF-BestAskSize3", "TX1N07C21700.TF-BestAskSize4", "TX1N07C21700.TF-BestAskSize5", "TX1N07C21700.TF-Name", "TX1N07C21700.TF-WContractDate", "TX1N07C21700.TF-SettlePrice", "TX1N07C21700.TF-UpLimit", "TX1N07C21700.TF-DownLimit", "TX1N07C21700.TF-OI", "TX1N07C21700.TF-TradingDate", "TX1N07C21700.TF-WRemainDate", "TX1N07C21700.TF-PreClose", "TX1N07C21700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21700", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21700.TF-Time", "TX1N07P21700.TF-TradingDate", "TX1N07P21700.TF-Open", "TX1N07P21700.TF-High", "TX1N07P21700.TF-Low", "TX1N07P21700.TF-Price", "TX1N07P21700.TF-TotalVolume", "TX1N07P21700.TF-Volume", "TX1N07P21700.TF-NWTotalBidContract", "TX1N07P21700.TF-NWTotalAskContract", "TX1N07P21700.TF-NWTotalBidSize", "TX1N07P21700.TF-NWTotalAskSize", "TX1N07P21700.TF-InSize", "TX1N07P21700.TF-OutSize", "TX1N07P21700.TF-TotalBidMatchTx", "TX1N07P21700.TF-TotalAskMatchTx", "TX1N07P21700.TF-BestBid1", "TX1N07P21700.TF-BestBid2", "TX1N07P21700.TF-BestBid3", "TX1N07P21700.TF-BestBid4", "TX1N07P21700.TF-BestBid5", "TX1N07P21700.TF-BestAsk1", "TX1N07P21700.TF-BestAsk2", "TX1N07P21700.TF-BestAsk3", "TX1N07P21700.TF-BestAsk4", "TX1N07P21700.TF-BestAsk5", "TX1N07P21700.TF-BestBidSize1", "TX1N07P21700.TF-BestBidSize2", "TX1N07P21700.TF-BestBidSize3", "TX1N07P21700.TF-BestBidSize4", "TX1N07P21700.TF-BestBidSize5", "TX1N07P21700.TF-BestAskSize1", "TX1N07P21700.TF-BestAskSize2", "TX1N07P21700.TF-BestAskSize3", "TX1N07P21700.TF-BestAskSize4", "TX1N07P21700.TF-BestAskSize5", "TX1N07P21700.TF-Name", "TX1N07P21700.TF-WContractDate", "TX1N07P21700.TF-SettlePrice", "TX1N07P21700.TF-UpLimit", "TX1N07P21700.TF-DownLimit", "TX1N07P21700.TF-OI", "TX1N07P21700.TF-TradingDate", "TX1N07P21700.TF-WRemainDate", "TX1N07P21700.TF-PreClose", "TX1N07P21700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21750", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21750.TF-Time", "TX1N07C21750.TF-TradingDate", "TX1N07C21750.TF-Open", "TX1N07C21750.TF-High", "TX1N07C21750.TF-Low", "TX1N07C21750.TF-Price", "TX1N07C21750.TF-TotalVolume", "TX1N07C21750.TF-Volume", "TX1N07C21750.TF-NWTotalBidContract", "TX1N07C21750.TF-NWTotalAskContract", "TX1N07C21750.TF-NWTotalBidSize", "TX1N07C21750.TF-NWTotalAskSize", "TX1N07C21750.TF-InSize", "TX1N07C21750.TF-OutSize", "TX1N07C21750.TF-TotalBidMatchTx", "TX1N07C21750.TF-TotalAskMatchTx", "TX1N07C21750.TF-BestBid1", "TX1N07C21750.TF-BestBid2", "TX1N07C21750.TF-BestBid3", "TX1N07C21750.TF-BestBid4", "TX1N07C21750.TF-BestBid5", "TX1N07C21750.TF-BestAsk1", "TX1N07C21750.TF-BestAsk2", "TX1N07C21750.TF-BestAsk3", "TX1N07C21750.TF-BestAsk4", "TX1N07C21750.TF-BestAsk5", "TX1N07C21750.TF-BestBidSize1", "TX1N07C21750.TF-BestBidSize2", "TX1N07C21750.TF-BestBidSize3", "TX1N07C21750.TF-BestBidSize4", "TX1N07C21750.TF-BestBidSize5", "TX1N07C21750.TF-BestAskSize1", "TX1N07C21750.TF-BestAskSize2", "TX1N07C21750.TF-BestAskSize3", "TX1N07C21750.TF-BestAskSize4", "TX1N07C21750.TF-BestAskSize5", "TX1N07C21750.TF-Name", "TX1N07C21750.TF-WContractDate", "TX1N07C21750.TF-SettlePrice", "TX1N07C21750.TF-UpLimit", "TX1N07C21750.TF-DownLimit", "TX1N07C21750.TF-OI", "TX1N07C21750.TF-TradingDate", "TX1N07C21750.TF-WRemainDate", "TX1N07C21750.TF-PreClose", "TX1N07C21750.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21750", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21750.TF-Time", "TX1N07P21750.TF-TradingDate", "TX1N07P21750.TF-Open", "TX1N07P21750.TF-High", "TX1N07P21750.TF-Low", "TX1N07P21750.TF-Price", "TX1N07P21750.TF-TotalVolume", "TX1N07P21750.TF-Volume", "TX1N07P21750.TF-NWTotalBidContract", "TX1N07P21750.TF-NWTotalAskContract", "TX1N07P21750.TF-NWTotalBidSize", "TX1N07P21750.TF-NWTotalAskSize", "TX1N07P21750.TF-InSize", "TX1N07P21750.TF-OutSize", "TX1N07P21750.TF-TotalBidMatchTx", "TX1N07P21750.TF-TotalAskMatchTx", "TX1N07P21750.TF-BestBid1", "TX1N07P21750.TF-BestBid2", "TX1N07P21750.TF-BestBid3", "TX1N07P21750.TF-BestBid4", "TX1N07P21750.TF-BestBid5", "TX1N07P21750.TF-BestAsk1", "TX1N07P21750.TF-BestAsk2", "TX1N07P21750.TF-BestAsk3", "TX1N07P21750.TF-BestAsk4", "TX1N07P21750.TF-BestAsk5", "TX1N07P21750.TF-BestBidSize1", "TX1N07P21750.TF-BestBidSize2", "TX1N07P21750.TF-BestBidSize3", "TX1N07P21750.TF-BestBidSize4", "TX1N07P21750.TF-BestBidSize5", "TX1N07P21750.TF-BestAskSize1", "TX1N07P21750.TF-BestAskSize2", "TX1N07P21750.TF-BestAskSize3", "TX1N07P21750.TF-BestAskSize4", "TX1N07P21750.TF-BestAskSize5", "TX1N07P21750.TF-Name", "TX1N07P21750.TF-WContractDate", "TX1N07P21750.TF-SettlePrice", "TX1N07P21750.TF-UpLimit", "TX1N07P21750.TF-DownLimit", "TX1N07P21750.TF-OI", "TX1N07P21750.TF-TradingDate", "TX1N07P21750.TF-WRemainDate", "TX1N07P21750.TF-PreClose", "TX1N07P21750.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21800", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21800.TF-Time", "TX1N07C21800.TF-TradingDate", "TX1N07C21800.TF-Open", "TX1N07C21800.TF-High", "TX1N07C21800.TF-Low", "TX1N07C21800.TF-Price", "TX1N07C21800.TF-TotalVolume", "TX1N07C21800.TF-Volume", "TX1N07C21800.TF-NWTotalBidContract", "TX1N07C21800.TF-NWTotalAskContract", "TX1N07C21800.TF-NWTotalBidSize", "TX1N07C21800.TF-NWTotalAskSize", "TX1N07C21800.TF-InSize", "TX1N07C21800.TF-OutSize", "TX1N07C21800.TF-TotalBidMatchTx", "TX1N07C21800.TF-TotalAskMatchTx", "TX1N07C21800.TF-BestBid1", "TX1N07C21800.TF-BestBid2", "TX1N07C21800.TF-BestBid3", "TX1N07C21800.TF-BestBid4", "TX1N07C21800.TF-BestBid5", "TX1N07C21800.TF-BestAsk1", "TX1N07C21800.TF-BestAsk2", "TX1N07C21800.TF-BestAsk3", "TX1N07C21800.TF-BestAsk4", "TX1N07C21800.TF-BestAsk5", "TX1N07C21800.TF-BestBidSize1", "TX1N07C21800.TF-BestBidSize2", "TX1N07C21800.TF-BestBidSize3", "TX1N07C21800.TF-BestBidSize4", "TX1N07C21800.TF-BestBidSize5", "TX1N07C21800.TF-BestAskSize1", "TX1N07C21800.TF-BestAskSize2", "TX1N07C21800.TF-BestAskSize3", "TX1N07C21800.TF-BestAskSize4", "TX1N07C21800.TF-BestAskSize5", "TX1N07C21800.TF-Name", "TX1N07C21800.TF-WContractDate", "TX1N07C21800.TF-SettlePrice", "TX1N07C21800.TF-UpLimit", "TX1N07C21800.TF-DownLimit", "TX1N07C21800.TF-OI", "TX1N07C21800.TF-TradingDate", "TX1N07C21800.TF-WRemainDate", "TX1N07C21800.TF-PreClose", "TX1N07C21800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21800", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21800.TF-Time", "TX1N07P21800.TF-TradingDate", "TX1N07P21800.TF-Open", "TX1N07P21800.TF-High", "TX1N07P21800.TF-Low", "TX1N07P21800.TF-Price", "TX1N07P21800.TF-TotalVolume", "TX1N07P21800.TF-Volume", "TX1N07P21800.TF-NWTotalBidContract", "TX1N07P21800.TF-NWTotalAskContract", "TX1N07P21800.TF-NWTotalBidSize", "TX1N07P21800.TF-NWTotalAskSize", "TX1N07P21800.TF-InSize", "TX1N07P21800.TF-OutSize", "TX1N07P21800.TF-TotalBidMatchTx", "TX1N07P21800.TF-TotalAskMatchTx", "TX1N07P21800.TF-BestBid1", "TX1N07P21800.TF-BestBid2", "TX1N07P21800.TF-BestBid3", "TX1N07P21800.TF-BestBid4", "TX1N07P21800.TF-BestBid5", "TX1N07P21800.TF-BestAsk1", "TX1N07P21800.TF-BestAsk2", "TX1N07P21800.TF-BestAsk3", "TX1N07P21800.TF-BestAsk4", "TX1N07P21800.TF-BestAsk5", "TX1N07P21800.TF-BestBidSize1", "TX1N07P21800.TF-BestBidSize2", "TX1N07P21800.TF-BestBidSize3", "TX1N07P21800.TF-BestBidSize4", "TX1N07P21800.TF-BestBidSize5", "TX1N07P21800.TF-BestAskSize1", "TX1N07P21800.TF-BestAskSize2", "TX1N07P21800.TF-BestAskSize3", "TX1N07P21800.TF-BestAskSize4", "TX1N07P21800.TF-BestAskSize5", "TX1N07P21800.TF-Name", "TX1N07P21800.TF-WContractDate", "TX1N07P21800.TF-SettlePrice", "TX1N07P21800.TF-UpLimit", "TX1N07P21800.TF-DownLimit", "TX1N07P21800.TF-OI", "TX1N07P21800.TF-TradingDate", "TX1N07P21800.TF-WRemainDate", "TX1N07P21800.TF-PreClose", "TX1N07P21800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21850", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21850.TF-Time", "TX1N07C21850.TF-TradingDate", "TX1N07C21850.TF-Open", "TX1N07C21850.TF-High", "TX1N07C21850.TF-Low", "TX1N07C21850.TF-Price", "TX1N07C21850.TF-TotalVolume", "TX1N07C21850.TF-Volume", "TX1N07C21850.TF-NWTotalBidContract", "TX1N07C21850.TF-NWTotalAskContract", "TX1N07C21850.TF-NWTotalBidSize", "TX1N07C21850.TF-NWTotalAskSize", "TX1N07C21850.TF-InSize", "TX1N07C21850.TF-OutSize", "TX1N07C21850.TF-TotalBidMatchTx", "TX1N07C21850.TF-TotalAskMatchTx", "TX1N07C21850.TF-BestBid1", "TX1N07C21850.TF-BestBid2", "TX1N07C21850.TF-BestBid3", "TX1N07C21850.TF-BestBid4", "TX1N07C21850.TF-BestBid5", "TX1N07C21850.TF-BestAsk1", "TX1N07C21850.TF-BestAsk2", "TX1N07C21850.TF-BestAsk3", "TX1N07C21850.TF-BestAsk4", "TX1N07C21850.TF-BestAsk5", "TX1N07C21850.TF-BestBidSize1", "TX1N07C21850.TF-BestBidSize2", "TX1N07C21850.TF-BestBidSize3", "TX1N07C21850.TF-BestBidSize4", "TX1N07C21850.TF-BestBidSize5", "TX1N07C21850.TF-BestAskSize1", "TX1N07C21850.TF-BestAskSize2", "TX1N07C21850.TF-BestAskSize3", "TX1N07C21850.TF-BestAskSize4", "TX1N07C21850.TF-BestAskSize5", "TX1N07C21850.TF-Name", "TX1N07C21850.TF-WContractDate", "TX1N07C21850.TF-SettlePrice", "TX1N07C21850.TF-UpLimit", "TX1N07C21850.TF-DownLimit", "TX1N07C21850.TF-OI", "TX1N07C21850.TF-TradingDate", "TX1N07C21850.TF-WRemainDate", "TX1N07C21850.TF-PreClose", "TX1N07C21850.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21850", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21850.TF-Time", "TX1N07P21850.TF-TradingDate", "TX1N07P21850.TF-Open", "TX1N07P21850.TF-High", "TX1N07P21850.TF-Low", "TX1N07P21850.TF-Price", "TX1N07P21850.TF-TotalVolume", "TX1N07P21850.TF-Volume", "TX1N07P21850.TF-NWTotalBidContract", "TX1N07P21850.TF-NWTotalAskContract", "TX1N07P21850.TF-NWTotalBidSize", "TX1N07P21850.TF-NWTotalAskSize", "TX1N07P21850.TF-InSize", "TX1N07P21850.TF-OutSize", "TX1N07P21850.TF-TotalBidMatchTx", "TX1N07P21850.TF-TotalAskMatchTx", "TX1N07P21850.TF-BestBid1", "TX1N07P21850.TF-BestBid2", "TX1N07P21850.TF-BestBid3", "TX1N07P21850.TF-BestBid4", "TX1N07P21850.TF-BestBid5", "TX1N07P21850.TF-BestAsk1", "TX1N07P21850.TF-BestAsk2", "TX1N07P21850.TF-BestAsk3", "TX1N07P21850.TF-BestAsk4", "TX1N07P21850.TF-BestAsk5", "TX1N07P21850.TF-BestBidSize1", "TX1N07P21850.TF-BestBidSize2", "TX1N07P21850.TF-BestBidSize3", "TX1N07P21850.TF-BestBidSize4", "TX1N07P21850.TF-BestBidSize5", "TX1N07P21850.TF-BestAskSize1", "TX1N07P21850.TF-BestAskSize2", "TX1N07P21850.TF-BestAskSize3", "TX1N07P21850.TF-BestAskSize4", "TX1N07P21850.TF-BestAskSize5", "TX1N07P21850.TF-Name", "TX1N07P21850.TF-WContractDate", "TX1N07P21850.TF-SettlePrice", "TX1N07P21850.TF-UpLimit", "TX1N07P21850.TF-DownLimit", "TX1N07P21850.TF-OI", "TX1N07P21850.TF-TradingDate", "TX1N07P21850.TF-WRemainDate", "TX1N07P21850.TF-PreClose", "TX1N07P21850.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21900", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21900.TF-Time", "TX1N07C21900.TF-TradingDate", "TX1N07C21900.TF-Open", "TX1N07C21900.TF-High", "TX1N07C21900.TF-Low", "TX1N07C21900.TF-Price", "TX1N07C21900.TF-TotalVolume", "TX1N07C21900.TF-Volume", "TX1N07C21900.TF-NWTotalBidContract", "TX1N07C21900.TF-NWTotalAskContract", "TX1N07C21900.TF-NWTotalBidSize", "TX1N07C21900.TF-NWTotalAskSize", "TX1N07C21900.TF-InSize", "TX1N07C21900.TF-OutSize", "TX1N07C21900.TF-TotalBidMatchTx", "TX1N07C21900.TF-TotalAskMatchTx", "TX1N07C21900.TF-BestBid1", "TX1N07C21900.TF-BestBid2", "TX1N07C21900.TF-BestBid3", "TX1N07C21900.TF-BestBid4", "TX1N07C21900.TF-BestBid5", "TX1N07C21900.TF-BestAsk1", "TX1N07C21900.TF-BestAsk2", "TX1N07C21900.TF-BestAsk3", "TX1N07C21900.TF-BestAsk4", "TX1N07C21900.TF-BestAsk5", "TX1N07C21900.TF-BestBidSize1", "TX1N07C21900.TF-BestBidSize2", "TX1N07C21900.TF-BestBidSize3", "TX1N07C21900.TF-BestBidSize4", "TX1N07C21900.TF-BestBidSize5", "TX1N07C21900.TF-BestAskSize1", "TX1N07C21900.TF-BestAskSize2", "TX1N07C21900.TF-BestAskSize3", "TX1N07C21900.TF-BestAskSize4", "TX1N07C21900.TF-BestAskSize5", "TX1N07C21900.TF-Name", "TX1N07C21900.TF-WContractDate", "TX1N07C21900.TF-SettlePrice", "TX1N07C21900.TF-UpLimit", "TX1N07C21900.TF-DownLimit", "TX1N07C21900.TF-OI", "TX1N07C21900.TF-TradingDate", "TX1N07C21900.TF-WRemainDate", "TX1N07C21900.TF-PreClose", "TX1N07C21900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21900", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21900.TF-Time", "TX1N07P21900.TF-TradingDate", "TX1N07P21900.TF-Open", "TX1N07P21900.TF-High", "TX1N07P21900.TF-Low", "TX1N07P21900.TF-Price", "TX1N07P21900.TF-TotalVolume", "TX1N07P21900.TF-Volume", "TX1N07P21900.TF-NWTotalBidContract", "TX1N07P21900.TF-NWTotalAskContract", "TX1N07P21900.TF-NWTotalBidSize", "TX1N07P21900.TF-NWTotalAskSize", "TX1N07P21900.TF-InSize", "TX1N07P21900.TF-OutSize", "TX1N07P21900.TF-TotalBidMatchTx", "TX1N07P21900.TF-TotalAskMatchTx", "TX1N07P21900.TF-BestBid1", "TX1N07P21900.TF-BestBid2", "TX1N07P21900.TF-BestBid3", "TX1N07P21900.TF-BestBid4", "TX1N07P21900.TF-BestBid5", "TX1N07P21900.TF-BestAsk1", "TX1N07P21900.TF-BestAsk2", "TX1N07P21900.TF-BestAsk3", "TX1N07P21900.TF-BestAsk4", "TX1N07P21900.TF-BestAsk5", "TX1N07P21900.TF-BestBidSize1", "TX1N07P21900.TF-BestBidSize2", "TX1N07P21900.TF-BestBidSize3", "TX1N07P21900.TF-BestBidSize4", "TX1N07P21900.TF-BestBidSize5", "TX1N07P21900.TF-BestAskSize1", "TX1N07P21900.TF-BestAskSize2", "TX1N07P21900.TF-BestAskSize3", "TX1N07P21900.TF-BestAskSize4", "TX1N07P21900.TF-BestAskSize5", "TX1N07P21900.TF-Name", "TX1N07P21900.TF-WContractDate", "TX1N07P21900.TF-SettlePrice", "TX1N07P21900.TF-UpLimit", "TX1N07P21900.TF-DownLimit", "TX1N07P21900.TF-OI", "TX1N07P21900.TF-TradingDate", "TX1N07P21900.TF-WRemainDate", "TX1N07P21900.TF-PreClose", "TX1N07P21900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C21950", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C21950.TF-Time", "TX1N07C21950.TF-TradingDate", "TX1N07C21950.TF-Open", "TX1N07C21950.TF-High", "TX1N07C21950.TF-Low", "TX1N07C21950.TF-Price", "TX1N07C21950.TF-TotalVolume", "TX1N07C21950.TF-Volume", "TX1N07C21950.TF-NWTotalBidContract", "TX1N07C21950.TF-NWTotalAskContract", "TX1N07C21950.TF-NWTotalBidSize", "TX1N07C21950.TF-NWTotalAskSize", "TX1N07C21950.TF-InSize", "TX1N07C21950.TF-OutSize", "TX1N07C21950.TF-TotalBidMatchTx", "TX1N07C21950.TF-TotalAskMatchTx", "TX1N07C21950.TF-BestBid1", "TX1N07C21950.TF-BestBid2", "TX1N07C21950.TF-BestBid3", "TX1N07C21950.TF-BestBid4", "TX1N07C21950.TF-BestBid5", "TX1N07C21950.TF-BestAsk1", "TX1N07C21950.TF-BestAsk2", "TX1N07C21950.TF-BestAsk3", "TX1N07C21950.TF-BestAsk4", "TX1N07C21950.TF-BestAsk5", "TX1N07C21950.TF-BestBidSize1", "TX1N07C21950.TF-BestBidSize2", "TX1N07C21950.TF-BestBidSize3", "TX1N07C21950.TF-BestBidSize4", "TX1N07C21950.TF-BestBidSize5", "TX1N07C21950.TF-BestAskSize1", "TX1N07C21950.TF-BestAskSize2", "TX1N07C21950.TF-BestAskSize3", "TX1N07C21950.TF-BestAskSize4", "TX1N07C21950.TF-BestAskSize5", "TX1N07C21950.TF-Name", "TX1N07C21950.TF-WContractDate", "TX1N07C21950.TF-SettlePrice", "TX1N07C21950.TF-UpLimit", "TX1N07C21950.TF-DownLimit", "TX1N07C21950.TF-OI", "TX1N07C21950.TF-TradingDate", "TX1N07C21950.TF-WRemainDate", "TX1N07C21950.TF-PreClose", "TX1N07C21950.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P21950", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P21950.TF-Time", "TX1N07P21950.TF-TradingDate", "TX1N07P21950.TF-Open", "TX1N07P21950.TF-High", "TX1N07P21950.TF-Low", "TX1N07P21950.TF-Price", "TX1N07P21950.TF-TotalVolume", "TX1N07P21950.TF-Volume", "TX1N07P21950.TF-NWTotalBidContract", "TX1N07P21950.TF-NWTotalAskContract", "TX1N07P21950.TF-NWTotalBidSize", "TX1N07P21950.TF-NWTotalAskSize", "TX1N07P21950.TF-InSize", "TX1N07P21950.TF-OutSize", "TX1N07P21950.TF-TotalBidMatchTx", "TX1N07P21950.TF-TotalAskMatchTx", "TX1N07P21950.TF-BestBid1", "TX1N07P21950.TF-BestBid2", "TX1N07P21950.TF-BestBid3", "TX1N07P21950.TF-BestBid4", "TX1N07P21950.TF-BestBid5", "TX1N07P21950.TF-BestAsk1", "TX1N07P21950.TF-BestAsk2", "TX1N07P21950.TF-BestAsk3", "TX1N07P21950.TF-BestAsk4", "TX1N07P21950.TF-BestAsk5", "TX1N07P21950.TF-BestBidSize1", "TX1N07P21950.TF-BestBidSize2", "TX1N07P21950.TF-BestBidSize3", "TX1N07P21950.TF-BestBidSize4", "TX1N07P21950.TF-BestBidSize5", "TX1N07P21950.TF-BestAskSize1", "TX1N07P21950.TF-BestAskSize2", "TX1N07P21950.TF-BestAskSize3", "TX1N07P21950.TF-BestAskSize4", "TX1N07P21950.TF-BestAskSize5", "TX1N07P21950.TF-Name", "TX1N07P21950.TF-WContractDate", "TX1N07P21950.TF-SettlePrice", "TX1N07P21950.TF-UpLimit", "TX1N07P21950.TF-DownLimit", "TX1N07P21950.TF-OI", "TX1N07P21950.TF-TradingDate", "TX1N07P21950.TF-WRemainDate", "TX1N07P21950.TF-PreClose", "TX1N07P21950.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22000", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22000.TF-Time", "TX1N07C22000.TF-TradingDate", "TX1N07C22000.TF-Open", "TX1N07C22000.TF-High", "TX1N07C22000.TF-Low", "TX1N07C22000.TF-Price", "TX1N07C22000.TF-TotalVolume", "TX1N07C22000.TF-Volume", "TX1N07C22000.TF-NWTotalBidContract", "TX1N07C22000.TF-NWTotalAskContract", "TX1N07C22000.TF-NWTotalBidSize", "TX1N07C22000.TF-NWTotalAskSize", "TX1N07C22000.TF-InSize", "TX1N07C22000.TF-OutSize", "TX1N07C22000.TF-TotalBidMatchTx", "TX1N07C22000.TF-TotalAskMatchTx", "TX1N07C22000.TF-BestBid1", "TX1N07C22000.TF-BestBid2", "TX1N07C22000.TF-BestBid3", "TX1N07C22000.TF-BestBid4", "TX1N07C22000.TF-BestBid5", "TX1N07C22000.TF-BestAsk1", "TX1N07C22000.TF-BestAsk2", "TX1N07C22000.TF-BestAsk3", "TX1N07C22000.TF-BestAsk4", "TX1N07C22000.TF-BestAsk5", "TX1N07C22000.TF-BestBidSize1", "TX1N07C22000.TF-BestBidSize2", "TX1N07C22000.TF-BestBidSize3", "TX1N07C22000.TF-BestBidSize4", "TX1N07C22000.TF-BestBidSize5", "TX1N07C22000.TF-BestAskSize1", "TX1N07C22000.TF-BestAskSize2", "TX1N07C22000.TF-BestAskSize3", "TX1N07C22000.TF-BestAskSize4", "TX1N07C22000.TF-BestAskSize5", "TX1N07C22000.TF-Name", "TX1N07C22000.TF-WContractDate", "TX1N07C22000.TF-SettlePrice", "TX1N07C22000.TF-UpLimit", "TX1N07C22000.TF-DownLimit", "TX1N07C22000.TF-OI", "TX1N07C22000.TF-TradingDate", "TX1N07C22000.TF-WRemainDate", "TX1N07C22000.TF-PreClose", "TX1N07C22000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22000", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22000.TF-Time", "TX1N07P22000.TF-TradingDate", "TX1N07P22000.TF-Open", "TX1N07P22000.TF-High", "TX1N07P22000.TF-Low", "TX1N07P22000.TF-Price", "TX1N07P22000.TF-TotalVolume", "TX1N07P22000.TF-Volume", "TX1N07P22000.TF-NWTotalBidContract", "TX1N07P22000.TF-NWTotalAskContract", "TX1N07P22000.TF-NWTotalBidSize", "TX1N07P22000.TF-NWTotalAskSize", "TX1N07P22000.TF-InSize", "TX1N07P22000.TF-OutSize", "TX1N07P22000.TF-TotalBidMatchTx", "TX1N07P22000.TF-TotalAskMatchTx", "TX1N07P22000.TF-BestBid1", "TX1N07P22000.TF-BestBid2", "TX1N07P22000.TF-BestBid3", "TX1N07P22000.TF-BestBid4", "TX1N07P22000.TF-BestBid5", "TX1N07P22000.TF-BestAsk1", "TX1N07P22000.TF-BestAsk2", "TX1N07P22000.TF-BestAsk3", "TX1N07P22000.TF-BestAsk4", "TX1N07P22000.TF-BestAsk5", "TX1N07P22000.TF-BestBidSize1", "TX1N07P22000.TF-BestBidSize2", "TX1N07P22000.TF-BestBidSize3", "TX1N07P22000.TF-BestBidSize4", "TX1N07P22000.TF-BestBidSize5", "TX1N07P22000.TF-BestAskSize1", "TX1N07P22000.TF-BestAskSize2", "TX1N07P22000.TF-BestAskSize3", "TX1N07P22000.TF-BestAskSize4", "TX1N07P22000.TF-BestAskSize5", "TX1N07P22000.TF-Name", "TX1N07P22000.TF-WContractDate", "TX1N07P22000.TF-SettlePrice", "TX1N07P22000.TF-UpLimit", "TX1N07P22000.TF-DownLimit", "TX1N07P22000.TF-OI", "TX1N07P22000.TF-TradingDate", "TX1N07P22000.TF-WRemainDate", "TX1N07P22000.TF-PreClose", "TX1N07P22000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22050", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22050.TF-Time", "TX1N07C22050.TF-TradingDate", "TX1N07C22050.TF-Open", "TX1N07C22050.TF-High", "TX1N07C22050.TF-Low", "TX1N07C22050.TF-Price", "TX1N07C22050.TF-TotalVolume", "TX1N07C22050.TF-Volume", "TX1N07C22050.TF-NWTotalBidContract", "TX1N07C22050.TF-NWTotalAskContract", "TX1N07C22050.TF-NWTotalBidSize", "TX1N07C22050.TF-NWTotalAskSize", "TX1N07C22050.TF-InSize", "TX1N07C22050.TF-OutSize", "TX1N07C22050.TF-TotalBidMatchTx", "TX1N07C22050.TF-TotalAskMatchTx", "TX1N07C22050.TF-BestBid1", "TX1N07C22050.TF-BestBid2", "TX1N07C22050.TF-BestBid3", "TX1N07C22050.TF-BestBid4", "TX1N07C22050.TF-BestBid5", "TX1N07C22050.TF-BestAsk1", "TX1N07C22050.TF-BestAsk2", "TX1N07C22050.TF-BestAsk3", "TX1N07C22050.TF-BestAsk4", "TX1N07C22050.TF-BestAsk5", "TX1N07C22050.TF-BestBidSize1", "TX1N07C22050.TF-BestBidSize2", "TX1N07C22050.TF-BestBidSize3", "TX1N07C22050.TF-BestBidSize4", "TX1N07C22050.TF-BestBidSize5", "TX1N07C22050.TF-BestAskSize1", "TX1N07C22050.TF-BestAskSize2", "TX1N07C22050.TF-BestAskSize3", "TX1N07C22050.TF-BestAskSize4", "TX1N07C22050.TF-BestAskSize5", "TX1N07C22050.TF-Name", "TX1N07C22050.TF-WContractDate", "TX1N07C22050.TF-SettlePrice", "TX1N07C22050.TF-UpLimit", "TX1N07C22050.TF-DownLimit", "TX1N07C22050.TF-OI", "TX1N07C22050.TF-TradingDate", "TX1N07C22050.TF-WRemainDate", "TX1N07C22050.TF-PreClose", "TX1N07C22050.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22050", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22050.TF-Time", "TX1N07P22050.TF-TradingDate", "TX1N07P22050.TF-Open", "TX1N07P22050.TF-High", "TX1N07P22050.TF-Low", "TX1N07P22050.TF-Price", "TX1N07P22050.TF-TotalVolume", "TX1N07P22050.TF-Volume", "TX1N07P22050.TF-NWTotalBidContract", "TX1N07P22050.TF-NWTotalAskContract", "TX1N07P22050.TF-NWTotalBidSize", "TX1N07P22050.TF-NWTotalAskSize", "TX1N07P22050.TF-InSize", "TX1N07P22050.TF-OutSize", "TX1N07P22050.TF-TotalBidMatchTx", "TX1N07P22050.TF-TotalAskMatchTx", "TX1N07P22050.TF-BestBid1", "TX1N07P22050.TF-BestBid2", "TX1N07P22050.TF-BestBid3", "TX1N07P22050.TF-BestBid4", "TX1N07P22050.TF-BestBid5", "TX1N07P22050.TF-BestAsk1", "TX1N07P22050.TF-BestAsk2", "TX1N07P22050.TF-BestAsk3", "TX1N07P22050.TF-BestAsk4", "TX1N07P22050.TF-BestAsk5", "TX1N07P22050.TF-BestBidSize1", "TX1N07P22050.TF-BestBidSize2", "TX1N07P22050.TF-BestBidSize3", "TX1N07P22050.TF-BestBidSize4", "TX1N07P22050.TF-BestBidSize5", "TX1N07P22050.TF-BestAskSize1", "TX1N07P22050.TF-BestAskSize2", "TX1N07P22050.TF-BestAskSize3", "TX1N07P22050.TF-BestAskSize4", "TX1N07P22050.TF-BestAskSize5", "TX1N07P22050.TF-Name", "TX1N07P22050.TF-WContractDate", "TX1N07P22050.TF-SettlePrice", "TX1N07P22050.TF-UpLimit", "TX1N07P22050.TF-DownLimit", "TX1N07P22050.TF-OI", "TX1N07P22050.TF-TradingDate", "TX1N07P22050.TF-WRemainDate", "TX1N07P22050.TF-PreClose", "TX1N07P22050.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22100", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22100.TF-Time", "TX1N07C22100.TF-TradingDate", "TX1N07C22100.TF-Open", "TX1N07C22100.TF-High", "TX1N07C22100.TF-Low", "TX1N07C22100.TF-Price", "TX1N07C22100.TF-TotalVolume", "TX1N07C22100.TF-Volume", "TX1N07C22100.TF-NWTotalBidContract", "TX1N07C22100.TF-NWTotalAskContract", "TX1N07C22100.TF-NWTotalBidSize", "TX1N07C22100.TF-NWTotalAskSize", "TX1N07C22100.TF-InSize", "TX1N07C22100.TF-OutSize", "TX1N07C22100.TF-TotalBidMatchTx", "TX1N07C22100.TF-TotalAskMatchTx", "TX1N07C22100.TF-BestBid1", "TX1N07C22100.TF-BestBid2", "TX1N07C22100.TF-BestBid3", "TX1N07C22100.TF-BestBid4", "TX1N07C22100.TF-BestBid5", "TX1N07C22100.TF-BestAsk1", "TX1N07C22100.TF-BestAsk2", "TX1N07C22100.TF-BestAsk3", "TX1N07C22100.TF-BestAsk4", "TX1N07C22100.TF-BestAsk5", "TX1N07C22100.TF-BestBidSize1", "TX1N07C22100.TF-BestBidSize2", "TX1N07C22100.TF-BestBidSize3", "TX1N07C22100.TF-BestBidSize4", "TX1N07C22100.TF-BestBidSize5", "TX1N07C22100.TF-BestAskSize1", "TX1N07C22100.TF-BestAskSize2", "TX1N07C22100.TF-BestAskSize3", "TX1N07C22100.TF-BestAskSize4", "TX1N07C22100.TF-BestAskSize5", "TX1N07C22100.TF-Name", "TX1N07C22100.TF-WContractDate", "TX1N07C22100.TF-SettlePrice", "TX1N07C22100.TF-UpLimit", "TX1N07C22100.TF-DownLimit", "TX1N07C22100.TF-OI", "TX1N07C22100.TF-TradingDate", "TX1N07C22100.TF-WRemainDate", "TX1N07C22100.TF-PreClose", "TX1N07C22100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22100", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22100.TF-Time", "TX1N07P22100.TF-TradingDate", "TX1N07P22100.TF-Open", "TX1N07P22100.TF-High", "TX1N07P22100.TF-Low", "TX1N07P22100.TF-Price", "TX1N07P22100.TF-TotalVolume", "TX1N07P22100.TF-Volume", "TX1N07P22100.TF-NWTotalBidContract", "TX1N07P22100.TF-NWTotalAskContract", "TX1N07P22100.TF-NWTotalBidSize", "TX1N07P22100.TF-NWTotalAskSize", "TX1N07P22100.TF-InSize", "TX1N07P22100.TF-OutSize", "TX1N07P22100.TF-TotalBidMatchTx", "TX1N07P22100.TF-TotalAskMatchTx", "TX1N07P22100.TF-BestBid1", "TX1N07P22100.TF-BestBid2", "TX1N07P22100.TF-BestBid3", "TX1N07P22100.TF-BestBid4", "TX1N07P22100.TF-BestBid5", "TX1N07P22100.TF-BestAsk1", "TX1N07P22100.TF-BestAsk2", "TX1N07P22100.TF-BestAsk3", "TX1N07P22100.TF-BestAsk4", "TX1N07P22100.TF-BestAsk5", "TX1N07P22100.TF-BestBidSize1", "TX1N07P22100.TF-BestBidSize2", "TX1N07P22100.TF-BestBidSize3", "TX1N07P22100.TF-BestBidSize4", "TX1N07P22100.TF-BestBidSize5", "TX1N07P22100.TF-BestAskSize1", "TX1N07P22100.TF-BestAskSize2", "TX1N07P22100.TF-BestAskSize3", "TX1N07P22100.TF-BestAskSize4", "TX1N07P22100.TF-BestAskSize5", "TX1N07P22100.TF-Name", "TX1N07P22100.TF-WContractDate", "TX1N07P22100.TF-SettlePrice", "TX1N07P22100.TF-UpLimit", "TX1N07P22100.TF-DownLimit", "TX1N07P22100.TF-OI", "TX1N07P22100.TF-TradingDate", "TX1N07P22100.TF-WRemainDate", "TX1N07P22100.TF-PreClose", "TX1N07P22100.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22150", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22150.TF-Time", "TX1N07C22150.TF-TradingDate", "TX1N07C22150.TF-Open", "TX1N07C22150.TF-High", "TX1N07C22150.TF-Low", "TX1N07C22150.TF-Price", "TX1N07C22150.TF-TotalVolume", "TX1N07C22150.TF-Volume", "TX1N07C22150.TF-NWTotalBidContract", "TX1N07C22150.TF-NWTotalAskContract", "TX1N07C22150.TF-NWTotalBidSize", "TX1N07C22150.TF-NWTotalAskSize", "TX1N07C22150.TF-InSize", "TX1N07C22150.TF-OutSize", "TX1N07C22150.TF-TotalBidMatchTx", "TX1N07C22150.TF-TotalAskMatchTx", "TX1N07C22150.TF-BestBid1", "TX1N07C22150.TF-BestBid2", "TX1N07C22150.TF-BestBid3", "TX1N07C22150.TF-BestBid4", "TX1N07C22150.TF-BestBid5", "TX1N07C22150.TF-BestAsk1", "TX1N07C22150.TF-BestAsk2", "TX1N07C22150.TF-BestAsk3", "TX1N07C22150.TF-BestAsk4", "TX1N07C22150.TF-BestAsk5", "TX1N07C22150.TF-BestBidSize1", "TX1N07C22150.TF-BestBidSize2", "TX1N07C22150.TF-BestBidSize3", "TX1N07C22150.TF-BestBidSize4", "TX1N07C22150.TF-BestBidSize5", "TX1N07C22150.TF-BestAskSize1", "TX1N07C22150.TF-BestAskSize2", "TX1N07C22150.TF-BestAskSize3", "TX1N07C22150.TF-BestAskSize4", "TX1N07C22150.TF-BestAskSize5", "TX1N07C22150.TF-Name", "TX1N07C22150.TF-WContractDate", "TX1N07C22150.TF-SettlePrice", "TX1N07C22150.TF-UpLimit", "TX1N07C22150.TF-DownLimit", "TX1N07C22150.TF-OI", "TX1N07C22150.TF-TradingDate", "TX1N07C22150.TF-WRemainDate", "TX1N07C22150.TF-PreClose", "TX1N07C22150.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22150", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22150.TF-Time", "TX1N07P22150.TF-TradingDate", "TX1N07P22150.TF-Open", "TX1N07P22150.TF-High", "TX1N07P22150.TF-Low", "TX1N07P22150.TF-Price", "TX1N07P22150.TF-TotalVolume", "TX1N07P22150.TF-Volume", "TX1N07P22150.TF-NWTotalBidContract", "TX1N07P22150.TF-NWTotalAskContract", "TX1N07P22150.TF-NWTotalBidSize", "TX1N07P22150.TF-NWTotalAskSize", "TX1N07P22150.TF-InSize", "TX1N07P22150.TF-OutSize", "TX1N07P22150.TF-TotalBidMatchTx", "TX1N07P22150.TF-TotalAskMatchTx", "TX1N07P22150.TF-BestBid1", "TX1N07P22150.TF-BestBid2", "TX1N07P22150.TF-BestBid3", "TX1N07P22150.TF-BestBid4", "TX1N07P22150.TF-BestBid5", "TX1N07P22150.TF-BestAsk1", "TX1N07P22150.TF-BestAsk2", "TX1N07P22150.TF-BestAsk3", "TX1N07P22150.TF-BestAsk4", "TX1N07P22150.TF-BestAsk5", "TX1N07P22150.TF-BestBidSize1", "TX1N07P22150.TF-BestBidSize2", "TX1N07P22150.TF-BestBidSize3", "TX1N07P22150.TF-BestBidSize4", "TX1N07P22150.TF-BestBidSize5", "TX1N07P22150.TF-BestAskSize1", "TX1N07P22150.TF-BestAskSize2", "TX1N07P22150.TF-BestAskSize3", "TX1N07P22150.TF-BestAskSize4", "TX1N07P22150.TF-BestAskSize5", "TX1N07P22150.TF-Name", "TX1N07P22150.TF-WContractDate", "TX1N07P22150.TF-SettlePrice", "TX1N07P22150.TF-UpLimit", "TX1N07P22150.TF-DownLimit", "TX1N07P22150.TF-OI", "TX1N07P22150.TF-TradingDate", "TX1N07P22150.TF-WRemainDate", "TX1N07P22150.TF-PreClose", "TX1N07P22150.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22200", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22200.TF-Time", "TX1N07C22200.TF-TradingDate", "TX1N07C22200.TF-Open", "TX1N07C22200.TF-High", "TX1N07C22200.TF-Low", "TX1N07C22200.TF-Price", "TX1N07C22200.TF-TotalVolume", "TX1N07C22200.TF-Volume", "TX1N07C22200.TF-NWTotalBidContract", "TX1N07C22200.TF-NWTotalAskContract", "TX1N07C22200.TF-NWTotalBidSize", "TX1N07C22200.TF-NWTotalAskSize", "TX1N07C22200.TF-InSize", "TX1N07C22200.TF-OutSize", "TX1N07C22200.TF-TotalBidMatchTx", "TX1N07C22200.TF-TotalAskMatchTx", "TX1N07C22200.TF-BestBid1", "TX1N07C22200.TF-BestBid2", "TX1N07C22200.TF-BestBid3", "TX1N07C22200.TF-BestBid4", "TX1N07C22200.TF-BestBid5", "TX1N07C22200.TF-BestAsk1", "TX1N07C22200.TF-BestAsk2", "TX1N07C22200.TF-BestAsk3", "TX1N07C22200.TF-BestAsk4", "TX1N07C22200.TF-BestAsk5", "TX1N07C22200.TF-BestBidSize1", "TX1N07C22200.TF-BestBidSize2", "TX1N07C22200.TF-BestBidSize3", "TX1N07C22200.TF-BestBidSize4", "TX1N07C22200.TF-BestBidSize5", "TX1N07C22200.TF-BestAskSize1", "TX1N07C22200.TF-BestAskSize2", "TX1N07C22200.TF-BestAskSize3", "TX1N07C22200.TF-BestAskSize4", "TX1N07C22200.TF-BestAskSize5", "TX1N07C22200.TF-Name", "TX1N07C22200.TF-WContractDate", "TX1N07C22200.TF-SettlePrice", "TX1N07C22200.TF-UpLimit", "TX1N07C22200.TF-DownLimit", "TX1N07C22200.TF-OI", "TX1N07C22200.TF-TradingDate", "TX1N07C22200.TF-WRemainDate", "TX1N07C22200.TF-PreClose", "TX1N07C22200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22200", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22200.TF-Time", "TX1N07P22200.TF-TradingDate", "TX1N07P22200.TF-Open", "TX1N07P22200.TF-High", "TX1N07P22200.TF-Low", "TX1N07P22200.TF-Price", "TX1N07P22200.TF-TotalVolume", "TX1N07P22200.TF-Volume", "TX1N07P22200.TF-NWTotalBidContract", "TX1N07P22200.TF-NWTotalAskContract", "TX1N07P22200.TF-NWTotalBidSize", "TX1N07P22200.TF-NWTotalAskSize", "TX1N07P22200.TF-InSize", "TX1N07P22200.TF-OutSize", "TX1N07P22200.TF-TotalBidMatchTx", "TX1N07P22200.TF-TotalAskMatchTx", "TX1N07P22200.TF-BestBid1", "TX1N07P22200.TF-BestBid2", "TX1N07P22200.TF-BestBid3", "TX1N07P22200.TF-BestBid4", "TX1N07P22200.TF-BestBid5", "TX1N07P22200.TF-BestAsk1", "TX1N07P22200.TF-BestAsk2", "TX1N07P22200.TF-BestAsk3", "TX1N07P22200.TF-BestAsk4", "TX1N07P22200.TF-BestAsk5", "TX1N07P22200.TF-BestBidSize1", "TX1N07P22200.TF-BestBidSize2", "TX1N07P22200.TF-BestBidSize3", "TX1N07P22200.TF-BestBidSize4", "TX1N07P22200.TF-BestBidSize5", "TX1N07P22200.TF-BestAskSize1", "TX1N07P22200.TF-BestAskSize2", "TX1N07P22200.TF-BestAskSize3", "TX1N07P22200.TF-BestAskSize4", "TX1N07P22200.TF-BestAskSize5", "TX1N07P22200.TF-Name", "TX1N07P22200.TF-WContractDate", "TX1N07P22200.TF-SettlePrice", "TX1N07P22200.TF-UpLimit", "TX1N07P22200.TF-DownLimit", "TX1N07P22200.TF-OI", "TX1N07P22200.TF-TradingDate", "TX1N07P22200.TF-WRemainDate", "TX1N07P22200.TF-PreClose", "TX1N07P22200.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22250", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22250.TF-Time", "TX1N07C22250.TF-TradingDate", "TX1N07C22250.TF-Open", "TX1N07C22250.TF-High", "TX1N07C22250.TF-Low", "TX1N07C22250.TF-Price", "TX1N07C22250.TF-TotalVolume", "TX1N07C22250.TF-Volume", "TX1N07C22250.TF-NWTotalBidContract", "TX1N07C22250.TF-NWTotalAskContract", "TX1N07C22250.TF-NWTotalBidSize", "TX1N07C22250.TF-NWTotalAskSize", "TX1N07C22250.TF-InSize", "TX1N07C22250.TF-OutSize", "TX1N07C22250.TF-TotalBidMatchTx", "TX1N07C22250.TF-TotalAskMatchTx", "TX1N07C22250.TF-BestBid1", "TX1N07C22250.TF-BestBid2", "TX1N07C22250.TF-BestBid3", "TX1N07C22250.TF-BestBid4", "TX1N07C22250.TF-BestBid5", "TX1N07C22250.TF-BestAsk1", "TX1N07C22250.TF-BestAsk2", "TX1N07C22250.TF-BestAsk3", "TX1N07C22250.TF-BestAsk4", "TX1N07C22250.TF-BestAsk5", "TX1N07C22250.TF-BestBidSize1", "TX1N07C22250.TF-BestBidSize2", "TX1N07C22250.TF-BestBidSize3", "TX1N07C22250.TF-BestBidSize4", "TX1N07C22250.TF-BestBidSize5", "TX1N07C22250.TF-BestAskSize1", "TX1N07C22250.TF-BestAskSize2", "TX1N07C22250.TF-BestAskSize3", "TX1N07C22250.TF-BestAskSize4", "TX1N07C22250.TF-BestAskSize5", "TX1N07C22250.TF-Name", "TX1N07C22250.TF-WContractDate", "TX1N07C22250.TF-SettlePrice", "TX1N07C22250.TF-UpLimit", "TX1N07C22250.TF-DownLimit", "TX1N07C22250.TF-OI", "TX1N07C22250.TF-TradingDate", "TX1N07C22250.TF-WRemainDate", "TX1N07C22250.TF-PreClose", "TX1N07C22250.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22250", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22250.TF-Time", "TX1N07P22250.TF-TradingDate", "TX1N07P22250.TF-Open", "TX1N07P22250.TF-High", "TX1N07P22250.TF-Low", "TX1N07P22250.TF-Price", "TX1N07P22250.TF-TotalVolume", "TX1N07P22250.TF-Volume", "TX1N07P22250.TF-NWTotalBidContract", "TX1N07P22250.TF-NWTotalAskContract", "TX1N07P22250.TF-NWTotalBidSize", "TX1N07P22250.TF-NWTotalAskSize", "TX1N07P22250.TF-InSize", "TX1N07P22250.TF-OutSize", "TX1N07P22250.TF-TotalBidMatchTx", "TX1N07P22250.TF-TotalAskMatchTx", "TX1N07P22250.TF-BestBid1", "TX1N07P22250.TF-BestBid2", "TX1N07P22250.TF-BestBid3", "TX1N07P22250.TF-BestBid4", "TX1N07P22250.TF-BestBid5", "TX1N07P22250.TF-BestAsk1", "TX1N07P22250.TF-BestAsk2", "TX1N07P22250.TF-BestAsk3", "TX1N07P22250.TF-BestAsk4", "TX1N07P22250.TF-BestAsk5", "TX1N07P22250.TF-BestBidSize1", "TX1N07P22250.TF-BestBidSize2", "TX1N07P22250.TF-BestBidSize3", "TX1N07P22250.TF-BestBidSize4", "TX1N07P22250.TF-BestBidSize5", "TX1N07P22250.TF-BestAskSize1", "TX1N07P22250.TF-BestAskSize2", "TX1N07P22250.TF-BestAskSize3", "TX1N07P22250.TF-BestAskSize4", "TX1N07P22250.TF-BestAskSize5", "TX1N07P22250.TF-Name", "TX1N07P22250.TF-WContractDate", "TX1N07P22250.TF-SettlePrice", "TX1N07P22250.TF-UpLimit", "TX1N07P22250.TF-DownLimit", "TX1N07P22250.TF-OI", "TX1N07P22250.TF-TradingDate", "TX1N07P22250.TF-WRemainDate", "TX1N07P22250.TF-PreClose", "TX1N07P22250.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22300", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22300.TF-Time", "TX1N07C22300.TF-TradingDate", "TX1N07C22300.TF-Open", "TX1N07C22300.TF-High", "TX1N07C22300.TF-Low", "TX1N07C22300.TF-Price", "TX1N07C22300.TF-TotalVolume", "TX1N07C22300.TF-Volume", "TX1N07C22300.TF-NWTotalBidContract", "TX1N07C22300.TF-NWTotalAskContract", "TX1N07C22300.TF-NWTotalBidSize", "TX1N07C22300.TF-NWTotalAskSize", "TX1N07C22300.TF-InSize", "TX1N07C22300.TF-OutSize", "TX1N07C22300.TF-TotalBidMatchTx", "TX1N07C22300.TF-TotalAskMatchTx", "TX1N07C22300.TF-BestBid1", "TX1N07C22300.TF-BestBid2", "TX1N07C22300.TF-BestBid3", "TX1N07C22300.TF-BestBid4", "TX1N07C22300.TF-BestBid5", "TX1N07C22300.TF-BestAsk1", "TX1N07C22300.TF-BestAsk2", "TX1N07C22300.TF-BestAsk3", "TX1N07C22300.TF-BestAsk4", "TX1N07C22300.TF-BestAsk5", "TX1N07C22300.TF-BestBidSize1", "TX1N07C22300.TF-BestBidSize2", "TX1N07C22300.TF-BestBidSize3", "TX1N07C22300.TF-BestBidSize4", "TX1N07C22300.TF-BestBidSize5", "TX1N07C22300.TF-BestAskSize1", "TX1N07C22300.TF-BestAskSize2", "TX1N07C22300.TF-BestAskSize3", "TX1N07C22300.TF-BestAskSize4", "TX1N07C22300.TF-BestAskSize5", "TX1N07C22300.TF-Name", "TX1N07C22300.TF-WContractDate", "TX1N07C22300.TF-SettlePrice", "TX1N07C22300.TF-UpLimit", "TX1N07C22300.TF-DownLimit", "TX1N07C22300.TF-OI", "TX1N07C22300.TF-TradingDate", "TX1N07C22300.TF-WRemainDate", "TX1N07C22300.TF-PreClose", "TX1N07C22300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22300", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22300.TF-Time", "TX1N07P22300.TF-TradingDate", "TX1N07P22300.TF-Open", "TX1N07P22300.TF-High", "TX1N07P22300.TF-Low", "TX1N07P22300.TF-Price", "TX1N07P22300.TF-TotalVolume", "TX1N07P22300.TF-Volume", "TX1N07P22300.TF-NWTotalBidContract", "TX1N07P22300.TF-NWTotalAskContract", "TX1N07P22300.TF-NWTotalBidSize", "TX1N07P22300.TF-NWTotalAskSize", "TX1N07P22300.TF-InSize", "TX1N07P22300.TF-OutSize", "TX1N07P22300.TF-TotalBidMatchTx", "TX1N07P22300.TF-TotalAskMatchTx", "TX1N07P22300.TF-BestBid1", "TX1N07P22300.TF-BestBid2", "TX1N07P22300.TF-BestBid3", "TX1N07P22300.TF-BestBid4", "TX1N07P22300.TF-BestBid5", "TX1N07P22300.TF-BestAsk1", "TX1N07P22300.TF-BestAsk2", "TX1N07P22300.TF-BestAsk3", "TX1N07P22300.TF-BestAsk4", "TX1N07P22300.TF-BestAsk5", "TX1N07P22300.TF-BestBidSize1", "TX1N07P22300.TF-BestBidSize2", "TX1N07P22300.TF-BestBidSize3", "TX1N07P22300.TF-BestBidSize4", "TX1N07P22300.TF-BestBidSize5", "TX1N07P22300.TF-BestAskSize1", "TX1N07P22300.TF-BestAskSize2", "TX1N07P22300.TF-BestAskSize3", "TX1N07P22300.TF-BestAskSize4", "TX1N07P22300.TF-BestAskSize5", "TX1N07P22300.TF-Name", "TX1N07P22300.TF-WContractDate", "TX1N07P22300.TF-SettlePrice", "TX1N07P22300.TF-UpLimit", "TX1N07P22300.TF-DownLimit", "TX1N07P22300.TF-OI", "TX1N07P22300.TF-TradingDate", "TX1N07P22300.TF-WRemainDate", "TX1N07P22300.TF-PreClose", "TX1N07P22300.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22350", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22350.TF-Time", "TX1N07C22350.TF-TradingDate", "TX1N07C22350.TF-Open", "TX1N07C22350.TF-High", "TX1N07C22350.TF-Low", "TX1N07C22350.TF-Price", "TX1N07C22350.TF-TotalVolume", "TX1N07C22350.TF-Volume", "TX1N07C22350.TF-NWTotalBidContract", "TX1N07C22350.TF-NWTotalAskContract", "TX1N07C22350.TF-NWTotalBidSize", "TX1N07C22350.TF-NWTotalAskSize", "TX1N07C22350.TF-InSize", "TX1N07C22350.TF-OutSize", "TX1N07C22350.TF-TotalBidMatchTx", "TX1N07C22350.TF-TotalAskMatchTx", "TX1N07C22350.TF-BestBid1", "TX1N07C22350.TF-BestBid2", "TX1N07C22350.TF-BestBid3", "TX1N07C22350.TF-BestBid4", "TX1N07C22350.TF-BestBid5", "TX1N07C22350.TF-BestAsk1", "TX1N07C22350.TF-BestAsk2", "TX1N07C22350.TF-BestAsk3", "TX1N07C22350.TF-BestAsk4", "TX1N07C22350.TF-BestAsk5", "TX1N07C22350.TF-BestBidSize1", "TX1N07C22350.TF-BestBidSize2", "TX1N07C22350.TF-BestBidSize3", "TX1N07C22350.TF-BestBidSize4", "TX1N07C22350.TF-BestBidSize5", "TX1N07C22350.TF-BestAskSize1", "TX1N07C22350.TF-BestAskSize2", "TX1N07C22350.TF-BestAskSize3", "TX1N07C22350.TF-BestAskSize4", "TX1N07C22350.TF-BestAskSize5", "TX1N07C22350.TF-Name", "TX1N07C22350.TF-WContractDate", "TX1N07C22350.TF-SettlePrice", "TX1N07C22350.TF-UpLimit", "TX1N07C22350.TF-DownLimit", "TX1N07C22350.TF-OI", "TX1N07C22350.TF-TradingDate", "TX1N07C22350.TF-WRemainDate", "TX1N07C22350.TF-PreClose", "TX1N07C22350.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22350", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22350.TF-Time", "TX1N07P22350.TF-TradingDate", "TX1N07P22350.TF-Open", "TX1N07P22350.TF-High", "TX1N07P22350.TF-Low", "TX1N07P22350.TF-Price", "TX1N07P22350.TF-TotalVolume", "TX1N07P22350.TF-Volume", "TX1N07P22350.TF-NWTotalBidContract", "TX1N07P22350.TF-NWTotalAskContract", "TX1N07P22350.TF-NWTotalBidSize", "TX1N07P22350.TF-NWTotalAskSize", "TX1N07P22350.TF-InSize", "TX1N07P22350.TF-OutSize", "TX1N07P22350.TF-TotalBidMatchTx", "TX1N07P22350.TF-TotalAskMatchTx", "TX1N07P22350.TF-BestBid1", "TX1N07P22350.TF-BestBid2", "TX1N07P22350.TF-BestBid3", "TX1N07P22350.TF-BestBid4", "TX1N07P22350.TF-BestBid5", "TX1N07P22350.TF-BestAsk1", "TX1N07P22350.TF-BestAsk2", "TX1N07P22350.TF-BestAsk3", "TX1N07P22350.TF-BestAsk4", "TX1N07P22350.TF-BestAsk5", "TX1N07P22350.TF-BestBidSize1", "TX1N07P22350.TF-BestBidSize2", "TX1N07P22350.TF-BestBidSize3", "TX1N07P22350.TF-BestBidSize4", "TX1N07P22350.TF-BestBidSize5", "TX1N07P22350.TF-BestAskSize1", "TX1N07P22350.TF-BestAskSize2", "TX1N07P22350.TF-BestAskSize3", "TX1N07P22350.TF-BestAskSize4", "TX1N07P22350.TF-BestAskSize5", "TX1N07P22350.TF-Name", "TX1N07P22350.TF-WContractDate", "TX1N07P22350.TF-SettlePrice", "TX1N07P22350.TF-UpLimit", "TX1N07P22350.TF-DownLimit", "TX1N07P22350.TF-OI", "TX1N07P22350.TF-TradingDate", "TX1N07P22350.TF-WRemainDate", "TX1N07P22350.TF-PreClose", "TX1N07P22350.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22400", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22400.TF-Time", "TX1N07C22400.TF-TradingDate", "TX1N07C22400.TF-Open", "TX1N07C22400.TF-High", "TX1N07C22400.TF-Low", "TX1N07C22400.TF-Price", "TX1N07C22400.TF-TotalVolume", "TX1N07C22400.TF-Volume", "TX1N07C22400.TF-NWTotalBidContract", "TX1N07C22400.TF-NWTotalAskContract", "TX1N07C22400.TF-NWTotalBidSize", "TX1N07C22400.TF-NWTotalAskSize", "TX1N07C22400.TF-InSize", "TX1N07C22400.TF-OutSize", "TX1N07C22400.TF-TotalBidMatchTx", "TX1N07C22400.TF-TotalAskMatchTx", "TX1N07C22400.TF-BestBid1", "TX1N07C22400.TF-BestBid2", "TX1N07C22400.TF-BestBid3", "TX1N07C22400.TF-BestBid4", "TX1N07C22400.TF-BestBid5", "TX1N07C22400.TF-BestAsk1", "TX1N07C22400.TF-BestAsk2", "TX1N07C22400.TF-BestAsk3", "TX1N07C22400.TF-BestAsk4", "TX1N07C22400.TF-BestAsk5", "TX1N07C22400.TF-BestBidSize1", "TX1N07C22400.TF-BestBidSize2", "TX1N07C22400.TF-BestBidSize3", "TX1N07C22400.TF-BestBidSize4", "TX1N07C22400.TF-BestBidSize5", "TX1N07C22400.TF-BestAskSize1", "TX1N07C22400.TF-BestAskSize2", "TX1N07C22400.TF-BestAskSize3", "TX1N07C22400.TF-BestAskSize4", "TX1N07C22400.TF-BestAskSize5", "TX1N07C22400.TF-Name", "TX1N07C22400.TF-WContractDate", "TX1N07C22400.TF-SettlePrice", "TX1N07C22400.TF-UpLimit", "TX1N07C22400.TF-DownLimit", "TX1N07C22400.TF-OI", "TX1N07C22400.TF-TradingDate", "TX1N07C22400.TF-WRemainDate", "TX1N07C22400.TF-PreClose", "TX1N07C22400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22400", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22400.TF-Time", "TX1N07P22400.TF-TradingDate", "TX1N07P22400.TF-Open", "TX1N07P22400.TF-High", "TX1N07P22400.TF-Low", "TX1N07P22400.TF-Price", "TX1N07P22400.TF-TotalVolume", "TX1N07P22400.TF-Volume", "TX1N07P22400.TF-NWTotalBidContract", "TX1N07P22400.TF-NWTotalAskContract", "TX1N07P22400.TF-NWTotalBidSize", "TX1N07P22400.TF-NWTotalAskSize", "TX1N07P22400.TF-InSize", "TX1N07P22400.TF-OutSize", "TX1N07P22400.TF-TotalBidMatchTx", "TX1N07P22400.TF-TotalAskMatchTx", "TX1N07P22400.TF-BestBid1", "TX1N07P22400.TF-BestBid2", "TX1N07P22400.TF-BestBid3", "TX1N07P22400.TF-BestBid4", "TX1N07P22400.TF-BestBid5", "TX1N07P22400.TF-BestAsk1", "TX1N07P22400.TF-BestAsk2", "TX1N07P22400.TF-BestAsk3", "TX1N07P22400.TF-BestAsk4", "TX1N07P22400.TF-BestAsk5", "TX1N07P22400.TF-BestBidSize1", "TX1N07P22400.TF-BestBidSize2", "TX1N07P22400.TF-BestBidSize3", "TX1N07P22400.TF-BestBidSize4", "TX1N07P22400.TF-BestBidSize5", "TX1N07P22400.TF-BestAskSize1", "TX1N07P22400.TF-BestAskSize2", "TX1N07P22400.TF-BestAskSize3", "TX1N07P22400.TF-BestAskSize4", "TX1N07P22400.TF-BestAskSize5", "TX1N07P22400.TF-Name", "TX1N07P22400.TF-WContractDate", "TX1N07P22400.TF-SettlePrice", "TX1N07P22400.TF-UpLimit", "TX1N07P22400.TF-DownLimit", "TX1N07P22400.TF-OI", "TX1N07P22400.TF-TradingDate", "TX1N07P22400.TF-WRemainDate", "TX1N07P22400.TF-PreClose", "TX1N07P22400.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22450", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22450.TF-Time", "TX1N07C22450.TF-TradingDate", "TX1N07C22450.TF-Open", "TX1N07C22450.TF-High", "TX1N07C22450.TF-Low", "TX1N07C22450.TF-Price", "TX1N07C22450.TF-TotalVolume", "TX1N07C22450.TF-Volume", "TX1N07C22450.TF-NWTotalBidContract", "TX1N07C22450.TF-NWTotalAskContract", "TX1N07C22450.TF-NWTotalBidSize", "TX1N07C22450.TF-NWTotalAskSize", "TX1N07C22450.TF-InSize", "TX1N07C22450.TF-OutSize", "TX1N07C22450.TF-TotalBidMatchTx", "TX1N07C22450.TF-TotalAskMatchTx", "TX1N07C22450.TF-BestBid1", "TX1N07C22450.TF-BestBid2", "TX1N07C22450.TF-BestBid3", "TX1N07C22450.TF-BestBid4", "TX1N07C22450.TF-BestBid5", "TX1N07C22450.TF-BestAsk1", "TX1N07C22450.TF-BestAsk2", "TX1N07C22450.TF-BestAsk3", "TX1N07C22450.TF-BestAsk4", "TX1N07C22450.TF-BestAsk5", "TX1N07C22450.TF-BestBidSize1", "TX1N07C22450.TF-BestBidSize2", "TX1N07C22450.TF-BestBidSize3", "TX1N07C22450.TF-BestBidSize4", "TX1N07C22450.TF-BestBidSize5", "TX1N07C22450.TF-BestAskSize1", "TX1N07C22450.TF-BestAskSize2", "TX1N07C22450.TF-BestAskSize3", "TX1N07C22450.TF-BestAskSize4", "TX1N07C22450.TF-BestAskSize5", "TX1N07C22450.TF-Name", "TX1N07C22450.TF-WContractDate", "TX1N07C22450.TF-SettlePrice", "TX1N07C22450.TF-UpLimit", "TX1N07C22450.TF-DownLimit", "TX1N07C22450.TF-OI", "TX1N07C22450.TF-TradingDate", "TX1N07C22450.TF-WRemainDate", "TX1N07C22450.TF-PreClose", "TX1N07C22450.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22450", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22450.TF-Time", "TX1N07P22450.TF-TradingDate", "TX1N07P22450.TF-Open", "TX1N07P22450.TF-High", "TX1N07P22450.TF-Low", "TX1N07P22450.TF-Price", "TX1N07P22450.TF-TotalVolume", "TX1N07P22450.TF-Volume", "TX1N07P22450.TF-NWTotalBidContract", "TX1N07P22450.TF-NWTotalAskContract", "TX1N07P22450.TF-NWTotalBidSize", "TX1N07P22450.TF-NWTotalAskSize", "TX1N07P22450.TF-InSize", "TX1N07P22450.TF-OutSize", "TX1N07P22450.TF-TotalBidMatchTx", "TX1N07P22450.TF-TotalAskMatchTx", "TX1N07P22450.TF-BestBid1", "TX1N07P22450.TF-BestBid2", "TX1N07P22450.TF-BestBid3", "TX1N07P22450.TF-BestBid4", "TX1N07P22450.TF-BestBid5", "TX1N07P22450.TF-BestAsk1", "TX1N07P22450.TF-BestAsk2", "TX1N07P22450.TF-BestAsk3", "TX1N07P22450.TF-BestAsk4", "TX1N07P22450.TF-BestAsk5", "TX1N07P22450.TF-BestBidSize1", "TX1N07P22450.TF-BestBidSize2", "TX1N07P22450.TF-BestBidSize3", "TX1N07P22450.TF-BestBidSize4", "TX1N07P22450.TF-BestBidSize5", "TX1N07P22450.TF-BestAskSize1", "TX1N07P22450.TF-BestAskSize2", "TX1N07P22450.TF-BestAskSize3", "TX1N07P22450.TF-BestAskSize4", "TX1N07P22450.TF-BestAskSize5", "TX1N07P22450.TF-Name", "TX1N07P22450.TF-WContractDate", "TX1N07P22450.TF-SettlePrice", "TX1N07P22450.TF-UpLimit", "TX1N07P22450.TF-DownLimit", "TX1N07P22450.TF-OI", "TX1N07P22450.TF-TradingDate", "TX1N07P22450.TF-WRemainDate", "TX1N07P22450.TF-PreClose", "TX1N07P22450.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22500", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22500.TF-Time", "TX1N07C22500.TF-TradingDate", "TX1N07C22500.TF-Open", "TX1N07C22500.TF-High", "TX1N07C22500.TF-Low", "TX1N07C22500.TF-Price", "TX1N07C22500.TF-TotalVolume", "TX1N07C22500.TF-Volume", "TX1N07C22500.TF-NWTotalBidContract", "TX1N07C22500.TF-NWTotalAskContract", "TX1N07C22500.TF-NWTotalBidSize", "TX1N07C22500.TF-NWTotalAskSize", "TX1N07C22500.TF-InSize", "TX1N07C22500.TF-OutSize", "TX1N07C22500.TF-TotalBidMatchTx", "TX1N07C22500.TF-TotalAskMatchTx", "TX1N07C22500.TF-BestBid1", "TX1N07C22500.TF-BestBid2", "TX1N07C22500.TF-BestBid3", "TX1N07C22500.TF-BestBid4", "TX1N07C22500.TF-BestBid5", "TX1N07C22500.TF-BestAsk1", "TX1N07C22500.TF-BestAsk2", "TX1N07C22500.TF-BestAsk3", "TX1N07C22500.TF-BestAsk4", "TX1N07C22500.TF-BestAsk5", "TX1N07C22500.TF-BestBidSize1", "TX1N07C22500.TF-BestBidSize2", "TX1N07C22500.TF-BestBidSize3", "TX1N07C22500.TF-BestBidSize4", "TX1N07C22500.TF-BestBidSize5", "TX1N07C22500.TF-BestAskSize1", "TX1N07C22500.TF-BestAskSize2", "TX1N07C22500.TF-BestAskSize3", "TX1N07C22500.TF-BestAskSize4", "TX1N07C22500.TF-BestAskSize5", "TX1N07C22500.TF-Name", "TX1N07C22500.TF-WContractDate", "TX1N07C22500.TF-SettlePrice", "TX1N07C22500.TF-UpLimit", "TX1N07C22500.TF-DownLimit", "TX1N07C22500.TF-OI", "TX1N07C22500.TF-TradingDate", "TX1N07C22500.TF-WRemainDate", "TX1N07C22500.TF-PreClose", "TX1N07C22500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22500", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22500.TF-Time", "TX1N07P22500.TF-TradingDate", "TX1N07P22500.TF-Open", "TX1N07P22500.TF-High", "TX1N07P22500.TF-Low", "TX1N07P22500.TF-Price", "TX1N07P22500.TF-TotalVolume", "TX1N07P22500.TF-Volume", "TX1N07P22500.TF-NWTotalBidContract", "TX1N07P22500.TF-NWTotalAskContract", "TX1N07P22500.TF-NWTotalBidSize", "TX1N07P22500.TF-NWTotalAskSize", "TX1N07P22500.TF-InSize", "TX1N07P22500.TF-OutSize", "TX1N07P22500.TF-TotalBidMatchTx", "TX1N07P22500.TF-TotalAskMatchTx", "TX1N07P22500.TF-BestBid1", "TX1N07P22500.TF-BestBid2", "TX1N07P22500.TF-BestBid3", "TX1N07P22500.TF-BestBid4", "TX1N07P22500.TF-BestBid5", "TX1N07P22500.TF-BestAsk1", "TX1N07P22500.TF-BestAsk2", "TX1N07P22500.TF-BestAsk3", "TX1N07P22500.TF-BestAsk4", "TX1N07P22500.TF-BestAsk5", "TX1N07P22500.TF-BestBidSize1", "TX1N07P22500.TF-BestBidSize2", "TX1N07P22500.TF-BestBidSize3", "TX1N07P22500.TF-BestBidSize4", "TX1N07P22500.TF-BestBidSize5", "TX1N07P22500.TF-BestAskSize1", "TX1N07P22500.TF-BestAskSize2", "TX1N07P22500.TF-BestAskSize3", "TX1N07P22500.TF-BestAskSize4", "TX1N07P22500.TF-BestAskSize5", "TX1N07P22500.TF-Name", "TX1N07P22500.TF-WContractDate", "TX1N07P22500.TF-SettlePrice", "TX1N07P22500.TF-UpLimit", "TX1N07P22500.TF-DownLimit", "TX1N07P22500.TF-OI", "TX1N07P22500.TF-TradingDate", "TX1N07P22500.TF-WRemainDate", "TX1N07P22500.TF-PreClose", "TX1N07P22500.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22550", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22550.TF-Time", "TX1N07C22550.TF-TradingDate", "TX1N07C22550.TF-Open", "TX1N07C22550.TF-High", "TX1N07C22550.TF-Low", "TX1N07C22550.TF-Price", "TX1N07C22550.TF-TotalVolume", "TX1N07C22550.TF-Volume", "TX1N07C22550.TF-NWTotalBidContract", "TX1N07C22550.TF-NWTotalAskContract", "TX1N07C22550.TF-NWTotalBidSize", "TX1N07C22550.TF-NWTotalAskSize", "TX1N07C22550.TF-InSize", "TX1N07C22550.TF-OutSize", "TX1N07C22550.TF-TotalBidMatchTx", "TX1N07C22550.TF-TotalAskMatchTx", "TX1N07C22550.TF-BestBid1", "TX1N07C22550.TF-BestBid2", "TX1N07C22550.TF-BestBid3", "TX1N07C22550.TF-BestBid4", "TX1N07C22550.TF-BestBid5", "TX1N07C22550.TF-BestAsk1", "TX1N07C22550.TF-BestAsk2", "TX1N07C22550.TF-BestAsk3", "TX1N07C22550.TF-BestAsk4", "TX1N07C22550.TF-BestAsk5", "TX1N07C22550.TF-BestBidSize1", "TX1N07C22550.TF-BestBidSize2", "TX1N07C22550.TF-BestBidSize3", "TX1N07C22550.TF-BestBidSize4", "TX1N07C22550.TF-BestBidSize5", "TX1N07C22550.TF-BestAskSize1", "TX1N07C22550.TF-BestAskSize2", "TX1N07C22550.TF-BestAskSize3", "TX1N07C22550.TF-BestAskSize4", "TX1N07C22550.TF-BestAskSize5", "TX1N07C22550.TF-Name", "TX1N07C22550.TF-WContractDate", "TX1N07C22550.TF-SettlePrice", "TX1N07C22550.TF-UpLimit", "TX1N07C22550.TF-DownLimit", "TX1N07C22550.TF-OI", "TX1N07C22550.TF-TradingDate", "TX1N07C22550.TF-WRemainDate", "TX1N07C22550.TF-PreClose", "TX1N07C22550.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22550", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22550.TF-Time", "TX1N07P22550.TF-TradingDate", "TX1N07P22550.TF-Open", "TX1N07P22550.TF-High", "TX1N07P22550.TF-Low", "TX1N07P22550.TF-Price", "TX1N07P22550.TF-TotalVolume", "TX1N07P22550.TF-Volume", "TX1N07P22550.TF-NWTotalBidContract", "TX1N07P22550.TF-NWTotalAskContract", "TX1N07P22550.TF-NWTotalBidSize", "TX1N07P22550.TF-NWTotalAskSize", "TX1N07P22550.TF-InSize", "TX1N07P22550.TF-OutSize", "TX1N07P22550.TF-TotalBidMatchTx", "TX1N07P22550.TF-TotalAskMatchTx", "TX1N07P22550.TF-BestBid1", "TX1N07P22550.TF-BestBid2", "TX1N07P22550.TF-BestBid3", "TX1N07P22550.TF-BestBid4", "TX1N07P22550.TF-BestBid5", "TX1N07P22550.TF-BestAsk1", "TX1N07P22550.TF-BestAsk2", "TX1N07P22550.TF-BestAsk3", "TX1N07P22550.TF-BestAsk4", "TX1N07P22550.TF-BestAsk5", "TX1N07P22550.TF-BestBidSize1", "TX1N07P22550.TF-BestBidSize2", "TX1N07P22550.TF-BestBidSize3", "TX1N07P22550.TF-BestBidSize4", "TX1N07P22550.TF-BestBidSize5", "TX1N07P22550.TF-BestAskSize1", "TX1N07P22550.TF-BestAskSize2", "TX1N07P22550.TF-BestAskSize3", "TX1N07P22550.TF-BestAskSize4", "TX1N07P22550.TF-BestAskSize5", "TX1N07P22550.TF-Name", "TX1N07P22550.TF-WContractDate", "TX1N07P22550.TF-SettlePrice", "TX1N07P22550.TF-UpLimit", "TX1N07P22550.TF-DownLimit", "TX1N07P22550.TF-OI", "TX1N07P22550.TF-TradingDate", "TX1N07P22550.TF-WRemainDate", "TX1N07P22550.TF-PreClose", "TX1N07P22550.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22600", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22600.TF-Time", "TX1N07C22600.TF-TradingDate", "TX1N07C22600.TF-Open", "TX1N07C22600.TF-High", "TX1N07C22600.TF-Low", "TX1N07C22600.TF-Price", "TX1N07C22600.TF-TotalVolume", "TX1N07C22600.TF-Volume", "TX1N07C22600.TF-NWTotalBidContract", "TX1N07C22600.TF-NWTotalAskContract", "TX1N07C22600.TF-NWTotalBidSize", "TX1N07C22600.TF-NWTotalAskSize", "TX1N07C22600.TF-InSize", "TX1N07C22600.TF-OutSize", "TX1N07C22600.TF-TotalBidMatchTx", "TX1N07C22600.TF-TotalAskMatchTx", "TX1N07C22600.TF-BestBid1", "TX1N07C22600.TF-BestBid2", "TX1N07C22600.TF-BestBid3", "TX1N07C22600.TF-BestBid4", "TX1N07C22600.TF-BestBid5", "TX1N07C22600.TF-BestAsk1", "TX1N07C22600.TF-BestAsk2", "TX1N07C22600.TF-BestAsk3", "TX1N07C22600.TF-BestAsk4", "TX1N07C22600.TF-BestAsk5", "TX1N07C22600.TF-BestBidSize1", "TX1N07C22600.TF-BestBidSize2", "TX1N07C22600.TF-BestBidSize3", "TX1N07C22600.TF-BestBidSize4", "TX1N07C22600.TF-BestBidSize5", "TX1N07C22600.TF-BestAskSize1", "TX1N07C22600.TF-BestAskSize2", "TX1N07C22600.TF-BestAskSize3", "TX1N07C22600.TF-BestAskSize4", "TX1N07C22600.TF-BestAskSize5", "TX1N07C22600.TF-Name", "TX1N07C22600.TF-WContractDate", "TX1N07C22600.TF-SettlePrice", "TX1N07C22600.TF-UpLimit", "TX1N07C22600.TF-DownLimit", "TX1N07C22600.TF-OI", "TX1N07C22600.TF-TradingDate", "TX1N07C22600.TF-WRemainDate", "TX1N07C22600.TF-PreClose", "TX1N07C22600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22600", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22600.TF-Time", "TX1N07P22600.TF-TradingDate", "TX1N07P22600.TF-Open", "TX1N07P22600.TF-High", "TX1N07P22600.TF-Low", "TX1N07P22600.TF-Price", "TX1N07P22600.TF-TotalVolume", "TX1N07P22600.TF-Volume", "TX1N07P22600.TF-NWTotalBidContract", "TX1N07P22600.TF-NWTotalAskContract", "TX1N07P22600.TF-NWTotalBidSize", "TX1N07P22600.TF-NWTotalAskSize", "TX1N07P22600.TF-InSize", "TX1N07P22600.TF-OutSize", "TX1N07P22600.TF-TotalBidMatchTx", "TX1N07P22600.TF-TotalAskMatchTx", "TX1N07P22600.TF-BestBid1", "TX1N07P22600.TF-BestBid2", "TX1N07P22600.TF-BestBid3", "TX1N07P22600.TF-BestBid4", "TX1N07P22600.TF-BestBid5", "TX1N07P22600.TF-BestAsk1", "TX1N07P22600.TF-BestAsk2", "TX1N07P22600.TF-BestAsk3", "TX1N07P22600.TF-BestAsk4", "TX1N07P22600.TF-BestAsk5", "TX1N07P22600.TF-BestBidSize1", "TX1N07P22600.TF-BestBidSize2", "TX1N07P22600.TF-BestBidSize3", "TX1N07P22600.TF-BestBidSize4", "TX1N07P22600.TF-BestBidSize5", "TX1N07P22600.TF-BestAskSize1", "TX1N07P22600.TF-BestAskSize2", "TX1N07P22600.TF-BestAskSize3", "TX1N07P22600.TF-BestAskSize4", "TX1N07P22600.TF-BestAskSize5", "TX1N07P22600.TF-Name", "TX1N07P22600.TF-WContractDate", "TX1N07P22600.TF-SettlePrice", "TX1N07P22600.TF-UpLimit", "TX1N07P22600.TF-DownLimit", "TX1N07P22600.TF-OI", "TX1N07P22600.TF-TradingDate", "TX1N07P22600.TF-WRemainDate", "TX1N07P22600.TF-PreClose", "TX1N07P22600.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22650", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22650.TF-Time", "TX1N07C22650.TF-TradingDate", "TX1N07C22650.TF-Open", "TX1N07C22650.TF-High", "TX1N07C22650.TF-Low", "TX1N07C22650.TF-Price", "TX1N07C22650.TF-TotalVolume", "TX1N07C22650.TF-Volume", "TX1N07C22650.TF-NWTotalBidContract", "TX1N07C22650.TF-NWTotalAskContract", "TX1N07C22650.TF-NWTotalBidSize", "TX1N07C22650.TF-NWTotalAskSize", "TX1N07C22650.TF-InSize", "TX1N07C22650.TF-OutSize", "TX1N07C22650.TF-TotalBidMatchTx", "TX1N07C22650.TF-TotalAskMatchTx", "TX1N07C22650.TF-BestBid1", "TX1N07C22650.TF-BestBid2", "TX1N07C22650.TF-BestBid3", "TX1N07C22650.TF-BestBid4", "TX1N07C22650.TF-BestBid5", "TX1N07C22650.TF-BestAsk1", "TX1N07C22650.TF-BestAsk2", "TX1N07C22650.TF-BestAsk3", "TX1N07C22650.TF-BestAsk4", "TX1N07C22650.TF-BestAsk5", "TX1N07C22650.TF-BestBidSize1", "TX1N07C22650.TF-BestBidSize2", "TX1N07C22650.TF-BestBidSize3", "TX1N07C22650.TF-BestBidSize4", "TX1N07C22650.TF-BestBidSize5", "TX1N07C22650.TF-BestAskSize1", "TX1N07C22650.TF-BestAskSize2", "TX1N07C22650.TF-BestAskSize3", "TX1N07C22650.TF-BestAskSize4", "TX1N07C22650.TF-BestAskSize5", "TX1N07C22650.TF-Name", "TX1N07C22650.TF-WContractDate", "TX1N07C22650.TF-SettlePrice", "TX1N07C22650.TF-UpLimit", "TX1N07C22650.TF-DownLimit", "TX1N07C22650.TF-OI", "TX1N07C22650.TF-TradingDate", "TX1N07C22650.TF-WRemainDate", "TX1N07C22650.TF-PreClose", "TX1N07C22650.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22650", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22650.TF-Time", "TX1N07P22650.TF-TradingDate", "TX1N07P22650.TF-Open", "TX1N07P22650.TF-High", "TX1N07P22650.TF-Low", "TX1N07P22650.TF-Price", "TX1N07P22650.TF-TotalVolume", "TX1N07P22650.TF-Volume", "TX1N07P22650.TF-NWTotalBidContract", "TX1N07P22650.TF-NWTotalAskContract", "TX1N07P22650.TF-NWTotalBidSize", "TX1N07P22650.TF-NWTotalAskSize", "TX1N07P22650.TF-InSize", "TX1N07P22650.TF-OutSize", "TX1N07P22650.TF-TotalBidMatchTx", "TX1N07P22650.TF-TotalAskMatchTx", "TX1N07P22650.TF-BestBid1", "TX1N07P22650.TF-BestBid2", "TX1N07P22650.TF-BestBid3", "TX1N07P22650.TF-BestBid4", "TX1N07P22650.TF-BestBid5", "TX1N07P22650.TF-BestAsk1", "TX1N07P22650.TF-BestAsk2", "TX1N07P22650.TF-BestAsk3", "TX1N07P22650.TF-BestAsk4", "TX1N07P22650.TF-BestAsk5", "TX1N07P22650.TF-BestBidSize1", "TX1N07P22650.TF-BestBidSize2", "TX1N07P22650.TF-BestBidSize3", "TX1N07P22650.TF-BestBidSize4", "TX1N07P22650.TF-BestBidSize5", "TX1N07P22650.TF-BestAskSize1", "TX1N07P22650.TF-BestAskSize2", "TX1N07P22650.TF-BestAskSize3", "TX1N07P22650.TF-BestAskSize4", "TX1N07P22650.TF-BestAskSize5", "TX1N07P22650.TF-Name", "TX1N07P22650.TF-WContractDate", "TX1N07P22650.TF-SettlePrice", "TX1N07P22650.TF-UpLimit", "TX1N07P22650.TF-DownLimit", "TX1N07P22650.TF-OI", "TX1N07P22650.TF-TradingDate", "TX1N07P22650.TF-WRemainDate", "TX1N07P22650.TF-PreClose", "TX1N07P22650.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22700", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22700.TF-Time", "TX1N07C22700.TF-TradingDate", "TX1N07C22700.TF-Open", "TX1N07C22700.TF-High", "TX1N07C22700.TF-Low", "TX1N07C22700.TF-Price", "TX1N07C22700.TF-TotalVolume", "TX1N07C22700.TF-Volume", "TX1N07C22700.TF-NWTotalBidContract", "TX1N07C22700.TF-NWTotalAskContract", "TX1N07C22700.TF-NWTotalBidSize", "TX1N07C22700.TF-NWTotalAskSize", "TX1N07C22700.TF-InSize", "TX1N07C22700.TF-OutSize", "TX1N07C22700.TF-TotalBidMatchTx", "TX1N07C22700.TF-TotalAskMatchTx", "TX1N07C22700.TF-BestBid1", "TX1N07C22700.TF-BestBid2", "TX1N07C22700.TF-BestBid3", "TX1N07C22700.TF-BestBid4", "TX1N07C22700.TF-BestBid5", "TX1N07C22700.TF-BestAsk1", "TX1N07C22700.TF-BestAsk2", "TX1N07C22700.TF-BestAsk3", "TX1N07C22700.TF-BestAsk4", "TX1N07C22700.TF-BestAsk5", "TX1N07C22700.TF-BestBidSize1", "TX1N07C22700.TF-BestBidSize2", "TX1N07C22700.TF-BestBidSize3", "TX1N07C22700.TF-BestBidSize4", "TX1N07C22700.TF-BestBidSize5", "TX1N07C22700.TF-BestAskSize1", "TX1N07C22700.TF-BestAskSize2", "TX1N07C22700.TF-BestAskSize3", "TX1N07C22700.TF-BestAskSize4", "TX1N07C22700.TF-BestAskSize5", "TX1N07C22700.TF-Name", "TX1N07C22700.TF-WContractDate", "TX1N07C22700.TF-SettlePrice", "TX1N07C22700.TF-UpLimit", "TX1N07C22700.TF-DownLimit", "TX1N07C22700.TF-OI", "TX1N07C22700.TF-TradingDate", "TX1N07C22700.TF-WRemainDate", "TX1N07C22700.TF-PreClose", "TX1N07C22700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22700", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22700.TF-Time", "TX1N07P22700.TF-TradingDate", "TX1N07P22700.TF-Open", "TX1N07P22700.TF-High", "TX1N07P22700.TF-Low", "TX1N07P22700.TF-Price", "TX1N07P22700.TF-TotalVolume", "TX1N07P22700.TF-Volume", "TX1N07P22700.TF-NWTotalBidContract", "TX1N07P22700.TF-NWTotalAskContract", "TX1N07P22700.TF-NWTotalBidSize", "TX1N07P22700.TF-NWTotalAskSize", "TX1N07P22700.TF-InSize", "TX1N07P22700.TF-OutSize", "TX1N07P22700.TF-TotalBidMatchTx", "TX1N07P22700.TF-TotalAskMatchTx", "TX1N07P22700.TF-BestBid1", "TX1N07P22700.TF-BestBid2", "TX1N07P22700.TF-BestBid3", "TX1N07P22700.TF-BestBid4", "TX1N07P22700.TF-BestBid5", "TX1N07P22700.TF-BestAsk1", "TX1N07P22700.TF-BestAsk2", "TX1N07P22700.TF-BestAsk3", "TX1N07P22700.TF-BestAsk4", "TX1N07P22700.TF-BestAsk5", "TX1N07P22700.TF-BestBidSize1", "TX1N07P22700.TF-BestBidSize2", "TX1N07P22700.TF-BestBidSize3", "TX1N07P22700.TF-BestBidSize4", "TX1N07P22700.TF-BestBidSize5", "TX1N07P22700.TF-BestAskSize1", "TX1N07P22700.TF-BestAskSize2", "TX1N07P22700.TF-BestAskSize3", "TX1N07P22700.TF-BestAskSize4", "TX1N07P22700.TF-BestAskSize5", "TX1N07P22700.TF-Name", "TX1N07P22700.TF-WContractDate", "TX1N07P22700.TF-SettlePrice", "TX1N07P22700.TF-UpLimit", "TX1N07P22700.TF-DownLimit", "TX1N07P22700.TF-OI", "TX1N07P22700.TF-TradingDate", "TX1N07P22700.TF-WRemainDate", "TX1N07P22700.TF-PreClose", "TX1N07P22700.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22750", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22750.TF-Time", "TX1N07C22750.TF-TradingDate", "TX1N07C22750.TF-Open", "TX1N07C22750.TF-High", "TX1N07C22750.TF-Low", "TX1N07C22750.TF-Price", "TX1N07C22750.TF-TotalVolume", "TX1N07C22750.TF-Volume", "TX1N07C22750.TF-NWTotalBidContract", "TX1N07C22750.TF-NWTotalAskContract", "TX1N07C22750.TF-NWTotalBidSize", "TX1N07C22750.TF-NWTotalAskSize", "TX1N07C22750.TF-InSize", "TX1N07C22750.TF-OutSize", "TX1N07C22750.TF-TotalBidMatchTx", "TX1N07C22750.TF-TotalAskMatchTx", "TX1N07C22750.TF-BestBid1", "TX1N07C22750.TF-BestBid2", "TX1N07C22750.TF-BestBid3", "TX1N07C22750.TF-BestBid4", "TX1N07C22750.TF-BestBid5", "TX1N07C22750.TF-BestAsk1", "TX1N07C22750.TF-BestAsk2", "TX1N07C22750.TF-BestAsk3", "TX1N07C22750.TF-BestAsk4", "TX1N07C22750.TF-BestAsk5", "TX1N07C22750.TF-BestBidSize1", "TX1N07C22750.TF-BestBidSize2", "TX1N07C22750.TF-BestBidSize3", "TX1N07C22750.TF-BestBidSize4", "TX1N07C22750.TF-BestBidSize5", "TX1N07C22750.TF-BestAskSize1", "TX1N07C22750.TF-BestAskSize2", "TX1N07C22750.TF-BestAskSize3", "TX1N07C22750.TF-BestAskSize4", "TX1N07C22750.TF-BestAskSize5", "TX1N07C22750.TF-Name", "TX1N07C22750.TF-WContractDate", "TX1N07C22750.TF-SettlePrice", "TX1N07C22750.TF-UpLimit", "TX1N07C22750.TF-DownLimit", "TX1N07C22750.TF-OI", "TX1N07C22750.TF-TradingDate", "TX1N07C22750.TF-WRemainDate", "TX1N07C22750.TF-PreClose", "TX1N07C22750.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22750", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22750.TF-Time", "TX1N07P22750.TF-TradingDate", "TX1N07P22750.TF-Open", "TX1N07P22750.TF-High", "TX1N07P22750.TF-Low", "TX1N07P22750.TF-Price", "TX1N07P22750.TF-TotalVolume", "TX1N07P22750.TF-Volume", "TX1N07P22750.TF-NWTotalBidContract", "TX1N07P22750.TF-NWTotalAskContract", "TX1N07P22750.TF-NWTotalBidSize", "TX1N07P22750.TF-NWTotalAskSize", "TX1N07P22750.TF-InSize", "TX1N07P22750.TF-OutSize", "TX1N07P22750.TF-TotalBidMatchTx", "TX1N07P22750.TF-TotalAskMatchTx", "TX1N07P22750.TF-BestBid1", "TX1N07P22750.TF-BestBid2", "TX1N07P22750.TF-BestBid3", "TX1N07P22750.TF-BestBid4", "TX1N07P22750.TF-BestBid5", "TX1N07P22750.TF-BestAsk1", "TX1N07P22750.TF-BestAsk2", "TX1N07P22750.TF-BestAsk3", "TX1N07P22750.TF-BestAsk4", "TX1N07P22750.TF-BestAsk5", "TX1N07P22750.TF-BestBidSize1", "TX1N07P22750.TF-BestBidSize2", "TX1N07P22750.TF-BestBidSize3", "TX1N07P22750.TF-BestBidSize4", "TX1N07P22750.TF-BestBidSize5", "TX1N07P22750.TF-BestAskSize1", "TX1N07P22750.TF-BestAskSize2", "TX1N07P22750.TF-BestAskSize3", "TX1N07P22750.TF-BestAskSize4", "TX1N07P22750.TF-BestAskSize5", "TX1N07P22750.TF-Name", "TX1N07P22750.TF-WContractDate", "TX1N07P22750.TF-SettlePrice", "TX1N07P22750.TF-UpLimit", "TX1N07P22750.TF-DownLimit", "TX1N07P22750.TF-OI", "TX1N07P22750.TF-TradingDate", "TX1N07P22750.TF-WRemainDate", "TX1N07P22750.TF-PreClose", "TX1N07P22750.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22800", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22800.TF-Time", "TX1N07C22800.TF-TradingDate", "TX1N07C22800.TF-Open", "TX1N07C22800.TF-High", "TX1N07C22800.TF-Low", "TX1N07C22800.TF-Price", "TX1N07C22800.TF-TotalVolume", "TX1N07C22800.TF-Volume", "TX1N07C22800.TF-NWTotalBidContract", "TX1N07C22800.TF-NWTotalAskContract", "TX1N07C22800.TF-NWTotalBidSize", "TX1N07C22800.TF-NWTotalAskSize", "TX1N07C22800.TF-InSize", "TX1N07C22800.TF-OutSize", "TX1N07C22800.TF-TotalBidMatchTx", "TX1N07C22800.TF-TotalAskMatchTx", "TX1N07C22800.TF-BestBid1", "TX1N07C22800.TF-BestBid2", "TX1N07C22800.TF-BestBid3", "TX1N07C22800.TF-BestBid4", "TX1N07C22800.TF-BestBid5", "TX1N07C22800.TF-BestAsk1", "TX1N07C22800.TF-BestAsk2", "TX1N07C22800.TF-BestAsk3", "TX1N07C22800.TF-BestAsk4", "TX1N07C22800.TF-BestAsk5", "TX1N07C22800.TF-BestBidSize1", "TX1N07C22800.TF-BestBidSize2", "TX1N07C22800.TF-BestBidSize3", "TX1N07C22800.TF-BestBidSize4", "TX1N07C22800.TF-BestBidSize5", "TX1N07C22800.TF-BestAskSize1", "TX1N07C22800.TF-BestAskSize2", "TX1N07C22800.TF-BestAskSize3", "TX1N07C22800.TF-BestAskSize4", "TX1N07C22800.TF-BestAskSize5", "TX1N07C22800.TF-Name", "TX1N07C22800.TF-WContractDate", "TX1N07C22800.TF-SettlePrice", "TX1N07C22800.TF-UpLimit", "TX1N07C22800.TF-DownLimit", "TX1N07C22800.TF-OI", "TX1N07C22800.TF-TradingDate", "TX1N07C22800.TF-WRemainDate", "TX1N07C22800.TF-PreClose", "TX1N07C22800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22800", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22800.TF-Time", "TX1N07P22800.TF-TradingDate", "TX1N07P22800.TF-Open", "TX1N07P22800.TF-High", "TX1N07P22800.TF-Low", "TX1N07P22800.TF-Price", "TX1N07P22800.TF-TotalVolume", "TX1N07P22800.TF-Volume", "TX1N07P22800.TF-NWTotalBidContract", "TX1N07P22800.TF-NWTotalAskContract", "TX1N07P22800.TF-NWTotalBidSize", "TX1N07P22800.TF-NWTotalAskSize", "TX1N07P22800.TF-InSize", "TX1N07P22800.TF-OutSize", "TX1N07P22800.TF-TotalBidMatchTx", "TX1N07P22800.TF-TotalAskMatchTx", "TX1N07P22800.TF-BestBid1", "TX1N07P22800.TF-BestBid2", "TX1N07P22800.TF-BestBid3", "TX1N07P22800.TF-BestBid4", "TX1N07P22800.TF-BestBid5", "TX1N07P22800.TF-BestAsk1", "TX1N07P22800.TF-BestAsk2", "TX1N07P22800.TF-BestAsk3", "TX1N07P22800.TF-BestAsk4", "TX1N07P22800.TF-BestAsk5", "TX1N07P22800.TF-BestBidSize1", "TX1N07P22800.TF-BestBidSize2", "TX1N07P22800.TF-BestBidSize3", "TX1N07P22800.TF-BestBidSize4", "TX1N07P22800.TF-BestBidSize5", "TX1N07P22800.TF-BestAskSize1", "TX1N07P22800.TF-BestAskSize2", "TX1N07P22800.TF-BestAskSize3", "TX1N07P22800.TF-BestAskSize4", "TX1N07P22800.TF-BestAskSize5", "TX1N07P22800.TF-Name", "TX1N07P22800.TF-WContractDate", "TX1N07P22800.TF-SettlePrice", "TX1N07P22800.TF-UpLimit", "TX1N07P22800.TF-DownLimit", "TX1N07P22800.TF-OI", "TX1N07P22800.TF-TradingDate", "TX1N07P22800.TF-WRemainDate", "TX1N07P22800.TF-PreClose", "TX1N07P22800.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22850", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22850.TF-Time", "TX1N07C22850.TF-TradingDate", "TX1N07C22850.TF-Open", "TX1N07C22850.TF-High", "TX1N07C22850.TF-Low", "TX1N07C22850.TF-Price", "TX1N07C22850.TF-TotalVolume", "TX1N07C22850.TF-Volume", "TX1N07C22850.TF-NWTotalBidContract", "TX1N07C22850.TF-NWTotalAskContract", "TX1N07C22850.TF-NWTotalBidSize", "TX1N07C22850.TF-NWTotalAskSize", "TX1N07C22850.TF-InSize", "TX1N07C22850.TF-OutSize", "TX1N07C22850.TF-TotalBidMatchTx", "TX1N07C22850.TF-TotalAskMatchTx", "TX1N07C22850.TF-BestBid1", "TX1N07C22850.TF-BestBid2", "TX1N07C22850.TF-BestBid3", "TX1N07C22850.TF-BestBid4", "TX1N07C22850.TF-BestBid5", "TX1N07C22850.TF-BestAsk1", "TX1N07C22850.TF-BestAsk2", "TX1N07C22850.TF-BestAsk3", "TX1N07C22850.TF-BestAsk4", "TX1N07C22850.TF-BestAsk5", "TX1N07C22850.TF-BestBidSize1", "TX1N07C22850.TF-BestBidSize2", "TX1N07C22850.TF-BestBidSize3", "TX1N07C22850.TF-BestBidSize4", "TX1N07C22850.TF-BestBidSize5", "TX1N07C22850.TF-BestAskSize1", "TX1N07C22850.TF-BestAskSize2", "TX1N07C22850.TF-BestAskSize3", "TX1N07C22850.TF-BestAskSize4", "TX1N07C22850.TF-BestAskSize5", "TX1N07C22850.TF-Name", "TX1N07C22850.TF-WContractDate", "TX1N07C22850.TF-SettlePrice", "TX1N07C22850.TF-UpLimit", "TX1N07C22850.TF-DownLimit", "TX1N07C22850.TF-OI", "TX1N07C22850.TF-TradingDate", "TX1N07C22850.TF-WRemainDate", "TX1N07C22850.TF-PreClose", "TX1N07C22850.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22850", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22850.TF-Time", "TX1N07P22850.TF-TradingDate", "TX1N07P22850.TF-Open", "TX1N07P22850.TF-High", "TX1N07P22850.TF-Low", "TX1N07P22850.TF-Price", "TX1N07P22850.TF-TotalVolume", "TX1N07P22850.TF-Volume", "TX1N07P22850.TF-NWTotalBidContract", "TX1N07P22850.TF-NWTotalAskContract", "TX1N07P22850.TF-NWTotalBidSize", "TX1N07P22850.TF-NWTotalAskSize", "TX1N07P22850.TF-InSize", "TX1N07P22850.TF-OutSize", "TX1N07P22850.TF-TotalBidMatchTx", "TX1N07P22850.TF-TotalAskMatchTx", "TX1N07P22850.TF-BestBid1", "TX1N07P22850.TF-BestBid2", "TX1N07P22850.TF-BestBid3", "TX1N07P22850.TF-BestBid4", "TX1N07P22850.TF-BestBid5", "TX1N07P22850.TF-BestAsk1", "TX1N07P22850.TF-BestAsk2", "TX1N07P22850.TF-BestAsk3", "TX1N07P22850.TF-BestAsk4", "TX1N07P22850.TF-BestAsk5", "TX1N07P22850.TF-BestBidSize1", "TX1N07P22850.TF-BestBidSize2", "TX1N07P22850.TF-BestBidSize3", "TX1N07P22850.TF-BestBidSize4", "TX1N07P22850.TF-BestBidSize5", "TX1N07P22850.TF-BestAskSize1", "TX1N07P22850.TF-BestAskSize2", "TX1N07P22850.TF-BestAskSize3", "TX1N07P22850.TF-BestAskSize4", "TX1N07P22850.TF-BestAskSize5", "TX1N07P22850.TF-Name", "TX1N07P22850.TF-WContractDate", "TX1N07P22850.TF-SettlePrice", "TX1N07P22850.TF-UpLimit", "TX1N07P22850.TF-DownLimit", "TX1N07P22850.TF-OI", "TX1N07P22850.TF-TradingDate", "TX1N07P22850.TF-WRemainDate", "TX1N07P22850.TF-PreClose", "TX1N07P22850.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22900", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22900.TF-Time", "TX1N07C22900.TF-TradingDate", "TX1N07C22900.TF-Open", "TX1N07C22900.TF-High", "TX1N07C22900.TF-Low", "TX1N07C22900.TF-Price", "TX1N07C22900.TF-TotalVolume", "TX1N07C22900.TF-Volume", "TX1N07C22900.TF-NWTotalBidContract", "TX1N07C22900.TF-NWTotalAskContract", "TX1N07C22900.TF-NWTotalBidSize", "TX1N07C22900.TF-NWTotalAskSize", "TX1N07C22900.TF-InSize", "TX1N07C22900.TF-OutSize", "TX1N07C22900.TF-TotalBidMatchTx", "TX1N07C22900.TF-TotalAskMatchTx", "TX1N07C22900.TF-BestBid1", "TX1N07C22900.TF-BestBid2", "TX1N07C22900.TF-BestBid3", "TX1N07C22900.TF-BestBid4", "TX1N07C22900.TF-BestBid5", "TX1N07C22900.TF-BestAsk1", "TX1N07C22900.TF-BestAsk2", "TX1N07C22900.TF-BestAsk3", "TX1N07C22900.TF-BestAsk4", "TX1N07C22900.TF-BestAsk5", "TX1N07C22900.TF-BestBidSize1", "TX1N07C22900.TF-BestBidSize2", "TX1N07C22900.TF-BestBidSize3", "TX1N07C22900.TF-BestBidSize4", "TX1N07C22900.TF-BestBidSize5", "TX1N07C22900.TF-BestAskSize1", "TX1N07C22900.TF-BestAskSize2", "TX1N07C22900.TF-BestAskSize3", "TX1N07C22900.TF-BestAskSize4", "TX1N07C22900.TF-BestAskSize5", "TX1N07C22900.TF-Name", "TX1N07C22900.TF-WContractDate", "TX1N07C22900.TF-SettlePrice", "TX1N07C22900.TF-UpLimit", "TX1N07C22900.TF-DownLimit", "TX1N07C22900.TF-OI", "TX1N07C22900.TF-TradingDate", "TX1N07C22900.TF-WRemainDate", "TX1N07C22900.TF-PreClose", "TX1N07C22900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22900", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22900.TF-Time", "TX1N07P22900.TF-TradingDate", "TX1N07P22900.TF-Open", "TX1N07P22900.TF-High", "TX1N07P22900.TF-Low", "TX1N07P22900.TF-Price", "TX1N07P22900.TF-TotalVolume", "TX1N07P22900.TF-Volume", "TX1N07P22900.TF-NWTotalBidContract", "TX1N07P22900.TF-NWTotalAskContract", "TX1N07P22900.TF-NWTotalBidSize", "TX1N07P22900.TF-NWTotalAskSize", "TX1N07P22900.TF-InSize", "TX1N07P22900.TF-OutSize", "TX1N07P22900.TF-TotalBidMatchTx", "TX1N07P22900.TF-TotalAskMatchTx", "TX1N07P22900.TF-BestBid1", "TX1N07P22900.TF-BestBid2", "TX1N07P22900.TF-BestBid3", "TX1N07P22900.TF-BestBid4", "TX1N07P22900.TF-BestBid5", "TX1N07P22900.TF-BestAsk1", "TX1N07P22900.TF-BestAsk2", "TX1N07P22900.TF-BestAsk3", "TX1N07P22900.TF-BestAsk4", "TX1N07P22900.TF-BestAsk5", "TX1N07P22900.TF-BestBidSize1", "TX1N07P22900.TF-BestBidSize2", "TX1N07P22900.TF-BestBidSize3", "TX1N07P22900.TF-BestBidSize4", "TX1N07P22900.TF-BestBidSize5", "TX1N07P22900.TF-BestAskSize1", "TX1N07P22900.TF-BestAskSize2", "TX1N07P22900.TF-BestAskSize3", "TX1N07P22900.TF-BestAskSize4", "TX1N07P22900.TF-BestAskSize5", "TX1N07P22900.TF-Name", "TX1N07P22900.TF-WContractDate", "TX1N07P22900.TF-SettlePrice", "TX1N07P22900.TF-UpLimit", "TX1N07P22900.TF-DownLimit", "TX1N07P22900.TF-OI", "TX1N07P22900.TF-TradingDate", "TX1N07P22900.TF-WRemainDate", "TX1N07P22900.TF-PreClose", "TX1N07P22900.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C22950", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C22950.TF-Time", "TX1N07C22950.TF-TradingDate", "TX1N07C22950.TF-Open", "TX1N07C22950.TF-High", "TX1N07C22950.TF-Low", "TX1N07C22950.TF-Price", "TX1N07C22950.TF-TotalVolume", "TX1N07C22950.TF-Volume", "TX1N07C22950.TF-NWTotalBidContract", "TX1N07C22950.TF-NWTotalAskContract", "TX1N07C22950.TF-NWTotalBidSize", "TX1N07C22950.TF-NWTotalAskSize", "TX1N07C22950.TF-InSize", "TX1N07C22950.TF-OutSize", "TX1N07C22950.TF-TotalBidMatchTx", "TX1N07C22950.TF-TotalAskMatchTx", "TX1N07C22950.TF-BestBid1", "TX1N07C22950.TF-BestBid2", "TX1N07C22950.TF-BestBid3", "TX1N07C22950.TF-BestBid4", "TX1N07C22950.TF-BestBid5", "TX1N07C22950.TF-BestAsk1", "TX1N07C22950.TF-BestAsk2", "TX1N07C22950.TF-BestAsk3", "TX1N07C22950.TF-BestAsk4", "TX1N07C22950.TF-BestAsk5", "TX1N07C22950.TF-BestBidSize1", "TX1N07C22950.TF-BestBidSize2", "TX1N07C22950.TF-BestBidSize3", "TX1N07C22950.TF-BestBidSize4", "TX1N07C22950.TF-BestBidSize5", "TX1N07C22950.TF-BestAskSize1", "TX1N07C22950.TF-BestAskSize2", "TX1N07C22950.TF-BestAskSize3", "TX1N07C22950.TF-BestAskSize4", "TX1N07C22950.TF-BestAskSize5", "TX1N07C22950.TF-Name", "TX1N07C22950.TF-WContractDate", "TX1N07C22950.TF-SettlePrice", "TX1N07C22950.TF-UpLimit", "TX1N07C22950.TF-DownLimit", "TX1N07C22950.TF-OI", "TX1N07C22950.TF-TradingDate", "TX1N07C22950.TF-WRemainDate", "TX1N07C22950.TF-PreClose", "TX1N07C22950.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P22950", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P22950.TF-Time", "TX1N07P22950.TF-TradingDate", "TX1N07P22950.TF-Open", "TX1N07P22950.TF-High", "TX1N07P22950.TF-Low", "TX1N07P22950.TF-Price", "TX1N07P22950.TF-TotalVolume", "TX1N07P22950.TF-Volume", "TX1N07P22950.TF-NWTotalBidContract", "TX1N07P22950.TF-NWTotalAskContract", "TX1N07P22950.TF-NWTotalBidSize", "TX1N07P22950.TF-NWTotalAskSize", "TX1N07P22950.TF-InSize", "TX1N07P22950.TF-OutSize", "TX1N07P22950.TF-TotalBidMatchTx", "TX1N07P22950.TF-TotalAskMatchTx", "TX1N07P22950.TF-BestBid1", "TX1N07P22950.TF-BestBid2", "TX1N07P22950.TF-BestBid3", "TX1N07P22950.TF-BestBid4", "TX1N07P22950.TF-BestBid5", "TX1N07P22950.TF-BestAsk1", "TX1N07P22950.TF-BestAsk2", "TX1N07P22950.TF-BestAsk3", "TX1N07P22950.TF-BestAsk4", "TX1N07P22950.TF-BestAsk5", "TX1N07P22950.TF-BestBidSize1", "TX1N07P22950.TF-BestBidSize2", "TX1N07P22950.TF-BestBidSize3", "TX1N07P22950.TF-BestBidSize4", "TX1N07P22950.TF-BestBidSize5", "TX1N07P22950.TF-BestAskSize1", "TX1N07P22950.TF-BestAskSize2", "TX1N07P22950.TF-BestAskSize3", "TX1N07P22950.TF-BestAskSize4", "TX1N07P22950.TF-BestAskSize5", "TX1N07P22950.TF-Name", "TX1N07P22950.TF-WContractDate", "TX1N07P22950.TF-SettlePrice", "TX1N07P22950.TF-UpLimit", "TX1N07P22950.TF-DownLimit", "TX1N07P22950.TF-OI", "TX1N07P22950.TF-TradingDate", "TX1N07P22950.TF-WRemainDate", "TX1N07P22950.TF-PreClose", "TX1N07P22950.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C23000", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C23000.TF-Time", "TX1N07C23000.TF-TradingDate", "TX1N07C23000.TF-Open", "TX1N07C23000.TF-High", "TX1N07C23000.TF-Low", "TX1N07C23000.TF-Price", "TX1N07C23000.TF-TotalVolume", "TX1N07C23000.TF-Volume", "TX1N07C23000.TF-NWTotalBidContract", "TX1N07C23000.TF-NWTotalAskContract", "TX1N07C23000.TF-NWTotalBidSize", "TX1N07C23000.TF-NWTotalAskSize", "TX1N07C23000.TF-InSize", "TX1N07C23000.TF-OutSize", "TX1N07C23000.TF-TotalBidMatchTx", "TX1N07C23000.TF-TotalAskMatchTx", "TX1N07C23000.TF-BestBid1", "TX1N07C23000.TF-BestBid2", "TX1N07C23000.TF-BestBid3", "TX1N07C23000.TF-BestBid4", "TX1N07C23000.TF-BestBid5", "TX1N07C23000.TF-BestAsk1", "TX1N07C23000.TF-BestAsk2", "TX1N07C23000.TF-BestAsk3", "TX1N07C23000.TF-BestAsk4", "TX1N07C23000.TF-BestAsk5", "TX1N07C23000.TF-BestBidSize1", "TX1N07C23000.TF-BestBidSize2", "TX1N07C23000.TF-BestBidSize3", "TX1N07C23000.TF-BestBidSize4", "TX1N07C23000.TF-BestBidSize5", "TX1N07C23000.TF-BestAskSize1", "TX1N07C23000.TF-BestAskSize2", "TX1N07C23000.TF-BestAskSize3", "TX1N07C23000.TF-BestAskSize4", "TX1N07C23000.TF-BestAskSize5", "TX1N07C23000.TF-Name", "TX1N07C23000.TF-WContractDate", "TX1N07C23000.TF-SettlePrice", "TX1N07C23000.TF-UpLimit", "TX1N07C23000.TF-DownLimit", "TX1N07C23000.TF-OI", "TX1N07C23000.TF-TradingDate", "TX1N07C23000.TF-WRemainDate", "TX1N07C23000.TF-PreClose", "TX1N07C23000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P23000", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P23000.TF-Time", "TX1N07P23000.TF-TradingDate", "TX1N07P23000.TF-Open", "TX1N07P23000.TF-High", "TX1N07P23000.TF-Low", "TX1N07P23000.TF-Price", "TX1N07P23000.TF-TotalVolume", "TX1N07P23000.TF-Volume", "TX1N07P23000.TF-NWTotalBidContract", "TX1N07P23000.TF-NWTotalAskContract", "TX1N07P23000.TF-NWTotalBidSize", "TX1N07P23000.TF-NWTotalAskSize", "TX1N07P23000.TF-InSize", "TX1N07P23000.TF-OutSize", "TX1N07P23000.TF-TotalBidMatchTx", "TX1N07P23000.TF-TotalAskMatchTx", "TX1N07P23000.TF-BestBid1", "TX1N07P23000.TF-BestBid2", "TX1N07P23000.TF-BestBid3", "TX1N07P23000.TF-BestBid4", "TX1N07P23000.TF-BestBid5", "TX1N07P23000.TF-BestAsk1", "TX1N07P23000.TF-BestAsk2", "TX1N07P23000.TF-BestAsk3", "TX1N07P23000.TF-BestAsk4", "TX1N07P23000.TF-BestAsk5", "TX1N07P23000.TF-BestBidSize1", "TX1N07P23000.TF-BestBidSize2", "TX1N07P23000.TF-BestBidSize3", "TX1N07P23000.TF-BestBidSize4", "TX1N07P23000.TF-BestBidSize5", "TX1N07P23000.TF-BestAskSize1", "TX1N07P23000.TF-BestAskSize2", "TX1N07P23000.TF-BestAskSize3", "TX1N07P23000.TF-BestAskSize4", "TX1N07P23000.TF-BestAskSize5", "TX1N07P23000.TF-Name", "TX1N07P23000.TF-WContractDate", "TX1N07P23000.TF-SettlePrice", "TX1N07P23000.TF-UpLimit", "TX1N07P23000.TF-DownLimit", "TX1N07P23000.TF-OI", "TX1N07P23000.TF-TradingDate", "TX1N07P23000.TF-WRemainDate", "TX1N07P23000.TF-PreClose", "TX1N07P23000.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C23050", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C23050.TF-Time", "TX1N07C23050.TF-TradingDate", "TX1N07C23050.TF-Open", "TX1N07C23050.TF-High", "TX1N07C23050.TF-Low", "TX1N07C23050.TF-Price", "TX1N07C23050.TF-TotalVolume", "TX1N07C23050.TF-Volume", "TX1N07C23050.TF-NWTotalBidContract", "TX1N07C23050.TF-NWTotalAskContract", "TX1N07C23050.TF-NWTotalBidSize", "TX1N07C23050.TF-NWTotalAskSize", "TX1N07C23050.TF-InSize", "TX1N07C23050.TF-OutSize", "TX1N07C23050.TF-TotalBidMatchTx", "TX1N07C23050.TF-TotalAskMatchTx", "TX1N07C23050.TF-BestBid1", "TX1N07C23050.TF-BestBid2", "TX1N07C23050.TF-BestBid3", "TX1N07C23050.TF-BestBid4", "TX1N07C23050.TF-BestBid5", "TX1N07C23050.TF-BestAsk1", "TX1N07C23050.TF-BestAsk2", "TX1N07C23050.TF-BestAsk3", "TX1N07C23050.TF-BestAsk4", "TX1N07C23050.TF-BestAsk5", "TX1N07C23050.TF-BestBidSize1", "TX1N07C23050.TF-BestBidSize2", "TX1N07C23050.TF-BestBidSize3", "TX1N07C23050.TF-BestBidSize4", "TX1N07C23050.TF-BestBidSize5", "TX1N07C23050.TF-BestAskSize1", "TX1N07C23050.TF-BestAskSize2", "TX1N07C23050.TF-BestAskSize3", "TX1N07C23050.TF-BestAskSize4", "TX1N07C23050.TF-BestAskSize5", "TX1N07C23050.TF-Name", "TX1N07C23050.TF-WContractDate", "TX1N07C23050.TF-SettlePrice", "TX1N07C23050.TF-UpLimit", "TX1N07C23050.TF-DownLimit", "TX1N07C23050.TF-OI", "TX1N07C23050.TF-TradingDate", "TX1N07C23050.TF-WRemainDate", "TX1N07C23050.TF-PreClose", "TX1N07C23050.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07P23050", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07P23050.TF-Time", "TX1N07P23050.TF-TradingDate", "TX1N07P23050.TF-Open", "TX1N07P23050.TF-High", "TX1N07P23050.TF-Low", "TX1N07P23050.TF-Price", "TX1N07P23050.TF-TotalVolume", "TX1N07P23050.TF-Volume", "TX1N07P23050.TF-NWTotalBidContract", "TX1N07P23050.TF-NWTotalAskContract", "TX1N07P23050.TF-NWTotalBidSize", "TX1N07P23050.TF-NWTotalAskSize", "TX1N07P23050.TF-InSize", "TX1N07P23050.TF-OutSize", "TX1N07P23050.TF-TotalBidMatchTx", "TX1N07P23050.TF-TotalAskMatchTx", "TX1N07P23050.TF-BestBid1", "TX1N07P23050.TF-BestBid2", "TX1N07P23050.TF-BestBid3", "TX1N07P23050.TF-BestBid4", "TX1N07P23050.TF-BestBid5", "TX1N07P23050.TF-BestAsk1", "TX1N07P23050.TF-BestAsk2", "TX1N07P23050.TF-BestAsk3", "TX1N07P23050.TF-BestAsk4", "TX1N07P23050.TF-BestAsk5", "TX1N07P23050.TF-BestBidSize1", "TX1N07P23050.TF-BestBidSize2", "TX1N07P23050.TF-BestBidSize3", "TX1N07P23050.TF-BestBidSize4", "TX1N07P23050.TF-BestBidSize5", "TX1N07P23050.TF-BestAskSize1", "TX1N07P23050.TF-BestAskSize2", "TX1N07P23050.TF-BestAskSize3", "TX1N07P23050.TF-BestAskSize4", "TX1N07P23050.TF-BestAskSize5", "TX1N07P23050.TF-Name", "TX1N07P23050.TF-WContractDate", "TX1N07P23050.TF-SettlePrice", "TX1N07P23050.TF-UpLimit", "TX1N07P23050.TF-DownLimit", "TX1N07P23050.TF-OI", "TX1N07P23050.TF-TradingDate", "TX1N07P23050.TF-WRemainDate", "TX1N07P23050.TF-PreClose", "TX1N07P23050.TF-PreTotalVolume"], "enabled": true}, {"symbol": "TX1N07C23100", "service": "XQTISC", "topic": "Quote", "items": ["TX1N07C23100.TF-Time", "TX1N07C23100.TF-TradingDate", "TX1N07C23100.TF-Open", "TX1N07C23100.TF-High", "TX1N07C23100.TF-Low", "TX1N07C23100.TF-Price", "TX1N07C23100.TF-TotalVolume", "TX1N07C23100.TF-Volume", "TX1N07C23100.TF-NWTotalBidContract", "TX1N07C23100.TF-NWTotalAskContract", "TX1N07C23100.TF-NWTotalBidSize", "TX1N07C23100.TF-NWTotalAskSize", "TX1N07C23100.TF-InSize", "TX1N07C23100.TF-OutSize", "TX1N07C23100.TF-TotalBidMatchTx", "TX1N07C23100.TF-TotalAskMatchTx", "TX1N07C23100.TF-BestBid1", "TX1N07C23100.TF-BestBid2", "TX1N07C23100.TF-BestBid3", "TX1N07C23100.TF-BestBid4", "TX1N07C23100.TF-BestBid5", "TX1N07C23100.TF-BestAsk1", "TX1N07C23100.TF-BestAsk2", "TX1N07C23100.TF-BestAsk3", "TX1N07C23100.TF-BestAsk4", "TX1N07C23100.TF-BestAsk5", "TX1N07C23100.TF-BestBidSize1", "TX1N07C23100.TF-BestBidSize2", "TX1N07C23100.TF-BestBidSize3", "TX1N07C23100.TF-BestBidSize4", "TX1N07C23100.TF-BestBidSize5", "TX1N07C23100.TF-BestAskSize1", "TX1N07C23100.TF-BestAskSize2", "TX1N07C23100.TF-BestAskSize3", "TX1N07C23100.TF-BestAskSize4", "TX1N07C23100.TF-BestAskSize5", "TX1N07C23100.TF-Name", "TX1N07C23100.TF-WContractDate", "TX1N07C23100.TF-SettlePrice", "TX1N07C23100.TF-UpLimit", "TX1N07C23100.TF-DownLimit", "TX1N07C23100.TF-OI", "TX1N07C23100.TF-TradingDate", "TX1N07C23100.TF-WRemainDate", "TX1N07C23100.TF-PreClose", "TX1N07C23100.TF-PreTotalVolume"], "enabled": true}]}