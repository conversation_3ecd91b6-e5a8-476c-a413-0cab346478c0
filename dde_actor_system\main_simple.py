#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE Actor System - 簡化版主程式

不依賴Qt的簡化版本，用於測試核心功能
"""

import asyncio
import argparse
import logging
import signal
import sys
import time
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from core.event_loop import EventLoopManager, EventLoopConfig
from core.performance import PerformanceMonitor
from actors.data_processor import DataProcessorActor
from actors.file_writer import FileWriterActor
from utils.config_manager import DynamicConfigManager
from utils.logger import setup_logger


class SimpleDDEReceiver:
    """簡化的DDE接收器（模擬）"""
    
    def __init__(self, message_router):
        self.message_router = message_router
        self.running = False
        self.task = None
        self.logger = logging.getLogger("SimpleDDEReceiver")
    
    async def start(self):
        """啟動模擬DDE接收"""
        self.running = True
        self.task = asyncio.create_task(self._simulate_dde_data())
        self.logger.info("模擬DDE接收器已啟動")
    
    async def stop(self):
        """停止模擬DDE接收"""
        self.running = False
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        self.logger.info("模擬DDE接收器已停止")
    
    async def _simulate_dde_data(self):
        """模擬DDE數據"""
        from core.message_system import create_dde_data_message
        
        counter = 0
        while self.running:
            try:
                # 模擬不同的DDE項目
                items = ["FITXN07.tick.成交價", "FITXN07.tick.成交量", "FITXN08.tick.成交價"]
                item = items[counter % len(items)]
                value = f"{15000 + (counter % 100)}"
                
                # 創建DDE數據消息
                message = create_dde_data_message(item, value, "SimpleDDEReceiver")
                
                # 發送到數據處理器
                await self.message_router.send_message("DataProcessor", message)
                
                counter += 1
                
                # 控制發送頻率 (每秒100筆)
                await asyncio.sleep(0.01)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"模擬DDE數據錯誤: {str(e)}")
                await asyncio.sleep(0.1)


class SimpleGUIUpdater:
    """簡化的GUI更新器（控制台輸出）"""
    
    def __init__(self):
        self.logger = logging.getLogger("SimpleGUIUpdater")
        self.update_count = 0
        self.last_display_time = time.time()
    
    async def handle_message(self, message):
        """處理GUI更新消息"""
        from core.message_system import MessageType
        
        if message.type == MessageType.GUI_BATCH_UPDATE:
            items = message.data.get('items', [])
            self.update_count += len(items)
            
            # 每5秒顯示一次統計
            current_time = time.time()
            if current_time - self.last_display_time >= 5.0:
                rate = self.update_count / (current_time - self.last_display_time)
                print(f"GUI更新統計: {self.update_count} 筆更新, 速率: {rate:.1f} 筆/秒")
                
                # 顯示最新的幾筆數據
                if items:
                    print("最新數據:")
                    for item in items[-3:]:  # 顯示最後3筆
                        print(f"  {item.get('item', '')}: {item.get('value', '')}")
                
                self.update_count = 0
                self.last_display_time = current_time


class SimpleDDEActorSystem:
    """簡化的DDE Actor系統"""
    
    def __init__(self, config_file: str):
        """初始化系統"""
        self.config_file = config_file
        self.logger = logging.getLogger("SimpleDDEActorSystem")
        
        # 核心組件
        self.config_manager = DynamicConfigManager(config_file)
        self.event_loop_manager = None
        self.performance_monitor = PerformanceMonitor()
        
        # 組件
        self.dde_receiver = None
        self.data_processor = None
        self.gui_updater = None
        self.file_writer = None
        
        # 系統狀態
        self.running = False
        self.startup_time = 0
    
    async def initialize(self) -> bool:
        """初始化系統"""
        try:
            self.logger.info("正在初始化簡化DDE Actor系統")
            
            # 加載配置
            if not self.config_manager.load_config():
                self.logger.error("加載配置文件失敗")
                return False
            
            # 設置日誌
            self._setup_logging()
            
            # 創建事件循環管理器
            loop_config = EventLoopConfig(
                max_workers=self.config_manager.performance_config.processing_workers,
                debug_mode=self.config_manager.system_config.debug_mode
            )
            self.event_loop_manager = EventLoopManager(loop_config)
            
            # 創建組件
            await self._create_components()
            
            # 設置消息路由
            await self._setup_message_routes()
            
            self.logger.info("簡化DDE Actor系統初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化系統失敗: {str(e)}")
            return False
    
    async def start(self) -> bool:
        """啟動系統"""
        try:
            if self.running:
                return False
            
            self.logger.info("正在啟動簡化DDE Actor系統")
            self.startup_time = time.time()
            
            # 啟動性能監控
            await self.performance_monitor.start()
            
            # 啟動事件循環管理器
            if not await self.event_loop_manager.start():
                return False
            
            # 啟動DDE接收器
            await self.dde_receiver.start()
            
            self.running = True
            
            # 顯示啟動信息
            self._show_startup_info()
            
            self.logger.info("簡化DDE Actor系統啟動成功")
            return True
            
        except Exception as e:
            self.logger.error(f"啟動系統失敗: {str(e)}")
            return False
    
    async def stop(self) -> bool:
        """停止系統"""
        try:
            if not self.running:
                return True
            
            self.logger.info("正在停止簡化DDE Actor系統")
            
            # 停止DDE接收器
            if self.dde_receiver:
                await self.dde_receiver.stop()
            
            # 停止事件循環管理器
            if self.event_loop_manager:
                await self.event_loop_manager.stop()
            
            # 停止性能監控
            await self.performance_monitor.stop()
            
            self.running = False
            
            # 顯示運行統計
            self._show_shutdown_info()
            
            self.logger.info("簡化DDE Actor系統停止成功")
            return True
            
        except Exception as e:
            self.logger.error(f"停止系統失敗: {str(e)}")
            return False
    
    async def run_forever(self):
        """運行系統直到收到停止信號"""
        try:
            if not await self.initialize():
                return False
            
            if not await self.start():
                return False
            
            # 運行事件循環
            await self.event_loop_manager.run_forever()
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("收到中斷信號")
            return True
        except Exception as e:
            self.logger.error(f"運行系統失敗: {str(e)}")
            return False
        finally:
            await self.stop()
    
    async def _create_components(self):
        """創建組件"""
        perf_config = self.config_manager.performance_config
        
        # 創建數據處理Actor
        processor_config = {
            'batch_size': perf_config.processing_batch_size,
            'queue_size': perf_config.processing_queue_size,
            'backpressure': {
                'high_watermark': perf_config.backpressure_high_watermark,
                'low_watermark': perf_config.backpressure_low_watermark,
                'strategy': perf_config.backpressure_strategy
            }
        }
        self.data_processor = DataProcessorActor('DataProcessor', processor_config)
        self.event_loop_manager.register_actor(self.data_processor)
        
        # 創建文件寫入Actor
        file_config = {
            'batch_size': perf_config.file_batch_size,
            'flush_interval': perf_config.file_flush_interval,
            'files': {
                'default': {
                    'filename': 'outputs/simple_dde_data.csv',
                    'format': 'csv',
                    'max_size_mb': 10,
                    'compress': False
                }
            }
        }
        self.file_writer = FileWriterActor('FileWriter', file_config)
        self.event_loop_manager.register_actor(self.file_writer)
        
        # 創建簡化組件
        self.dde_receiver = SimpleDDEReceiver(self.event_loop_manager.message_router)
        self.gui_updater = SimpleGUIUpdater()
        
        self.logger.info("組件創建完成")
    
    async def _setup_message_routes(self):
        """設置消息路由"""
        from core.message_system import MessageType
        
        router = self.event_loop_manager.message_router
        
        # 註冊GUI更新處理器
        async def gui_message_handler(message):
            await self.gui_updater.handle_message(message)
        
        # 手動處理GUI消息（因為不是真正的Actor）
        original_send = router.send_message
        
        async def enhanced_send(target, message):
            if target == "GUIUpdater":
                await gui_message_handler(message)
                return True
            else:
                return await original_send(target, message)
        
        router.send_message = enhanced_send
    
    def _setup_logging(self):
        """設置日誌"""
        system_config = self.config_manager.system_config
        
        setup_logger(
            level=system_config.log_level,
            log_file=system_config.log_file,
            max_size_mb=system_config.log_max_size_mb,
            backup_count=system_config.log_backup_count
        )
    
    def _show_startup_info(self):
        """顯示啟動信息"""
        uptime = time.time() - self.startup_time
        
        print("\n" + "=" * 60)
        print("簡化DDE Actor System - 高性能DDE數據處理系統")
        print("=" * 60)
        print(f"版本: {self.config_manager.system_config.version}")
        print(f"啟動時間: {uptime:.2f} 秒")
        print(f"配置文件: {self.config_file}")
        print(f"產品數量: {len(self.config_manager.product_configs)}")
        print(f"性能監控: 已啟用")
        print("=" * 60)
        print("系統已就緒，開始處理模擬DDE數據...")
        print("按 Ctrl+C 停止系統")
        print("=" * 60 + "\n")
    
    def _show_shutdown_info(self):
        """顯示關閉信息"""
        if self.startup_time > 0:
            uptime = time.time() - self.startup_time
            
            print("\n" + "=" * 60)
            print("系統關閉統計")
            print("=" * 60)
            print(f"運行時間: {uptime:.2f} 秒")
            
            # 顯示性能統計
            if self.performance_monitor:
                report = self.performance_monitor.get_performance_report()
                current_metrics = report.get('current_metrics')
                if current_metrics:
                    print(f"處理消息: {current_metrics.messages_per_second:.0f} 筆/秒")
                    print(f"平均延遲: {current_metrics.avg_latency_ms:.2f} ms")
                    print(f"內存使用: {current_metrics.memory_usage_mb:.1f} MB")
            
            print("=" * 60 + "\n")


def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='簡化DDE Actor System')
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/test_config.json',
        help='配置文件路径'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='啟用調試模式'
    )
    
    return parser.parse_args()


async def main():
    """主函數"""
    args = parse_arguments()
    
    # 創建系統實例
    system = SimpleDDEActorSystem(args.config)
    
    try:
        # 運行系統
        success = await system.run_forever()
        return 0 if success else 1
        
    except Exception as e:
        print(f"系統運行失敗: {str(e)}")
        return 1


if __name__ == "__main__":
    # 設置基本日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    
    # 運行系統
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
