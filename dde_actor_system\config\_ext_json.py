import json

list_symbols = ["FITXN07.TF", "FIMTXN07.TF", "FITMN07.TF", "FITXN08.TF", "FIMTXN08.TF", "FITMN08.TF", "FITX07.TF", "FIMTX07.TF", "FITM07.TF", "FITX08.TF", "FIMTX08.TF", "FITM08.TF"
, "2330.TW", "2317.TW"
, "TXoN07C22000.TF", "TXoN07C22100.TF", "TXoN07C22200.TF", "TXoN07C22300.TF", "TXoN07C22400.TF", "TXoN07C22500.TF", "TXoN07C22600.TF", "TXoN07C22700.TF", "TXoN07C22800.TF", "TXoN07C22900.TF"
, "TXoN07C23000.TF", "TXoN07C23100.TF", "TXoN07C23200.TF", "TXoN07C23300.TF", "TXoN07C23400.TF", "TXoN07C23500.TF", "TXoN07C23600.TF", "TXoN07C23700.TF", "TXoN07C23800.TF", "TXoN07C23900.TF"
, "TXoN07C24000.TF", "TXoN07C24100.TF", "TXoN07C24200.TF", "TXoN07C24300.TF", "TXoN07C24400.TF", "TXoN07C24500.TF", "TXoN07C24600.TF", "TXoN07C24700.TF", "TXoN07C24800.TF", "TXoN07C24900.TF"
, "TXoN07C25000.TF", "TXoN07C25100.TF", "TXoN07C25200.TF", "TXoN07C25300.TF", "TXoN07C25400.TF", "TXoN07C25500.TF", "TXoN07C25600.TF", "TXoN07C25700.TF", "TXoN07C25800.TF", "TXoN07C25900.TF"
, "TXoN07P22000.TF", "TXoN07P22100.TF", "TXoN07P22200.TF", "TXoN07P22300.TF", "TXoN07P22400.TF", "TXoN07P22500.TF", "TXoN07P22600.TF", "TXoN07P22700.TF", "TXoN07P22800.TF", "TXoN07P22900.TF"
, "TXoN07P23000.TF", "TXoN07P23100.TF", "TXoN07P23200.TF", "TXoN07P23300.TF", "TXoN07P23400.TF", "TXoN07P23500.TF", "TXoN07P23600.TF", "TXoN07P23700.TF", "TXoN07P23800.TF", "TXoN07P23900.TF"
, "TXoN07P24000.TF", "TXoN07P24100.TF", "TXoN07P24200.TF", "TXoN07P24300.TF", "TXoN07P24400.TF", "TXoN07P24500.TF", "TXoN07P24600.TF", "TXoN07P24700.TF", "TXoN07P24800.TF", "TXoN07P24900.TF"
, "TXoN07P25000.TF", "TXoN07P25100.TF", "TXoN07P25200.TF", "TXoN07P25300.TF", "TXoN07P25400.TF", "TXoN07P25500.TF", "TXoN07P25600.TF", "TXoN07P25700.TF", "TXoN07P25800.TF", "TXoN07P25900.TF"
]

list_itemCode = [
    "Time", "TradingDate", "Open", "High", "Low", "Price", "TotalVolume", "Volume",
    "NWTotalBidContract", "NWTotalAskContract", "NWTotalBidSize", "NWTotalAskSize",
    "InSize", "OutSize", "TotalBidMatchTx", "TotalAskMatchTx",
    "BestBid1", "BestBid2", "BestBid3", "BestBid4", "BestBid5",
    "BestAsk1", "BestAsk2", "BestAsk3", "BestAsk4", "BestAsk5",
    "BestBidSize1", "BestBidSize2", "BestBidSize3", "BestBidSize4", "BestBidSize5",
    "BestAskSize1", "BestAskSize2", "BestAskSize3", "BestAskSize4", "BestAskSize5",
    "Name", "WContractDate", "SettlePrice", "UpLimit", "DownLimit", "OI",
    "TradingDate", "WRemainDate", "PreClose", "PreTotalVolume"
]

output = []

for full_symbol in list_symbols:
    parts = full_symbol.split('.')
    if len(parts) == 2:
        base_symbol, suffix = parts
        entry = {
            "symbol": base_symbol,
            "service": "XQTISC",
            "topic": "Quote",
            "items": [f"{base_symbol}.{suffix}-{code}" for code in list_itemCode],
            "enabled": True
        }
        output.append(entry)
    else:
        print(f"⚠️ 無法解析 symbol: {full_symbol}")

with open("output.json", "w", encoding="utf-8") as f:
    json.dump(output, f, indent=2, ensure_ascii=False)

print("✅ output.json 已完成，已根據每個 symbol 動態調整 item 格式。")
