# DDE 引擎版架構概覽

## 目錄結構

```
dde_data_engine/
├── docs/                           # 文件目錄
│   ├── DATA_PROCESSING_FLOW.md     # 資料處理流程文件
│   └── ARCHITECTURE_OVERVIEW.md    # 架構概覽文件
├── dydde/                          # DDE 核心模組
│   ├── __init__.py
│   ├── dydde.py                    # DDE 連接和訂閱功能
│   ├── dydde_manual.txt            # DDE 使用手冊
│   └── dydde_request_usage_guide.txt # DDE 請求使用指南
├── __init__.py                     # 引擎模組初始化
├── config_handler.py               # 配置處理器
├── data_processor.py               # 核心資料處理器
├── data_structures.py              # 資料結構定義
├── file_handler.py                 # 檔案處理器
├── example.py                      # 使用範例
├── README.md                       # 專案說明
└── INTEGRATION_GUIDE.md            # 整合指南
```

## 核心模組架構

### 1. DDEDataProcessor (data_processor.py)
**核心資料處理器**
- **職責**: 處理所有DDE資料的接收、處理、過濾和輸出邏輯
- **主要功能**:
  - DDE資料接收和初始值建立
  - 資料補全和完整行建立
  - 時間間隔和重複項目檢查
  - 值變化檢測和有效行識別
  - 資料輸出和記錄

**關鍵方法**:
```python
process_dde_data(item, value)      # 主要資料處理入口
check_time_interval()              # 時間間隔檢查
set_initial_values(values)         # 設置初始值
get_current_data()                 # 獲取當前資料
get_stats()                        # 獲取統計資訊
```

### 2. DataFileHandler (file_handler.py)
**檔案處理器**
- **職責**: 處理所有檔案輸出功能
- **支援格式**:
  - CSV 完整資料檔案
  - CSV 逐項資料檔案
  - 純文字日誌檔案

**關鍵方法**:
```python
init_file_paths(log, data, complete)  # 初始化檔案路徑
save_row(row, is_complete)            # 保存資料行
init_file_headers()                   # 初始化檔案標題
clear_files()                         # 清空檔案
```

### 3. DataProcessorConfig (config_handler.py)
**配置管理器**
- **職責**: 管理所有配置參數和驗證
- **配置類型**:
  - 時間間隔設定
  - 值變化檢查設定
  - 檔案輸出設定
  - 資料處理設定

**關鍵方法**:
```python
get(key, default)                  # 獲取配置值
get_bool(key, default)             # 獲取布林配置
get_float(key, default)            # 獲取浮點配置
validate()                         # 驗證配置
```

### 4. 資料結構 (data_structures.py)
**核心資料結構定義**

#### ItemData - DDE項目資料
```python
@dataclass
class ItemData:
    name: str                      # 項目顯示名稱
    code: str                      # DDE項目代碼
    value: Optional[str] = None    # 當前值
    update_time: Optional[datetime] = None  # 最後更新時間
    status: str = "未訂閱"         # 訂閱狀態
```

#### RawDataRow - 原始資料行
```python
@dataclass
class RawDataRow:
    receive_date: str              # 接收日期
    receive_time: str              # 接收時間
    values: Dict[str, str]         # 項目值字典
    is_complete: bool = False      # 是否完整
```

#### ProcessorStats - 處理統計
```python
@dataclass
class ProcessorStats:
    total_received: int = 0        # 總接收筆數
    total_processed: int = 0       # 總處理筆數
    total_saved: int = 0           # 總保存筆數
    total_skipped: int = 0         # 總跳過筆數
    last_update_time: Optional[datetime] = None
```

## 資料流架構

### 資料處理管道
```
DDE資料輸入 → DDEDataProcessor → 資料處理邏輯 → DataFileHandler → 檔案輸出
     ↓              ↓                    ↓              ↓
  統計更新    → 配置管理        → 回調觸發    → 多格式輸出
     ↓              ↓                    ↓              ↓
  錯誤處理    → 參數驗證        → 事件通知    → 檔案管理
```

### 處理流程圖
```
開始
 ↓
接收DDE資料 (process_dde_data)
 ↓
項目重複檢查 (_check_item_repeat_newline)
 ↓
是否重複? → 是 → 補齊資料 → 值變化檢查 → 保存/跳過 → 建立新行
 ↓ 否
更新原始資料 (_update_raw_data)
 ↓
更新項目資料 (_update_items_data)
 ↓
更新時間記錄
 ↓
結束
```

### 時間間隔檢查流程
```
定時檢查 (check_time_interval)
 ↓
時間間隔判斷
 ↓
間隔到達? → 否 → 結束
 ↓ 是
檢查當前行資料
 ↓
有資料? → 否 → 結束
 ↓ 是
補齊缺失資料
 ↓
值變化檢查
 ↓
有變化? → 是 → 保存資料行
 ↓ 否      ↓
跳過資料行  建立新行
 ↓         ↓
建立新行    更新時間
 ↓         ↓
更新時間    結束
 ↓
結束
```

## 設計模式

### 1. 策略模式 (Strategy Pattern)
**值變化檢查策略**
- SingleCheckStrategy: 單項檢查
- MultipleCheckStrategy: 多項檢查  
- AllCheckStrategy: 全部檢查

### 2. 觀察者模式 (Observer Pattern)
**回調機制**
```python
# 事件回調
on_data_received(item, value)      # 資料接收事件
on_row_saved(row)                  # 資料行保存事件
on_row_skipped(row)                # 資料行跳過事件
```

### 3. 建造者模式 (Builder Pattern)
**配置建構**
```python
config = DataProcessorConfig()
config.set('enable_time_newline', True)
config.set('time_newline_interval', 0.8)
config.set('value_change_check_mode', 'single')
```

### 4. 單一職責原則 (Single Responsibility Principle)
- **DDEDataProcessor**: 只負責資料處理邏輯
- **DataFileHandler**: 只負責檔案操作
- **DataProcessorConfig**: 只負責配置管理
- **data_structures**: 只定義資料結構

## 執行緒安全設計

### 鎖機制
```python
# 使用重入鎖保證執行緒安全
self._lock = threading.RLock()

# 關鍵區域保護
with self._lock:
    # 資料結構修改
    # 統計資訊更新
    # 狀態變更
```

### 原子操作
- 所有統計更新都是原子的
- 資料結構修改具有一致性
- 回調函數在鎖外執行避免死鎖

## 錯誤處理架構

### 分層錯誤處理
```python
try:
    # 業務邏輯
    result = process_business_logic()
except SpecificBusinessError as e:
    # 業務錯誤處理
    handle_business_error(e)
except ValidationError as e:
    # 驗證錯誤處理
    handle_validation_error(e)
except Exception as e:
    # 通用錯誤處理
    handle_generic_error(e)
finally:
    # 清理資源
    cleanup_resources()
```

### 錯誤恢復機制
- 資料完整性保護
- 狀態一致性維護
- 詳細錯誤日誌記錄

## 效能考量

### 記憶體管理
- 使用 `deque` 限制歷史資料數量
- 及時清理不需要的物件
- 避免記憶體洩漏

### I/O 最佳化
- 批次檔案寫入
- 適當的緩衝區大小
- 避免頻繁檔案操作

### 計算最佳化
- 快速資料查找演算法
- 避免不必要的資料複製
- 高效的字串處理

## 擴展性設計

### 水平擴展
- 支援多個處理器實例
- 獨立的配置管理
- 無狀態設計原則

### 垂直擴展
- 可插拔的處理策略
- 可擴展的回調機制
- 靈活的配置系統

### 整合擴展
- 標準化的介面設計
- 鬆耦合的模組架構
- 清晰的依賴關係

## 測試架構

### 單元測試
- 每個模組獨立測試
- 模擬外部依賴
- 邊界條件測試

### 整合測試
- 端到端流程測試
- 多模組協作測試
- 錯誤場景測試

### 效能測試
- 高頻資料處理測試
- 記憶體使用監控
- 併發處理測試

---

*本架構概覽基於 DDE 引擎版的實際實現，提供了完整的系統架構視圖和設計理念說明。*
