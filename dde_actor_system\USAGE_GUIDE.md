# DDE Actor System - 使用指南

## 🚀 快速開始

### 1. 啟動系統
```bash
# 在第一個終端中啟動主系統
python main.py --config config/system_config.json
```

### 2. 監控系統狀態
```bash
# 在第二個終端中檢查狀態
python check_status.py

# 或者實時監控
python monitor_status.py
```

### 3. 觸發數據訂閱
```bash
# 在第三個終端中手動觸發訂閱
python trigger_subscription.py
```

## 📊 系統狀態說明

### ✅ 正常運行的標誌
1. **進程運行** - `check_status.py` 能找到 DDE Actor System 進程
2. **DDE 連接成功** - 日誌顯示 "成功連接到 DDE 服務器"
3. **Actor 啟動** - 日誌顯示 "成功啟動 4/4 個Actor"
4. **數據文件生成** - `outputs/` 目錄中有 `dde_data.csv` 或 `dde_data.json`

### ⚠️ 需要注意的情況
1. **Qt Timer 警告** - 這是正常的，不影響功能
2. **沒有數據文件** - 可能需要手動觸發訂閱
3. **DDE 連接失敗** - 檢查 DDE 服務是否運行

## 🔧 故障排除

### 問題 1: 系統啟動後沒有數據
**症狀**: 系統運行正常，但 `outputs/` 目錄沒有數據文件

**解決方案**:
```bash
# 1. 檢查系統狀態
python check_status.py

# 2. 手動觸發訂閱
python trigger_subscription.py

# 3. 檢查配置文件中的項目是否正確
```

### 問題 2: DDE 連接失敗
**症狀**: 日誌顯示 "DDE 連接失敗"

**解決方案**:
1. 確保 DDE 服務 (如 XQTISC) 正在運行
2. 檢查服務名稱和主題是否正確
3. 確認網絡連接正常

### 問題 3: 系統進程意外停止
**症狀**: `check_status.py` 找不到進程

**解決方案**:
1. 檢查日誌文件中的錯誤信息
2. 重新啟動系統
3. 如果持續出現，使用 GUI 版本進行調試

## 🖥️ GUI 版本使用

### 演示版本 (推薦)
```bash
# 使用模擬數據，不需要真實 DDE 連接
python main_gui_demo.py
```

### 完整版本
```bash
# 需要真實 DDE 環境
python main_gui.py --config config/system_config.json
```

## 📁 文件說明

### 輸出文件
- `outputs/dde_data.csv` - CSV 格式的 DDE 數據
- `outputs/dde_data.json` - JSON 格式的 DDE 數據
- `outputs/quick_test.csv` - 測試數據文件

### 日誌文件
- `logs/system.log` - 系統主日誌
- `logs/gui_test_system.log` - GUI 測試日誌

### 配置文件
- `config/system_config.json` - 主系統配置
- `config/test_config.json` - 測試配置
- `config/gui_test_config.json` - GUI 測試配置

## 🎯 使用場景

### 場景 1: 開發和測試
```bash
# 使用 GUI 演示版本
python main_gui_demo.py

# 或者運行快速測試
python quick_test.py
```

### 場景 2: 生產環境監控
```bash
# 終端 1: 啟動主系統
python main.py --config config/system_config.json

# 終端 2: 實時監控
python monitor_status.py

# 終端 3: 根據需要觸發訂閱
python trigger_subscription.py
```

### 場景 3: 調試問題
```bash
# 1. 檢查狀態
python check_status.py

# 2. 測試 DDE 連接
python test_dde_subscribe.py

# 3. 查看日誌
cat logs/system.log
```

## 📈 性能監控

### 關鍵指標
1. **吞吐量** - 每秒處理的消息數
2. **延遲** - 消息處理的平均時間
3. **內存使用** - 系統內存消耗
4. **文件大小** - 輸出文件的增長速度

### 監控命令
```bash
# 實時狀態監控
python monitor_status.py

# 快速狀態檢查
python check_status.py

# 系統資源監控
top -p $(pgrep -f "python main.py")
```

## 🔄 常用操作

### 重啟系統
```bash
# 1. 停止當前系統 (Ctrl+C)
# 2. 等待幾秒
# 3. 重新啟動
python main.py --config config/system_config.json
```

### 清理數據
```bash
# 清理輸出文件
rm outputs/dde_data.*

# 清理日誌文件
rm logs/*.log
```

### 備份數據
```bash
# 備份輸出文件
cp outputs/dde_data.csv backups/dde_data_$(date +%Y%m%d_%H%M%S).csv
```

## 💡 最佳實踐

1. **使用多終端** - 一個運行系統，一個監控狀態
2. **定期檢查** - 每隔一段時間運行 `check_status.py`
3. **保存日誌** - 重要的運行日誌要及時備份
4. **測試環境** - 先在測試環境驗證配置
5. **監控資源** - 注意系統的 CPU 和內存使用

## 🆘 獲取幫助

如果遇到問題：
1. 運行 `python check_status.py` 檢查系統狀態
2. 查看 `logs/system.log` 中的錯誤信息
3. 嘗試使用 GUI 版本進行可視化調試
4. 檢查配置文件是否正確

---

**記住**: DDE Actor System 已經完全可用，主要是需要正確的配置和操作流程！
