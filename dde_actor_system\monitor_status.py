#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE Actor System - 實時狀態監控

在另一個終端中運行此腳本來監控系統狀態
"""

import os
import time
import json
import psutil
from pathlib import Path
from datetime import datetime


def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')


def get_file_stats():
    """獲取文件統計"""
    stats = {}
    
    # 檢查輸出文件
    output_dir = Path("outputs")
    if output_dir.exists():
        for file_path in output_dir.glob("*.csv"):
            if file_path.exists():
                stat = file_path.stat()
                stats[file_path.name] = {
                    'size_bytes': stat.st_size,
                    'size_mb': stat.st_size / 1024 / 1024,
                    'modified': datetime.fromtimestamp(stat.st_mtime).strftime('%H:%M:%S'),
                    'lines': 0
                }
                
                # 計算行數
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        stats[file_path.name]['lines'] = sum(1 for _ in f)
                except:
                    pass
    
    return stats


def get_log_tail():
    """獲取最新日誌"""
    log_files = [
        "logs/system.log",
        "logs/gui_test_system.log"
    ]
    
    latest_logs = []
    
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists():
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 取最後10行
                    for line in lines[-10:]:
                        if line.strip():
                            latest_logs.append(line.strip())
            except:
                pass
    
    return latest_logs[-15:]  # 最多顯示15行


def get_system_stats():
    """獲取系統統計"""
    try:
        # 查找 Python 進程
        python_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_info']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'main.py' in cmdline or 'dde_actor_system' in cmdline:
                        python_processes.append({
                            'pid': proc.info['pid'],
                            'cpu_percent': proc.info['cpu_percent'],
                            'memory_mb': proc.info['memory_info'].rss / 1024 / 1024 if proc.info['memory_info'] else 0,
                            'cmdline': cmdline[:80] + '...' if len(cmdline) > 80 else cmdline
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return {
            'system_cpu': psutil.cpu_percent(),
            'system_memory': psutil.virtual_memory().percent,
            'python_processes': python_processes
        }
    except:
        return {
            'system_cpu': 0,
            'system_memory': 0,
            'python_processes': []
        }


def display_status():
    """顯示狀態"""
    clear_screen()
    
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    print("=" * 80)
    print(f"🖥️  DDE Actor System - 實時狀態監控")
    print(f"⏰ 當前時間: {current_time}")
    print("=" * 80)
    
    # 系統統計
    system_stats = get_system_stats()
    print(f"\n📊 系統資源:")
    print(f"   CPU 使用率: {system_stats['system_cpu']:.1f}%")
    print(f"   內存使用率: {system_stats['system_memory']:.1f}%")
    
    # Python 進程
    if system_stats['python_processes']:
        print(f"\n🐍 Python 進程:")
        for proc in system_stats['python_processes']:
            print(f"   PID {proc['pid']}: CPU {proc['cpu_percent']:.1f}%, 內存 {proc['memory_mb']:.1f}MB")
            print(f"      {proc['cmdline']}")
    else:
        print(f"\n❌ 未找到 DDE Actor System 進程")
    
    # 文件統計
    file_stats = get_file_stats()
    if file_stats:
        print(f"\n📁 輸出文件:")
        for filename, stats in file_stats.items():
            print(f"   {filename}:")
            print(f"      大小: {stats['size_mb']:.2f} MB ({stats['size_bytes']} bytes)")
            print(f"      行數: {stats['lines']}")
            print(f"      修改時間: {stats['modified']}")
    else:
        print(f"\n📁 輸出文件: 無文件或文件為空")
    
    # 最新日誌
    latest_logs = get_log_tail()
    if latest_logs:
        print(f"\n📝 最新日誌 (最後 {len(latest_logs)} 行):")
        for log in latest_logs:
            # 簡化日誌顯示
            if len(log) > 100:
                log = log[:97] + "..."
            print(f"   {log}")
    else:
        print(f"\n📝 最新日誌: 無日誌或無法讀取")
    
    print(f"\n" + "=" * 80)
    print(f"🔄 自動刷新中... (按 Ctrl+C 退出)")
    print(f"💡 提示: 在另一個終端運行 'python main.py --config config/system_config.json'")
    print("=" * 80)


def check_dde_data_activity():
    """檢查 DDE 數據活動"""
    csv_file = Path("outputs/dde_data.csv")
    json_file = Path("outputs/dde_data.json")
    
    activity = {
        'csv_active': False,
        'json_active': False,
        'last_csv_size': 0,
        'last_json_size': 0
    }
    
    if csv_file.exists():
        current_size = csv_file.stat().st_size
        activity['csv_active'] = current_size > 0
        activity['last_csv_size'] = current_size
    
    if json_file.exists():
        current_size = json_file.stat().st_size
        activity['json_active'] = current_size > 0
        activity['last_json_size'] = current_size
    
    return activity


def main():
    """主函數"""
    print("🚀 啟動 DDE Actor System 狀態監控...")
    print("正在監控系統狀態...")
    
    last_csv_size = 0
    last_json_size = 0
    
    try:
        while True:
            display_status()
            
            # 檢查數據活動
            activity = check_dde_data_activity()
            
            # 檢測文件大小變化
            if activity['last_csv_size'] != last_csv_size:
                print(f"\n🔥 檢測到 CSV 文件更新! 大小: {activity['last_csv_size']} bytes")
                last_csv_size = activity['last_csv_size']
            
            if activity['last_json_size'] != last_json_size:
                print(f"\n🔥 檢測到 JSON 文件更新! 大小: {activity['last_json_size']} bytes")
                last_json_size = activity['last_json_size']
            
            # 等待5秒後刷新
            time.sleep(5)
            
    except KeyboardInterrupt:
        clear_screen()
        print("\n👋 狀態監控已停止")
        print("感謝使用 DDE Actor System 監控工具!")


if __name__ == "__main__":
    main()
