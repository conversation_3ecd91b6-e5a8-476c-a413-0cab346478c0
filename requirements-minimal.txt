# DDE Monitor Project - Minimal Requirements
# ==========================================
# 
# 這個檔案只包含運行 DDE 監控程式的最基本依賴
# 適用於生產環境或最小化安裝
# 
# 使用方法: pip install -r requirements-minimal.txt

# Core GUI Framework (必須)
PySide6>=6.5.0

# Windows API Support (必須)
pywin32>=306

# 注意事項:
# ========
# 1. 以上兩個套件是運行程式的最低要求
# 2. Python 內建模組不需要額外安裝:
#    - configparser (設定檔處理)
#    - logging (日誌系統)
#    - threading (多線程)
#    - queue (佇列)
#    - datetime (日期時間)
#    - os, sys (系統功能)
#    - dataclasses (資料類別)
#    - typing (型別提示)
#    - collections (集合類別)
# 
# 3. 如需開發或測試功能，請使用 requirements.txt
# 4. 如需效能監控功能，可額外安裝 psutil
# 5. 如需資料分析功能，可額外安裝 pandas, numpy
