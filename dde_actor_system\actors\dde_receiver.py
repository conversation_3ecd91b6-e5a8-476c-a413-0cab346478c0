#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE接收Actor實現

提供高性能的DDE數據接收功能，包括：
- 最小化DDE回調處理時間
- 無鎖環形緩衝區
- 批量數據處理
- 多DDE連接管理
"""

import asyncio
import ctypes
import logging
import mmap
import time
import threading
from collections import deque
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass

from core.actor_base import ActorBase
from core.message_system import Message, MessageType, create_dde_data_message
from dydde.dydde import DDEClient


@dataclass
class DDEConnectionConfig:
    """DDE連接配置"""
    service: str
    topic: str
    items: List[str]
    auto_reconnect: bool = True
    reconnect_delay: float = 5.0
    connection_timeout: float = 30.0


class LockFreeRingBuffer:
    """無鎖環形緩衝區
    
    使用原子操作實現高性能的數據緩衝
    """
    
    def __init__(self, size: int = 1024 * 1024):
        """初始化環形緩衝區
        
        Args:
            size: 緩衝區大小（字節）
        """
        self.size = size
        self.buffer = (ctypes.c_char * size)()
        self.write_pos = ctypes.c_uint64(0)
        self.read_pos = ctypes.c_uint64(0)
        self.data_available = threading.Event()
        
        # 統計信息
        self.writes = 0
        self.reads = 0
        self.overruns = 0
    
    def write_atomic(self, item: str, value: str, timestamp: float) -> bool:
        """原子寫入操作
        
        Args:
            item: DDE項目
            value: DDE值
            timestamp: 時間戳
            
        Returns:
            bool: 寫入是否成功
        """
        try:
            # 格式化數據
            data_str = f"{timestamp:.6f}|{item}|{value}\n"
            data_bytes = data_str.encode('utf-8')
            data_len = len(data_bytes)
            
            if data_len >= self.size:
                return False  # 數據太大
            
            # 獲取當前寫入位置
            current_write = self.write_pos.value
            current_read = self.read_pos.value
            
            # 檢查是否有足夠空間
            available_space = self.size - (current_write - current_read)
            if data_len > available_space:
                self.overruns += 1
                return False
            
            # 計算實際寫入位置（處理環形回繞）
            actual_pos = current_write % self.size
            
            if actual_pos + data_len <= self.size:
                # 直接寫入
                ctypes.memmove(
                    ctypes.addressof(self.buffer) + actual_pos,
                    data_bytes,
                    data_len
                )
            else:
                # 分兩段寫入（環形回繞）
                first_part_len = self.size - actual_pos
                second_part_len = data_len - first_part_len
                
                ctypes.memmove(
                    ctypes.addressof(self.buffer) + actual_pos,
                    data_bytes[:first_part_len],
                    first_part_len
                )
                ctypes.memmove(
                    ctypes.addressof(self.buffer),
                    data_bytes[first_part_len:],
                    second_part_len
                )
            
            # 原子更新寫入位置
            self.write_pos.value = current_write + data_len
            self.writes += 1
            
            # 通知有數據可讀
            self.data_available.set()
            
            return True
            
        except Exception:
            return False
    
    def read_batch(self, max_items: int = 1000) -> List[tuple]:
        """批量讀取數據
        
        Args:
            max_items: 最大讀取項目數
            
        Returns:
            List[tuple]: (timestamp, item, value) 元組列表
        """
        results = []
        
        try:
            current_read = self.read_pos.value
            current_write = self.write_pos.value
            
            if current_read >= current_write:
                return results
            
            # 計算可讀數據長度
            available_data = current_write - current_read
            if available_data <= 0:
                return results
            
            # 讀取數據
            read_buffer = []
            bytes_read = 0
            
            while bytes_read < available_data and len(results) < max_items:
                actual_pos = (current_read + bytes_read) % self.size
                
                # 查找下一個換行符
                line_end = -1
                search_len = min(1024, available_data - bytes_read)
                
                for i in range(search_len):
                    pos = (actual_pos + i) % self.size
                    if self.buffer[pos] == ord('\n'):
                        line_end = i
                        break
                
                if line_end == -1:
                    break  # 沒有完整的行
                
                # 讀取一行數據
                line_data = bytearray()
                for i in range(line_end):
                    pos = (actual_pos + i) % self.size
                    line_data.append(self.buffer[pos])
                
                # 解析數據
                try:
                    line_str = line_data.decode('utf-8')
                    parts = line_str.split('|', 2)
                    if len(parts) == 3:
                        timestamp = float(parts[0])
                        item = parts[1]
                        value = parts[2]
                        results.append((timestamp, item, value))
                except (ValueError, UnicodeDecodeError):
                    pass  # 忽略解析錯誤的數據
                
                bytes_read += line_end + 1  # +1 for newline
            
            # 更新讀取位置
            if bytes_read > 0:
                self.read_pos.value = current_read + bytes_read
                self.reads += len(results)
            
            # 如果沒有更多數據，清除事件
            if self.read_pos.value >= self.write_pos.value:
                self.data_available.clear()
            
        except Exception:
            pass  # 忽略讀取錯誤
        
        return results
    
    def get_stats(self) -> Dict[str, int]:
        """獲取統計信息
        
        Returns:
            Dict: 統計信息
        """
        return {
            'writes': self.writes,
            'reads': self.reads,
            'overruns': self.overruns,
            'pending_bytes': self.write_pos.value - self.read_pos.value
        }


class DDEReceiverActor(ActorBase):
    """DDE接收Actor
    
    負責高性能的DDE數據接收和初步處理
    """
    
    def __init__(self, name: str, config: Optional[Dict] = None):
        """初始化DDE接收Actor
        
        Args:
            name: Actor名稱
            config: 配置字典
        """
        super().__init__(name, config)
        
        # DDE連接管理
        self.dde_connections: Dict[str, DDEClient] = {}
        self.connection_configs: Dict[str, DDEConnectionConfig] = {}
        
        # 高性能緩衝區
        buffer_size = self.config.get('buffer_size', 1024 * 1024)
        self.ring_buffer = LockFreeRingBuffer(buffer_size)
        
        # 批量處理配置
        self.batch_size = self.config.get('batch_size', 1000)
        self.batch_timeout = self.config.get('batch_timeout', 0.01)  # 10ms
        
        # 處理統計
        self.stats = {
            'dde_callbacks': 0,
            'data_received': 0,
            'data_processed': 0,
            'data_sent': 0,
            'errors': 0,
            'reconnections': 0
        }
        
        # 處理任務
        self.processing_task: Optional[asyncio.Task] = None
    
    async def on_start(self):
        """Actor啟動時的初始化"""
        try:
            # 解析連接配置
            await self._parse_connection_configs()
            
            # 建立DDE連接
            await self._establish_dde_connections()
            
            # 啟動數據處理任務
            self.processing_task = asyncio.create_task(self._data_processing_loop())
            
            self.logger.info(f"DDE接收Actor {self.name} 啟動成功")
            
        except Exception as e:
            self.logger.error(f"DDE接收Actor啟動失敗: {str(e)}")
            raise
    
    async def on_stop(self):
        """Actor停止時的清理"""
        try:
            # 停止處理任務
            if self.processing_task:
                self.processing_task.cancel()
                try:
                    await self.processing_task
                except asyncio.CancelledError:
                    pass
            
            # 關閉DDE連接
            await self._close_dde_connections()
            
            self.logger.info(f"DDE接收Actor {self.name} 停止成功")
            
        except Exception as e:
            self.logger.error(f"DDE接收Actor停止失敗: {str(e)}")
    
    async def handle_message(self, message: Message):
        """處理接收到的消息"""
        try:
            if message.type == MessageType.DDE_CONNECT:
                await self._handle_connect_message(message)
            elif message.type == MessageType.DDE_DISCONNECT:
                await self._handle_disconnect_message(message)
            elif message.type == MessageType.DDE_SUBSCRIBE:
                await self._handle_subscribe_message(message)
            elif message.type == MessageType.DDE_UNSUBSCRIBE:
                await self._handle_unsubscribe_message(message)
            else:
                self.logger.warning(f"未知消息類型: {message.type}")
                
        except Exception as e:
            self.logger.error(f"處理消息失敗: {str(e)}")
            self.stats['errors'] += 1
    
    def on_dde_data(self, item: str, value: str):
        """DDE數據回調函數 - 最小化處理時間
        
        這是性能關鍵路徑，必須盡可能快
        """
        try:
            # 直接寫入環形緩衝區，無鎖操作
            timestamp = time.time()
            success = self.ring_buffer.write_atomic(item, value, timestamp)
            
            if success:
                self.stats['dde_callbacks'] += 1
                self.stats['data_received'] += 1
            else:
                self.stats['errors'] += 1
                
        except Exception:
            self.stats['errors'] += 1
    
    async def _parse_connection_configs(self):
        """解析連接配置"""
        connections_config = self.config.get('connections', [])
        
        for conn_config in connections_config:
            conn_id = conn_config.get('id', f"conn_{len(self.connection_configs)}")
            
            config = DDEConnectionConfig(
                service=conn_config['service'],
                topic=conn_config['topic'],
                items=conn_config.get('items', []),
                auto_reconnect=conn_config.get('auto_reconnect', True),
                reconnect_delay=conn_config.get('reconnect_delay', 5.0),
                connection_timeout=conn_config.get('connection_timeout', 30.0)
            )
            
            self.connection_configs[conn_id] = config
            self.logger.info(f"配置DDE連接: {conn_id} -> {config.service}.{config.topic}")
    
    async def _establish_dde_connections(self):
        """建立DDE連接"""
        for conn_id, config in self.connection_configs.items():
            try:
                # 創建DDE客戶端
                dde_client = DDEClient(
                    service=config.service,
                    topic=config.topic,
                    auto_reconnect=config.auto_reconnect
                )
                
                # 連接DDE服務
                dde_client.connect()
                
                # 訂閱項目
                for item in config.items:
                    dde_client.advise(item, self.on_dde_data)
                
                self.dde_connections[conn_id] = dde_client
                self.logger.info(f"DDE連接建立成功: {conn_id}")
                
            except Exception as e:
                self.logger.error(f"建立DDE連接失敗 {conn_id}: {str(e)}")
                self.stats['errors'] += 1
    
    async def _close_dde_connections(self):
        """關閉DDE連接"""
        for conn_id, dde_client in self.dde_connections.items():
            try:
                dde_client.disconnect()
                self.logger.info(f"DDE連接關閉: {conn_id}")
            except Exception as e:
                self.logger.error(f"關閉DDE連接失敗 {conn_id}: {str(e)}")
        
        self.dde_connections.clear()
    
    async def _data_processing_loop(self):
        """數據處理循環"""
        self.logger.info("數據處理循環開始")
        
        while self.running:
            try:
                # 等待數據可用或超時
                try:
                    await asyncio.wait_for(
                        asyncio.to_thread(self.ring_buffer.data_available.wait),
                        timeout=self.batch_timeout
                    )
                except asyncio.TimeoutError:
                    pass  # 超時也要檢查是否有數據
                
                # 批量讀取數據
                batch_data = self.ring_buffer.read_batch(self.batch_size)
                
                if batch_data:
                    await self._process_data_batch(batch_data)
                    self.stats['data_processed'] += len(batch_data)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"數據處理循環錯誤: {str(e)}")
                self.stats['errors'] += 1
                await asyncio.sleep(0.1)  # 避免錯誤循環
        
        self.logger.info("數據處理循環結束")
    
    async def _process_data_batch(self, batch_data: List[tuple]):
        """處理數據批次
        
        Args:
            batch_data: 批次數據列表
        """
        try:
            # 創建批量消息
            messages = []
            for timestamp, item, value in batch_data:
                message = create_dde_data_message(item, value, self.name)
                message.metadata = {'original_timestamp': timestamp}
                messages.append(message)
            
            # 批量發送到數據處理Actor
            if messages:
                # 創建批量消息
                batch_message = Message(
                    type=MessageType.DATA_PROCESS,
                    data={'batch': messages, 'batch_size': len(messages)},
                    sender=self.name
                )

                success = await self.send_message("DataProcessor", batch_message)
                if success:
                    self.stats['data_sent'] += len(messages)
                else:
                    self.stats['errors'] += len(messages)
                    
        except Exception as e:
            self.logger.error(f"處理數據批次失敗: {str(e)}")
            self.stats['errors'] += 1
    
    async def _handle_connect_message(self, message: Message):
        """處理連接消息"""
        # 實現動態連接邏輯
        pass
    
    async def _handle_disconnect_message(self, message: Message):
        """處理斷開連接消息"""
        # 實現動態斷開邏輯
        pass
    
    async def _handle_subscribe_message(self, message: Message):
        """處理訂閱消息"""
        # 實現動態訂閱邏輯
        pass
    
    async def _handle_unsubscribe_message(self, message: Message):
        """處理取消訂閱消息"""
        # 實現動態取消訂閱邏輯
        pass
    
    def get_dde_stats(self) -> Dict[str, Any]:
        """獲取DDE統計信息
        
        Returns:
            Dict: DDE統計信息
        """
        buffer_stats = self.ring_buffer.get_stats()
        
        return {
            'actor_stats': self.stats.copy(),
            'buffer_stats': buffer_stats,
            'connections': {
                conn_id: {
                    'service': config.service,
                    'topic': config.topic,
                    'items_count': len(config.items),
                    'connected': conn_id in self.dde_connections
                }
                for conn_id, config in self.connection_configs.items()
            }
        }
