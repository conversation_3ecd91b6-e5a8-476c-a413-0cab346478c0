#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime, time

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_time_parsing():
    print("测试时间解析")
    
    # 测试时间字符串解析
    time_ranges = [
        "08:45:00-13:45:00",
        "09:00:00-13:30:00", 
        "15:00:00-05:00:00;08:45:00-13:45:00",
        "15:00:00-05:00:00"
    ]
    
    current_time = datetime.now().time()
    print(f"当前时间: {current_time}")
    
    for time_range_str in time_ranges:
        print(f"\n时间表: {time_range_str}")
        
        # 解析时间表
        schedule_times = []
        time_ranges_list = time_range_str.split(';')
        
        for time_range in time_ranges_list:
            time_range = time_range.strip()
            if '-' in time_range:
                start_str, end_str = time_range.split('-', 1)
                start_time = datetime.strptime(start_str.strip(), '%H:%M:%S').time()
                end_time = datetime.strptime(end_str.strip(), '%H:%M:%S').time()
                schedule_times.append((start_time, end_time))
        
        print(f"解析结果: {schedule_times}")
        
        # 检查当前时间是否在范围内
        should_connect = False
        for start_time, end_time in schedule_times:
            if start_time <= end_time:
                # 同一天的时间段
                if start_time <= current_time <= end_time:
                    should_connect = True
                    print(f"  在同一天时间段内: {start_time} - {end_time}")
            else:
                # 跨天的时间段
                if current_time >= start_time or current_time <= end_time:
                    should_connect = True
                    print(f"  在跨天时间段内: {start_time} - {end_time}")
        
        print(f"应该连接: {should_connect}")

if __name__ == "__main__":
    test_time_parsing()
