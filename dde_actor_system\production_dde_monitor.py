#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生產版本 DDE 監控系統

基於調試版本的穩定 DDE 數據監控
"""

import asyncio
import signal
import time
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from dydde.dydde import DDEClient
from core.message_system import MessageRouter, create_dde_data_message
from actors.data_processor import DataProcessorActor
from actors.file_writer import FileWriterActor


class ProductionDDEMonitor:
    """生產版本 DDE 監控器"""
    
    def __init__(self, config_file="config/system_config.json"):
        self.config_file = config_file
        self.config = None
        self.router = None
        self.data_processor = None
        self.file_writer = None
        self.dde_clients = {}
        self.running = False
        self.data_count = 0
        self.start_time = None
        
    def load_config(self):
        """加載配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            return True
        except Exception as e:
            print(f"❌ 配置加載失敗: {str(e)}")
            return False
    
    async def setup_components(self):
        """設置組件"""
        print("🔧 設置數據處理組件...")
        
        # 創建消息路由
        self.router = MessageRouter()
        
        # 創建數據處理器
        processor_config = {
            'batch_size': 10,
            'queue_size': 1000
        }
        self.data_processor = DataProcessorActor('DataProcessor', processor_config)
        self.router.register_actor(self.data_processor)
        
        # 創建文件寫入器
        file_config = {
            'batch_size': 10,
            'flush_interval': 5.0,  # 5秒刷新
            'files': {
                'default': {
                    'filename': 'outputs/production_dde_data.csv',
                    'format': 'csv',
                    'max_size_mb': 100,
                    'compress': False
                },
                'json': {
                    'filename': 'outputs/production_dde_data.json',
                    'format': 'json',
                    'max_size_mb': 100,
                    'compress': False
                }
            }
        }
        self.file_writer = FileWriterActor('FileWriter', file_config)
        self.router.register_actor(self.file_writer)
        
        # 啟動組件
        await self.data_processor.start()
        await self.file_writer.start()
        
        print("✅ 組件設置完成")
    
    async def setup_dde_connections(self):
        """設置 DDE 連接"""
        print("🔗 設置 DDE 連接...")
        
        products = self.config.get('products', [])
        enabled_products = [p for p in products if p.get('enabled', False)]
        
        for product in enabled_products:
            symbol = product.get('symbol')
            service = product.get('service')
            topic = product.get('topic')
            items = product.get('items', [])
            
            print(f"   連接產品: {symbol}")
            
            try:
                # 創建 DDE 客戶端
                client = DDEClient(service, topic)
                client.connect()
                
                # 測試項目並建立熱鏈接
                valid_items = []
                for item in items:
                    try:
                        test_result = client.request(item)
                        if test_result and test_result.strip():
                            valid_items.append(item)
                            print(f"     ✅ {item}: {test_result}")
                        else:
                            print(f"     ⚪ {item}: 無數據")
                    except Exception as e:
                        print(f"     ❌ {item}: {str(e)}")
                
                if valid_items:
                    self.dde_clients[symbol] = {
                        'client': client,
                        'items': valid_items,
                        'service': service,
                        'topic': topic
                    }
                    print(f"   ✅ {symbol}: {len(valid_items)}/{len(items)} 項目可用")
                else:
                    client.disconnect()
                    print(f"   ❌ {symbol}: 沒有可用項目")
                
            except Exception as e:
                print(f"   ❌ {symbol} 連接失敗: {str(e)}")
        
        print(f"✅ DDE 連接設置完成: {len(self.dde_clients)} 個產品")
    
    async def start_monitoring(self):
        """開始監控"""
        print("\n🚀 開始 DDE 數據監控")
        print("=" * 60)
        
        self.running = True
        self.start_time = time.time()
        
        # 設置信號處理
        def signal_handler(signum, frame):
            print(f"\n📡 接收到停止信號")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        print(f"📊 監控 {len(self.dde_clients)} 個產品")
        print(f"📁 數據保存到: outputs/production_dde_data.*")
        print(f"⏰ 開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🛑 按 Ctrl+C 停止監控")
        print("=" * 60)
        
        # 主監控循環
        await self.monitoring_loop()
    
    async def monitoring_loop(self):
        """監控循環"""
        last_status_time = time.time()
        
        try:
            while self.running:
                # 輪詢所有 DDE 項目
                for symbol, dde_info in self.dde_clients.items():
                    client = dde_info['client']
                    items = dde_info['items']
                    
                    for item in items:
                        try:
                            value = client.request(item)
                            if value and value.strip():
                                # 發送到處理器（只發送到 DataProcessor，不發送到 GUIUpdater）
                                message = create_dde_data_message(item, value, "Production")
                                success = await self.router.send_message("DataProcessor", message)
                                if success:
                                    self.data_count += 1
                        except Exception as e:
                            # 靜默處理錯誤，避免日誌過多
                            pass
                
                # 每30秒顯示一次狀態
                current_time = time.time()
                if current_time - last_status_time >= 30:
                    await self.show_status()
                    last_status_time = current_time
                
                # 輪詢間隔
                await asyncio.sleep(1.0)
                
        except Exception as e:
            print(f"❌ 監控循環錯誤: {str(e)}")
        finally:
            await self.stop_monitoring()
    
    async def show_status(self):
        """顯示狀態"""
        try:
            elapsed = time.time() - self.start_time
            rate = self.data_count / elapsed if elapsed > 0 else 0
            
            print(f"\n📊 監控狀態 ({datetime.now().strftime('%H:%M:%S')})")
            print(f"   運行時間: {elapsed/60:.1f} 分鐘")
            print(f"   數據筆數: {self.data_count}")
            print(f"   數據速率: {rate:.1f} 筆/秒")
            
            # 檢查文件大小
            csv_file = Path("outputs/production_dde_data.csv")
            json_file = Path("outputs/production_dde_data.json")
            
            if csv_file.exists():
                size = csv_file.stat().st_size
                print(f"   CSV 文件: {size/1024:.1f} KB")
            
            if json_file.exists():
                size = json_file.stat().st_size
                print(f"   JSON 文件: {size/1024:.1f} KB")
            
        except Exception as e:
            print(f"⚠️  狀態顯示錯誤: {str(e)}")
    
    async def stop_monitoring(self):
        """停止監控"""
        print(f"\n🛑 停止監控...")
        self.running = False
        
        # 關閉 DDE 連接
        for symbol, dde_info in self.dde_clients.items():
            try:
                dde_info['client'].disconnect()
                print(f"   ✅ {symbol} 連接已關閉")
            except Exception as e:
                print(f"   ⚠️  {symbol} 關閉錯誤: {str(e)}")
        
        # 停止組件
        if self.data_processor:
            await self.data_processor.stop()
        
        if self.file_writer:
            await self.file_writer.stop()
        
        # 最終統計
        if self.start_time:
            elapsed = time.time() - self.start_time
            rate = self.data_count / elapsed if elapsed > 0 else 0
            
            print(f"\n📊 最終統計:")
            print(f"   總運行時間: {elapsed/60:.1f} 分鐘")
            print(f"   總數據筆數: {self.data_count}")
            print(f"   平均速率: {rate:.1f} 筆/秒")
        
        print("✅ 監控停止完成")
    
    async def run(self):
        """運行監控器"""
        try:
            # 加載配置
            if not self.load_config():
                return False
            
            # 設置組件
            await self.setup_components()
            
            # 設置 DDE 連接
            await self.setup_dde_connections()
            
            if not self.dde_clients:
                print("❌ 沒有可用的 DDE 連接")
                return False
            
            # 開始監控
            await self.start_monitoring()
            
            return True
            
        except KeyboardInterrupt:
            print(f"\n⏹️  用戶中斷")
        except Exception as e:
            print(f"❌ 運行失敗: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            await self.stop_monitoring()
        
        return False


async def main():
    """主函數"""
    import argparse
    
    parser = argparse.ArgumentParser(description='生產版本 DDE 監控系統')
    parser.add_argument('--config', default='config/system_config.json', 
                       help='配置文件路徑')
    
    args = parser.parse_args()
    
    print("🏭 生產版本 DDE 監控系統")
    print("=" * 60)
    
    monitor = ProductionDDEMonitor(args.config)
    await monitor.run()


if __name__ == "__main__":
    # 設置事件循環策略
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 運行監控器
    asyncio.run(main())
