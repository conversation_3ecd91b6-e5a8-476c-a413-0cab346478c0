#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能監控模組

提供系統性能監控和分析功能，包括：
- 實時性能指標收集
- 性能分析和分析器
- 內存使用監控
- 延遲統計和分析
"""

import asyncio
import logging
import psutil
import statistics
import time
import threading
from collections import deque, defaultdict
from contextlib import contextmanager
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Callable
import gc


@dataclass
class PerformanceMetrics:
    """性能指標數據類"""
    timestamp: float = field(default_factory=time.time)
    
    # 吞吐量指標
    messages_per_second: float = 0.0
    data_processed_per_second: float = 0.0
    gui_updates_per_second: float = 0.0
    
    # 延遲指標
    avg_latency_ms: float = 0.0
    p50_latency_ms: float = 0.0
    p95_latency_ms: float = 0.0
    p99_latency_ms: float = 0.0
    max_latency_ms: float = 0.0
    
    # 系統資源指標
    cpu_usage_percent: float = 0.0
    memory_usage_mb: float = 0.0
    memory_usage_percent: float = 0.0
    
    # 隊列指標
    queue_sizes: Dict[str, int] = field(default_factory=dict)
    queue_utilization: Dict[str, float] = field(default_factory=dict)
    
    # 錯誤指標
    error_rate: float = 0.0
    error_count: int = 0
    
    # 自定義指標
    custom_metrics: Dict[str, float] = field(default_factory=dict)


class LatencyTracker:
    """延遲追蹤器"""
    
    def __init__(self, max_samples: int = 10000):
        """初始化延遲追蹤器
        
        Args:
            max_samples: 最大樣本數量
        """
        self.max_samples = max_samples
        self.samples = deque(maxlen=max_samples)
        self.lock = threading.Lock()
    
    def record(self, latency_ms: float):
        """記錄延遲樣本
        
        Args:
            latency_ms: 延遲時間（毫秒）
        """
        with self.lock:
            self.samples.append(latency_ms)
    
    def get_statistics(self) -> Dict[str, float]:
        """獲取延遲統計信息
        
        Returns:
            Dict: 延遲統計信息
        """
        with self.lock:
            if not self.samples:
                return {
                    'avg': 0.0,
                    'p50': 0.0,
                    'p95': 0.0,
                    'p99': 0.0,
                    'max': 0.0,
                    'min': 0.0,
                    'count': 0
                }
            
            sorted_samples = sorted(self.samples)
            count = len(sorted_samples)
            
            return {
                'avg': statistics.mean(sorted_samples),
                'p50': sorted_samples[int(count * 0.5)],
                'p95': sorted_samples[int(count * 0.95)],
                'p99': sorted_samples[int(count * 0.99)],
                'max': max(sorted_samples),
                'min': min(sorted_samples),
                'count': count
            }
    
    def clear(self):
        """清空樣本"""
        with self.lock:
            self.samples.clear()


class ThroughputCounter:
    """吞吐量計數器"""
    
    def __init__(self, window_size: int = 60):
        """初始化吞吐量計數器
        
        Args:
            window_size: 時間窗口大小（秒）
        """
        self.window_size = window_size
        self.timestamps = deque()
        self.lock = threading.Lock()
    
    def increment(self):
        """增加計數"""
        current_time = time.time()
        with self.lock:
            self.timestamps.append(current_time)
            self._cleanup_old_timestamps(current_time)
    
    def get_rate(self) -> float:
        """獲取當前速率（每秒）
        
        Returns:
            float: 每秒處理數量
        """
        current_time = time.time()
        with self.lock:
            self._cleanup_old_timestamps(current_time)
            return len(self.timestamps) / self.window_size
    
    def _cleanup_old_timestamps(self, current_time: float):
        """清理過期的時間戳"""
        cutoff_time = current_time - self.window_size
        while self.timestamps and self.timestamps[0] < cutoff_time:
            self.timestamps.popleft()


class MemoryTracker:
    """內存使用追蹤器"""
    
    def __init__(self):
        """初始化內存追蹤器"""
        self.process = psutil.Process()
        self.initial_memory = self.process.memory_info().rss
        self.peak_memory = self.initial_memory
        self.gc_stats = {'collections': 0, 'collected': 0, 'uncollectable': 0}
    
    def get_memory_info(self) -> Dict[str, Any]:
        """獲取內存信息
        
        Returns:
            Dict: 內存信息
        """
        memory_info = self.process.memory_info()
        current_memory = memory_info.rss
        
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory
        
        # 獲取GC統計信息
        gc_stats = gc.get_stats()
        total_collections = sum(stat['collections'] for stat in gc_stats)
        
        return {
            'current_mb': current_memory / 1024 / 1024,
            'peak_mb': self.peak_memory / 1024 / 1024,
            'growth_mb': (current_memory - self.initial_memory) / 1024 / 1024,
            'percent': self.process.memory_percent(),
            'gc_collections': total_collections,
            'gc_objects': len(gc.get_objects())
        }


class Profiler:
    """性能分析器"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.profiles: Dict[str, List[float]] = defaultdict(list)
        self.active_profiles: Dict[str, float] = {}
        self.lock = threading.Lock()
    
    @contextmanager
    def profile(self, name: str):
        """性能分析上下文管理器
        
        Args:
            name: 分析項目名稱
        """
        start_time = time.perf_counter()
        try:
            with self.lock:
                self.active_profiles[name] = start_time
            yield
        finally:
            end_time = time.perf_counter()
            elapsed = (end_time - start_time) * 1000  # 轉換為毫秒
            
            with self.lock:
                self.profiles[name].append(elapsed)
                if name in self.active_profiles:
                    del self.active_profiles[name]
    
    def get_report(self) -> Dict[str, Dict[str, float]]:
        """獲取性能報告
        
        Returns:
            Dict: 性能報告
        """
        report = {}
        
        with self.lock:
            for name, times in self.profiles.items():
                if times:
                    report[name] = {
                        'count': len(times),
                        'total_time_ms': sum(times),
                        'avg_time_ms': statistics.mean(times),
                        'max_time_ms': max(times),
                        'min_time_ms': min(times),
                        'p95_time_ms': sorted(times)[int(len(times) * 0.95)] if len(times) > 20 else max(times)
                    }
        
        return report
    
    def clear(self):
        """清空分析數據"""
        with self.lock:
            self.profiles.clear()
            self.active_profiles.clear()


class PerformanceMonitor:
    """性能監控器
    
    負責收集和分析系統性能指標
    """
    
    def __init__(self, collection_interval: float = 1.0):
        """初始化性能監控器
        
        Args:
            collection_interval: 數據收集間隔（秒）
        """
        self.collection_interval = collection_interval
        self.logger = logging.getLogger("PerformanceMonitor")
        
        # 性能追蹤器
        self.latency_tracker = LatencyTracker()
        self.throughput_counters: Dict[str, ThroughputCounter] = {}
        self.memory_tracker = MemoryTracker()
        self.profiler = Profiler()
        
        # 指標歷史
        self.metrics_history: deque = deque(maxlen=3600)  # 保存1小時的數據
        
        # 監控狀態
        self.running = False
        self.collection_task: Optional[asyncio.Task] = None
        
        # 告警配置
        self.alert_thresholds = {
            'cpu_usage_percent': 80.0,
            'memory_usage_percent': 85.0,
            'avg_latency_ms': 100.0,
            'error_rate': 0.05
        }
        self.alert_callbacks: List[Callable[[str, Dict], None]] = []
    
    async def start(self):
        """啟動性能監控"""
        if self.running:
            return
        
        self.running = True
        self.collection_task = asyncio.create_task(self._collection_loop())
        self.logger.info("性能監控器已啟動")
    
    async def stop(self):
        """停止性能監控"""
        if not self.running:
            return
        
        self.running = False
        
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("性能監控器已停止")
    
    def record_latency(self, operation: str, latency_ms: float):
        """記錄操作延遲
        
        Args:
            operation: 操作名稱
            latency_ms: 延遲時間（毫秒）
        """
        self.latency_tracker.record(latency_ms)
    
    def increment_throughput(self, counter_name: str):
        """增加吞吐量計數
        
        Args:
            counter_name: 計數器名稱
        """
        if counter_name not in self.throughput_counters:
            self.throughput_counters[counter_name] = ThroughputCounter()
        
        self.throughput_counters[counter_name].increment()
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """獲取當前性能指標
        
        Returns:
            PerformanceMetrics: 當前性能指標
        """
        # 延遲統計
        latency_stats = self.latency_tracker.get_statistics()
        
        # 吞吐量統計
        throughput_stats = {}
        for name, counter in self.throughput_counters.items():
            throughput_stats[name] = counter.get_rate()
        
        # 內存統計
        memory_info = self.memory_tracker.get_memory_info()
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent()
        
        # 創建指標對象
        metrics = PerformanceMetrics(
            avg_latency_ms=latency_stats['avg'],
            p50_latency_ms=latency_stats['p50'],
            p95_latency_ms=latency_stats['p95'],
            p99_latency_ms=latency_stats['p99'],
            max_latency_ms=latency_stats['max'],
            
            cpu_usage_percent=cpu_percent,
            memory_usage_mb=memory_info['current_mb'],
            memory_usage_percent=memory_info['percent'],
            
            messages_per_second=throughput_stats.get('messages', 0.0),
            data_processed_per_second=throughput_stats.get('data_processed', 0.0),
            gui_updates_per_second=throughput_stats.get('gui_updates', 0.0)
        )
        
        return metrics
    
    def add_alert_callback(self, callback: Callable[[str, Dict], None]):
        """添加告警回調函數
        
        Args:
            callback: 告警回調函數
        """
        self.alert_callbacks.append(callback)
    
    def set_alert_threshold(self, metric_name: str, threshold: float):
        """設置告警閾值
        
        Args:
            metric_name: 指標名稱
            threshold: 閾值
        """
        self.alert_thresholds[metric_name] = threshold
    
    async def _collection_loop(self):
        """數據收集循環"""
        while self.running:
            try:
                # 收集當前指標
                metrics = self.get_current_metrics()
                
                # 保存到歷史記錄
                self.metrics_history.append(metrics)
                
                # 檢查告警條件
                await self._check_alerts(metrics)
                
                # 等待下次收集
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"性能數據收集失敗: {str(e)}")
                await asyncio.sleep(self.collection_interval)
    
    async def _check_alerts(self, metrics: PerformanceMetrics):
        """檢查告警條件
        
        Args:
            metrics: 當前指標
        """
        alerts = []
        
        # 檢查各項指標
        if metrics.cpu_usage_percent > self.alert_thresholds.get('cpu_usage_percent', 80):
            alerts.append({
                'type': 'cpu_high',
                'message': f'CPU使用率過高: {metrics.cpu_usage_percent:.1f}%',
                'value': metrics.cpu_usage_percent,
                'threshold': self.alert_thresholds['cpu_usage_percent']
            })
        
        if metrics.memory_usage_percent > self.alert_thresholds.get('memory_usage_percent', 85):
            alerts.append({
                'type': 'memory_high',
                'message': f'內存使用率過高: {metrics.memory_usage_percent:.1f}%',
                'value': metrics.memory_usage_percent,
                'threshold': self.alert_thresholds['memory_usage_percent']
            })
        
        if metrics.avg_latency_ms > self.alert_thresholds.get('avg_latency_ms', 100):
            alerts.append({
                'type': 'latency_high',
                'message': f'平均延遲過高: {metrics.avg_latency_ms:.1f}ms',
                'value': metrics.avg_latency_ms,
                'threshold': self.alert_thresholds['avg_latency_ms']
            })
        
        # 觸發告警回調
        for alert in alerts:
            for callback in self.alert_callbacks:
                try:
                    callback(alert['type'], alert)
                except Exception as e:
                    self.logger.error(f"告警回調執行失敗: {str(e)}")
    
    def get_metrics_history(self, duration_seconds: int = 300) -> List[PerformanceMetrics]:
        """獲取指標歷史
        
        Args:
            duration_seconds: 歷史時間長度（秒）
            
        Returns:
            List[PerformanceMetrics]: 指標歷史列表
        """
        cutoff_time = time.time() - duration_seconds
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """獲取性能報告
        
        Returns:
            Dict: 性能報告
        """
        current_metrics = self.get_current_metrics()
        profiler_report = self.profiler.get_report()
        memory_info = self.memory_tracker.get_memory_info()
        
        return {
            'current_metrics': current_metrics,
            'profiler_report': profiler_report,
            'memory_info': memory_info,
            'metrics_history_count': len(self.metrics_history),
            'alert_thresholds': self.alert_thresholds
        }
