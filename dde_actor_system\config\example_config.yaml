# DDE Actor System 配置範例
# 此文件展示所有可用的配置選項和說明
# 注意：系統實際使用 JSON 格式，此文件僅供參考

# ============================================================================
# 系統配置
# ============================================================================
system:
  system_name: "DDE Actor System"    # ✅ 使用：系統名稱，顯示在啟動信息中
  version: "1.0.0"                   # ✅ 使用：版本號，顯示在啟動信息中
  
  # 以下配置項目存在於 system_config.json 但未被實際使用：
  debug_mode: false                  # ❌ 未使用：調試模式開關
  log_level: "INFO"                  # ❌ 未使用：日誌級別（硬編碼為 INFO）
  log_file: "logs/system.log"        # ❌ 未使用：日誌文件路徑
  log_max_size_mb: 100               # ❌ 未使用：日誌文件最大大小
  log_backup_count: 5                # ❌ 未使用：日誌備份文件數量
  enable_monitoring: true            # ❌ 未使用：啟用系統監控
  monitoring_interval: 60.0          # ❌ 未使用：監控間隔（秒）
  health_check_interval: 300.0       # ❌ 未使用：健康檢查間隔（秒）
  enable_remote_api: false           # ❌ 未使用：啟用遠程 API
  api_host: "localhost"              # ❌ 未使用：API 主機地址
  api_port: 8080                     # ❌ 未使用：API 端口

# ============================================================================
# DDE 配置（精簡版本，僅包含實際使用的項目）
# ============================================================================
dde:
  polling_interval: 1.0              # ✅ 使用：DDE 數據輪詢間隔（秒）
                                     #          建議值：0.5-2.0 秒
  batch_size: 1000                   # ✅ 使用：DDE 批次處理大小
                                     #          建議值：100-1000
  batch_timeout: 0.01                # ✅ 使用：批次超時時間（秒）
                                     #          建議值：0.01-0.1 秒

# ============================================================================
# 文件輸出配置
# ============================================================================
file_output:
  batch_size: 1000                   # ✅ 使用：文件寫入批次大小
                                     #          建議值：100-1000
  flush_interval: 5.0                # ✅ 使用：文件刷新間隔（秒）
                                     #          建議值：1.0-10.0 秒

# ============================================================================
# 性能配置（完整版本，大部分未使用）
# ============================================================================
performance:
  # DDE 相關配置
  dde_buffer_size: 1048576           # ❌ 未使用：DDE 緩衝區大小
  dde_batch_size: 1000               # ✅ 使用：同上面 dde.batch_size
  dde_batch_timeout: 0.01            # ✅ 使用：同上面 dde.batch_timeout
  dde_enable_polling: true           # ✅ 使用：啟用輪詢模式
  dde_polling_interval: 1.0          # ✅ 使用：同上面 dde.polling_interval
  
  # 數據處理配置
  processing_batch_size: 1000        # ❌ 未使用：數據處理批次大小
  processing_queue_size: 10000       # ❌ 未使用：處理隊列大小
  processing_workers: 4              # ❌ 未使用：處理工作線程數
  
  # GUI 配置（全部未使用）
  gui_update_interval_ms: 16         # ❌ 未使用：GUI 更新間隔（毫秒）
  gui_max_batch_size: 1000           # ❌ 未使用：GUI 最大批次大小
  gui_virtual_rows: 100              # ❌ 未使用：GUI 虛擬行數
  
  # 文件配置
  file_batch_size: 1000              # ✅ 使用：同上面 file_output.batch_size
  file_flush_interval: 5.0           # ✅ 使用：同上面 file_output.flush_interval
  file_buffer_size: 8192             # ❌ 未使用：文件緩衝區大小
  
  # 背壓控制配置（全部未使用）
  backpressure_high_watermark: 8000  # ❌ 未使用：背壓高水位標記
  backpressure_low_watermark: 6000   # ❌ 未使用：背壓低水位標記
  backpressure_strategy: "drop_oldest" # ❌ 未使用：背壓處理策略
  
  # 內存管理配置（全部未使用）
  memory_pool_initial_size: 1000     # ❌ 未使用：內存池初始大小
  gc_threshold: 10000                # ❌ 未使用：垃圾回收閾值

# ============================================================================
# 產品配置
# ============================================================================
products:
  # 台指期貨07月合約
  - symbol: "FITXN07"                # ✅ 使用：產品代碼，用於識別
    data_types: ["tick", "order"]    # ❌ 未使用：數據類型（重要：不起作用！）
                                     #          原始 INI 版本用於模板系統
                                     #          當前版本直接使用 items 列表
    service: "XQTISC"                # ✅ 使用：DDE 服務名稱
    topic: "Quote"                   # ✅ 使用：DDE 主題名稱
    items:                           # ✅ 使用：DDE 項目列表（直接使用，不經過模板）
      - "FITXN07.TF-Time"            #          時間
      - "FITXN07.TF-TradingDate"     #          交易日期
      - "FITXN07.TF-Open"            #          開盤價
      - "FITXN07.TF-High"            #          最高價
      - "FITXN07.TF-Low"             #          最低價
      - "FITXN07.TF-Price"           #          成交價
      - "FITXN07.TF-TotalVolume"     #          總成交量
      - "FITXN07.TF-Volume"          #          成交量
    enabled: true                    # ✅ 使用：是否啟用此產品
    priority: 1                      # ❌ 未使用：優先級
    custom_settings: {}              # ❌ 未使用：自定義設置

  # 台指期貨08月合約
  - symbol: "FITXN08"
    data_types: ["tick", "order"]    # ❌ 未使用：同上說明
    service: "XQTISC"
    topic: "Quote"
    items:
      - "FITXN08.TF-Time"
      - "FITXN08.TF-TradingDate"
      - "FITXN08.TF-Open"
      - "FITXN08.TF-High"
      - "FITXN08.TF-Low"
      - "FITXN08.TF-Price"
      - "FITXN08.TF-TotalVolume"
      - "FITXN08.TF-Volume"
    enabled: true
    priority: 2                      # ❌ 未使用
    custom_settings: {}              # ❌ 未使用

  # 台積電股票（範例，已禁用）
  - symbol: "2330"
    data_types: ["tick"]             # ❌ 未使用
    service: "XQTISC"
    topic: "Quote"
    items:
      - "2330.TW-Time"
      - "2330.TW-TradingDate"
      - "2330.TW-Open"
      - "2330.TW-High"
      - "2330.TW-Low"
      - "2330.TW-Price"
      - "2330.TW-TotalVolume"
      - "2330.TW-Volume"
    enabled: false                   # ✅ 使用：禁用此產品
    priority: 3                      # ❌ 未使用
    custom_settings: {}              # ❌ 未使用

# ============================================================================
# 輸出格式說明
# ============================================================================
# 當前系統輸出格式（與原始 INI 版本不同）：
#
# 文件位置：outputs/
# ├── dde_data.csv          # 主要數據文件
# ├── dde_data.json         # JSON 格式數據
# └── production_dde_data.csv # 生產版本數據
#
# CSV 格式：
# timestamp,item,original_value,processed_value
# 2025-07-02 10:11:35.123,FITXN07.TF-Price,22277,22277
# 2025-07-02 10:11:35.456,FITXN07.TF-Volume,1.000000,1.000000
#
# 原始 INI 版本輸出格式：
# outputs/
# ├── tick/
# │   ├── FITXN07_tick.csv
# │   └── FITXN08_tick.csv
# ├── order/
# │   ├── FITXN07_order.csv
# │   └── FITXN08_order.csv
# └── ...

# ============================================================================
# 使用建議
# ============================================================================
# 1. 新用戶：使用 simple_config.json（僅包含實際使用的配置）
# 2. 高級用戶：複製 simple_config.json 並根據需要修改
# 3. 調試：使用 clean_dde_monitor.py（無警告版本）
# 4. 生產：使用 production_dde_monitor.py（完整功能版本）

# ============================================================================
# 重要提醒
# ============================================================================
# 1. data_types 配置不起作用！系統直接使用 items 列表
# 2. 約70%的配置項目未被實際使用
# 3. 輸出格式與原始 INI 版本完全不同
# 4. 系統實際使用 JSON 格式，此 YAML 文件僅供參考
