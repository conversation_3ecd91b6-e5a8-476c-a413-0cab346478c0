# DDE Monitor Project Requirements
# ================================
# 
# 這個檔案列出了 DDE 監控程式所需的所有 Python 套件
# 使用方法: pip install -r requirements.txt
#
# 建議在虛擬環境中安裝:
# python -m venv venv
# venv\Scripts\activate  (Windows)
# pip install -r requirements.txt

# Core GUI Framework
# ==================
PySide6>=6.5.0
# Qt6 based GUI framework for Python
# 提供現代化的使用者介面元件

# Windows API Support
# ===================
pywin32>=306
# Windows API bindings for Python
# 提供 Windows DDE API 和其他系統 API 的存取

# Configuration Management
# ========================
# configparser is built-in to Python 3.x
# 設定檔解析功能 (Python 內建)

# Logging
# =======
# logging is built-in to Python 3.x
# 日誌系統 (Python 內建)

# Data Structures and Utilities
# =============================
# dataclasses is built-in to Python 3.7+
# 資料類別支援 (Python 內建)

# typing is built-in to Python 3.5+
# 型別提示支援 (Python 內建)

# collections is built-in to Python
# 集合類別 (Python 內建)

# Threading and Concurrency
# =========================
# threading is built-in to Python
# 多線程支援 (Python 內建)

# queue is built-in to Python
# 佇列資料結構 (Python 內建)

# Date and Time
# =============
# datetime is built-in to Python
# 日期時間處理 (Python 內建)

# File System Operations
# ======================
# os is built-in to Python
# 作業系統介面 (Python 內建)

# sys is built-in to Python
# 系統特定參數和函數 (Python 內建)

# Development and Testing (Optional)
# ==================================
# 以下套件用於開發和測試，可選安裝

# Testing Framework
pytest>=7.0.0
# 現代化的 Python 測試框架

pytest-cov>=4.0.0
# pytest 的覆蓋率插件

pytest-mock>=3.10.0
# pytest 的 mock 插件

# Code Quality
flake8>=6.0.0
# Python 程式碼風格檢查工具

black>=23.0.0
# Python 程式碼格式化工具

isort>=5.12.0
# Python import 排序工具

# Type Checking
mypy>=1.0.0
# 靜態型別檢查工具

# Documentation
sphinx>=6.0.0
# 文件生成工具

sphinx-rtd-theme>=1.2.0
# Sphinx 的 Read the Docs 主題

# Performance Monitoring (Optional)
# =================================
psutil>=5.9.0
# 系統和程序監控工具
# 用於監控記憶體使用、CPU 使用率等

memory-profiler>=0.61.0
# 記憶體使用分析工具
# 用於分析記憶體洩漏問題

# Data Analysis (Optional)
# ========================
pandas>=2.0.0
# 資料分析和處理工具
# 如果需要進階資料分析功能

numpy>=1.24.0
# 數值計算工具
# pandas 的依賴，也可用於數值處理

# Visualization (Optional)
# ========================
matplotlib>=3.7.0
# 繪圖工具
# 如果需要圖表顯示功能

plotly>=5.14.0
# 互動式圖表工具
# 現代化的圖表顯示

# Packaging (Optional)
# ====================
pyinstaller>=5.10.0
# 將 Python 程式打包成執行檔
# 用於部署和分發

# Utility Libraries (Optional)
# ============================
colorama>=0.4.6
# 跨平台彩色終端輸出
# 改善命令列輸出的可讀性

tqdm>=4.65.0
# 進度條工具
# 用於顯示長時間運行操作的進度

# JSON handling (built-in but enhanced version available)
ujson>=5.7.0
# 快速的 JSON 處理工具
# 比內建 json 模組更快

# Configuration file formats (Optional)
# =====================================
pyyaml>=6.0
# YAML 格式支援
# 如果需要支援 YAML 設定檔

toml>=0.10.2
# TOML 格式支援
# 如果需要支援 TOML 設定檔

# Networking (Optional)
# =====================
requests>=2.31.0
# HTTP 請求工具
# 如果需要網路功能

# Database (Optional)
# ==================
sqlite3
# SQLite 資料庫支援 (Python 內建)
# 如果需要資料庫功能

# Encryption (Optional)
# =====================
cryptography>=41.0.0
# 加密工具
# 如果需要資料加密功能

# Environment Management
# ======================
python-dotenv>=1.0.0
# .env 檔案支援
# 用於管理環境變數

# Version Information
# ==================
# 最低 Python 版本要求: Python 3.10+
# 建議 Python 版本: Python 3.11+
# 
# 核心依賴 (必須安裝):
# - PySide6: GUI 框架
# - pywin32: Windows API 支援
# 
# 可選依賴:
# - 開發工具: pytest, flake8, black, mypy
# - 效能監控: psutil, memory-profiler  
# - 資料分析: pandas, numpy
# - 視覺化: matplotlib, plotly
# - 打包工具: pyinstaller
# 
# 安裝建議:
# 1. 基本安裝: pip install PySide6 pywin32
# 2. 完整安裝: pip install -r requirements.txt
# 3. 開發安裝: pip install -r requirements.txt (包含所有可選依賴)
