# 引擎包装器版GUI测试指南

## 📋 概述

引擎包装器版现在提供了简单的GUI界面，便于与其他版本进行直观比较。GUI界面参考了多商品版本的设计，提供了相似的功能和布局。

## 🖥️ GUI界面特性

### 主要功能
- ✅ **控制面板**: 初始化、连接、断开DDE服务
- ✅ **统计信息**: 实时显示数据接收统计
- ✅ **商品标签页**: 按商品分组显示数据
- ✅ **数据类型标签**: 每个商品下按数据类型分类
- ✅ **实时数据表格**: 显示DDE项目和接收值
- ✅ **运行日志**: 实时显示系统日志
- ✅ **状态栏**: 显示连接状态和当前时间

### 界面布局
```
┌─────────────────────────────────────────────────────────┐
│  引擎包装器版多商品DDE测试                                │
├─────────────────────────────────────────────────────────┤
│  [初始化并连接] [清除日志] [重置统计]                      │
├─────────────────────────────────────────────────────────┤
│  总接收: 0  |  接收率: 0/秒  |  运行时间: 0秒  |  引擎数: 0  │
├─────────────────────────────────────────────────────────┤
│  ┌─ FITXN07.TF ─────────────────────────────────────┐   │
│  │  ┌─ TICK ─┐ ┌─ ORDER ─┐ ┌─ LEVEL2 ─┐ ┌─ DAILY ─┐ │   │
│  │  │项目名称 │ │项目名称  │ │项目名称   │ │项目名称  │ │   │
│  │  │项目代码 │ │项目代码  │ │项目代码   │ │项目代码  │ │   │
│  │  │当前值   │ │当前值    │ │当前值     │ │当前值    │ │   │
│  │  │状态     │ │状态      │ │状态       │ │状态      │ │   │
│  │  └─────────┘ └─────────┘ └─────────┘ └─────────┘ │   │
│  └─────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────┤
│  运行日志                                               │
│  13:15:30 [INFO] 配置解析完成                           │
│  13:15:31 [INFO] 包装器初始化完成                       │
│  13:15:32 [INFO] DDE连接成功                           │
├─────────────────────────────────────────────────────────┤
│  已连接                           2025/06/26 13:15:33   │
└─────────────────────────────────────────────────────────┘
```

## 🚀 使用方法

### 1. 启动GUI测试程序

```bash
cd dde_engine_wrapper
python run_gui_test.py
```

### 2. 启动比较测试（同时运行两个版本）

```bash
cd dde_engine_wrapper
python run_comparison_gui.py
```

### 3. 操作步骤

1. **启动程序**: 运行上述命令之一
2. **初始化**: 点击"初始化并连接"按钮
3. **连接DDE**: 初始化完成后，按钮变为"连接DDE"，点击连接
4. **观察数据**: 在各个标签页中观察DDE数据接收情况
5. **监控统计**: 查看统计信息面板的实时数据
6. **查看日志**: 在日志面板中查看详细运行信息
7. **断开连接**: 点击"断开连接"按钮停止DDE连接

## 📊 界面功能详解

### 控制面板
- **初始化并连接**: 第一次点击进行系统初始化
- **连接DDE/断开连接**: 连接或断开DDE服务
- **清除日志**: 清空日志显示区域
- **重置统计**: 重置所有统计计数器

### 统计信息面板
- **总接收**: 累计接收的DDE数据条数
- **接收率**: 每秒接收的数据条数
- **运行时间**: 程序运行的总时间
- **引擎数**: 当前运行的DDE引擎数量

### 商品标签页
- 每个商品一个标签页
- 标签页名称为商品代码（如FITXN07.TF）
- 支持多商品同时监控

### 数据类型标签
- **TICK**: 成交数据（价格、量等）
- **ORDER**: 委托数据（买卖盘等）
- **LEVEL2**: 五档报价数据
- **DAILY**: 日线数据（开高低收等）

### 数据表格
- **项目名称**: DDE项目的简化名称
- **项目代码**: 完整的DDE项目代码
- **当前值**: 最新接收到的数据值
- **状态**: 数据接收状态（等待/已更新）

## 🔍 与其他版本比较

### 比较要点

1. **启动速度**
   - 观察初始化时间
   - 比较连接建立速度

2. **数据接收**
   - 对比接收率统计
   - 检查数据完整性
   - 观察更新频率

3. **界面响应**
   - 测试界面操作流畅度
   - 观察数据更新延迟
   - 检查界面冻结情况

4. **资源使用**
   - 通过任务管理器观察CPU使用率
   - 监控内存占用情况
   - 比较进程数量

5. **稳定性**
   - 长时间运行测试
   - 观察错误处理能力
   - 检查内存泄漏情况

### 预期优势（引擎包装器版）

1. **更好的架构**
   - 模块化设计
   - 更清晰的代码结构
   - 更好的错误处理

2. **增强的监控**
   - 实时统计信息
   - 详细的日志记录
   - 系统状态监控

3. **改进的性能**
   - 并行处理机制
   - 智能资源管理
   - 优化的数据流程

## 🛠️ 故障排除

### 常见问题

1. **GUI无法启动**
   ```
   错误: PySide6 不可用
   解决: pip install PySide6
   ```

2. **配置文件错误**
   ```
   错误: 配置文件不存在
   解决: 确保 config/multi_config.ini 存在
   ```

3. **DDE连接失败**
   ```
   错误: DDE服务连接失败
   解决: 检查DDE服务是否运行，确认商品代码正确
   ```

4. **数据不更新**
   ```
   可能原因: DDE项目订阅失败
   解决: 检查日志中的订阅信息，确认项目代码正确
   ```

### 调试技巧

1. **查看详细日志**
   - 日志文件位置: `logs/wrapper_gui_test.log`
   - GUI日志面板显示实时信息

2. **检查进程状态**
   ```bash
   # Windows
   tasklist | findstr python
   
   # 查看资源使用
   任务管理器 -> 详细信息
   ```

3. **验证配置解析**
   ```bash
   cd dde_engine_wrapper
   python examples/simple_multi_test.py
   ```

## 📝 测试建议

### 基本功能测试
1. 启动GUI程序
2. 初始化系统
3. 连接DDE服务
4. 观察数据接收
5. 测试断开重连
6. 检查日志记录

### 性能测试
1. 长时间运行（30分钟以上）
2. 监控资源使用情况
3. 记录数据接收统计
4. 观察界面响应速度
5. 检查内存使用趋势

### 比较测试
1. 同时启动两个版本
2. 使用相同配置文件
3. 对比数据接收率
4. 比较资源使用情况
5. 测试稳定性差异

## 🎯 测试目标

通过GUI界面的直观比较，验证引擎包装器版本在以下方面的改进：

- ✅ **功能完整性**: 与原版功能一致
- ✅ **性能优化**: 更高的数据处理效率
- ✅ **稳定性**: 更好的错误处理和恢复
- ✅ **可维护性**: 更清晰的架构和日志
- ✅ **用户体验**: 更友好的界面和操作

现在您可以通过GUI界面轻松地与原版多商品DDE监控进行直观比较了！
