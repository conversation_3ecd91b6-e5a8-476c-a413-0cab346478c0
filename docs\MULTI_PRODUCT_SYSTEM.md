# 多商品DDE监控系统 v7.0

## 概述

多商品DDE监控系统是基于原有模块化版本改造的增强版本，支持同时监控多个商品的不同数据类型，提供统一的GUI界面和灵活的配置管理。

## 主要特性

### 🎯 核心功能
- **多商品同时监控**: 支持期货、股票等不同类型商品的混合监控
- **模板化配置**: 使用 `{symbol}` 占位符创建可重用的配置模板
- **分层自动化**: 支持系统级和商品级的不同自动化策略
- **统一GUI界面**: 标签页式界面，每个商品独立显示
- **实时数据处理**: 多线程数据处理，支持高频数据更新

### 📊 数据类型支持
- **tick**: 逐笔交易数据（价格、成交量等）
- **order**: 委托订单数据（买卖盘信息）
- **level2**: 五档报价数据
- **daily**: 日线数据（结算价、涨跌停等）

### 🔄 自动化功能
- **时间表连接**: 支持多时间段的自动连接（如期货夜盘+日盘）
- **商品特定策略**: 每个商品可配置不同的自动化策略
- **灵活结束动作**: 支持完全断开或仅取消订阅两种结束方式
- **全局自动结束**: 统一的程序自动结束时间

## 文件结构

```
dde_monitor/
├── dde_monitor_multi.py          # 多商品主程序
├── multi_config.ini              # 多商品配置文件
├── run_multi_product.bat         # 启动脚本
├── test_multi_product.py         # 测试脚本
├── utils/
│   └── config_manager.py         # 扩展的配置管理器
├── core/
│   ├── data_handler.py           # 多商品数据处理器
│   └── multi_auto_manager.py     # 多商品自动化管理器
├── gui/
│   └── multi_product_window.py   # 多商品主窗口
└── docs/
    └── MULTI_PRODUCT_SYSTEM.md   # 本文档
```

## 配置文件说明

### multi_config.ini 结构

```ini
# 系统设定
[System]
dde_service = XQTISC
dde_topic = Quote
disconnect_on_exit = false
output_base_path = ./data/
log_base_path = ./logs/

# 商品清单
[Symbols]
symbol_list = FITXN07.TF,FITX07.TF,2330.TW

# 数据类型模板
[Template_Tick_Items]
item1_name = 交易時間
item1_code = {symbol}-Time
item2_name = 成交價
item2_code = {symbol}-Price
...

# 数据类型配置
[DataType_tick]
items_template = Template_Tick_Items
table_enable_time_newline = true
...

# 自动连接模板
[AutoConnect_FuturesDay]
enable_auto_connect = True
auto_connect_mode = schedule
schedule_connect_times = 08:45:00-13:45:00
...

# 商品配置
[FITXN07.TF]
enabled_types = tick,order,level2,daily
output_path = ./outputs/FITXN07/
auto_connect_template = AutoConnect_FuturesFullDay
```

### 配置要点

1. **模板化设计**: 使用 `{symbol}` 占位符，一套模板适用所有商品
2. **商品特定配置**: 每个商品可启用不同的数据类型组合
3. **自动化模板**: 预定义的自动化策略，可重复使用
4. **灵活输出**: 每个商品可配置独立的输出路径

## 使用方法

### 启动程序

```bash
# 使用默认配置文件
python dde_monitor_multi.py

# 使用指定配置文件
python dde_monitor_multi.py -c my_config.ini

# 或使用批处理文件
run_multi_product.bat
```

### 生成单商品配置

```bash
# 从多商品配置生成单商品配置文件
python dde_monitor_multi.py --generate-single FITXN07.TF tick config_FITXN07_tick.ini
```

### GUI操作

1. **连接设置**: 配置DDE服务名称和主题
2. **服务连线**: 建立与DDE服务器的连接
3. **测试项目**: 测试所有商品的监控项目可用性
4. **订阅项目**: 开始接收实时数据更新
5. **商品标签页**: 切换查看不同商品的数据
6. **自动化状态**: 查看和管理自动化功能

## 技术架构

### 核心组件

1. **MultiProductConfigManager**: 多商品配置管理
   - 模板解析和商品配置生成
   - 配置验证和错误检查
   - 单商品配置文件生成

2. **MultiProductDataProcessor**: 多商品数据处理
   - 并行处理多个商品的数据
   - 智能项目解析和分类
   - 独立的文件输出管理

3. **MultiProductAutoManager**: 多商品自动化管理
   - 商品级自动化策略
   - 时间表管理和状态跟踪
   - 全局自动结束控制

4. **MultiProductMainWindow**: 多商品GUI界面
   - 标签页式商品显示
   - 实时数据更新
   - 自动化状态监控

### 数据流程

```
DDE Server → DDEClient → MultiProductDataProcessor → GUI Display
                                    ↓
                              File Output (per symbol/type)
```

### 自动化流程

```
MultiProductAutoManager → Symbol Schedule Check → Auto Connect/Disconnect
                                    ↓
                              Status Update → GUI Display
```

## 优势对比

### vs 单商品版本

| 特性 | 单商品版本 | 多商品版本 |
|------|------------|------------|
| 商品数量 | 1个 | 多个 |
| 配置管理 | 独立配置文件 | 模板化配置 |
| 资源占用 | N个进程 | 1个进程 |
| 界面管理 | N个窗口 | 1个统一界面 |
| 自动化策略 | 单一策略 | 商品特定策略 |

### 性能优化

- **统一DDE连接**: 所有商品共享一个DDE连接，减少资源占用
- **批量数据处理**: 优化的数据处理队列，提高处理效率
- **GUI更新控制**: 可调节的更新频率，平衡性能和实时性
- **内存管理**: 限制数据队列大小，防止内存溢出

## 扩展性

### 添加新数据类型

1. 在配置文件中添加新的模板：
```ini
[Template_NewType_Items]
item1_name = 新项目名称
item1_code = {symbol}-NewCode
```

2. 添加数据类型配置：
```ini
[DataType_newtype]
items_template = Template_NewType_Items
```

3. 在商品配置中启用：
```ini
[SYMBOL.XX]
enabled_types = tick,order,newtype
```

### 添加新商品

1. 在商品列表中添加：
```ini
[Symbols]
symbol_list = FITXN07.TF,NEW_SYMBOL.XX
```

2. 添加商品配置：
```ini
[NEW_SYMBOL.XX]
enabled_types = tick,order
output_path = ./outputs/NEW_SYMBOL/
auto_connect_template = AutoConnect_Stock
```

## 故障排除

### 常见问题

1. **配置文件错误**: 运行测试脚本检查配置
2. **DDE连接失败**: 检查服务名称和主题设置
3. **数据无更新**: 验证项目代码格式和DDE服务状态
4. **自动化不工作**: 检查时间表配置和商品自动化模板

### 调试工具

- `test_multi_product.py`: 全面的功能测试
- 程序日志: 详细的运行日志记录
- GUI状态显示: 实时的连接和数据状态
- 资源监控: CPU和内存使用情况

## 未来规划

- [ ] 数据库存储支持
- [ ] Web界面管理
- [ ] 更多数据类型支持
- [ ] 分布式部署能力
- [ ] 高级数据分析功能
