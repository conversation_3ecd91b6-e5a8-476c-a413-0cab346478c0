# 🚀 DDE Actor System - 啟動指南

## 📋 快速開始

### 🎮 方案 1：GUI 演示版本（推薦）
```bash
python main_gui_demo.py
```
- ✅ 最佳體驗，有可視化界面
- ✅ 使用模擬數據，不需要 DDE 服務
- ✅ 可以看到實時數據流

### 🖥️ 方案 2：命令行版本
```bash
python main.py --config config/system_config.json
```
- ✅ 使用真實 DDE 數據
- ⚠️ 需要 DDE 服務運行

## 📊 監控數據

### 在另一個終端運行：
```bash
# 實時監控數據文件變化
python watch_data.py

# 或者檢查系統狀態
python check_status.py
```

## 📁 查看結果

### 輸出文件位置：
- `outputs/dde_data.csv` - 主要數據文件
- `outputs/dde_data.json` - JSON 格式數據
- `logs/system.log` - 系統日誌

### 手動查看文件：
```bash
# Windows
type outputs\dde_data.csv
dir outputs\

# 或使用 Python
python -c "import pandas as pd; print(pd.read_csv('outputs/dde_data.csv').head())"
```

## 🔧 故障排除

### 如果 DDE 連接失敗：
```bash
# 使用 GUI 演示版本
python main_gui_demo.py
```

### 如果沒有數據：
```bash
# 運行完整測試
python test_complete_system.py
```

### 如果系統無響應：
1. 按 `Ctrl+C` 停止
2. 等待 3 秒
3. 重新啟動

## 🎯 推薦操作流程

### 第一次使用：
1. **啟動 GUI 演示版本**
   ```bash
   python main_gui_demo.py
   ```

2. **在新終端監控數據**
   ```bash
   python watch_data.py
   ```

3. **查看生成的文件**
   ```bash
   dir outputs\
   type outputs\dde_data.csv
   ```

### 日常使用：
1. **清理舊數據（可選）**
   ```bash
   python cleanup_and_backup.py
   ```

2. **啟動系統**
   ```bash
   python main.py --config config/system_config.json
   ```

3. **監控狀態**
   ```bash
   python check_status.py
   ```

## 📈 預期結果

### 成功運行時你會看到：
- ✅ 系統啟動信息
- ✅ Actor 啟動成功
- ✅ DDE 連接建立（如果使用真實數據）
- ✅ 數據文件生成在 `outputs/` 目錄
- ✅ 實時數據更新

### 數據文件格式：
```csv
timestamp,item,original_value,processed_value
2025-07-02 09:30:14.488,FITXN07.tick.成交價,22272,22272
2025-07-02 09:30:14.574,FITXN07.tick.成交量,1,1
...
```

## 🆘 需要幫助？

### 運行診斷：
```bash
python simple_test.py        # 基本功能測試
python check_syntax.py       # 語法檢查
python test_complete_system.py  # 完整流程測試
```

### 查看日誌：
```bash
type logs\system.log
```

## 🎉 系統已就緒！

你的 DDE Actor System 已經完全可用：
- ✅ 所有組件正常工作
- ✅ 數據處理流程完美
- ✅ 文件寫入功能正常
- ✅ 支持實時數據監控

**現在就可以開始使用！**
