# DDE 監控程式文件總覽

## 文件結構

本專案包含以下技術文件，為後續開發提供完整的參考資料：

### 📋 專案分析報告
**檔案**: [PROJECT_ANALYSIS.md](PROJECT_ANALYSIS.md)
**內容**: 
- 專案概述和技術架構
- 專案結構詳細分析
- 核心模組功能說明
- 設定檔配置分析
- 已實現功能清單
- 待改進功能識別
- 技術債務評估
- 開發建議和風險評估

### 🏗️ 架構設計文件
**檔案**: [ARCHITECTURE_DESIGN.md](ARCHITECTURE_DESIGN.md)
**內容**:
- 系統整體架構圖
- 核心模組設計說明
- 資料流設計和資料結構
- 線程模型和同步機制
- 設定管理架構
- 錯誤處理策略
- 效能優化設計
- 擴展性和安全性考量

### 📚 API 參考文件
**檔案**: [API_REFERENCE.md](API_REFERENCE.md)
**內容**:
- DDEMonitor 類別完整 API
- DataFileHandler 檔案處理 API
- DataProcessor 資料處理 API
- dydde.DDEClient DDE 客戶端 API
- 資料結構定義
- 設定檔 API 說明
- 錯誤處理 API

### 🛠️ 開發指南
**檔案**: [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md)
**內容**:
- 開發環境設置
- 開發規範和程式碼風格
- 開發流程和提交規範
- 常見開發任務示例
- 測試指南和除錯技巧
- 部署指南

### 🔧 故障排除指南
**檔案**: [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
**內容**:
- 常見問題診斷和解決方案
- DDE 連接問題處理
- 資料接收問題排查
- 檔案輸出問題解決
- UI 介面問題修復
- 效能問題優化
- 日誌分析方法
- 緊急處理程序

### 🤖 自動化功能指南
**檔案**: [AUTO_FEATURES_GUIDE.md](AUTO_FEATURES_GUIDE.md)
**內容**:
- 自動連線功能詳解
- 定時斷線和自動結束
- 時間表配置和跨日支援
- 使用場景範例
- 配置最佳實踐
- 故障排除和技術支援

## 快速導航

### 🚀 新手入門
如果您是第一次接觸這個專案：
1. 先閱讀 [PROJECT_ANALYSIS.md](PROJECT_ANALYSIS.md) 了解專案概況
2. 查看 [ARCHITECTURE_DESIGN.md](ARCHITECTURE_DESIGN.md) 理解系統架構
3. 參考 [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md) 設置開發環境

### 🔍 功能開發
如果您需要開發新功能：
1. 查閱 [API_REFERENCE.md](API_REFERENCE.md) 了解現有 API
2. 參考 [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md) 的開發流程
3. 遵循 [ARCHITECTURE_DESIGN.md](ARCHITECTURE_DESIGN.md) 的設計原則

### 🐛 問題排查
如果遇到問題需要排查：
1. 首先查看 [TROUBLESHOOTING.md](TROUBLESHOOTING.md) 的常見問題
2. 參考 [API_REFERENCE.md](API_REFERENCE.md) 確認 API 使用方式
3. 檢查 [PROJECT_ANALYSIS.md](PROJECT_ANALYSIS.md) 的已知問題

### 🏗️ 架構改進
如果需要進行架構層面的改進：
1. 參考 [ARCHITECTURE_DESIGN.md](ARCHITECTURE_DESIGN.md) 的現有設計
2. 查看 [PROJECT_ANALYSIS.md](PROJECT_ANALYSIS.md) 的技術債務
3. 遵循 [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md) 的開發規範

## 專案狀態

### ✅ 已完成
- 基本 DDE 連接和資料接收功能
- GUI 介面和使用者互動
- 檔案輸出和日誌系統
- 多線程資料處理
- 完整的技術文件
- **🆕 自動連線功能** (立即/延遲/時間表模式)
- **🆕 自動斷線和重連機制**
- **🆕 定時自動結束程式**
- **🆕 跨日時間表支援**
- **🆕 週末跳過功能**

### ⚠️ 需要改進
- ~~DDE 連接穩定性~~ (已改善，新增自動重連)
- ~~錯誤處理和恢復機制~~ (已改善)
- UI/UX 使用者體驗
- 效能優化
- 測試覆蓋率

### 🔮 未來規劃
- 資料分析功能
- 圖表顯示
- 插件系統
- 跨平台支援
- 雲端整合
- 系統托盤功能
- 聲音通知
- 電子郵件通知

## 重要注意事項

### ⚠️ dydde 模組
- **dydde 模組是半成品，除非絕對必要，否則不要修改**
- 該模組提供了完整的 DDE 客戶端功能
- 如需修改，請先備份並充分測試

### 🔧 開發環境
- 僅支援 Windows 平台 (DDE 協議限制)
- 需要 Python 3.10+ 和 PySide6
- 建議使用虛擬環境進行開發

### 📝 文件維護
- 程式碼變更時請同步更新相關文件
- 新增功能時請更新 API 參考文件
- 發現問題時請更新故障排除指南

## 聯絡資訊

### 技術支援
- 查看相關技術文件
- 檢查日誌檔案
- 參考故障排除指南

### 文件回饋
如果發現文件中的錯誤或需要補充的內容，請：
1. 記錄具體的問題描述
2. 提供改進建議
3. 更新相關文件

## 版本資訊

- **專案版本**: DDE Monitor v6
- **文件版本**: v1.0
- **建立日期**: 2025-06-16
- **最後更新**: 2025-06-16
- **Python 版本**: 3.10+
- **主要依賴**: PySide6, pywin32

## 授權資訊

本專案及相關文件僅供內部開發使用，請勿外傳。

---

**📖 開始閱讀**: 建議從 [PROJECT_ANALYSIS.md](PROJECT_ANALYSIS.md) 開始，全面了解專案現狀和後續開發方向。
