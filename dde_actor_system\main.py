#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE Actor System - 主程式入口

高性能DDE數據處理系統的主要入口點
"""

import asyncio
import argparse
import logging
import signal
import sys
import time
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from core.event_loop import EventLoopManager, EventLoopConfig
from core.performance import PerformanceMonitor
from actors.dde_receiver import DDEReceiverActor
from actors.data_processor import DataProcessorActor
from actors.gui_updater import GUIUpdaterActor
from actors.file_writer import FileWriterActor
from utils.config_manager import DynamicConfigManager
from utils.logger import setup_logger


class DDEActorSystem:
    """DDE Actor系統主類"""
    
    def __init__(self, config_file: str):
        """初始化DDE Actor系統
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = logging.getLogger("DDEActorSystem")
        
        # 核心組件
        self.config_manager = DynamicConfigManager(config_file)
        self.event_loop_manager = None
        self.performance_monitor = PerformanceMonitor()
        
        # Actor實例
        self.actors = {}
        
        # 系統狀態
        self.running = False
        self.startup_time = 0
    
    async def initialize(self) -> bool:
        """初始化系統
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("正在初始化DDE Actor系統")
            
            # 加載配置
            if not self.config_manager.load_config():
                self.logger.error("加載配置文件失敗")
                return False
            
            # 驗證配置
            errors = self.config_manager.validate_config()
            if errors:
                self.logger.error(f"配置驗證失敗: {errors}")
                return False
            
            # 設置日誌
            self._setup_logging()
            
            # 創建事件循環管理器
            loop_config = EventLoopConfig(
                max_workers=self.config_manager.performance_config.processing_workers,
                debug_mode=self.config_manager.system_config.debug_mode
            )
            self.event_loop_manager = EventLoopManager(loop_config)
            
            # 創建Actor實例
            await self._create_actors()
            
            # 註冊Actor到事件循環管理器
            await self._register_actors()
            
            # 設置消息路由
            await self._setup_message_routes()
            
            self.logger.info("DDE Actor系統初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化系統失敗: {str(e)}")
            return False
    
    async def start(self) -> bool:
        """啟動系統
        
        Returns:
            bool: 啟動是否成功
        """
        try:
            if self.running:
                self.logger.warning("系統已經在運行")
                return False
            
            self.logger.info("正在啟動DDE Actor系統")
            self.startup_time = time.time()
            
            # 啟動性能監控
            await self.performance_monitor.start()
            
            # 啟動事件循環管理器
            if not await self.event_loop_manager.start():
                self.logger.error("啟動事件循環管理器失敗")
                return False
            
            # 啟動配置文件監控
            await self.config_manager.start_watching()
            
            # 啟用自動性能調優
            self.config_manager.enable_auto_tuning(self.performance_monitor)
            
            self.running = True
            
            # 顯示啟動信息
            self._show_startup_info()
            
            self.logger.info("DDE Actor系統啟動成功")
            return True
            
        except Exception as e:
            self.logger.error(f"啟動系統失敗: {str(e)}")
            return False
    
    async def stop(self) -> bool:
        """停止系統
        
        Returns:
            bool: 停止是否成功
        """
        try:
            if not self.running:
                return True
            
            self.logger.info("正在停止DDE Actor系統")
            
            # 停止配置文件監控
            await self.config_manager.stop_watching()
            
            # 禁用自動調優
            self.config_manager.disable_auto_tuning()
            
            # 停止事件循環管理器
            if self.event_loop_manager:
                await self.event_loop_manager.stop()
            
            # 停止性能監控
            await self.performance_monitor.stop()
            
            self.running = False
            
            # 顯示運行統計
            self._show_shutdown_info()
            
            self.logger.info("DDE Actor系統停止成功")
            return True
            
        except Exception as e:
            self.logger.error(f"停止系統失敗: {str(e)}")
            return False
    
    async def run_forever(self):
        """運行系統直到收到停止信號"""
        try:
            if not await self.initialize():
                return False
            
            if not await self.start():
                return False
            
            # 運行事件循環
            await self.event_loop_manager.run_forever()
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("收到中斷信號")
            return True
        except Exception as e:
            self.logger.error(f"運行系統失敗: {str(e)}")
            return False
        finally:
            await self.stop()
    
    async def _create_actors(self):
        """創建Actor實例"""
        try:
            perf_config = self.config_manager.performance_config
            
            # 創建DDE接收Actor
            dde_config = {
                'buffer_size': perf_config.dde_buffer_size,
                'batch_size': perf_config.dde_batch_size,
                'batch_timeout': perf_config.dde_batch_timeout,
                'enable_polling': getattr(perf_config, 'dde_enable_polling', True),
                'polling_interval': getattr(perf_config, 'dde_polling_interval', 1.0),
                'connections': self._build_dde_connections_config()
            }
            self.actors['DDEReceiver'] = DDEReceiverActor('DDEReceiver', dde_config)
            
            # 創建數據處理Actor
            processor_config = {
                'batch_size': perf_config.processing_batch_size,
                'queue_size': perf_config.processing_queue_size,
                'backpressure': {
                    'high_watermark': perf_config.backpressure_high_watermark,
                    'low_watermark': perf_config.backpressure_low_watermark,
                    'strategy': perf_config.backpressure_strategy
                }
            }
            self.actors['DataProcessor'] = DataProcessorActor('DataProcessor', processor_config)
            
            # 創建GUI更新Actor
            gui_config = {
                'batch_updater': {
                    'update_interval_ms': perf_config.gui_update_interval_ms,
                    'max_batch_size': perf_config.gui_max_batch_size
                },
                'virtual_table': {
                    'visible_rows': perf_config.gui_virtual_rows
                }
            }
            self.actors['GUIUpdater'] = GUIUpdaterActor('GUIUpdater', gui_config)
            
            # 創建文件寫入Actor
            file_config = {
                'batch_size': perf_config.file_batch_size,
                'flush_interval': perf_config.file_flush_interval,
                'files': self._build_file_configs()
            }
            self.actors['FileWriter'] = FileWriterActor('FileWriter', file_config)
            
            self.logger.info(f"創建了 {len(self.actors)} 個Actor")
            
        except Exception as e:
            self.logger.error(f"創建Actor失敗: {str(e)}")
            raise
    
    async def _register_actors(self):
        """註冊Actor到事件循環管理器"""
        for actor in self.actors.values():
            self.event_loop_manager.register_actor(actor)
    
    async def _setup_message_routes(self):
        """設置消息路由"""
        from core.message_system import MessageType

        router = self.event_loop_manager.message_router

        # DDE數據路由
        router.add_route(MessageType.DDE_DATA, "DataProcessor")

        # 數據處理路由
        router.add_route(MessageType.DATA_PROCESS, "DataProcessor")

        # GUI更新路由
        router.add_route(MessageType.GUI_UPDATE, "GUIUpdater")
        router.add_route(MessageType.GUI_BATCH_UPDATE, "GUIUpdater")

        # 文件寫入路由
        router.add_route(MessageType.FILE_WRITE, "FileWriter")
    
    def _build_dde_connections_config(self) -> list:
        """構建DDE連接配置"""
        connections = []
        
        for symbol, product_config in self.config_manager.product_configs.items():
            if product_config.enabled:
                connection = {
                    'id': f"conn_{symbol}",
                    'service': product_config.service,
                    'topic': product_config.topic,
                    'items': product_config.items
                }
                connections.append(connection)
        
        return connections
    
    def _build_file_configs(self) -> dict:
        """構建文件配置"""
        return {
            'default': {
                'filename': 'outputs/dde_data.csv',
                'format': 'csv',
                'max_size_mb': 100,
                'compress': True
            },
            'json': {
                'filename': 'outputs/dde_data.json',
                'format': 'json',
                'max_size_mb': 50,
                'compress': True
            }
        }
    
    def _setup_logging(self):
        """設置日誌"""
        system_config = self.config_manager.system_config
        
        setup_logger(
            level=system_config.log_level,
            log_file=system_config.log_file,
            max_size_mb=system_config.log_max_size_mb,
            backup_count=system_config.log_backup_count
        )
    
    def _show_startup_info(self):
        """顯示啟動信息"""
        uptime = time.time() - self.startup_time
        
        print("\n" + "=" * 60)
        print("DDE Actor System - 高性能DDE數據處理系統")
        print("=" * 60)
        print(f"版本: {self.config_manager.system_config.version}")
        print(f"啟動時間: {uptime:.2f} 秒")
        print(f"配置文件: {self.config_file}")
        print(f"Actor數量: {len(self.actors)}")
        print(f"產品數量: {len(self.config_manager.product_configs)}")
        print(f"性能監控: 已啟用")
        print(f"自動調優: 已啟用")
        print("=" * 60)
        print("系統已就緒，開始處理DDE數據...")
        print("按 Ctrl+C 停止系統")
        print("=" * 60 + "\n")
    
    def _show_shutdown_info(self):
        """顯示關閉信息"""
        if self.startup_time > 0:
            uptime = time.time() - self.startup_time
            
            print("\n" + "=" * 60)
            print("系統關閉統計")
            print("=" * 60)
            print(f"運行時間: {uptime:.2f} 秒")
            
            # 顯示性能統計
            if self.performance_monitor:
                report = self.performance_monitor.get_performance_report()
                current_metrics = report.get('current_metrics')
                if current_metrics:
                    print(f"處理消息: {current_metrics.messages_per_second:.0f} 筆/秒")
                    print(f"平均延遲: {current_metrics.avg_latency_ms:.2f} ms")
                    print(f"內存使用: {current_metrics.memory_usage_mb:.1f} MB")
            
            print("=" * 60 + "\n")
    
    def get_system_status(self) -> dict:
        """獲取系統狀態
        
        Returns:
            dict: 系統狀態信息
        """
        status = {
            'running': self.running,
            'uptime': time.time() - self.startup_time if self.startup_time > 0 else 0,
            'actors': {},
            'config_summary': self.config_manager.get_config_summary()
        }
        
        # 獲取Actor狀態
        for name, actor in self.actors.items():
            status['actors'][name] = actor.get_info()
        
        # 獲取性能信息
        if self.performance_monitor:
            status['performance'] = self.performance_monitor.get_performance_report()
        
        return status


def parse_arguments():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='DDE Actor System - 高性能DDE數據處理系統')
    
    parser.add_argument(
        '--config', '-c',
        type=str,
        default='config/system_config.json',
        help='配置文件路径 (默認: config/system_config.json)'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='啟用調試模式'
    )
    
    parser.add_argument(
        '--log-level',
        type=str,
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日誌級別 (默認: INFO)'
    )
    
    return parser.parse_args()


async def main():
    """主函數"""
    args = parse_arguments()
    
    # 創建系統實例
    system = DDEActorSystem(args.config)
    
    try:
        # 運行系統
        success = await system.run_forever()
        return 0 if success else 1
        
    except Exception as e:
        print(f"系統運行失敗: {str(e)}")
        return 1


if __name__ == "__main__":
    # 設置基本日誌
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s'
    )
    
    # 運行系統
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
