# DDE引擎包裝器 API 參考

## 📋 概述

本文件提供DDE引擎包裝器的完整API參考，包括所有公開類別、方法和配置選項。

## 🏗️ 核心類別

### MultiProductDDEWrapper

主要的包裝器類別，提供多商品DDE監控的統一介面。

```python
class MultiProductDDEWrapper:
    """多商品DDE包裝器主類"""
    
    def __init__(self, config_file: str):
        """初始化包裝器
        
        Args:
            config_file: 配置檔案路徑
        """
    
    def start_monitoring(self) -> bool:
        """開始監控所有配置的商品
        
        Returns:
            bool: 是否成功啟動
        """
    
    def stop_monitoring(self) -> bool:
        """停止所有監控
        
        Returns:
            bool: 是否成功停止
        """
    
    def add_product(self, symbol: str, data_types: List[str]) -> bool:
        """動態新增商品監控
        
        Args:
            symbol: 商品代碼
            data_types: 資料類型列表
            
        Returns:
            bool: 是否成功新增
        """
    
    def remove_product(self, symbol: str, data_type: str = None) -> bool:
        """移除商品監控
        
        Args:
            symbol: 商品代碼
            data_type: 資料類型，None表示移除所有類型
            
        Returns:
            bool: 是否成功移除
        """
    
    def get_status(self) -> Dict[str, Any]:
        """獲取系統狀態
        
        Returns:
            Dict: 包含所有引擎狀態的字典
        """
```

### DDEEngineManager

引擎管理器，負責管理多個DDE引擎實例。

```python
class DDEEngineManager:
    """DDE引擎管理器"""
    
    def __init__(self, max_engines: int = 100):
        """初始化引擎管理器
        
        Args:
            max_engines: 最大引擎數量
        """
    
    def create_engine(self, engine_id: str, config: Dict) -> bool:
        """創建新的引擎實例
        
        Args:
            engine_id: 引擎唯一識別碼
            config: 引擎配置
            
        Returns:
            bool: 是否成功創建
        """
    
    def destroy_engine(self, engine_id: str) -> bool:
        """銷毀引擎實例
        
        Args:
            engine_id: 引擎識別碼
            
        Returns:
            bool: 是否成功銷毀
        """
    
    def get_engine(self, engine_id: str) -> Optional[DDEDataProcessor]:
        """獲取引擎實例
        
        Args:
            engine_id: 引擎識別碼
            
        Returns:
            DDEDataProcessor: 引擎實例或None
        """
    
    def get_all_engines(self) -> Dict[str, DDEDataProcessor]:
        """獲取所有引擎實例
        
        Returns:
            Dict: 引擎識別碼到實例的映射
        """
    
    def get_engine_status(self, engine_id: str) -> Dict[str, Any]:
        """獲取引擎狀態
        
        Args:
            engine_id: 引擎識別碼
            
        Returns:
            Dict: 引擎狀態資訊
        """
```

### WrapperConfigManager

配置管理器，處理多商品配置的載入和驗證。

```python
class WrapperConfigManager:
    """包裝器配置管理器"""
    
    def __init__(self, config_file: str):
        """初始化配置管理器
        
        Args:
            config_file: 主配置檔案路徑
        """
    
    def load_config(self) -> bool:
        """載入配置檔案
        
        Returns:
            bool: 是否成功載入
        """
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """驗證配置有效性
        
        Returns:
            Tuple[bool, List[str]]: (是否有效, 錯誤訊息列表)
        """
    
    def get_products(self) -> List[str]:
        """獲取所有配置的商品
        
        Returns:
            List[str]: 商品代碼列表
        """
    
    def get_product_config(self, symbol: str) -> Dict[str, Any]:
        """獲取商品配置
        
        Args:
            symbol: 商品代碼
            
        Returns:
            Dict: 商品配置
        """
    
    def get_datatype_config(self, symbol: str, data_type: str) -> Dict[str, Any]:
        """獲取資料類型配置
        
        Args:
            symbol: 商品代碼
            data_type: 資料類型
            
        Returns:
            Dict: 資料類型配置
        """
```

## 📄 配置檔案格式

### 主配置檔案 (multi_wrapper_config.ini)

```ini
[General]
# 系統基本設定
max_engines = 100
thread_pool_size = 20
log_level = INFO
enable_monitoring = true

[DDE]
# DDE連線設定
service = XQTISC
topic = Quote
connection_timeout = 30
retry_attempts = 3

[Products]
# 商品列表
symbols = FITXN07,FITXN08,FITMN07,FITMN08
data_types = tick,order,level2,daily

[Paths]
# 路徑設定
config_templates = config/templates/
log_output = logs/
data_output = outputs/
```

### 商品配置模板 (product_template.ini)

```ini
[Product_{symbol}]
# 商品基本資訊
name = {symbol}
market = TW_FUTURES
enabled = true

# 資料類型配置
data_types = tick,order,level2,daily

# 輸出路徑
output_base = outputs/{symbol}/

[AutoConnect_{symbol}]
# 自動連線設定
enable_auto_connect = true
auto_connect_mode = schedule
schedule_connect_times = 08:25:00-13:45:15;14:50:00-05:05:05
```

## 🔧 使用範例

### 基本使用

```python
from core import MultiProductDDEWrapper

# 初始化包裝器
wrapper = MultiProductDDEWrapper('config/multi_wrapper_config.ini')

# 啟動監控
if wrapper.start_monitoring():
    print("監控已啟動")
    
    # 動態新增商品
    wrapper.add_product('FITXN09', ['tick', 'order'])
    
    # 獲取狀態
    status = wrapper.get_status()
    print(f"活躍引擎數: {status['active_engines']}")
    
    # 停止監控
    wrapper.stop_monitoring()
```

### 進階使用

```python
from core import MultiProductDDEWrapper, DDEEngineManager

# 自訂引擎管理器
engine_manager = DDEEngineManager(max_engines=200)

# 初始化包裝器
wrapper = MultiProductDDEWrapper(
    config_file='config/multi_wrapper_config.ini',
    engine_manager=engine_manager
)

# 設定回調函數
def on_data_received(symbol, data_type, item, value):
    print(f"收到資料: {symbol}-{data_type} {item}={value}")

def on_engine_error(engine_id, error):
    print(f"引擎錯誤: {engine_id} - {error}")

wrapper.set_data_callback(on_data_received)
wrapper.set_error_callback(on_engine_error)

# 啟動監控
wrapper.start_monitoring()
```

## 📊 狀態和監控

### 系統狀態結構

```python
{
    "active_engines": 80,
    "total_engines": 80,
    "memory_usage": "156MB",
    "cpu_usage": "23%",
    "uptime": "02:34:56",
    "engines": {
        "FITXN07-tick": {
            "status": "running",
            "data_received": 15234,
            "data_processed": 15234,
            "last_update": "2025-06-26 10:30:45"
        }
    }
}
```

### 錯誤代碼

| 代碼 | 說明 | 處理方式 |
|------|------|----------|
| E001 | 配置檔案錯誤 | 檢查配置檔案格式 |
| E002 | DDE連線失敗 | 檢查DDE服務狀態 |
| E003 | 引擎創建失敗 | 檢查系統資源 |
| E004 | 資料處理錯誤 | 檢查資料格式 |
| E005 | 檔案寫入錯誤 | 檢查磁碟空間和權限 |
