#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速狀態檢查
"""

import psutil
import time
from pathlib import Path
from datetime import datetime


def check_system_status():
    """檢查系統狀態"""
    print("🔍 DDE Actor System 狀態檢查")
    print("=" * 50)
    
    # 1. 檢查 Python 進程
    print("1. 檢查運行中的進程...")
    dde_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                # 更寬鬆的匹配條件
                if 'main.py' in cmdline or ('python' in cmdline and 'config' in cmdline and 'system_config' in cmdline):
                    dde_processes.append({
                        'pid': proc.info['pid'],
                        'cmdline': cmdline
                    })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if dde_processes:
        print("✅ 找到 DDE Actor System 進程:")
        for proc in dde_processes:
            print(f"   PID {proc['pid']}: {proc['cmdline']}")
    else:
        print("❌ 未找到 DDE Actor System 進程")
        print("   請確保系統正在運行: python main.py --config config/system_config.json")
    
    # 2. 檢查輸出文件
    print(f"\n2. 檢查輸出文件...")
    output_dir = Path("outputs")
    
    if not output_dir.exists():
        print("❌ outputs 目錄不存在")
        return
    
    files_found = False
    for pattern in ["*.csv", "*.json"]:
        for file_path in output_dir.glob(pattern):
            if file_path.exists():
                stat = file_path.stat()
                size_mb = stat.st_size / 1024 / 1024
                modified = datetime.fromtimestamp(stat.st_mtime).strftime('%H:%M:%S')
                
                print(f"   📁 {file_path.name}:")
                print(f"      大小: {size_mb:.3f} MB ({stat.st_size} bytes)")
                print(f"      修改時間: {modified}")
                
                # 如果是 CSV 文件，顯示行數
                if file_path.suffix == '.csv' and stat.st_size > 0:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            line_count = sum(1 for _ in f)
                        print(f"      行數: {line_count}")
                        
                        # 顯示最後幾行
                        if line_count > 0:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                lines = f.readlines()
                                print(f"      最後3行:")
                                for line in lines[-3:]:
                                    if line.strip():
                                        print(f"        {line.strip()}")
                    except Exception as e:
                        print(f"      讀取錯誤: {str(e)}")
                
                files_found = True
    
    if not files_found:
        print("⚠️  沒有找到輸出文件")
        print("   這可能表示:")
        print("   - 系統剛啟動，還沒有數據")
        print("   - DDE 連接有問題")
        print("   - 沒有訂閱任何項目")
    
    # 3. 檢查日誌文件
    print(f"\n3. 檢查日誌文件...")
    log_files = ["logs/system.log", "logs/gui_test_system.log"]
    
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists():
            stat = log_path.stat()
            modified = datetime.fromtimestamp(stat.st_mtime).strftime('%H:%M:%S')
            print(f"   📝 {log_path.name}: {stat.st_size} bytes, 修改: {modified}")
            
            # 顯示最後幾行日誌
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"      最後3行:")
                        for line in lines[-3:]:
                            if line.strip():
                                print(f"        {line.strip()}")
            except Exception as e:
                print(f"      讀取錯誤: {str(e)}")
    
    # 4. 系統建議
    print(f"\n4. 系統建議:")
    if dde_processes:
        if files_found:
            print("✅ 系統運行正常，正在處理數據")
        else:
            print("⚠️  系統運行中但沒有輸出數據")
            print("   建議:")
            print("   - 檢查 DDE 連接是否正常")
            print("   - 確認是否有訂閱項目")
            print("   - 運行: python test_dde_subscribe.py")
    else:
        print("❌ 系統未運行")
        print("   請啟動系統: python main.py --config config/system_config.json")
    
    print(f"\n💡 提示:")
    print("   - 運行 'python test_dde_subscribe.py' 測試 DDE 訂閱")
    print("   - 運行 'python monitor_status.py' 實時監控狀態")
    print("   - 運行 'python main_gui_demo.py' 啟動 GUI 演示版本")


if __name__ == "__main__":
    check_system_status()
