#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 資料處理引擎使用範例
展示如何使用DDE資料處理引擎進行資料處理
"""

import time
import logging
import sys
import os
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 導入DDE資料處理引擎
from dde_data_engine import (
    DDEDataProcessor,
    DataProcessorConfig,
    DataFileHandler,
    ItemData
)


def setup_logging():
    """設置日誌"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('dde_engine_example.log', encoding='utf-8')
        ]
    )


def create_sample_items():
    """創建範例項目資料"""
    items_data = {
        "PRICE": ItemData(name="價格", code="PRICE"),
        "VOLUME": ItemData(name="總量", code="VOLUME"),
        "BID": ItemData(name="買價", code="BID"),
        "ASK": ItemData(name="賣價", code="ASK"),
        "BID_SIZE": ItemData(name="買量", code="BID_SIZE"),
        "ASK_SIZE": ItemData(name="賣量", code="ASK_SIZE"),
    }
    return items_data


def example_single_mode():
    """範例: 單項檢查模式"""
    print("\n=== 單項檢查模式範例 ===")
    
    # 準備項目資料
    items_data = create_sample_items()
    
    # 設定配置 - 單項模式檢查 "總量"
    config = DataProcessorConfig({
        'enable_time_newline': True,
        'time_newline_interval': 1.0,
        'enable_value_change_check': True,
        'value_change_check_mode': 'single',
        'value_change_check_items': '總量',
    })
    
    # 設定檔案處理器
    file_handler = DataFileHandler()
    file_handler.init_file_paths(
        log_file="./example_output/single_mode.log",
        complete_data_file="./example_output/single_mode_complete.csv"
    )
    file_handler.set_items_data(items_data)
    
    # 創建資料處理器
    processor = DDEDataProcessor(
        config=config,
        items_data=items_data,
        file_handler=file_handler
    )
    
    # 設定回調函數
    def on_row_saved(row):
        print(f"  ✓ 保存資料行: 總量={row.values.get('VOLUME', 'N/A')}")
    
    def on_row_skipped(row):
        print(f"  ✗ 跳過資料行: 總量未變化")
    
    processor.on_row_saved = on_row_saved
    processor.on_row_skipped = on_row_skipped
    
    # 設定初始值
    initial_values = {
        "PRICE": "100.0",
        "VOLUME": "1000",
        "BID": "99.5",
        "ASK": "100.5",
        "BID_SIZE": "50",
        "ASK_SIZE": "60",
    }
    processor.set_initial_values(initial_values)
    
    # 模擬資料更新
    print("開始資料更新...")
    
    # 第一組資料 - 總量變化，應該保存
    print("第一組: 總量變化 1000 -> 1100")
    processor.process_dde_data("VOLUME", "1100")
    processor.process_dde_data("PRICE", "101.0")
    time.sleep(1.2)  # 觸發時間間隔
    processor.check_time_interval()
    
    # 第二組資料 - 總量不變，應該跳過
    print("第二組: 總量不變，價格變化")
    processor.process_dde_data("PRICE", "102.0")
    processor.process_dde_data("BID", "101.5")
    time.sleep(1.2)
    processor.check_time_interval()
    
    # 第三組資料 - 總量變化，應該保存
    print("第三組: 總量變化 1100 -> 1200")
    processor.process_dde_data("VOLUME", "1200")
    processor.process_dde_data("ASK", "103.0")
    time.sleep(1.2)
    processor.check_time_interval()
    
    # 顯示統計
    stats = processor.get_stats()
    print(f"統計: 接收={stats.total_received}, 處理={stats.total_processed}, 保存={stats.total_saved}, 跳過={stats.total_skipped}")


def example_all_mode():
    """範例: 全部檢查模式"""
    print("\n=== 全部檢查模式範例 ===")
    
    # 準備項目資料
    items_data = create_sample_items()
    
    # 設定配置 - 全部模式
    config = DataProcessorConfig({
        'enable_time_newline': True,
        'time_newline_interval': 1.0,
        'enable_value_change_check': True,
        'value_change_check_mode': 'all',
        'value_change_check_items': '',
    })
    
    # 設定檔案處理器
    file_handler = DataFileHandler()
    file_handler.init_file_paths(
        log_file="./example_output/all_mode.log",
        complete_data_file="./example_output/all_mode_complete.csv"
    )
    file_handler.set_items_data(items_data)
    
    # 創建資料處理器
    processor = DDEDataProcessor(
        config=config,
        items_data=items_data,
        file_handler=file_handler
    )
    
    # 設定回調函數
    def on_row_saved(row):
        print(f"  ✓ 保存資料行: 有項目值變化")
    
    def on_row_skipped(row):
        print(f"  ✗ 跳過資料行: 所有項目值都未變化")
    
    processor.on_row_saved = on_row_saved
    processor.on_row_skipped = on_row_skipped
    
    # 設定初始值
    initial_values = {
        "PRICE": "100.0",
        "VOLUME": "1000",
        "BID": "99.5",
        "ASK": "100.5",
        "BID_SIZE": "50",
        "ASK_SIZE": "60",
    }
    processor.set_initial_values(initial_values)
    
    # 模擬資料更新
    print("開始資料更新...")
    
    # 第一組資料 - 有變化，應該保存
    print("第一組: 價格變化")
    processor.process_dde_data("PRICE", "101.0")
    time.sleep(1.2)
    processor.check_time_interval()
    
    # 第二組資料 - 相同值，應該跳過
    print("第二組: 所有值都相同")
    processor.process_dde_data("PRICE", "101.0")
    processor.process_dde_data("VOLUME", "1000")
    time.sleep(1.2)
    processor.check_time_interval()
    
    # 第三組資料 - 有變化，應該保存
    print("第三組: 買量變化")
    processor.process_dde_data("BID_SIZE", "55")
    time.sleep(1.2)
    processor.check_time_interval()
    
    # 顯示統計
    stats = processor.get_stats()
    print(f"統計: 接收={stats.total_received}, 處理={stats.total_processed}, 保存={stats.total_saved}, 跳過={stats.total_skipped}")


def example_multiple_mode():
    """範例: 多項檢查模式"""
    print("\n=== 多項檢查模式範例 ===")
    
    # 準備項目資料
    items_data = create_sample_items()
    
    # 設定配置 - 多項模式檢查 "價格,總量"
    config = DataProcessorConfig({
        'enable_time_newline': True,
        'time_newline_interval': 1.0,
        'enable_value_change_check': True,
        'value_change_check_mode': 'multiple',
        'value_change_check_items': '價格,總量',
    })
    
    # 設定檔案處理器
    file_handler = DataFileHandler()
    file_handler.init_file_paths(
        log_file="./example_output/multiple_mode.log",
        complete_data_file="./example_output/multiple_mode_complete.csv"
    )
    file_handler.set_items_data(items_data)
    
    # 創建資料處理器
    processor = DDEDataProcessor(
        config=config,
        items_data=items_data,
        file_handler=file_handler
    )
    
    # 設定回調函數
    def on_row_saved(row):
        price = row.values.get('PRICE', 'N/A')
        volume = row.values.get('VOLUME', 'N/A')
        print(f"  ✓ 保存資料行: 價格={price}, 總量={volume}")
    
    def on_row_skipped(row):
        print(f"  ✗ 跳過資料行: 價格和總量都未變化")
    
    processor.on_row_saved = on_row_saved
    processor.on_row_skipped = on_row_skipped
    
    # 設定初始值
    initial_values = {
        "PRICE": "100.0",
        "VOLUME": "1000",
        "BID": "99.5",
        "ASK": "100.5",
        "BID_SIZE": "50",
        "ASK_SIZE": "60",
    }
    processor.set_initial_values(initial_values)
    
    # 模擬資料更新
    print("開始資料更新...")
    
    # 第一組資料 - 價格變化，應該保存
    print("第一組: 價格變化")
    processor.process_dde_data("PRICE", "101.0")
    processor.process_dde_data("BID", "100.0")  # 其他項目變化，但不影響
    time.sleep(1.2)
    processor.check_time_interval()
    
    # 第二組資料 - 價格和總量都不變，應該跳過
    print("第二組: 價格和總量都不變，其他項目變化")
    processor.process_dde_data("BID", "100.5")
    processor.process_dde_data("ASK", "101.5")
    time.sleep(1.2)
    processor.check_time_interval()
    
    # 第三組資料 - 總量變化，應該保存
    print("第三組: 總量變化")
    processor.process_dde_data("VOLUME", "1100")
    time.sleep(1.2)
    processor.check_time_interval()
    
    # 顯示統計
    stats = processor.get_stats()
    print(f"統計: 接收={stats.total_received}, 處理={stats.total_processed}, 保存={stats.total_saved}, 跳過={stats.total_skipped}")


def main():
    """主函數"""
    print("DDE 資料處理引擎使用範例")
    print("=" * 50)
    
    # 設置日誌
    setup_logging()
    
    try:
        # 執行各種模式的範例
        example_single_mode()
        example_all_mode()
        example_multiple_mode()
        
        print("\n" + "=" * 50)
        print("所有範例執行完成！")
        print("請檢查 example_output 目錄中的輸出檔案。")
        
    except Exception as e:
        print(f"執行範例時發生錯誤: {str(e)}")
        logging.error(f"執行範例失敗: {str(e)}", exc_info=True)


if __name__ == "__main__":
    main()
