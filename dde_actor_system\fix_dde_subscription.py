#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修復 DDE 訂閱

為運行中的 DDE Actor System 手動建立數據訂閱
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from dydde.dydde import DDEClient
from core.message_system import create_dde_data_message


class DDEDataFeeder:
    """DDE 數據饋送器"""
    
    def __init__(self):
        self.clients = {}
        self.running = False
        self.data_callback = None
        
    def set_data_callback(self, callback):
        """設置數據回調函數"""
        self.data_callback = callback
    
    def connect_to_service(self, service, topic, connection_id):
        """連接到 DDE 服務"""
        try:
            client = DDEClient(service, topic)
            client.connect()
            self.clients[connection_id] = {
                'client': client,
                'service': service,
                'topic': topic,
                'items': []
            }
            print(f"✅ 連接成功: {connection_id} -> {service}.{topic}")
            return True
        except Exception as e:
            print(f"❌ 連接失敗: {connection_id} -> {service}.{topic}: {str(e)}")
            return False
    
    def subscribe_item(self, connection_id, item):
        """訂閱項目"""
        if connection_id not in self.clients:
            print(f"❌ 連接不存在: {connection_id}")
            return False
        
        try:
            client_info = self.clients[connection_id]
            client = client_info['client']
            
            # 創建回調函數
            def item_callback(item_name, value):
                if self.data_callback:
                    self.data_callback(item_name, value)
            
            # 建立熱鏈接
            client.advise(item, item_callback)
            client_info['items'].append(item)
            
            print(f"✅ 訂閱成功: {item}")
            return True
            
        except Exception as e:
            print(f"❌ 訂閱失敗: {item}: {str(e)}")
            return False
    
    def request_data(self, connection_id, item):
        """請求數據"""
        if connection_id not in self.clients:
            return None
        
        try:
            client = self.clients[connection_id]['client']
            result = client.request(item)
            return result
        except Exception as e:
            print(f"❌ 請求數據失敗: {item}: {str(e)}")
            return None
    
    def disconnect_all(self):
        """斷開所有連接"""
        for connection_id, client_info in self.clients.items():
            try:
                client = client_info['client']
                
                # 取消所有訂閱
                for item in client_info['items']:
                    try:
                        client.unadvise(item)
                    except:
                        pass
                
                # 斷開連接
                client.disconnect()
                print(f"✅ 斷開連接: {connection_id}")
                
            except Exception as e:
                print(f"❌ 斷開連接失敗: {connection_id}: {str(e)}")
        
        self.clients.clear()


async def feed_data_to_system():
    """向系統饋送數據"""
    print("🚀 DDE 數據饋送器")
    print("=" * 50)
    
    # 加載配置
    config_file = Path("config/system_config.json")
    if not config_file.exists():
        print("❌ 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 配置加載失敗: {str(e)}")
        return False
    
    # 創建數據饋送器
    feeder = DDEDataFeeder()
    
    # 數據計數器
    data_count = 0
    
    def data_callback(item, value):
        """數據回調"""
        nonlocal data_count
        data_count += 1
        print(f"📊 接收數據 {data_count}: {item} = {value}")
        
        # 這裡可以將數據發送到 DDE Actor System
        # 但由於我們是獨立進程，直接寫入文件
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        # 寫入到專用文件
        output_file = Path("outputs/live_dde_data.csv")
        with open(output_file, 'a', encoding='utf-8') as f:
            f.write(f"{timestamp},{item},{value},{value}\n")
    
    feeder.set_data_callback(data_callback)
    
    try:
        print("\n1. 建立 DDE 連接...")
        
        # 獲取產品配置
        products = config.get('products', [])
        enabled_products = [p for p in products if p.get('enabled', False)]
        
        connections_established = 0
        
        for product in enabled_products:
            symbol = product.get('symbol')
            service = product.get('service')
            topic = product.get('topic')
            items = product.get('items', [])
            
            connection_id = f"conn_{symbol}"
            
            print(f"\n   產品: {symbol}")
            print(f"   服務: {service}.{topic}")
            print(f"   項目數: {len(items)}")
            
            # 建立連接
            if feeder.connect_to_service(service, topic, connection_id):
                connections_established += 1
                
                # 先請求一次數據看看是否有效
                print(f"   測試數據請求...")
                test_items = items[:3]  # 只測試前3個
                valid_items = []
                
                for item in test_items:
                    result = feeder.request_data(connection_id, item)
                    if result and result.strip():
                        print(f"     ✅ {item}: {result}")
                        valid_items.append(item)
                    else:
                        print(f"     ⚪ {item}: 無數據")
                
                # 只訂閱有效的項目
                if valid_items:
                    print(f"   建立熱鏈接...")
                    for item in valid_items:
                        feeder.subscribe_item(connection_id, item)
                else:
                    print(f"   ⚠️  沒有有效項目，跳過訂閱")
        
        if connections_established == 0:
            print("❌ 沒有建立任何連接")
            return False
        
        print(f"\n✅ 建立了 {connections_established} 個連接")
        
        print(f"\n2. 開始接收數據...")
        print("   數據將寫入 outputs/live_dde_data.csv")
        print("   按 Ctrl+C 停止")
        
        # 創建輸出文件並寫入標題
        output_file = Path("outputs/live_dde_data.csv")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("timestamp,item,original_value,processed_value\n")
        
        # 運行數據接收循環
        start_time = time.time()
        
        while True:
            await asyncio.sleep(1)
            
            # 每10秒顯示統計
            elapsed = time.time() - start_time
            if int(elapsed) % 10 == 0 and elapsed > 0:
                rate = data_count / elapsed
                print(f"📈 統計: 接收 {data_count} 筆數據, 速率: {rate:.1f} 筆/秒")
    
    except KeyboardInterrupt:
        print(f"\n⏹️  用戶停止")
    except Exception as e:
        print(f"\n❌ 運行失敗: {str(e)}")
    finally:
        print(f"\n3. 清理資源...")
        feeder.disconnect_all()
        
        # 顯示最終統計
        if data_count > 0:
            print(f"✅ 總共接收 {data_count} 筆數據")
            print(f"✅ 數據已保存到 outputs/live_dde_data.csv")
        else:
            print(f"⚠️  沒有接收到數據")
    
    return True


async def main():
    """主函數"""
    print("🔧 DDE 訂閱修復工具")
    print("此工具將建立真實的 DDE 數據訂閱")
    print("並將數據直接寫入文件")
    print("=" * 60)
    
    try:
        success = await feed_data_to_system()
        
        if success:
            print(f"\n🎉 數據饋送完成!")
            print("建議:")
            print("1. 檢查 outputs/live_dde_data.csv 文件")
            print("2. 如果有數據，說明 DDE 訂閱機制正常")
            print("3. 可以將此邏輯整合到 DDE Receiver 中")
        else:
            print(f"\n❌ 數據饋送失敗")
    
    except Exception as e:
        print(f"\n❌ 執行失敗: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
