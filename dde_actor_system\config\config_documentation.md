# DDE Actor System 配置文件說明

## 📋 配置文件概述

DDE Actor System 支援兩種配置格式：
- `system_config.json` - 完整配置（包含未使用項目）
- `simple_config.json` - 精簡配置（僅包含實際使用項目）

## 🎯 推薦使用精簡配置

建議使用 `simple_config.json`，因為它只包含實際使用的配置項目。

## 📊 配置項目詳細說明

### 1. 系統配置 (system)

| 配置項目 | 類型 | 預設值 | 說明 | 是否使用 |
|---------|------|--------|------|----------|
| `system_name` | string | "DDE Actor System" | 系統名稱，用於顯示 | ✅ 使用 |
| `version` | string | "1.0.0" | 版本號，用於顯示 | ✅ 使用 |

### 2. DDE 配置 (dde)

| 配置項目 | 類型 | 預設值 | 說明 | 是否使用 |
|---------|------|--------|------|----------|
| `polling_interval` | number | 1.0 | 輪詢間隔（秒） | ✅ 使用 |
| `batch_size` | number | 1000 | 批次處理大小 | ✅ 使用 |
| `batch_timeout` | number | 0.01 | 批次超時時間（秒） | ✅ 使用 |

### 3. 文件輸出配置 (file_output)

| 配置項目 | 類型 | 預設值 | 說明 | 是否使用 |
|---------|------|--------|------|----------|
| `batch_size` | number | 1000 | 文件寫入批次大小 | ✅ 使用 |
| `flush_interval` | number | 5.0 | 文件刷新間隔（秒） | ✅ 使用 |

### 4. 產品配置 (products)

每個產品包含以下配置：

| 配置項目 | 類型 | 說明 | 是否使用 |
|---------|------|------|----------|
| `symbol` | string | 產品代碼（如 FITXN07） | ✅ 使用 |
| `service` | string | DDE 服務名稱（如 XQTISC） | ✅ 使用 |
| `topic` | string | DDE 主題（如 Quote） | ✅ 使用 |
| `items` | array | DDE 項目列表 | ✅ 使用 |
| `enabled` | boolean | 是否啟用此產品 | ✅ 使用 |

## 🚫 未使用的配置項目

以下配置項目在 `system_config.json` 中存在但**未被實際使用**：

### 系統配置中未使用：
- `debug_mode` - 調試模式
- `log_level` - 日誌級別
- `log_file` - 日誌文件路徑
- `log_max_size_mb` - 日誌文件最大大小
- `log_backup_count` - 日誌備份數量
- `enable_monitoring` - 啟用監控
- `monitoring_interval` - 監控間隔
- `health_check_interval` - 健康檢查間隔
- `enable_remote_api` - 啟用遠程API
- `api_host` - API主機
- `api_port` - API端口

### 性能配置中未使用：
- `dde_buffer_size` - DDE緩衝區大小
- `processing_batch_size` - 處理批次大小
- `processing_queue_size` - 處理隊列大小
- `processing_workers` - 處理工作線程數
- `gui_update_interval_ms` - GUI更新間隔
- `gui_max_batch_size` - GUI最大批次大小
- `gui_virtual_rows` - GUI虛擬行數
- `file_buffer_size` - 文件緩衝區大小
- `backpressure_high_watermark` - 背壓高水位
- `backpressure_low_watermark` - 背壓低水位
- `backpressure_strategy` - 背壓策略
- `memory_pool_initial_size` - 內存池初始大小
- `gc_threshold` - 垃圾回收閾值

### 產品配置中未使用：
- `data_types` - 數據類型（重要：此項目不起作用！）
- `priority` - 優先級
- `custom_settings` - 自定義設置

## ⚠️ 重要說明：data_types 不起作用

在原始 INI 版本中，`data_types` 用於：
- 動態生成 DDE 項目列表
- 支援 tick, order, level2, daily 四種類型
- 使用模板系統

**在當前 JSON 版本中，`data_types` 完全沒有作用！**
系統直接使用 `items` 列表中的項目，忽略 `data_types` 設置。

## 📁 輸出格式差異

### 原始 INI 版本輸出：
```
outputs/
├── tick/
│   ├── FITXN07_tick.csv
│   └── FITXN08_tick.csv
├── order/
│   ├── FITXN07_order.csv
│   └── FITXN08_order.csv
└── ...
```

### 當前 JSON 版本輸出：
```
outputs/
├── dde_data.csv          # 統一格式
├── dde_data.json         # JSON格式
└── production_dde_data.csv
```

輸出格式：
```csv
timestamp,item,original_value,processed_value
2025-07-02 10:11:35.123,FITXN07.TF-Price,22277,22277
2025-07-02 10:11:35.456,FITXN07.TF-Volume,1.000000,1.000000
```

## 🎯 使用建議

### 1. 新用戶（推薦）
使用精簡配置：
```bash
python clean_dde_monitor.py --config config/simple_config.json
```

### 2. 需要完整功能
如果需要與原始 INI 版本相同的功能（分類輸出、模板系統等），需要額外開發。

### 3. 自定義配置
複製 `simple_config.json` 並根據需要修改：
- 添加/移除產品
- 調整輪詢間隔
- 修改批次大小

## 📝 配置範例

### 最小配置範例：
```json
{
  "products": [
    {
      "symbol": "FITXN07",
      "service": "XQTISC",
      "topic": "Quote",
      "items": ["FITXN07.TF-Price", "FITXN07.TF-Volume"],
      "enabled": true
    }
  ]
}
```

### 多產品配置範例：
```json
{
  "dde": {
    "polling_interval": 0.5
  },
  "products": [
    {
      "symbol": "FITXN07",
      "service": "XQTISC",
      "topic": "Quote",
      "items": ["FITXN07.TF-Price", "FITXN07.TF-Volume"],
      "enabled": true
    },
    {
      "symbol": "FITXN08", 
      "service": "XQTISC",
      "topic": "Quote",
      "items": ["FITXN08.TF-Price", "FITXN08.TF-Volume"],
      "enabled": true
    }
  ]
}
```

## 🔧 故障排除

### 配置文件不生效？
1. 檢查 JSON 語法是否正確
2. 確認文件路徑正確
3. 檢查配置項目是否在"使用"列表中

### DDE 連接失敗？
1. 檢查 `service` 和 `topic` 是否正確
2. 確認 DDE 服務正在運行
3. 檢查 `items` 列表中的項目名稱

### 沒有數據輸出？
1. 確認產品 `enabled: true`
2. 檢查 `items` 是否有效
3. 確認在交易時間內
