# DDE 監控程式架構設計文件

## 系統架構概覽

### 整體架構圖
```
┌─────────────────────────────────────────────────────────────┐
│                    DDE Monitor Application                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │     UI      │  │   Config    │  │    File Handler     │  │
│  │  (PySide6)  │  │ Management  │  │   (CSV Output)      │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    Data     │  │   Thread    │  │      Logging        │  │
│  │  Processor  │  │  Manager    │  │      System         │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐  │
│  │              DDE Client (dydde module)                 │  │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────┐  │  │
│  │  │ Connection  │  │ Subscription│  │   Data Request  │  │  │
│  │  │  Manager    │  │   Manager   │  │     Handler     │  │  │
│  │  └─────────────┘  └─────────────┘  └─────────────────┘  │  │
│  └─────────────────────────────────────────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                Windows DDE API                          │  │
│  └─────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 核心模組設計

### 1. DDEMonitor (主控制器)
**職責**: 整個應用程式的核心控制器
**設計模式**: Singleton + Observer

```python
class DDEMonitor(QMainWindow):
    """主控制器類別
    
    職責:
    - 協調各個子系統
    - 管理應用程式生命週期
    - 處理使用者互動
    - 維護全域狀態
    """
```

**關鍵屬性**:
- `config`: 設定管理器
- `dde_client`: DDE 客戶端實例
- `file_handler`: 檔案處理器
- `data_processor`: 資料處理器
- `items_data`: 監控項目資料容器

**關鍵方法**:
- `load_config()`: 載入設定檔
- `init_ui()`: 初始化使用者介面
- `connect_service()`: 建立 DDE 連接
- `toggle_subscription()`: 管理訂閱狀態

### 2. DataFileHandler (檔案處理器)
**職責**: 管理所有檔案輸出操作
**設計模式**: Strategy Pattern

```python
class DataFileHandler:
    """檔案處理策略類別
    
    職責:
    - 管理檔案輸出策略
    - 確保檔案系統操作安全
    - 處理檔案路徑和格式
    """
```

**檔案輸出策略**:
- 原始資料檔案 (Raw Data)
- 完整資料檔案 (Complete Data)
- 日誌檔案 (Log Files)

### 3. DataProcessor (資料處理器)
**職責**: 處理即時資料流
**設計模式**: Producer-Consumer + Observer

```python
class DataProcessor(QObject):
    """資料處理器
    
    職責:
    - 管理資料佇列
    - 批次處理資料
    - 觸發資料更新事件
    """
```

**處理流程**:
1. 接收 DDE 資料
2. 加入處理佇列
3. 批次處理資料
4. 觸發更新信號
5. 更新 UI 和檔案

### 4. dydde 模組 (DDE 客戶端)
**職責**: 封裝 Windows DDE API
**設計模式**: Facade + Adapter

```python
class DDEClient(DDEBase):
    """DDE 客戶端門面類別
    
    職責:
    - 封裝複雜的 DDE API
    - 提供簡化的介面
    - 管理連接生命週期
    - 處理錯誤和重連
    """
```

## 資料流設計

### 資料流向圖
```
DDE Server → dydde.DDEClient → DataProcessor → DDEMonitor
                                     ↓
                              DataFileHandler
                                     ↓
                               CSV Files
```

### 資料結構設計

#### ItemData (項目資料)
```python
@dataclass
class ItemData:
    name: str                    # 項目顯示名稱
    code: str                    # DDE 項目代碼
    value: Optional[str]         # 當前值
    update_time: Optional[datetime]  # 最後更新時間
    status: str                  # 訂閱狀態
```

#### RawDataRow (原始資料行)
```python
@dataclass
class RawDataRow:
    receive_date: str            # 接收日期
    receive_time: str            # 接收時間
    values: Dict[str, str]       # 項目值字典
    is_complete: bool            # 是否為完整資料行
```

## 線程模型設計

### 線程架構
```
Main Thread (UI)
├── DataProcessor Thread (資料處理)
├── DDE Callback Thread (DDE 回調)
└── File I/O Thread (檔案操作)
```

### 線程同步機制
- **QThread**: 用於資料處理器
- **QSignal/QSlot**: 線程間通訊
- **QMutex**: 共享資源保護
- **Queue**: 資料佇列管理

## 設定管理設計

### 設定檔結構
```ini
[DDE]          # DDE 連接設定
[Items]        # 監控項目設定
[Table]        # 資料處理設定
[FileOutput]   # 檔案輸出控制
[OutputPath]   # 輸出路徑設定
```

### 設定載入流程
1. 讀取 config.ini
2. 驗證設定值
3. 設定預設值
4. 初始化各子系統

## 錯誤處理設計

### 錯誤分類
- **DDE 錯誤**: 連接、訂閱、資料傳輸錯誤
- **檔案錯誤**: 檔案讀寫、路徑錯誤
- **系統錯誤**: 記憶體、線程、資源錯誤
- **設定錯誤**: 設定檔格式、參數錯誤

### 錯誤處理策略
```python
try:
    # 業務邏輯
except DDEError as e:
    # DDE 特定錯誤處理
    self.handle_dde_error(e)
except FileNotFoundError as e:
    # 檔案錯誤處理
    self.handle_file_error(e)
except Exception as e:
    # 通用錯誤處理
    self.handle_generic_error(e)
```

## 效能優化設計

### 資料處理優化
- **批次處理**: 減少 I/O 操作頻率
- **資料佇列**: 平滑資料流量
- **非同步處理**: 避免 UI 阻塞

### 記憶體管理
- **資料容器限制**: 使用 deque(maxlen=1000)
- **及時清理**: 定期清理過期資料
- **資源追蹤**: 追蹤和釋放 DDE 資源

### UI 更新優化
- **定時更新**: 使用 QTimer 控制更新頻率
- **差異更新**: 只更新變化的資料
- **延遲載入**: 大量資料分批載入

## 擴展性設計

### 插件架構 (未來規劃)
```python
class PluginInterface:
    def process_data(self, data: RawDataRow) -> RawDataRow:
        pass
    
    def get_analysis_result(self) -> Dict[str, Any]:
        pass
```

### 配置擴展
- 動態項目配置
- 自定義資料處理規則
- 可配置的輸出格式

## 安全性設計

### 資料安全
- 檔案權限控制
- 資料完整性檢查
- 錯誤資料過濾

### 系統安全
- 資源使用限制
- 異常情況恢復
- 安全的資源清理

## 測試策略

### 單元測試
- 各個類別的獨立測試
- 資料處理邏輯測試
- 錯誤處理測試

### 整合測試
- DDE 連接測試
- 資料流測試
- 檔案輸出測試

### 效能測試
- 大量資料處理測試
- 長時間運行測試
- 記憶體洩漏測試

---
*文件版本*: v1.0  
*建立日期*: 2025-06-16  
*最後更新*: 2025-06-16
