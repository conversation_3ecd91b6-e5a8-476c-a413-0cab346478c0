#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多商品配置解析器
解析multi_config.ini格式的配置文件并转换为引擎包装器格式
"""

import configparser
import logging
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

try:
    from config_manager import ProductConfig, EngineConfig
except ImportError:
    from core.config_manager import ProductConfig, EngineConfig


@dataclass
class MultiProductItem:
    """多商品项目配置"""
    name: str
    code: str


@dataclass
class DataTypeTemplate:
    """数据类型模板"""
    name: str
    items: List[MultiProductItem]
    enable_time_newline: bool = True
    time_newline_interval: float = 0.8
    enable_value_change_check: bool = True
    value_change_check_mode: str = 'single'
    value_change_check_items: List[str] = None
    log_level: str = 'INFO'


@dataclass
class AutoConnectTemplate:
    """自动连接模板"""
    name: str
    enable_auto_connect: bool = True
    auto_connect_mode: str = 'schedule'
    auto_connect_delay: float = 5.0
    schedule_connect_times: str = ''
    prevent_weekend_startup: bool = True
    schedule_end_action: str = 'unadvise_only'


@dataclass
class MultiProductSymbol:
    """多商品符号配置"""
    symbol: str
    enabled_types: List[str]
    output_path: str
    auto_connect_template: str


class MultiConfigParser:
    """多商品配置解析器"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        
        # 解析后的配置
        self.system_config: Dict[str, Any] = {}
        self.symbols: List[str] = []
        self.data_type_templates: Dict[str, DataTypeTemplate] = {}
        self.auto_connect_templates: Dict[str, AutoConnectTemplate] = {}
        self.symbol_configs: Dict[str, MultiProductSymbol] = {}
        self.item_templates: Dict[str, List[MultiProductItem]] = {}
        
    def parse_config(self) -> bool:
        """解析配置文件"""
        try:
            if not os.path.exists(self.config_file):
                self.logger.error(f"配置文件不存在: {self.config_file}")
                return False
            
            config = configparser.ConfigParser()
            config.read(self.config_file, encoding='utf-8')
            
            # 解析系统配置
            self._parse_system_config(config)
            
            # 解析商品清单
            self._parse_symbols(config)
            
            # 解析项目模板
            self._parse_item_templates(config)
            
            # 解析数据类型配置
            self._parse_data_type_configs(config)
            
            # 解析自动连接模板
            self._parse_auto_connect_templates(config)
            
            # 解析商品配置
            self._parse_symbol_configs(config)
            
            self.logger.info("多商品配置解析完成")
            return True
            
        except Exception as e:
            self.logger.error(f"解析配置文件失败: {str(e)}")
            return False
    
    def _parse_system_config(self, config: configparser.ConfigParser):
        """解析系统配置"""
        if 'System' in config:
            self.system_config = dict(config['System'])
            self.logger.info(f"系统配置: {self.system_config}")
    
    def _parse_symbols(self, config: configparser.ConfigParser):
        """解析商品清单"""
        if 'Symbols' in config and 'symbol_list' in config['Symbols']:
            symbol_list = config['Symbols']['symbol_list']
            self.symbols = [s.strip() for s in symbol_list.split(',') if s.strip()]
            self.logger.info(f"商品清单: {self.symbols}")
    
    def _parse_item_templates(self, config: configparser.ConfigParser):
        """解析项目模板"""
        template_sections = [s for s in config.sections() if s.startswith('Template_')]
        
        for section_name in template_sections:
            template_name = section_name.replace('Template_', '').replace('_Items', '')
            items = []
            
            section = config[section_name]
            item_names = [key for key in section.keys() if key.endswith('_name')]
            
            for name_key in item_names:
                item_num = name_key.replace('_name', '')
                code_key = f"{item_num}_code"
                
                if code_key in section:
                    item = MultiProductItem(
                        name=section[name_key],
                        code=section[code_key]
                    )
                    items.append(item)
            
            self.item_templates[template_name] = items
            self.logger.info(f"项目模板 {template_name}: {len(items)} 个项目")
    
    def _parse_data_type_configs(self, config: configparser.ConfigParser):
        """解析数据类型配置"""
        datatype_sections = [s for s in config.sections() if s.startswith('DataType_')]
        
        for section_name in datatype_sections:
            data_type = section_name.replace('DataType_', '')
            section = config[section_name]
            
            # 获取模板
            template_name = section.get('items_template', '').replace('Template_', '').replace('_Items', '')
            items = self.item_templates.get(template_name, [])
            
            # 解析值变化检查项目
            value_change_items = []
            if 'table_value_change_check_items' in section:
                items_str = section['table_value_change_check_items']
                if items_str and items_str != 'all':
                    value_change_items = [item.strip() for item in items_str.split(',')]
            
            template = DataTypeTemplate(
                name=data_type,
                items=items,
                enable_time_newline=section.getboolean('table_enable_time_newline', True),
                time_newline_interval=section.getfloat('table_time_newline_interval', 0.8),
                enable_value_change_check=section.getboolean('table_enable_value_change_check', True),
                value_change_check_mode=section.get('table_value_change_check_mode', 'single'),
                value_change_check_items=value_change_items,
                log_level=section.get('log_level', 'INFO')
            )
            
            self.data_type_templates[data_type] = template
            self.logger.info(f"数据类型配置 {data_type}: {len(items)} 个项目")
    
    def _parse_auto_connect_templates(self, config: configparser.ConfigParser):
        """解析自动连接模板"""
        autoconnect_sections = [s for s in config.sections() if s.startswith('AutoConnect_')]
        
        for section_name in autoconnect_sections:
            template_name = section_name.replace('AutoConnect_', '')
            section = config[section_name]
            
            template = AutoConnectTemplate(
                name=template_name,
                enable_auto_connect=section.getboolean('enable_auto_connect', True),
                auto_connect_mode=section.get('auto_connect_mode', 'schedule'),
                auto_connect_delay=section.getfloat('auto_connect_delay', 5.0),
                schedule_connect_times=section.get('schedule_connect_times', ''),
                prevent_weekend_startup=section.getboolean('prevent_weekend_startup', True),
                schedule_end_action=section.get('schedule_end_action', 'unadvise_only')
            )
            
            self.auto_connect_templates[template_name] = template
            self.logger.info(f"自动连接模板 {template_name}")
    
    def _parse_symbol_configs(self, config: configparser.ConfigParser):
        """解析商品配置"""
        for symbol in self.symbols:
            if symbol in config:
                section = config[symbol]
                
                enabled_types = []
                if 'enabled_types' in section:
                    enabled_types = [t.strip() for t in section['enabled_types'].split(',')]
                
                symbol_config = MultiProductSymbol(
                    symbol=symbol,
                    enabled_types=enabled_types,
                    output_path=section.get('output_path', f'./outputs/{symbol}/'),
                    auto_connect_template=section.get('auto_connect_template', '')
                )
                
                self.symbol_configs[symbol] = symbol_config
                self.logger.info(f"商品配置 {symbol}: {enabled_types}")
    
    def convert_to_wrapper_config(self) -> Dict[str, ProductConfig]:
        """转换为引擎包装器配置格式"""
        products = {}
        
        try:
            for symbol, symbol_config in self.symbol_configs.items():
                # 获取自动连接配置
                auto_connect = False
                auto_connect_times = []
                
                if symbol_config.auto_connect_template in self.auto_connect_templates:
                    template = self.auto_connect_templates[symbol_config.auto_connect_template]
                    auto_connect = template.enable_auto_connect
                    
                    # 解析连接时间
                    if template.schedule_connect_times:
                        times = template.schedule_connect_times.split(';')
                        for time_range in times:
                            if '-' in time_range:
                                start_time, end_time = time_range.split('-', 1)
                                auto_connect_times.extend([start_time.strip(), end_time.strip()])
                
                # 创建产品配置
                product_config = ProductConfig(
                    symbol=symbol,
                    data_types=symbol_config.enabled_types,
                    dde_service=self.system_config.get('dde_service', 'XQTISC'),
                    dde_topic=self.system_config.get('dde_topic', 'Quote'),
                    output_path=symbol_config.output_path,
                    auto_connect=auto_connect,
                    auto_connect_times=auto_connect_times
                )
                
                products[symbol] = product_config
                
            self.logger.info(f"转换完成，共 {len(products)} 个商品配置")
            return products
            
        except Exception as e:
            self.logger.error(f"转换配置失败: {str(e)}")
            return {}
    
    def get_dde_items_for_symbol(self, symbol: str, data_type: str) -> List[str]:
        """获取指定商品和数据类型的DDE项目列表"""
        try:
            if data_type not in self.data_type_templates:
                return []
            
            template = self.data_type_templates[data_type]
            items = []
            
            for item in template.items:
                # 替换{symbol}占位符
                dde_item = item.code.replace('{symbol}', symbol)
                items.append(dde_item)
            
            return items
            
        except Exception as e:
            self.logger.error(f"获取DDE项目失败: {symbol}, {data_type}, {str(e)}")
            return []
    
    def get_data_type_config(self, data_type: str) -> Optional[DataTypeTemplate]:
        """获取数据类型配置"""
        return self.data_type_templates.get(data_type)
    
    def get_symbol_list(self) -> List[str]:
        """获取商品列表"""
        return self.symbols.copy()
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return self.system_config.copy()
