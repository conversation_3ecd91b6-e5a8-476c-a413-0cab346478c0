# DDE Monitor Project .gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# DDE Monitor Specific Files
# =========================

# Log files and directories
logs/
*.log
*.log.*

# Data output files
data/
output/
outputs/
*.csv
complete_data_*.csv
dde_data_*.csv

# Temporary files
temp/
tmp/
*.tmp
*.temp

# Backup files
backup/
*.bak
*.backup
config.ini.bak
config_*.ini

# Test files
test_data/
test_output/
*_test.py
test_*.py

# Performance monitoring files
*.prof
*.profile

# Memory dumps
*.dmp
*.dump

# Process ID files
*.pid

# Lock files
*.lock

# Configuration overrides (keep template, ignore local configs)
config_local.ini
config_dev.ini
config_prod.ini
config_test.ini

# User-specific settings
user_settings.ini
personal_config.ini

# Runtime generated files
runtime/
cache/
*.cache

# Compiled executables (from PyInstaller)
*.exe
dde_monitor.exe
build/
dist/

# Documentation build files
docs/_build/
docs/build/

# IDE specific files
.idea/
.vscode/settings.json
.vscode/launch.json
*.sublime-project
*.sublime-workspace

# OS generated files
.DS_Store?
.Spotlight-V100
.Trashes
Icon?
ehthumbs.db
Thumbs.db

# Archive files
*.zip
*.rar
*.7z
*.tar
*.tar.gz
*.tar.bz2

# Database files
*.db
*.sqlite
*.sqlite3

# Certificate files
*.pem
*.key
*.crt
*.cert

# Environment specific
.env.local
.env.development
.env.test
.env.production

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# pytest
.pytest_cache/
pytest.ini

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Profiling data
*.prof
*.lprof

# mypy
.mypy_cache/

# Pyre type checker
.pyre/

# Local development scripts
run_local.bat
debug.bat
test_local.py

# Sensitive data (if any)
secrets/
credentials/
api_keys/
