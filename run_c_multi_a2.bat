d:

cd D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_multi_engine_hybrid\logs
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_multi_engine_hybrid\logs\multi_product_monitor.log /Q

cd D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_multi_engine_hybrid\outputs\TEMP\data\mon\XQ\FITXN07\_m
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_multi_engine_hybrid\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_tick.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_multi_engine_hybrid\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_order.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_multi_engine_hybrid\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_level2.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_multi_engine_hybrid\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_daily.csv /Q

cd D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\logs
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\logs\multi_product_monitor.log /Q

cd D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_tick.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_order.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_level2.csv /Q
del D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\outputs\TEMP\data\mon\XQ\FITXN07\_m\complete_data_daily.csv /Q
pause
wt -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor" cmd /k "python dde_monitor_multi.py" ; new-tab -p "命令提示字元" -d "D:\Python\project\DDE\dde08\_Run\dde08\dde_monitor\dde_multi_engine_hybrid" cmd /k "python run_hybrid_test.py"