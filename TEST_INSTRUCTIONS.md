# DDE 資料處理引擎測試說明

## 📋 測試目的

驗證新開發的 **DDE 資料處理引擎** 是否與現有的 **模組化版本** 產生相同的結果。

## 🕐 測試時間

建議在以下交易時間進行測試：
- **夜盤**: 15:00 - 05:00 (次日)
- **日盤**: 08:45 - 13:45

## 📁 測試檔案

### 核心檔案
- `dde_engine_real_test.py` - 使用新引擎的實際DDE測試程式
- `compare_results.py` - 結果比較工具
- `run_comparison_test.py` - 自動化測試啟動腳本

### 配置檔案
- `config_FITXN07_02.ini` - 測試配置（order類型，multiple模式）

### 輸出檔案
- `complete_data_02.csv` - 模組化版本輸出
- `complete_data_02_engine.csv` - 引擎版本輸出

## 🚀 測試方法

### 方法一：自動化測試（推薦）

```bash
python run_comparison_test.py
```

這個腳本會：
1. 檢查必要檔案
2. 清理舊的輸出檔案
3. 同時啟動模組化版本和引擎版本
4. 監控運行狀態
5. 自動比較結果

### 方法二：手動測試

#### 步驟1：啟動模組化版本
```bash
python dde_monitor_new.py config_FITXN07_02.ini
```

#### 步驟2：啟動引擎版本（新終端）
```bash
python dde_engine_real_test.py
```

#### 步驟3：運行一段時間後停止（Ctrl+C）

#### 步驟4：比較結果
```bash
python compare_results.py
```

## 📊 測試配置詳情

### config_FITXN07_02.ini 配置
- **資料類型**: order（委託資料）
- **檢查模式**: multiple
- **檢查項目**: 外盤量,累賣成筆
- **時間間隔**: 0.800秒

### 預期行為
- 當 **外盤量** 或 **累賣成筆** 有變化時，保存資料行
- 當兩個項目都沒有變化時，跳過資料行
- 其他項目的變化不影響保存決策

## 🔍 比較項目

比較工具會檢查：

1. **標題行**: 是否完全相同
2. **資料行數量**: 是否相等
3. **資料內容**: 逐行逐欄比較
4. **時間模式**: 分析時間間隔分布
5. **關鍵欄位**: 重點比較外盤量和累賣成筆

## ✅ 成功標準

測試成功的標準：
- ✅ 標題行完全相同
- ✅ 資料行數量相等
- ✅ 所有資料內容完全一致
- ✅ 關鍵欄位值完全匹配

## 🐛 問題排查

### 常見問題

1. **DDE連接失敗**
   - 確認券商軟體已開啟
   - 檢查DDE服務是否可用
   - 確認項目代碼正確

2. **檔案權限錯誤**
   - 確認輸出目錄有寫入權限
   - 檢查檔案是否被其他程式佔用

3. **資料不一致**
   - 檢查兩個版本是否同時啟動
   - 確認使用相同的配置檔案
   - 檢查時間同步

### 日誌檔案

查看詳細日誌：
- `dde_engine_real_test.log` - 引擎版本日誌
- `outputs/TEMP/logs/mon/XQ/FITXN07/_a/dde_monitor_02.log` - 模組化版本日誌
- `outputs/TEMP/logs/mon/XQ/FITXN07/_a/dde_monitor_02_engine.log` - 引擎版本日誌

## 📈 測試結果解讀

### 完全一致
```
🎉 完全一致！DDE 資料處理引擎與模組化版本產生相同結果
```
表示新引擎完全正確，可以用於生產環境。

### 發現差異
```
⚠️  發現差異，需要進一步檢查:
  - 標題行不同
  - 資料行數量不同  
  - 資料內容不同
```
需要檢查差異原因，可能需要調整引擎邏輯。

## 🔧 測試建議

1. **測試時長**: 建議運行至少10-15分鐘，確保有足夠的資料樣本
2. **多次測試**: 在不同時間段進行多次測試，驗證一致性
3. **不同配置**: 可以測試其他配置檔案（如 config_FITXN07_01.ini）
4. **性能監控**: 觀察CPU和記憶體使用情況

## 📞 技術支援

如果測試過程中遇到問題，請提供：
1. 錯誤訊息截圖
2. 相關日誌檔案
3. 測試環境資訊
4. 具體的操作步驟

---

**注意**: 請確保在測試前備份重要資料，避免意外覆蓋現有檔案。
