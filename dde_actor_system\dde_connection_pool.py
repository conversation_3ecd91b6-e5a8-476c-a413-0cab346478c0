#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 連接池管理

解決 127 個連接限制問題
"""

import time
import threading
from collections import defaultdict
from typing import Dict, List, Optional
import sys
from pathlib import Path

# 添加項目根目錄到Python路徑
sys.path.insert(0, str(Path(__file__).parent))

from dydde.dydde import DDEClient


class DDEConnectionPool:
    """DDE 連接池管理器"""
    
    def __init__(self, max_connections=120):  # 留一些餘量
        self.max_connections = max_connections
        self.connections: Dict[str, DDEClient] = {}
        self.connection_items: Dict[str, List[str]] = defaultdict(list)
        self.connection_count = 0
        self.lock = threading.Lock()
        
    def get_connection_key(self, service: str, topic: str) -> str:
        """生成連接鍵值"""
        return f"{service}.{topic}"
    
    def create_connection(self, service: str, topic: str) -> Optional[DDEClient]:
        """創建新連接"""
        with self.lock:
            if self.connection_count >= self.max_connections:
                print(f"⚠️  已達到最大連接數 {self.max_connections}")
                return None
            
            conn_key = self.get_connection_key(service, topic)
            
            if conn_key in self.connections:
                return self.connections[conn_key]
            
            try:
                client = DDEClient(service, topic)
                client.connect()
                
                self.connections[conn_key] = client
                self.connection_count += 1
                
                print(f"✅ 創建連接 {conn_key} ({self.connection_count}/{self.max_connections})")
                return client
                
            except Exception as e:
                print(f"❌ 創建連接失敗 {conn_key}: {str(e)}")
                return None
    
    def get_connection(self, service: str, topic: str) -> Optional[DDEClient]:
        """獲取連接（復用現有連接）"""
        conn_key = self.get_connection_key(service, topic)
        
        with self.lock:
            if conn_key in self.connections:
                return self.connections[conn_key]
        
        return self.create_connection(service, topic)
    
    def add_item_to_connection(self, service: str, topic: str, item: str):
        """將項目添加到連接"""
        conn_key = self.get_connection_key(service, topic)
        
        with self.lock:
            if item not in self.connection_items[conn_key]:
                self.connection_items[conn_key].append(item)
    
    def request_data(self, service: str, topic: str, item: str) -> Optional[str]:
        """請求數據"""
        client = self.get_connection(service, topic)
        if not client:
            return None
        
        try:
            result = client.request(item)
            self.add_item_to_connection(service, topic, item)
            return result
        except Exception as e:
            print(f"❌ 數據請求失敗 {item}: {str(e)}")
            return None
    
    def get_stats(self) -> Dict:
        """獲取統計信息"""
        with self.lock:
            total_items = sum(len(items) for items in self.connection_items.values())
            
            return {
                'total_connections': self.connection_count,
                'max_connections': self.max_connections,
                'total_items': total_items,
                'connections': dict(self.connection_items)
            }
    
    def cleanup(self):
        """清理所有連接"""
        with self.lock:
            print(f"🧹 清理 {self.connection_count} 個連接...")
            
            for conn_key, client in self.connections.items():
                try:
                    client.disconnect()
                    print(f"   ✅ 已清理 {conn_key}")
                except Exception as e:
                    print(f"   ⚠️  清理失敗 {conn_key}: {str(e)}")
            
            self.connections.clear()
            self.connection_items.clear()
            self.connection_count = 0
            
            print("✅ 連接池清理完成")


class SmartDDEMonitor:
    """智能 DDE 監控器（使用連接池）"""
    
    def __init__(self, max_connections=120):
        self.pool = DDEConnectionPool(max_connections)
        self.products = []
        
    def add_product(self, symbol: str, service: str, topic: str, items: List[str]):
        """添加產品"""
        self.products.append({
            'symbol': symbol,
            'service': service,
            'topic': topic,
            'items': items
        })
    
    def test_all_products(self):
        """測試所有產品"""
        print(f"🚀 開始測試 {len(self.products)} 個產品")
        print("=" * 60)
        
        successful_products = 0
        total_items = 0
        
        for i, product in enumerate(self.products, 1):
            symbol = product['symbol']
            service = product['service']
            topic = product['topic']
            items = product['items']
            
            print(f"\n📦 測試產品 {i}/{len(self.products)}: {symbol}")
            
            successful_items = 0
            
            for item in items:
                result = self.pool.request_data(service, topic, item)
                if result:
                    successful_items += 1
                    total_items += 1
                else:
                    print(f"   ❌ {item}: 失敗")
            
            if successful_items > 0:
                successful_products += 1
                print(f"   ✅ {symbol}: {successful_items}/{len(items)} 項目成功")
            else:
                print(f"   ❌ {symbol}: 所有項目失敗")
        
        # 顯示統計
        stats = self.pool.get_stats()
        
        print(f"\n" + "=" * 60)
        print(f"📊 測試結果統計")
        print(f"=" * 60)
        print(f"成功產品: {successful_products}/{len(self.products)}")
        print(f"成功項目: {total_items}")
        print(f"使用連接: {stats['total_connections']}/{stats['max_connections']}")
        
        print(f"\n📋 連接分布:")
        for conn_key, items in stats['connections'].items():
            print(f"   {conn_key}: {len(items)} 項目")
        
        return stats
    
    def cleanup(self):
        """清理資源"""
        self.pool.cleanup()


def create_test_products():
    """創建測試產品列表"""
    products = []
    
    # 期貨產品
    futures = ["FITXN07", "FITXN08", "FITXN09", "FITM08"]
    for symbol in futures:
        items = [
            f"{symbol}.TF-Time",
            f"{symbol}.TF-Price",
            f"{symbol}.TF-Volume",
            f"{symbol}.TF-TotalVolume"
        ]
        products.append({
            'symbol': symbol,
            'service': 'XQTISC',
            'topic': 'Quote',
            'items': items
        })
    
    # 股票產品
    stocks = ["2330", "2317", "2454"]
    for symbol in stocks:
        items = [
            f"{symbol}.TW-Time",
            f"{symbol}.TW-Price",
            f"{symbol}.TW-Volume",
            f"{symbol}.TW-TotalVolume"
        ]
        products.append({
            'symbol': symbol,
            'service': 'XQTISC',
            'topic': 'Quote',
            'items': items
        })
    
    return products


def main():
    """主函數"""
    print("🔧 DDE 連接池測試")
    print("解決 127 個連接限制問題")
    print("=" * 60)
    
    # 創建智能監控器
    monitor = SmartDDEMonitor(max_connections=120)
    
    # 添加測試產品
    products = create_test_products()
    for product in products:
        monitor.add_product(
            product['symbol'],
            product['service'],
            product['topic'],
            product['items']
        )
    
    try:
        # 測試所有產品
        stats = monitor.test_all_products()
        
        print(f"\n💡 連接池優勢:")
        print(f"   - 復用連接，減少連接數")
        print(f"   - 智能管理，避免超限")
        print(f"   - 統一清理，防止洩漏")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  測試被中斷")
    except Exception as e:
        print(f"\n❌ 測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        monitor.cleanup()


if __name__ == "__main__":
    main()
