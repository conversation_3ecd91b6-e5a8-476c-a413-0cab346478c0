#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件驗證工具
檢查配置項之間的一致性和潛在問題
"""

import configparser
import logging
from typing import List, Dict, Tuple

class ConfigValidator:
    """配置文件驗證器"""
    
    def __init__(self):
        self.warnings = []
        self.errors = []
        self.log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        self.log_level_values = {level: i for i, level in enumerate(self.log_levels)}
    
    def validate_config(self, config: configparser.ConfigParser) -> Tuple[List[str], List[str]]:
        """驗證配置文件
        
        Returns:
            Tuple[List[str], List[str]]: (warnings, errors)
        """
        self.warnings = []
        self.errors = []
        
        # 檢查日誌相關配置
        self._check_logging_config(config)
        
        # 檢查文件輸出配置
        self._check_file_output_config(config)
        
        # 檢查通知配置
        self._check_notification_config(config)
        
        # 檢查自動化配置
        self._check_automation_config(config)
        
        return self.warnings, self.errors
    
    def _check_logging_config(self, config: configparser.ConfigParser):
        """檢查日誌配置"""
        if 'Logging' not in config:
            self.warnings.append("缺少 [Logging] 配置段，將使用預設值")
            return
        
        logging_section = config['Logging']
        
        # 檢查日誌級別設置
        log_level = logging_section.get('log_level', 'WARNING').upper()
        console_level = logging_section.get('console_log_level', log_level).upper()
        file_level = logging_section.get('file_log_level', 'INFO').upper()
        
        # 驗證日誌級別有效性
        for level_name, level in [('log_level', log_level), 
                                 ('console_log_level', console_level),
                                 ('file_log_level', file_level)]:
            if level not in self.log_levels:
                self.errors.append(f"無效的日誌級別 {level_name}={level}，有效值：{', '.join(self.log_levels)}")
        
        # 檢查級別邏輯關係
        if all(level in self.log_levels for level in [log_level, console_level, file_level]):
            main_level_val = self.log_level_values[log_level]
            console_level_val = self.log_level_values[console_level]
            file_level_val = self.log_level_values[file_level]
            
            if console_level_val < main_level_val:
                self.warnings.append(f"console_log_level ({console_level}) 低於 log_level ({log_level})，可能不會顯示預期的日誌")
            
            if file_level_val < main_level_val:
                self.warnings.append(f"file_log_level ({file_level}) 低於 log_level ({log_level})，可能不會記錄預期的日誌")
            
            if file_level_val > console_level_val:
                self.warnings.append(f"file_log_level ({file_level}) 高於 console_log_level ({console_level})，建議文件記錄更詳細的信息")
    
    def _check_file_output_config(self, config: configparser.ConfigParser):
        """檢查文件輸出配置"""
        if 'FileOutput' not in config:
            self.warnings.append("缺少 [FileOutput] 配置段，將使用預設值")
            return
        
        file_output = config['FileOutput']
        enable_log_file = file_output.getboolean('enable_log_file', True)
        
        # 檢查日誌文件與日誌級別的一致性
        if not enable_log_file and 'Logging' in config:
            logging_section = config['Logging']
            if 'file_log_level' in logging_section:
                self.warnings.append("enable_log_file=false 但設置了 file_log_level，該設置將被忽略")
        
        # 檢查路徑配置
        if 'OutputPath' not in config:
            self.warnings.append("缺少 [OutputPath] 配置段，將使用預設路徑")
        else:
            output_path = config['OutputPath']
            
            if enable_log_file and 'log_file' not in output_path:
                self.warnings.append("啟用了日誌文件但未指定 log_file 路徑")
            
            enable_data_file = file_output.getboolean('enable_data_file', True)
            if enable_data_file and 'data_file' not in output_path:
                self.warnings.append("啟用了數據文件但未指定 data_file 路徑")
            
            enable_complete_file = file_output.getboolean('enable_complete_data_file', True)
            if enable_complete_file and 'complete_data_file' not in output_path:
                self.warnings.append("啟用了完整數據文件但未指定 complete_data_file 路徑")
    
    def _check_notification_config(self, config: configparser.ConfigParser):
        """檢查通知配置"""
        if 'Notifications' not in config:
            self.warnings.append("缺少 [Notifications] 配置段，將使用預設值")
            return
        
        notifications = config['Notifications']
        enable_notifications = notifications.getboolean('enable_system_notifications', True)
        
        if enable_notifications:
            notification_levels_str = notifications.get('notification_levels', 'WARNING,ERROR')
            notification_levels = [level.strip().upper() for level in notification_levels_str.split(',')]
            
            # 檢查通知級別有效性
            for level in notification_levels:
                if level not in self.log_levels:
                    self.errors.append(f"無效的通知級別：{level}")
            
            # 檢查與主日誌級別的關係
            if 'Logging' in config:
                main_log_level = config['Logging'].get('log_level', 'WARNING').upper()
                if main_log_level in self.log_levels:
                    main_level_val = self.log_level_values[main_log_level]
                    
                    for level in notification_levels:
                        if level in self.log_levels:
                            level_val = self.log_level_values[level]
                            if level_val < main_level_val:
                                self.warnings.append(f"通知級別 {level} 低於主日誌級別 {main_log_level}，可能不會觸發通知")
    
    def _check_automation_config(self, config: configparser.ConfigParser):
        """檢查自動化配置"""
        # 檢查自動連線配置
        if 'AutoConnect' in config:
            auto_connect = config['AutoConnect']
            enable_auto_connect = auto_connect.getboolean('enable_auto_connect', False)
            
            if enable_auto_connect:
                mode = auto_connect.get('auto_connect_mode', 'delay')
                if mode not in ['immediate', 'delay', 'schedule']:
                    self.errors.append(f"無效的自動連線模式：{mode}")
                
                if mode == 'schedule':
                    schedule_times = auto_connect.get('schedule_connect_times', '')
                    if not schedule_times:
                        self.errors.append("schedule 模式需要設置 schedule_connect_times")

                    # 檢查 schedule_end_action 設定
                    schedule_end_action = auto_connect.get('schedule_end_action', 'unadvise_only')
                    if schedule_end_action.lower() not in ['disconnect', 'unadvise_only']:
                        self.warnings.append(f"無效的 schedule_end_action 值：{schedule_end_action}，將自動修正為 unadvise_only")
        
        # 檢查自動結束配置
        if 'AutoShutdown' in config:
            auto_shutdown = config['AutoShutdown']
            enable_auto_shutdown = auto_shutdown.getboolean('enable_auto_shutdown', False)
            
            if enable_auto_shutdown:
                shutdown_time = auto_shutdown.get('shutdown_time', '')
                if not shutdown_time:
                    self.errors.append("啟用自動結束需要設置 shutdown_time")
                else:
                    # 簡單的時間格式檢查
                    try:
                        parts = shutdown_time.split(':')
                        if len(parts) != 3:
                            raise ValueError()
                        hour, minute, second = map(int, parts)
                        if not (0 <= hour <= 23 and 0 <= minute <= 59 and 0 <= second <= 59):
                            raise ValueError()
                    except ValueError:
                        self.errors.append(f"無效的結束時間格式：{shutdown_time}，應為 HH:MM:SS")

def validate_config_file(config_path: str = 'config.ini') -> None:
    """驗證配置文件並輸出結果"""
    try:
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        
        validator = ConfigValidator()
        warnings, errors = validator.validate_config(config)
        
        print(f"📋 配置文件驗證結果：{config_path}")
        print("=" * 50)
        
        if errors:
            print("❌ 錯誤：")
            for i, error in enumerate(errors, 1):
                print(f"  {i}. {error}")
            print()
        
        if warnings:
            print("⚠️ 警告：")
            for i, warning in enumerate(warnings, 1):
                print(f"  {i}. {warning}")
            print()
        
        if not errors and not warnings:
            print("✅ 配置文件驗證通過，沒有發現問題！")
        
        print(f"總計：{len(errors)} 個錯誤，{len(warnings)} 個警告")
        
    except FileNotFoundError:
        print(f"❌ 找不到配置文件：{config_path}")
    except Exception as e:
        print(f"❌ 驗證配置文件時發生錯誤：{str(e)}")

if __name__ == "__main__":
    validate_config_file()
