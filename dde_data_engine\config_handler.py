#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDE 資料處理器配置處理
處理資料處理器的配置參數
"""

from typing import Dict, List, Optional, Any
import logging


class DataProcessorConfig:
    """資料處理器配置類別
    
    管理DDE資料處理器的所有配置參數
    """
    
    def __init__(self, config_dict: Optional[Dict[str, Any]] = None):
        """初始化配置
        
        Args:
            config_dict: 配置字典
        """
        self.config = config_dict or {}
        self._set_defaults()
    
    def _set_defaults(self):
        """設置默認配置值"""
        defaults = {
            # 時間間隔設定
            'enable_time_newline': True,
            'time_newline_interval': 0.800,
            
            # 值變化檢查設定
            'enable_value_change_check': True,
            'value_change_check_mode': 'single',  # single, multiple, all
            'value_change_check_items': '',
            
            # 檔案輸出設定
            'enable_data_file': False,
            'enable_complete_data_file': True,
            'enable_log_file': True,
            
            # 資料處理設定
            'max_history_rows': 10,
            'max_raw_data_queue': 1000,
            
            # 日誌設定
            'log_level': 'INFO',
            'debug_mode': False
        }
        
        for key, value in defaults.items():
            if key not in self.config:
                self.config[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """獲取配置值
        
        Args:
            key: 配置鍵
            default: 默認值
            
        Returns:
            配置值
        """
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """設置配置值
        
        Args:
            key: 配置鍵
            value: 配置值
        """
        self.config[key] = value
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """獲取布林配置值
        
        Args:
            key: 配置鍵
            default: 默認值
            
        Returns:
            布林值
        """
        value = self.get(key, default)
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return bool(value)
    
    def get_float(self, key: str, default: float = 0.0) -> float:
        """獲取浮點數配置值
        
        Args:
            key: 配置鍵
            default: 默認值
            
        Returns:
            浮點數值
        """
        try:
            return float(self.get(key, default))
        except (ValueError, TypeError):
            return default
    
    def get_int(self, key: str, default: int = 0) -> int:
        """獲取整數配置值
        
        Args:
            key: 配置鍵
            default: 默認值
            
        Returns:
            整數值
        """
        try:
            return int(self.get(key, default))
        except (ValueError, TypeError):
            return default
    
    def get_list(self, key: str, separator: str = ',', default: Optional[List[str]] = None) -> List[str]:
        """獲取列表配置值
        
        Args:
            key: 配置鍵
            separator: 分隔符
            default: 默認值
            
        Returns:
            字符串列表
        """
        value = self.get(key, '')
        if not value:
            return default or []
        
        if isinstance(value, list):
            return value
        
        return [item.strip() for item in str(value).split(separator) if item.strip()]
    
    def validate(self) -> List[str]:
        """驗證配置
        
        Returns:
            錯誤訊息列表
        """
        errors = []
        
        # 檢查時間間隔
        interval = self.get_float('time_newline_interval')
        if interval <= 0:
            errors.append("time_newline_interval 必須大於 0")
        
        # 檢查值變化檢查模式
        mode = self.get('value_change_check_mode', '').lower()
        if mode not in ['single', 'multiple', 'all']:
            errors.append("value_change_check_mode 必須是 single, multiple 或 all")
        
        # 檢查歷史記錄數量
        max_history = self.get_int('max_history_rows')
        if max_history <= 0:
            errors.append("max_history_rows 必須大於 0")
        
        return errors
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典
        
        Returns:
            配置字典
        """
        return self.config.copy()
    
    def update(self, other_config: Dict[str, Any]):
        """更新配置
        
        Args:
            other_config: 其他配置字典
        """
        self.config.update(other_config)
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"DataProcessorConfig({self.config})"
