{"system": {"system_name": "DDE Actor System", "version": "1.0.0", "debug_mode": false, "log_level": "INFO", "log_file": "logs/system.log", "log_max_size_mb": 100, "log_backup_count": 5, "enable_monitoring": true, "monitoring_interval": 60.0, "health_check_interval": 300.0, "enable_remote_api": false, "api_host": "localhost", "api_port": 8080}, "performance": {"dde_buffer_size": 1048576, "dde_batch_size": 1000, "dde_batch_timeout": 0.01, "processing_batch_size": 1000, "processing_queue_size": 10000, "processing_workers": 4, "gui_update_interval_ms": 16, "gui_max_batch_size": 1000, "gui_virtual_rows": 100, "file_batch_size": 1000, "file_flush_interval": 5.0, "file_buffer_size": 8192, "backpressure_high_watermark": 8000, "backpressure_low_watermark": 6000, "backpressure_strategy": "drop_oldest", "memory_pool_initial_size": 1000, "gc_threshold": 10000}, "products": [{"symbol": "FITXN07", "data_types": ["tick", "order"], "service": "SKCOM", "topic": "SKCOM", "items": ["FITXN07.tick.成交價", "FITXN07.tick.成交量", "FITXN07.tick.累計量", "FITXN07.order.買價", "FITXN07.order.買量", "FITXN07.order.賣價", "FITXN07.order.賣量"], "enabled": true, "priority": 1, "custom_settings": {"description": "台指期貨07", "exchange": "TAIFEX", "contract_type": "futures"}}, {"symbol": "FITXN08", "data_types": ["tick", "order"], "service": "SKCOM", "topic": "SKCOM", "items": ["FITXN08.tick.成交價", "FITXN08.tick.成交量", "FITXN08.tick.累計量", "FITXN08.order.買價", "FITXN08.order.買量", "FITXN08.order.賣價", "FITXN08.order.賣量"], "enabled": true, "priority": 2, "custom_settings": {"description": "台指期貨08", "exchange": "TAIFEX", "contract_type": "futures"}}, {"symbol": "2330", "data_types": ["tick"], "service": "SKCOM", "topic": "SKCOM", "items": ["2330.tick.成交價", "2330.tick.成交量", "2330.tick.累計量"], "enabled": false, "priority": 3, "custom_settings": {"description": "台積電", "exchange": "TWSE", "contract_type": "stock"}}]}