#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
數據處理Actor實現

提供高性能的數據處理功能，包括：
- 批量數據處理
- 數據驗證和轉換
- 業務邏輯處理
- 背壓控制機制
"""

import asyncio
import logging
import time
import statistics
from collections import deque, defaultdict
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import threading

from core.actor_base import ActorBase
from core.message_system import Message, MessageType
from core.performance import PerformanceMonitor


@dataclass
class ProcessingRule:
    """數據處理規則"""
    name: str
    condition: Callable[[str, str], bool]  # (item, value) -> bool
    action: Callable[[str, str], Any]      # (item, value) -> processed_value
    priority: int = 0


@dataclass
class ValidationRule:
    """數據驗證規則"""
    name: str
    validator: Callable[[str, str], bool]  # (item, value) -> is_valid
    error_action: str = "log"  # "log", "drop", "fix"


class BackpressureController:
    """背壓控制器
    
    當系統負載過高時，智能控制數據流量
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化背壓控制器
        
        Args:
            config: 配置字典
        """
        self.max_queue_size = config.get('max_queue_size', 10000)
        self.high_watermark = config.get('high_watermark', 8000)
        self.low_watermark = config.get('low_watermark', 6000)
        self.drop_strategy = config.get('drop_strategy', 'drop_oldest')
        
        self.current_load = 0
        self.dropped_count = 0
        self.throttle_factor = 1.0
        
        self.logger = logging.getLogger("BackpressureController")
    
    def should_process(self, queue_size: int) -> bool:
        """判斷是否應該處理新數據
        
        Args:
            queue_size: 當前隊列大小
            
        Returns:
            bool: 是否應該處理
        """
        self.current_load = queue_size
        
        if queue_size > self.high_watermark:
            # 高負載，開始限流
            self.throttle_factor = max(0.1, 1.0 - (queue_size - self.high_watermark) / 
                                     (self.max_queue_size - self.high_watermark))
            return False
        elif queue_size < self.low_watermark:
            # 低負載，恢復正常
            self.throttle_factor = 1.0
            return True
        else:
            # 中等負載，根據限流因子決定
            return True
    
    def get_drop_count(self) -> int:
        """獲取丟棄的數據數量"""
        return self.dropped_count
    
    def reset_stats(self):
        """重置統計信息"""
        self.dropped_count = 0


class MemoryPool:
    """內存池管理器
    
    預分配對象以減少GC壓力
    """
    
    def __init__(self, object_factory: Callable, initial_size: int = 1000):
        """初始化內存池
        
        Args:
            object_factory: 對象工廠函數
            initial_size: 初始池大小
        """
        self.object_factory = object_factory
        self.available = deque()
        self.in_use = set()
        self.lock = threading.Lock()
        
        # 預分配對象
        for _ in range(initial_size):
            obj = object_factory()
            self.available.append(obj)
    
    def acquire(self):
        """獲取對象"""
        with self.lock:
            if self.available:
                obj = self.available.popleft()
            else:
                obj = self.object_factory()
            
            self.in_use.add(obj)
            return obj
    
    def release(self, obj):
        """釋放對象"""
        with self.lock:
            if obj in self.in_use:
                self.in_use.remove(obj)
                # 重置對象狀態
                if hasattr(obj, 'reset'):
                    obj.reset()
                self.available.append(obj)
    
    def get_stats(self) -> Dict[str, int]:
        """獲取池統計信息"""
        with self.lock:
            return {
                'available': len(self.available),
                'in_use': len(self.in_use),
                'total': len(self.available) + len(self.in_use)
            }


class DataProcessorActor(ActorBase):
    """數據處理Actor
    
    負責高性能的數據處理和業務邏輯
    """
    
    def __init__(self, name: str, config: Optional[Dict] = None):
        """初始化數據處理Actor
        
        Args:
            name: Actor名稱
            config: 配置字典
        """
        super().__init__(name, config)
        
        # 處理配置
        self.batch_size = self.config.get('batch_size', 1000)
        self.batch_timeout = self.config.get('batch_timeout', 0.01)
        self.enable_validation = self.config.get('enable_validation', True)
        self.enable_transformation = self.config.get('enable_transformation', True)
        
        # 背壓控制
        self.backpressure = BackpressureController(self.config.get('backpressure', {}))
        
        # 處理規則
        self.processing_rules: List[ProcessingRule] = []
        self.validation_rules: List[ValidationRule] = []
        
        # 內存池
        self.memory_pool = MemoryPool(dict, 1000)
        
        # 統計信息
        self.stats = {
            'batches_processed': 0,
            'items_processed': 0,
            'items_validated': 0,
            'items_transformed': 0,
            'items_dropped': 0,
            'validation_errors': 0,
            'processing_errors': 0,
            'avg_batch_size': 0.0,
            'avg_processing_time_ms': 0.0
        }
        
        # 性能監控
        self.performance_monitor = PerformanceMonitor()
        self.processing_times = deque(maxlen=1000)
        
        # 處理隊列
        self.processing_queue = asyncio.Queue(maxsize=self.config.get('queue_size', 10000))
        self.processing_task: Optional[asyncio.Task] = None
    
    async def on_start(self):
        """Actor啟動時的初始化"""
        try:
            # 加載處理規則
            await self._load_processing_rules()
            
            # 加載驗證規則
            await self._load_validation_rules()
            
            # 啟動性能監控
            await self.performance_monitor.start()
            
            # 啟動處理任務
            self.processing_task = asyncio.create_task(self._processing_loop())
            
            self.logger.info(f"數據處理Actor {self.name} 啟動成功")
            
        except Exception as e:
            self.logger.error(f"數據處理Actor啟動失敗: {str(e)}")
            raise
    
    async def on_stop(self):
        """Actor停止時的清理"""
        try:
            # 停止處理任務
            if self.processing_task:
                self.processing_task.cancel()
                try:
                    await self.processing_task
                except asyncio.CancelledError:
                    pass
            
            # 停止性能監控
            await self.performance_monitor.stop()
            
            self.logger.info(f"數據處理Actor {self.name} 停止成功")
            
        except Exception as e:
            self.logger.error(f"數據處理Actor停止失敗: {str(e)}")
    
    async def handle_message(self, message: Message):
        """處理接收到的消息"""
        try:
            if message.type == MessageType.DATA_PROCESS:
                await self._handle_data_process_message(message)
            elif message.type == MessageType.DDE_DATA:
                await self._handle_dde_data_message(message)
            elif message.type == MessageType.SYSTEM_CONFIG_UPDATE:
                await self._handle_config_update_message(message)
            else:
                self.logger.warning(f"未知消息類型: {message.type}")
                
        except Exception as e:
            self.logger.error(f"處理消息失敗: {str(e)}")
            self.stats['processing_errors'] += 1
    
    async def _handle_data_process_message(self, message: Message):
        """處理數據處理消息"""
        try:
            data = message.data
            
            if 'batch' in data:
                # 批量處理
                batch_messages = data['batch']
                await self._process_message_batch(batch_messages)
            else:
                # 單個處理
                await self.processing_queue.put(message)
                
        except Exception as e:
            self.logger.error(f"處理數據處理消息失敗: {str(e)}")
            self.stats['processing_errors'] += 1
    
    async def _handle_dde_data_message(self, message: Message):
        """處理DDE數據消息"""
        try:
            # 檢查背壓控制
            if not self.backpressure.should_process(self.processing_queue.qsize()):
                self.stats['items_dropped'] += 1
                return
            
            await self.processing_queue.put(message)
            
        except asyncio.QueueFull:
            self.stats['items_dropped'] += 1
            self.logger.warning("處理隊列已滿，丟棄數據")
        except Exception as e:
            self.logger.error(f"處理DDE數據消息失敗: {str(e)}")
            self.stats['processing_errors'] += 1
    
    async def _handle_config_update_message(self, message: Message):
        """處理配置更新消息"""
        try:
            new_config = message.data.get('config', {})
            
            # 更新配置
            self.config.update(new_config)
            
            # 重新加載規則
            await self._load_processing_rules()
            await self._load_validation_rules()
            
            self.logger.info("配置更新成功")
            
        except Exception as e:
            self.logger.error(f"處理配置更新失敗: {str(e)}")
    
    async def _processing_loop(self):
        """數據處理循環"""
        self.logger.info("數據處理循環開始")
        
        batch = []
        last_process_time = time.time()
        
        while self.running:
            try:
                # 收集批量數據
                try:
                    timeout = self.batch_timeout - (time.time() - last_process_time)
                    if timeout <= 0:
                        timeout = 0.001
                    
                    message = await asyncio.wait_for(
                        self.processing_queue.get(),
                        timeout=timeout
                    )
                    batch.append(message)
                    
                    # 繼續收集直到達到批量大小或超時
                    while len(batch) < self.batch_size:
                        try:
                            message = await asyncio.wait_for(
                                self.processing_queue.get(),
                                timeout=0.001
                            )
                            batch.append(message)
                        except asyncio.TimeoutError:
                            break
                            
                except asyncio.TimeoutError:
                    pass  # 超時也要處理現有批次
                
                # 處理批次
                if batch:
                    await self._process_message_batch(batch)
                    batch.clear()
                    last_process_time = time.time()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"數據處理循環錯誤: {str(e)}")
                self.stats['processing_errors'] += 1
                await asyncio.sleep(0.1)
        
        self.logger.info("數據處理循環結束")
    
    async def _process_message_batch(self, messages: List[Message]):
        """處理消息批次
        
        Args:
            messages: 消息列表
        """
        start_time = time.time()
        
        try:
            processed_items = []
            
            for message in messages:
                if message.type == MessageType.DDE_DATA:
                    item = message.data.get('item', '')
                    value = message.data.get('value', '')
                    
                    # 數據驗證
                    if self.enable_validation and not await self._validate_data(item, value):
                        self.stats['validation_errors'] += 1
                        continue
                    
                    # 數據轉換
                    if self.enable_transformation:
                        processed_value = await self._transform_data(item, value)
                    else:
                        processed_value = value
                    
                    processed_items.append({
                        'item': item,
                        'value': processed_value,
                        'original_value': value,
                        'timestamp': message.timestamp,
                        'metadata': message.metadata or {}
                    })
                    
                    self.stats['items_processed'] += 1
            
            # 發送處理結果
            if processed_items:
                await self._send_processed_data(processed_items)
            
            # 更新統計信息
            processing_time = (time.time() - start_time) * 1000
            self.processing_times.append(processing_time)
            self.stats['batches_processed'] += 1
            self.stats['avg_batch_size'] = (
                (self.stats['avg_batch_size'] * (self.stats['batches_processed'] - 1) + len(messages)) /
                self.stats['batches_processed']
            )
            self.stats['avg_processing_time_ms'] = statistics.mean(self.processing_times)
            
            # 記錄性能指標
            self.performance_monitor.record_latency('batch_processing', processing_time)
            self.performance_monitor.increment_throughput('data_processed')
            
        except Exception as e:
            self.logger.error(f"處理消息批次失敗: {str(e)}")
            self.stats['processing_errors'] += 1

    async def _validate_data(self, item: str, value: str) -> bool:
        """驗證數據

        Args:
            item: DDE項目
            value: DDE值

        Returns:
            bool: 驗證是否通過
        """
        try:
            for rule in self.validation_rules:
                if not rule.validator(item, value):
                    self.logger.warning(f"數據驗證失敗: {rule.name}, {item}={value}")

                    if rule.error_action == "drop":
                        return False
                    elif rule.error_action == "fix":
                        # 實現數據修復邏輯
                        pass

            self.stats['items_validated'] += 1
            return True

        except Exception as e:
            self.logger.error(f"數據驗證錯誤: {str(e)}")
            return False

    async def _transform_data(self, item: str, value: str) -> Any:
        """轉換數據

        Args:
            item: DDE項目
            value: DDE值

        Returns:
            Any: 轉換後的值
        """
        try:
            transformed_value = value

            # 應用處理規則
            for rule in sorted(self.processing_rules, key=lambda r: r.priority):
                if rule.condition(item, value):
                    transformed_value = rule.action(item, transformed_value)

            self.stats['items_transformed'] += 1
            return transformed_value

        except Exception as e:
            self.logger.error(f"數據轉換錯誤: {str(e)}")
            return value

    async def _send_processed_data(self, processed_items: List[Dict]):
        """發送處理後的數據

        Args:
            processed_items: 處理後的數據項目列表
        """
        try:
            # 發送到GUI更新Actor
            gui_message = Message(
                type=MessageType.GUI_BATCH_UPDATE,
                data={'items': processed_items},
                sender=self.name
            )
            await self.send_message("GUIUpdater", gui_message)

            # 發送到文件寫入Actor
            file_message = Message(
                type=MessageType.FILE_WRITE,
                data={'items': processed_items},
                sender=self.name
            )
            await self.send_message("FileWriter", file_message)

        except Exception as e:
            self.logger.error(f"發送處理後數據失敗: {str(e)}")

    async def _load_processing_rules(self):
        """加載處理規則"""
        try:
            rules_config = self.config.get('processing_rules', [])
            self.processing_rules.clear()

            for rule_config in rules_config:
                # 這裡可以實現動態規則加載
                # 暫時使用簡單的示例規則
                pass

            # 添加默認規則
            self._add_default_processing_rules()

        except Exception as e:
            self.logger.error(f"加載處理規則失敗: {str(e)}")

    async def _load_validation_rules(self):
        """加載驗證規則"""
        try:
            rules_config = self.config.get('validation_rules', [])
            self.validation_rules.clear()

            # 添加默認驗證規則
            self._add_default_validation_rules()

        except Exception as e:
            self.logger.error(f"加載驗證規則失敗: {str(e)}")

    def _add_default_processing_rules(self):
        """添加默認處理規則"""
        # 數值格式化規則
        def format_number(item: str, value: str) -> str:
            try:
                if '.' in value:
                    return f"{float(value):.2f}"
                else:
                    return str(int(value))
            except ValueError:
                return value

        number_rule = ProcessingRule(
            name="format_number",
            condition=lambda item, value: value.replace('.', '').replace('-', '').isdigit(),
            action=format_number,
            priority=1
        )
        self.processing_rules.append(number_rule)

    def _add_default_validation_rules(self):
        """添加默認驗證規則"""
        # 非空驗證
        empty_rule = ValidationRule(
            name="not_empty",
            validator=lambda item, value: bool(item and value),
            error_action="drop"
        )
        self.validation_rules.append(empty_rule)

        # 長度驗證
        length_rule = ValidationRule(
            name="max_length",
            validator=lambda item, value: len(value) <= 1000,
            error_action="log"
        )
        self.validation_rules.append(length_rule)

    def add_processing_rule(self, rule: ProcessingRule):
        """添加處理規則

        Args:
            rule: 處理規則
        """
        self.processing_rules.append(rule)
        self.processing_rules.sort(key=lambda r: r.priority)

    def add_validation_rule(self, rule: ValidationRule):
        """添加驗證規則

        Args:
            rule: 驗證規則
        """
        self.validation_rules.append(rule)

    def get_processing_stats(self) -> Dict[str, Any]:
        """獲取處理統計信息

        Returns:
            Dict: 處理統計信息
        """
        backpressure_stats = {
            'current_load': self.backpressure.current_load,
            'throttle_factor': self.backpressure.throttle_factor,
            'dropped_count': self.backpressure.get_drop_count()
        }

        memory_pool_stats = self.memory_pool.get_stats()

        return {
            'processing_stats': self.stats.copy(),
            'backpressure_stats': backpressure_stats,
            'memory_pool_stats': memory_pool_stats,
            'queue_size': self.processing_queue.qsize(),
            'processing_rules_count': len(self.processing_rules),
            'validation_rules_count': len(self.validation_rules)
        }
